"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[9319],{5299:(e,i,o)=>{o.d(i,{A:()=>c});var t=o(7723);const c=({defaultTitle:e=(0,t.__)("Step","woocommerce"),defaultDescription:i=(0,t.__)("Step description text.","woocommerce"),defaultShowStepNumber:o=!0})=>({title:{type:"string",default:e},description:{type:"string",default:i},showStepNumber:{type:"boolean",default:o}})},4621:(e,i,o)=>{o.r(i),o.d(i,{default:()=>R});var t=o(4921),c=o(1616),s=o(4656),r=o(7143),n=o(7594),p=o(6473),a=o(8331),l=o(4199),h=o(7723),d=o(4530),m=o(8415),u=o(4970),g=o(6087),k=o(3932),_=o(5703),w=o(1069),x=o(910),b=o(790);const v=({minRate:e,maxRate:i,multiple:o=!1})=>{if(void 0===e||void 0===i)return null;const t=(0,_.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),c=(0,_.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(i.price,10)+parseInt(i.taxes,10):parseInt(i.price,10),r=0===t?(0,b.jsx)("em",{children:(0,h.__)("free","woocommerce")}):(0,b.jsx)(s.FormattedMonetaryAmount,{currency:(0,x.getCurrencyFromPriceResponse)(e),value:t});return(0,b.jsx)("span",{className:"wc-block-checkout__shipping-method-option-price",children:t!==c||o?(0,g.createInterpolateElement)(0===t&&0===c?"<price />":(0,h.__)("from <price />","woocommerce"),{price:r}):r})};function f(e){return e?{min:e.reduce(((e,i)=>(0,k.jV)(i.method_id)?e:void 0===e||parseInt(i.price,10)<parseInt(e.price,10)?i:e),void 0),max:e.reduce(((e,i)=>(0,k.jV)(i.method_id)?e:void 0===e||parseInt(i.price,10)>parseInt(e.price,10)?i:e),void 0)}:{min:void 0,max:void 0}}function j(e){return e?{min:e.reduce(((e,i)=>(0,k.jV)(i.method_id)&&(void 0===e||i.price<e.price)?i:e),void 0),max:e.reduce(((e,i)=>(0,k.jV)(i.method_id)&&(void 0===e||i.price>e.price)?i:e),void 0)}:{min:void 0,max:void 0}}const C=(0,h.__)("Pickup","woocommerce"),I=(0,h.__)("Ship","woocommerce");var S=o(4784);const P={hidden:!0,message:(0,h.__)("Shipping options are not available","woocommerce")},N=({checked:e,rate:i,showPrice:o,showIcon:c,toggleText:s,multiple:r,onClick:n})=>(0,b.jsxs)(w.$,{render:(0,b.jsx)("div",{}),role:"radio",onClick:n,"aria-checked":"pickup"===e,className:(0,t.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"pickup"===e}),children:[(0,b.jsxs)("span",{className:"wc-block-checkout__shipping-method-option-title-wrapper",children:[!0===c&&(0,b.jsx)(d.A,{icon:m.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,b.jsx)("span",{className:"wc-block-checkout__shipping-method-option-title",children:s})]}),!0===o&&(0,b.jsx)(v,{multiple:r,minRate:i.min,maxRate:i.max})]}),T=({checked:e,rate:i,showPrice:o,showIcon:c,toggleText:s,onClick:p,shippingCostRequiresAddress:a=!1})=>{const l=(0,r.useSelect)((e=>e(n.cartStore).getShippingRates().some((({shipping_rates:e})=>!e.every(k.J_))))),m=a&&(0,S.ND)()&&!l,_=void 0!==i.min&&void 0!==i.max,{setValidationErrors:x,clearValidationError:f}=(0,r.useDispatch)(n.validationStore);(0,g.useEffect)((()=>("shipping"!==e||_?f("shipping-rates-error"):x({"shipping-rates-error":P}),()=>f("shipping-rates-error"))),[e,f,_,x]);const j=void 0===i.min||m?(0,b.jsx)("span",{className:"wc-block-checkout__shipping-method-option-price",children:(0,h.__)("calculated with an address","woocommerce")}):(0,b.jsx)(v,{minRate:i.min,maxRate:i.max});return(0,b.jsxs)(w.$,{render:(0,b.jsx)("div",{}),role:"radio",onClick:p,"aria-checked":"shipping"===e,className:(0,t.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"shipping"===e}),children:[(0,b.jsxs)("span",{className:"wc-block-checkout__shipping-method-option-title-wrapper",children:[!0===c&&(0,b.jsx)(d.A,{icon:u.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,b.jsx)("span",{className:"wc-block-checkout__shipping-method-option-title",children:s})]}),!0===o&&j]})},y=({checked:e,onChange:i,showPrice:o,showIcon:t,localPickupText:c,shippingText:s})=>{const{shippingRates:r}=(0,p.m)(),n=(0,_.getSetting)("shippingCostRequiresAddress",!1),a=(0,_.getSetting)("localPickupText",c||C);return(0,b.jsxs)("div",{id:"shipping-method",className:"components-button-group wc-block-checkout__shipping-method-container",role:"radiogroup",children:[(0,b.jsx)(T,{checked:e,onClick:()=>{i("shipping")},rate:f(r[0]?.shipping_rates),showPrice:o,showIcon:t,shippingCostRequiresAddress:n,toggleText:s||I}),(0,b.jsx)(N,{checked:e,onClick:()=>{i("pickup")},rate:j(r[0]?.shipping_rates),multiple:r.length>1,showPrice:o,showIcon:t,toggleText:a})]})},A={...(0,o(5299).A)({defaultTitle:(0,h.__)("Delivery","woocommerce"),defaultDescription:(0,h.__)("Select how you would like to receive your order.","woocommerce")}),className:{type:"string",default:""},showIcon:{type:"boolean",default:!0},showPrice:{type:"boolean",default:!1},localPickupText:{type:"string",default:C},shippingText:{type:"string",default:I},lock:{type:"object",default:{move:!0,remove:!0}}},R=(0,c.withFilteredAttributes)(A)((({title:e,description:i,children:o,className:c,showPrice:h,showIcon:d,shippingText:m,localPickupText:u})=>{const{showFormStepNumbers:g}=(0,l.O)(),{checkoutIsProcessing:k,prefersCollection:_}=(0,r.useSelect)((e=>{const i=e(n.checkoutStore);return{checkoutIsProcessing:i.isProcessing(),prefersCollection:i.prefersCollection()}})),{setPrefersCollection:w}=(0,r.useDispatch)(n.checkoutStore),{needsShipping:x,isCollectable:v}=(0,p.m)();return a.h0&&x&&v&&a.F7&&a.mH?(0,b.jsxs)(s.FormStep,{id:"shipping-method",disabled:k,className:(0,t.A)("wc-block-checkout__shipping-method",c),title:e,description:i,showStepNumber:g,children:[(0,b.jsx)(y,{checked:_?"pickup":"shipping",onChange:e=>{w("pickup"===e)},showPrice:h,showIcon:d,localPickupText:u,shippingText:m}),o]}):null}))}}]);