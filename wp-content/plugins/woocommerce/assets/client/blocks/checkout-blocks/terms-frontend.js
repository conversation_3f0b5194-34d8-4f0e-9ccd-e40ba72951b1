"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4654],{8889:(e,o,c)=>{c.d(o,{VM:()=>i,Xm:()=>s,iG:()=>n});var t=c(1e3),r=c(5460),a=c(790);const s=()=>{const{extensions:e,receiveCart:o,...c}=(0,r.V)(),s={extensions:e,cart:c,context:"woocommerce/checkout"};return(0,a.jsx)(t.ExperimentalOrderMeta.Slot,{...s})},{Fill:n,Slot:i}=(0,t.createSlotFill)("checkoutOrderSummaryActionArea")},7690:(e,o,c)=>{c.r(o),c.d(o,{default:()=>b});var t=c(7723),r=c(4921),a=c(6087),s=c(4656),n=c(4908),i=c(9491),l=c(7143),d=c(7594),m=c(8331);const h=m.gu?`<a href="${m.gu}" target="_blank">${(0,t.__)("Terms and Conditions","woocommerce")}</a>`:(0,t.__)("Terms and Conditions","woocommerce"),u=m.pk?`<a href="${m.pk}" target="_blank">${(0,t.__)("Privacy Policy","woocommerce")}</a>`:(0,t.__)("Privacy Policy","woocommerce"),_=(0,t.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,t.__)("By proceeding with your purchase you agree to our %1$s and %2$s","woocommerce"),h,u),k=(0,t.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,t.__)("You must accept our %1$s and %2$s to continue with your purchase.","woocommerce"),h,u);var p=c(8889),w=c(790);const b=(0,i.withInstanceId)((({text:e,checkbox:o,instanceId:c,className:i,showSeparator:m})=>{const[h,u]=(0,a.useState)(!1),{isDisabled:b}=(0,n.w)(),x="terms-and-conditions-"+c,{setValidationErrors:g,clearValidationError:C}=(0,l.useDispatch)(d.validationStore),y=(0,l.useSelect)((e=>e(d.validationStore).getValidationError(x))),S=!(!y?.message||y?.hidden);return(0,a.useEffect)((()=>{if(o)return h?C(x):g({[x]:{message:(0,t.__)("Please read and accept the terms and conditions.","woocommerce"),hidden:!0}}),()=>{C(x)}}),[o,h,x,C,g]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(p.VM,{}),(0,w.jsx)("div",{className:(0,r.A)("wc-block-checkout__terms",{"wc-block-checkout__terms--disabled":b,"wc-block-checkout__terms--with-separator":"false"!==m&&!1!==m},i),children:o?(0,w.jsx)(w.Fragment,{children:(0,w.jsx)(s.CheckboxControl,{id:"terms-and-conditions",checked:h,onChange:()=>u((e=>!e)),hasError:S,disabled:b,children:(0,w.jsx)("span",{className:"wc-block-components-checkbox__label",dangerouslySetInnerHTML:{__html:e||k}})})}):(0,w.jsx)("span",{className:"wc-block-components-checkbox__label",dangerouslySetInnerHTML:{__html:e||_}})})]})}))}}]);