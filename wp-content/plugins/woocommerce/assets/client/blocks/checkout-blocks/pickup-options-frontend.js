"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3024],{5299:(e,t,c)=>{c.d(t,{A:()=>r});var o=c(7723);const r=({defaultTitle:e=(0,o.__)("Step","woocommerce"),defaultDescription:t=(0,o.__)("Step description text.","woocommerce"),defaultShowStepNumber:c=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:c}})},184:(e,t,c)=>{c.r(t),c.d(t,{default:()=>F});var o=c(4921),r=c(1616),a=c(4656),n=c(7143),i=c(7594),s=c(8331),l=c(4199),p=c(7723),d=c(6087),u=c(6473),m=c(5460),k=c(910),g=c(8537),h=c(5703),_=c(4530),f=c(9835),x=c(3932),b=c(1e3),C=c(4923),S=c(9194),y=c(4007),w=c(790);const P=(e,t)=>{const c=(0,h.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):e.price,o=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_location"===e.key));return t?t.value:""}return""})(e),r=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_address"===e.key));return t?t.value:""}return""})(e),n=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_details"===e.key));return t?t.value:""}return""})(e);let i=(0,w.jsx)("em",{children:(0,p.__)("free","woocommerce")});return parseInt(c,10)>0&&(i=1===t?(0,w.jsx)(a.FormattedMonetaryAmount,{currency:(0,k.getCurrencyFromPriceResponse)(e),value:c}):(0,d.createInterpolateElement)(/* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */ /* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */
(0,p._n)("<price/> x <packageCount/> package","<price/> x <packageCount/> packages",t,"woocommerce"),{price:(0,w.jsx)(a.FormattedMonetaryAmount,{currency:(0,k.getCurrencyFromPriceResponse)(e),value:c}),packageCount:(0,w.jsx)(w.Fragment,{children:t})})),{value:e.rate_id,label:o?(0,g.decodeEntities)(o):(0,g.decodeEntities)(e.name),secondaryLabel:i,description:r?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(_.A,{icon:f.A,className:"wc-block-editor-components-block-icon"}),(0,g.decodeEntities)(r)]}):void 0,secondaryDescription:n?(0,w.jsx)(S.A,{maxLines:2,children:(0,g.decodeEntities)(n)}):void 0}},j=()=>{const{shippingRates:e,selectShippingRate:t}=(0,u.m)(),c=(0,d.useMemo)((()=>(e[0]?.shipping_rates||[]).filter(x.J_)),[e]),[o,r]=(0,d.useState)((()=>c.find((e=>e.selected))?.rate_id||"")),a=(0,d.useCallback)((e=>{t(e)}),[t]),{extensions:n,receiveCart:i,...s}=(0,m.V)(),l={extensions:n,cart:s,components:{ShippingRatesControlPackage:y.A,LocalPickupSelect:C.G},renderPickupLocation:P};(0,d.useEffect)((()=>{!o&&c[0]&&o!==c[0].rate_id&&(r(c[0].rate_id),a(c[0].rate_id))}),[c,o]);const p=(0,x.T4)(e);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(b.ExperimentalOrderLocalPickupPackages.Slot,{...l}),(0,w.jsx)(b.ExperimentalOrderLocalPickupPackages,{children:(0,w.jsx)(C.G,{title:e[0].name,setSelectedOption:r,onSelectRate:a,selectedOption:o,renderPickupLocation:P,pickupLocations:c,packageCount:p})})]})},v={...(0,c(5299).A)({defaultTitle:(0,p.__)("Pickup locations","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}},F=(0,r.withFilteredAttributes)(v)((({title:e,description:t,children:c,className:r})=>{const{checkoutIsProcessing:p,prefersCollection:d}=(0,n.useSelect)((e=>{const t=e(i.checkoutStore);return{checkoutIsProcessing:t.isProcessing(),prefersCollection:t.prefersCollection()}})),{showFormStepNumbers:u}=(0,l.O)();return d&&s.F7?(0,w.jsxs)(a.FormStep,{id:"pickup-options",disabled:p,className:(0,o.A)("wc-block-checkout__pickup-options",r),title:e,description:t,showStepNumber:u,children:[(0,w.jsx)(j,{}),c]}):null}))}}]);