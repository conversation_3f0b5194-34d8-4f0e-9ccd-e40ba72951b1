"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[3024],{63325:(e,s,c)=>{c.r(s),c.d(s,{default:()=>a});var t=c(4921),o=c(41616),i=c(14656),l=c(47143),r=c(47594),n=c(78331),p=c(94199),u=c(47520),k=c(16078),h=c(10790);const a=(0,o.withFilteredAttributes)(k.A)((({title:e,description:s,children:c,className:o})=>{const{checkoutIsProcessing:k,prefersCollection:a}=(0,l.useSelect)((e=>{const s=e(r.checkoutStore);return{checkoutIsProcessing:s.isProcessing(),prefersCollection:s.prefersCollection()}})),{showFormStepNumbers:b}=(0,p.O)();return a&&n.F7?(0,h.jsxs)(i.FormStep,{id:"pickup-options",disabled:k,className:(0,t.A)("wc-block-checkout__pickup-options",o),title:e,description:s,showStepNumber:b,children:[(0,h.jsx)(u.A,{}),c]}):null}))}}]);