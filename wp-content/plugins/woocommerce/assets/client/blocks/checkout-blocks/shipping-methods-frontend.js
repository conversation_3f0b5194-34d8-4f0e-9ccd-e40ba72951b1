"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[5806],{5299:(e,t,s)=>{s.d(t,{A:()=>o});var i=s(7723);const o=({defaultTitle:e=(0,i.__)("Step","woocommerce"),defaultDescription:t=(0,i.__)("Step description text.","woocommerce"),defaultShowStepNumber:s=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:s}})},7362:(e,t,s)=>{s.r(t),s.d(t,{default:()=>F});var i=s(4921),o=s(1616),n=s(4656),r=s(7792),c=s(7143),a=s(7594),p=s(4199),l=s(7723),d=s(6473),h=s(9702),u=s(8228),m=s(3932),g=s(4982),_=s(910),b=s(7370),w=s(8696),f=s(8537),k=s(5703),S=s(9021),x=s(6087),j=s(790);const y=e=>{const t=(0,k.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),s=0===t?(0,j.jsx)("span",{className:"wc-block-checkout__shipping-option--free",children:(0,l.__)("Free","woocommerce")}):(0,j.jsx)(n.FormattedMonetaryAmount,{currency:(0,_.getCurrencyFromPriceResponse)(e),value:t});return{label:(0,f.decodeEntities)(e.name),value:e.rate_id,description:(0,f.decodeEntities)(e.delivery_time),secondaryLabel:s,secondaryDescription:(0,f.decodeEntities)(e.description)}},N=()=>(0,j.jsx)("p",{role:"status","aria-live":"polite",className:"wc-block-components-shipping-rates-control__no-shipping-address-message",children:(0,l.__)("Enter a shipping address to view shipping options.","woocommerce")}),C=({noShippingPlaceholder:e=null})=>{const{isEditor:t}=(0,b.m)(),{shippingRates:s,needsShipping:i,isLoadingRates:o,hasCalculatedShipping:r,isCollectable:c}=(0,d.m)(),{shippingAddress:a}=(0,h.q)(),p=(0,x.useMemo)((()=>c?s.map((e=>({...e,shipping_rates:e.shipping_rates.filter((e=>!(0,m.jV)(e.method_id)))}))):s),[s,c]);if(!i)return null;const _=(0,m.T4)(s);if(!r&&!_)return(0,j.jsx)(N,{});const f=(0,g.KY)(a);return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(n.StoreNoticesContainer,{context:w.tG.SHIPPING_METHODS}),t&&!_?e:(0,j.jsx)(u.A,{noResultsMessage:(0,j.jsx)(j.Fragment,{children:f?(0,j.jsx)(S.A,{isDismissible:!1,className:"wc-block-components-shipping-rates-control__no-results-notice",status:"warning",children:(0,l.__)("No shipping options are available for this address. Please verify the address is correct or try a different address.","woocommerce")}):(0,j.jsx)(N,{})}),renderOption:y,collapsible:!1,shippingRates:p,isLoadingRates:o,context:"woocommerce/checkout"})]})},v={...(0,s(5299).A)({defaultTitle:(0,l.__)("Shipping options","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}},F=(0,o.withFilteredAttributes)(v)((({title:e,description:t,children:s,className:o})=>{const{showFormStepNumbers:l}=(0,p.O)(),d=(0,c.useSelect)((e=>e(a.checkoutStore).isProcessing())),{showShippingMethods:h}=(0,r.C)();return h?(0,j.jsxs)(n.FormStep,{id:"shipping-option",disabled:d,className:(0,i.A)("wc-block-checkout__shipping-option",o),title:e,description:t,showStepNumber:l,children:[(0,j.jsx)(C,{}),s]}):null}))}}]);