{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/blockified-product-details", "title": "Blockified Product Details", "description": "Display a product's description, attributes, and reviews", "category": "woocommerce", "textdomain": "woocommerce", "ancestor": ["woocommerce/single-product", "woocommerce/product-template", "core/post-template"], "supports": {"interactivity": {"clientNavigation": true}, "align": ["wide", "full"]}, "attributes": {}, "usesContext": ["query", "queryId", "postId", "postType"]}