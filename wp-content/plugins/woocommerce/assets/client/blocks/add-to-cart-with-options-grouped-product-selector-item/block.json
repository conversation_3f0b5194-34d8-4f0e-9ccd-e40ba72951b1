{"name": "woocommerce/add-to-cart-with-options-grouped-product-selector-item", "title": "Grouped Product Selector Item Template (Experimental)", "description": "A list item template that represents a child product within the Grouped Product Selector block.", "category": "woocommerce-product-elements", "keywords": ["WooCommerce"], "usesContext": ["postId"], "ancestor": ["woocommerce/add-to-cart-with-options-grouped-product-selector"], "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json", "supports": {"inserter": false, "interactivity": true}, "style": "file:../woocommerce/add-to-cart-with-options-grouped-product-selector-item-style.css"}