(()=>{var e,t,o,s={9612:(e,t,o)=>{"use strict";var s=o(4921),r=o(5573),c=o(790);const n=(0,c.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",fill:"none",children:[(0,c.jsx)("path",{stroke:"currentColor",strokeWidth:"1.5",fill:"none",d:"M5 3.75h14c.69 0 1.25.56 1.25 1.25v14c0 .69-.56 1.25-1.25 1.25H5c-.69 0-1.25-.56-1.25-1.25V5c0-.69.56-1.25 1.25-1.25z"}),(0,c.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.4 10.75c0-.47.38-.85.85-.85h9.5c.47 0 .85.38.85.85v1.5c0 .47-.38.85-.85.85h-9.5a.85.85 0 01-.85-.85v-1.5zm1.2.35v.8h8.8v-.8H7.6zM12.4 15.25c0-.47.38-.85.85-.85h3.5c.47 0 .85.38.85.85v1.5c0 .47-.38.85-.85.85h-3.5a.85.85 0 01-.85-.85v-1.5zm1.2.35v.8h2.8v-.8h-2.8zM6.5 15.9a.6.6 0 01.6-.6h2.8a.6.6 0 010 1.2H7.1a.6.6 0 01-.6-.6zM6.5 7.9a.6.6 0 01.6-.6h9.8a.6.6 0 110 1.2H7.1a.6.6 0 01-.6-.6z",clipRule:"evenodd"})]});var i=o(4530);const a=window.wp.blocks,l=window.wp.blockEditor;var d=o(6087),p=o(9491);const m=(0,d.createContext)({hasContainerWidth:!1,containerClassName:"",isMobile:!1,isSmall:!1,isMedium:!1,isLarge:!1}),u=()=>(0,d.useContext)(m),h=({children:e,className:t=""})=>{const[o,r]=(()=>{const[e,{width:t}]=(0,p.useResizeObserver)();let o="";return t>700?o="is-large":t>520?o="is-medium":t>400?o="is-small":t&&(o="is-mobile"),[e,o]})(),n={hasContainerWidth:""!==r,containerClassName:r,isMobile:"is-mobile"===r,isSmall:"is-small"===r,isMedium:"is-medium"===r,isLarge:"is-large"===r};return(0,c.jsx)(m.Provider,{value:n,children:(0,c.jsxs)("div",{className:(0,s.A)(t,r),children:[o,e]})})};o(9163);const g=({children:e,className:t})=>(0,c.jsx)(h,{className:(0,s.A)("wc-block-components-sidebar-layout",t),children:e}),_=window.wp.data,k=(0,d.createContext)({isEditor:!1,currentPostId:0,currentView:"",previewData:{},getPreviewData:()=>({})}),b=()=>(0,d.useContext)(k),w=({children:e,currentPostId:t=0,previewData:o={},currentView:s="",isPreview:r=!1})=>{const n=(0,_.useSelect)((e=>t||e("core/editor").getCurrentPostId()),[t]),i=(0,d.useCallback)(((e,t={})=>o&&e in o?o[e]:t),[o]),a={isEditor:!0,currentPostId:n,currentView:s,previewData:o,getPreviewData:i,isPreview:r};return(0,c.jsx)(k.Provider,{value:a,children:e})},y=window.wp.plugins,x=window.wc.wcSettings;var f=o(7723);const v=(0,x.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),S=v.pluginUrl+"assets/images/",j=(v.pluginUrl,x.STORE_PAGES.shop,x.STORE_PAGES.checkout?.id),C=(x.STORE_PAGES.checkout,x.STORE_PAGES.privacy?.permalink),P=(x.STORE_PAGES.privacy,x.STORE_PAGES.terms?.permalink),E=(x.STORE_PAGES.terms,x.STORE_PAGES.cart?.id),N=x.STORE_PAGES.cart?.permalink,A=(x.STORE_PAGES.myaccount?.permalink?x.STORE_PAGES.myaccount.permalink:(0,x.getSetting)("wpLoginUrl","/wp-login.php"),(0,x.getSetting)("localPickupEnabled",!1)),I=((0,x.getSetting)("shippingMethodsExist",!1),(0,x.getSetting)("shippingEnabled",!0)),R=(0,x.getSetting)("countries",{}),T=(0,x.getSetting)("countryData",{}),M=Object.fromEntries(Object.keys(T).filter((e=>!0===T[e].allowBilling)).map((e=>[e,R[e]||""]))),B=Object.fromEntries(Object.keys(T).filter((e=>!0===T[e].allowShipping)).map((e=>[e,R[e]||""]))),D={...M,...B},O=Object.fromEntries(Object.keys(D).map((e=>[e,T[e].states||{}]))),F=Object.fromEntries(Object.keys(D).map((e=>[e,T[e].locale||{}]))),L={address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]},V=(0,x.getSetting)("addressFieldsLocations",L).address,U=(0,x.getSetting)("addressFieldsLocations",L).contact,$=(0,x.getSetting)("addressFieldsLocations",L).order,H=((0,x.getSetting)("additionalOrderFields",{}),(0,x.getSetting)("additionalContactFields",{}),(0,x.getSetting)("additionalAddressFields",{}),()=>{const{experimentalBlocksEnabled:e}=(0,x.getSetting)("wcBlocksConfig",{experimentalBlocksEnabled:!1});return e}),q=({imageUrl:e=`${S}/block-error.svg`,header:t=(0,f.__)("Oops!","woocommerce"),text:o=(0,f.__)("There was an error loading the content.","woocommerce"),errorMessage:s,errorMessagePrefix:r=(0,f.__)("Error:","woocommerce"),button:n,showErrorBlock:i=!0})=>i?(0,c.jsxs)("div",{className:"wc-block-error wc-block-components-error",children:[e&&(0,c.jsx)("img",{className:"wc-block-error__image wc-block-components-error__image",src:e,alt:""}),(0,c.jsxs)("div",{className:"wc-block-error__content wc-block-components-error__content",children:[t&&(0,c.jsx)("p",{className:"wc-block-error__header wc-block-components-error__header",children:t}),o&&(0,c.jsx)("p",{className:"wc-block-error__text wc-block-components-error__text",children:o}),s&&(0,c.jsxs)("p",{className:"wc-block-error__message wc-block-components-error__message",children:[r?r+" ":"",s]}),n&&(0,c.jsx)("p",{className:"wc-block-error__button wc-block-components-error__button",children:n})]})]}):null;o(5893);class z extends d.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("strong",{children:e.status}),": ",e.statusText]}),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{header:e,imageUrl:t,showErrorMessage:o=!0,showErrorBlock:s=!0,text:r,errorMessagePrefix:n,renderError:i,button:a}=this.props,{errorMessage:l,hasError:d}=this.state;return d?"function"==typeof i?i({errorMessage:l}):(0,c.jsx)(q,{showErrorBlock:s,errorMessage:o?l:null,header:e,imageUrl:t,text:r,errorMessagePrefix:n,button:a}):this.props.children}}const W=z,Y=window.wc.wcBlocksData;var G=o(1659),K=o.n(G);let Z=function(e){return e.ADD_EVENT_CALLBACK="add_event_callback",e.REMOVE_EVENT_CALLBACK="remove_event_callback",e}({});const J={},X=(e=J,{type:t,eventType:o,id:s,callback:r,priority:c})=>{const n=e.hasOwnProperty(o)?new Map(e[o]):new Map;switch(t){case Z.ADD_EVENT_CALLBACK:return n.set(s,{priority:c,callback:r}),{...e,[o]:n};case Z.REMOVE_EVENT_CALLBACK:return n.delete(s),{...e,[o]:n}}},Q=(e,t)=>(o,s=10)=>{const r=((e,t,o=10)=>({id:Math.floor(Math.random()*Date.now()).toString(),type:Z.ADD_EVENT_CALLBACK,eventType:e,callback:t,priority:o}))(e,o,s);return t(r),()=>{var o;t((o=e,{id:r.id,type:Z.REMOVE_EVENT_CALLBACK,eventType:o}))}},ee=(0,d.createContext)({onPaymentProcessing:()=>()=>()=>{},onPaymentSetup:()=>()=>()=>{}}),te=({children:e})=>{const{isProcessing:t,isIdle:o,isCalculating:s,hasError:r}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{isProcessing:t.isProcessing(),isIdle:t.isIdle(),hasError:t.hasError(),isCalculating:t.isCalculating()}})),{isPaymentReady:n}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{isPaymentProcessing:t.isPaymentProcessing(),isPaymentReady:t.isPaymentReady()}})),{setValidationErrors:i}=(0,_.useDispatch)(Y.validationStore),[a,l]=(0,d.useReducer)(X,{}),{onPaymentSetup:p}=(e=>(0,d.useMemo)((()=>({onPaymentSetup:Q("payment_setup",e)})),[e]))(l),m=(0,d.useRef)(a);(0,d.useEffect)((()=>{m.current=a}),[a]);const{__internalSetPaymentProcessing:u,__internalSetPaymentIdle:h,__internalEmitPaymentProcessingEvent:g}=(0,_.useDispatch)(Y.paymentStore);(0,d.useEffect)((()=>{!t||r||s||(u(),g(m.current,i))}),[t,r,s,u,g,i]),(0,d.useEffect)((()=>{o&&!n&&h()}),[o,n,h]),(0,d.useEffect)((()=>{r&&n&&h()}),[r,n,h]);const k={onPaymentProcessing:(0,d.useMemo)((()=>function(...e){return K()("onPaymentProcessing",{alternative:"onPaymentSetup",plugin:"WooCommerce Blocks"}),p(...e)}),[p]),onPaymentSetup:p};return(0,c.jsx)(ee.Provider,{value:k,children:e})},oe={NONE:"none",INVALID_ADDRESS:"invalid_address",UNKNOWN:"unknown_error"},se={INVALID_COUNTRY:"woocommerce_rest_cart_shipping_rates_invalid_country",MISSING_COUNTRY:"woocommerce_rest_cart_shipping_rates_missing_country",INVALID_STATE:"woocommerce_rest_cart_shipping_rates_invalid_state"},re={shippingErrorStatus:{isPristine:!0,isValid:!1,hasInvalidAddress:!1,hasError:!1},dispatchErrorStatus:e=>e,shippingErrorTypes:oe,onShippingRateSuccess:()=>()=>{},onShippingRateFail:()=>()=>{},onShippingRateSelectSuccess:()=>()=>{},onShippingRateSelectFail:()=>()=>{}},ce=(e,{type:t})=>Object.values(oe).includes(t)?t:e,ne="shipping_rates_success",ie="shipping_rates_fail",ae="shipping_rate_select_success",le="shipping_rate_select_fail",de=e=>({onSuccess:Q(ne,e),onFail:Q(ie,e),onSelectSuccess:Q(ae,e),onSelectFail:Q(le,e)}),pe=window.wc.wcTypes;let me=function(e){return e.CART="wc/cart",e.CHECKOUT="wc/checkout",e.PAYMENTS="wc/checkout/payments",e.EXPRESS_PAYMENTS="wc/checkout/express-payments",e.CONTACT_INFORMATION="wc/checkout/contact-information",e.SHIPPING_ADDRESS="wc/checkout/shipping-address",e.BILLING_ADDRESS="wc/checkout/billing-address",e.SHIPPING_METHODS="wc/checkout/shipping-methods",e.CHECKOUT_ACTIONS="wc/checkout/checkout-actions",e.ORDER_INFORMATION="wc/checkout/order-information",e}({});const ue=async(e,t,o)=>{const s=((e,t)=>e[t]?Array.from(e[t].values()).sort(((e,t)=>e.priority-t.priority)):[])(e,t),r=[];for(const e of s)try{const t=await Promise.resolve(e.callback(o));"object"==typeof t&&r.push(t)}catch(e){console.error(e)}return!r.length||r};var he=o(1824),ge=o.n(he);const _e=window.wp.htmlEntities,ke=Object.entries(F).reduce(((e,[t,o])=>(e[t]=Object.entries(o).reduce(((e,[t,o])=>(e[t]=(e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,f.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,f.__)("%s (optional)","woocommerce"),e.label)),e.index&&((0,pe.isNumber)(e.index)&&(t.index=e.index),(0,pe.isString)(e.index)&&(t.index=parseInt(e.index,10))),e.hidden&&(t.required=!1),t})(o),e)),{}),e)),{}),be=(e,t,o="")=>{const s=o&&void 0!==ke[o]?ke[o]:{};return e.map((e=>({key:e,...t&&e in t?t[e]:{},...s&&e in s?s[e]:{}}))).sort(((e,t)=>e.index-t.index))},we=window.wp.url,ye=(e,t)=>e in t,xe=e=>{const t=be(V,x.defaultFields,e.country),o=Object.assign({},e);return t.forEach((({key:t,hidden:s})=>{!0===s&&ye(t,e)&&(o[t]="")})),o},fe=(e,t=[])=>{if(!e.country)return!1;const o=be(V,x.defaultFields,e.country);return(t.length>0?o.filter((({key:e})=>t.includes(e))):o).every((({key:t,hidden:o,required:s})=>!0===o||!1===s||ye(t,e)&&""!==e[t]))},ve=window.CustomEvent||null,Se=(e,t,o=!1,s=!1)=>{if("function"!=typeof jQuery)return()=>{};const r=()=>{((e,{bubbles:t=!1,cancelable:o=!1,element:s,detail:r={}})=>{if(!ve)return;s||(s=document.body);const c=new ve(e,{bubbles:t,cancelable:o,detail:r});s.dispatchEvent(c)})(t,{bubbles:o,cancelable:s})};return jQuery(document).on(e,r),()=>jQuery(document).off(e,r)},je=e=>{const t=e?.detail;t&&t.preserveCartData||(0,_.dispatch)(Y.cartStore).invalidateResolutionForStore()},Ce=e=>{(e?.persisted||"back_forward"===(window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:""))&&(0,_.dispatch)(Y.cartStore).invalidateResolutionForStore()},Pe=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},Ee={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},Ne={...Ee,email:""},Ae={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:Y.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},Ie=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,_e.decodeEntities)(t)]))),Re={cartCoupons:Y.EMPTY_CART_COUPONS,cartItems:Y.EMPTY_CART_ITEMS,cartFees:Y.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:Y.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:Y.EMPTY_CART_ITEM_ERRORS,cartTotals:Ae,cartIsLoading:!0,cartErrors:Y.EMPTY_CART_ERRORS,billingData:Ne,billingAddress:Ne,shippingAddress:Ee,shippingRates:Y.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:Y.EMPTY_PAYMENT_METHODS,paymentRequirements:Y.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:Y.EMPTY_EXTENSIONS},Te=(e={shouldSelect:!0})=>{const{shouldSelect:t}=e,o=(0,d.useRef)(),s=(0,d.useRef)(Ne),r=(0,d.useRef)(Ee);(0,d.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),window.wcBlocksStoreCartListeners?.count>0)return void window.wcBlocksStoreCartListeners.count++;document.body.addEventListener("wc-blocks_added_to_cart",je),document.body.addEventListener("wc-blocks_removed_from_cart",je),window.addEventListener("pageshow",Ce);const e=Se("added_to_cart","wc-blocks_added_to_cart"),t=Se("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",je),document.body.removeEventListener("wc-blocks_removed_from_cart",je),window.removeEventListener("pageshow",Ce),e(),t()}})(),Pe)),[]);const c=(0,_.useSelect)(((e,{dispatch:o})=>{if(!t)return Re;const c=e(Y.cartStore),n=c.getCartData(),i=c.getCartErrors(),a=c.getCartTotals(),l=!c.hasFinishedResolution("getCartData"),d=c.isCustomerDataUpdating(),{receiveCart:p,receiveCartContents:m}=o(Y.cartStore),u=n.fees.length>0?n.fees.map((e=>Ie(e))):Y.EMPTY_CART_FEES,h=n.coupons.length>0?n.coupons.map((e=>({...e,label:e.code}))):Y.EMPTY_CART_COUPONS,g=xe(Ie(n.billingAddress)),_=n.needsShipping?xe(Ie(n.shippingAddress)):g;return ge()(g,s.current)||(s.current=g),ge()(_,r.current)||(r.current=_),{cartCoupons:h,cartItems:n.items,crossSellsProducts:n.crossSells,cartFees:u,cartItemsCount:n.itemsCount,cartItemsWeight:n.itemsWeight,cartNeedsPayment:n.needsPayment,cartNeedsShipping:n.needsShipping,cartItemErrors:n.errors,cartTotals:a,cartIsLoading:l,cartErrors:i,billingData:s.current,billingAddress:s.current,shippingAddress:r.current,extensions:n.extensions,shippingRates:n.shippingRates,isLoadingRates:d,cartHasCalculatedShipping:n.hasCalculatedShipping,paymentRequirements:n.paymentRequirements,receiveCart:p,receiveCartContents:m}}),[t]);return o.current&&ge()(o.current,c)||(o.current=c),o.current},Me=e=>e.length,Be=(0,x.getSetting)("collectableMethodIds",[]),De=e=>Be.includes(e.method_id),Oe=e=>!!A&&(Array.isArray(e)?!!e.find((e=>Be.includes(e))):Be.includes(e)),Fe=e=>e.some((e=>!!e.shipping_rates.length)),Le=e=>Object.fromEntries(e.map((({package_id:e,shipping_rates:t})=>[e,t.find((e=>e.selected))?.rate_id||""])));var Ve=o(923),Ue=o.n(Ve);const $e=window.wp.hooks,He=()=>({dispatchStoreEvent:(0,d.useCallback)(((e,t={})=>{try{(0,$e.doAction)(`experimental__woocommerce_blocks-${e}`,t)}catch(e){console.error(e)}}),[]),dispatchCheckoutEvent:(0,d.useCallback)(((e,t={})=>{try{(0,$e.doAction)(`experimental__woocommerce_blocks-checkout-${e}`,{...t,storeCart:(0,_.select)("wc/store/cart").getCartData()})}catch(e){console.error(e)}}),[])}),qe=()=>{const{shippingRates:e,needsShipping:t,hasCalculatedShipping:o,isLoadingRates:s,isCollectable:r,isSelectingRate:c}=(0,_.useSelect)((e=>{const t=e(Y.cartStore),o=t.getShippingRates();return{shippingRates:o,needsShipping:t.getNeedsShipping(),hasCalculatedShipping:t.getHasCalculatedShipping(),isLoadingRates:t.isCustomerDataUpdating(),isCollectable:o.every((({shipping_rates:e})=>e.find((({method_id:e})=>Oe(e))))),isSelectingRate:t.isShippingRateBeingSelected()}}),[]),n=(0,d.useRef)({});(0,d.useEffect)((()=>{const t=Le(e);(0,pe.isObject)(t)&&!Ue()(n.current,t)&&(n.current=t)}),[e]);const{selectShippingRate:i}=(0,_.useDispatch)(Y.cartStore),a=Oe(Object.values(n.current).map((e=>e.split(":")[0]))),{dispatchCheckoutEvent:l}=He(),p=(0,d.useCallback)(((e,t)=>{let o;void 0!==e&&(o=Oe(e.split(":")[0])?i(e,null):i(e,t),o.then((()=>{l("set-selected-shipping-rate",{shippingRateId:e})})).catch((e=>{(0,Y.processErrorResponse)(e)})))}),[i,l]);return{isSelectingRate:c,selectedRates:n.current,selectShippingRate:p,shippingRates:e,needsShipping:t,hasCalculatedShipping:o,isLoadingRates:s,isCollectable:r,hasSelectedLocalPickup:a}},{NONE:ze,INVALID_ADDRESS:We,UNKNOWN:Ye}=oe,Ge=(0,d.createContext)(re),Ke=()=>(0,d.useContext)(Ge),Ze=({children:e})=>{const{__internalStartCalculation:t,__internalFinishCalculation:o}=(0,_.useDispatch)(Y.checkoutStore),{shippingRates:s,isLoadingRates:r,cartErrors:n}=Te(),{selectedRates:i,isSelectingRate:a}=qe(),[l,p]=(0,d.useReducer)(ce,ze),[m,u]=(0,d.useReducer)(X,{}),h=(0,d.useRef)(m),g=(0,d.useMemo)((()=>({onShippingRateSuccess:de(u).onSuccess,onShippingRateFail:de(u).onFail,onShippingRateSelectSuccess:de(u).onSelectSuccess,onShippingRateSelectFail:de(u).onSelectFail})),[u]);(0,d.useEffect)((()=>{h.current=m}),[m]),(0,d.useEffect)((()=>{r?t():o()}),[r,t,o]),(0,d.useEffect)((()=>{a?t():o()}),[t,o,a]),(0,d.useEffect)((()=>{n.length>0&&n.some((e=>!(!e.code||!Object.values(se).includes(e.code))))?p({type:We}):p({type:ze})}),[n]);const k=(0,d.useMemo)((()=>({isPristine:l===ze,isValid:l===ze,hasInvalidAddress:l===We,hasError:l===Ye||l===We})),[l]);(0,d.useEffect)((()=>{r||0!==s.length&&!k.hasError||ue(h.current,ie,{hasInvalidAddress:k.hasInvalidAddress,hasError:k.hasError})}),[s,r,k.hasError,k.hasInvalidAddress]),(0,d.useEffect)((()=>{!r&&s.length>0&&!k.hasError&&ue(h.current,ne,s)}),[s,r,k.hasError]),(0,d.useEffect)((()=>{a||(k.hasError?ue(h.current,le,{hasError:k.hasError,hasInvalidAddress:k.hasInvalidAddress}):ue(h.current,ae,i.current))}),[i,a,k.hasError,k.hasInvalidAddress]);const b={shippingErrorStatus:k,dispatchErrorStatus:p,shippingErrorTypes:oe,...g};return(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(Ge.Provider,{value:b,children:e})})};function Je(e,t){const o=(0,d.useRef)();return(0,d.useEffect)((()=>{o.current===e||t&&!t(e,o.current)||(o.current=e)}),[e,t]),o.current}const Xe=window.wp.notices,Qe=window.wc.blocksCheckoutEvents,et={},tt={},ot=()=>et,st=()=>tt,rt=(0,d.createContext)({onSubmit:()=>{},onCheckoutAfterProcessingWithSuccess:()=>()=>{},onCheckoutAfterProcessingWithError:()=>()=>{},onCheckoutBeforeProcessing:()=>()=>{},onCheckoutValidationBeforeProcessing:()=>()=>{},onCheckoutSuccess:()=>()=>{},onCheckoutFail:()=>()=>{},onCheckoutValidation:()=>()=>{}}),ct=({children:e,redirectUrl:t})=>{const o=ot(),s=st(),{isEditor:r}=b(),{__internalUpdateAvailablePaymentMethods:n}=(0,_.useDispatch)(Y.paymentStore);(0,d.useEffect)((()=>{(r||0!==Object.keys(o).length||0!==Object.keys(s).length)&&n()}),[r,o,s,n]);const{__internalSetRedirectUrl:i,__internalEmitValidateEvent:a,__internalEmitAfterProcessingEvents:l,__internalSetBeforeProcessing:p}=(0,_.useDispatch)(Y.checkoutStore),{checkoutRedirectUrl:m,checkoutStatus:u,isCheckoutBeforeProcessing:h,isCheckoutAfterProcessing:g,checkoutHasError:k,checkoutOrderId:w,checkoutOrderNotes:y,checkoutCustomerId:x}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{checkoutRedirectUrl:t.getRedirectUrl(),checkoutStatus:t.getCheckoutStatus(),isCheckoutBeforeProcessing:t.isBeforeProcessing(),isCheckoutAfterProcessing:t.isAfterProcessing(),checkoutHasError:t.hasError(),checkoutOrderId:t.getOrderId(),checkoutOrderNotes:t.getOrderNotes(),checkoutCustomerId:t.getCustomerId()}}));t&&t!==m&&i(t);const{setValidationErrors:f}=(0,_.useDispatch)(Y.validationStore),{dispatchCheckoutEvent:v}=He(),S=Object.values(me).filter((e=>e!==me.PAYMENTS&&e!==me.EXPRESS_PAYMENTS)),j=(0,_.useSelect)((e=>{const{getNotices:t}=e(Xe.store);return S.reduce(((e,o)=>[...e,...t(o)]),[])}),[S]),{paymentNotices:C,expressPaymentNotices:P}=(0,_.useSelect)((e=>{const{getNotices:t}=e(Xe.store);return{paymentNotices:t(me.PAYMENTS),expressPaymentNotices:t(me.EXPRESS_PAYMENTS)}}),[]),[E]=(0,d.useReducer)(X,{}),N=(0,d.useRef)(E),{onCheckoutValidation:A,onCheckoutSuccess:I,onCheckoutFail:R}=Qe.checkoutEvents;(0,d.useEffect)((()=>{N.current=E}),[E]);const T=(0,d.useMemo)((()=>function(...e){return K()("onCheckoutBeforeProcessing",{alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks"}),A(...e)}),[A]),M=(0,d.useMemo)((()=>function(...e){return K()("onCheckoutValidationBeforeProcessing",{since:"9.7.0",alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),A(...e)}),[A]),B=(0,d.useMemo)((()=>function(...e){return K()("onCheckoutAfterProcessingWithSuccess",{since:"9.7.0",alternative:"onCheckoutSuccess",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),I(...e)}),[I]),D=(0,d.useMemo)((()=>function(...e){return K()("onCheckoutAfterProcessingWithError",{since:"9.7.0",alternative:"onCheckoutFail",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),R(...e)}),[R]);(0,d.useEffect)((()=>{h&&a({setValidationErrors:f})}),[h,f,a]);const O=Je(u),F=Je(k);(0,d.useEffect)((()=>{u===O&&k===F||g&&l({notices:{checkoutNotices:j,paymentNotices:C,expressPaymentNotices:P}})}),[u,k,m,w,x,y,g,h,O,F,j,P,C,a,l]);const L={onSubmit:(0,d.useCallback)((()=>{v("submit"),p()}),[v,p]),onCheckoutBeforeProcessing:T,onCheckoutValidationBeforeProcessing:M,onCheckoutAfterProcessingWithSuccess:B,onCheckoutAfterProcessingWithError:D,onCheckoutSuccess:I,onCheckoutFail:R,onCheckoutValidation:A};return(0,c.jsx)(rt.Provider,{value:L,children:e})},nt=window.wp.apiFetch;var it=o.n(nt);(0,f.__)("Something went wrong. Please contact us to get assistance.","woocommerce");const at=window.wc.wcBlocksRegistry,lt=(e,t,o)=>{const s=Object.keys(e).map((t=>({key:t,value:e[t]})),[]),r=`wc-${o}-new-payment-method`;return s.push({key:r,value:t}),s},dt=e=>{if(!e)return;const{__internalSetCustomerId:t}=(0,_.dispatch)(Y.checkoutStore);it().setNonce&&"function"==typeof it().setNonce&&it().setNonce(e),it().setCartHash&&"function"==typeof it().setCartHash&&it().setCartHash(e),e?.get("User-ID")&&t(parseInt(e.get("User-ID")||"0",10))},pt=()=>{const{customerData:e,isInitialized:t}=(0,_.useSelect)((e=>{const t=e(Y.cartStore);return{customerData:t.getCustomerData(),isInitialized:t.hasFinishedResolution("getCartData")}})),{setShippingAddress:o,setBillingAddress:s}=(0,_.useDispatch)(Y.cartStore);return{isInitialized:t,billingAddress:e.billingAddress,shippingAddress:e.shippingAddress,setBillingAddress:s,setShippingAddress:o}},mt=()=>{const{isEditor:e,getPreviewData:t}=b(),{needsShipping:o}=qe(),{useShippingAsBilling:s,prefersCollection:r,editingBillingAddress:c,editingShippingAddress:n}=(0,_.useSelect)((e=>({useShippingAsBilling:e(Y.checkoutStore).getUseShippingAsBilling(),prefersCollection:e(Y.checkoutStore).prefersCollection(),editingBillingAddress:e(Y.checkoutStore).getEditingBillingAddress(),editingShippingAddress:e(Y.checkoutStore).getEditingShippingAddress()}))),{__internalSetUseShippingAsBilling:i,setEditingBillingAddress:a,setEditingShippingAddress:l}=(0,_.useDispatch)(Y.checkoutStore),{billingAddress:p,setBillingAddress:m,shippingAddress:u,setShippingAddress:h}=pt(),g=(0,d.useCallback)((e=>{m({email:e})}),[m]),k=(0,x.getSetting)("forcedBillingAddress",!1);return{shippingAddress:u,billingAddress:p,setShippingAddress:h,setBillingAddress:m,setEmail:g,defaultFields:e?t("defaultFields",x.defaultFields):x.defaultFields,useShippingAsBilling:s,setUseShippingAsBilling:i,editingBillingAddress:c,editingShippingAddress:n,setEditingBillingAddress:a,setEditingShippingAddress:l,needsShipping:o,showShippingFields:!k&&o&&!r,showShippingMethods:o&&!r,showBillingFields:!o||!s||!!r,forcedBillingAddress:k,useBillingAsShipping:k||!!r}},ut=()=>{const{onCheckoutValidation:e}=Qe.checkoutEvents,{additionalFields:t,customerId:o,customerPassword:s,extensionData:r,hasError:c,isBeforeProcessing:n,isComplete:i,isProcessing:a,orderNotes:l,redirectUrl:p,shouldCreateAccount:m}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId(),customerPassword:t.getCustomerPassword(),extensionData:t.getExtensionData(),hasError:t.hasError(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),isProcessing:t.isProcessing(),orderNotes:t.getOrderNotes(),redirectUrl:t.getRedirectUrl(),shouldCreateAccount:t.getShouldCreateAccount()}}),[]),{__internalSetHasError:u,__internalProcessCheckoutResponse:h}=(0,_.useDispatch)(Y.checkoutStore),g=(0,_.useSelect)((e=>e(Y.validationStore).hasValidationErrors),[]),{shippingErrorStatus:k}=Ke(),{shippingAddress:b,billingAddress:w,useBillingAsShipping:y}=mt(),{cartNeedsPayment:x,cartNeedsShipping:v,receiveCartContents:S}=Te(),{activePaymentMethod:j,paymentMethodData:C,isExpressPaymentMethodActive:P,hasPaymentError:E,isPaymentReady:N,shouldSavePayment:A}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),hasPaymentError:t.hasPaymentError(),isPaymentReady:t.isPaymentReady(),shouldSavePayment:t.getShouldSavePaymentMethod()}}),[]),I=(0,at.getPaymentMethods)(),R=(0,at.getExpressPaymentMethods)(),T=(0,d.useRef)(w),M=(0,d.useRef)(b),B=(0,d.useRef)(p),[D,O]=(0,d.useState)(!1),F=(0,d.useMemo)((()=>{const e={...R,...I};return e?.[j]?.paymentMethodId}),[j,R,I]),L=g()&&!P||E||k.hasError,V=!c&&!L&&(N||!x)&&a;(0,d.useEffect)((()=>{L===c||!a&&!n||P||u(L)}),[L,c,a,n,P,u]),(0,d.useEffect)((()=>{T.current=w,M.current=b,B.current=p}),[w,b,p]);const U=(0,d.useCallback)((()=>g()?void 0!==(0,_.select)(Y.validationStore).getValidationError("shipping-rates-error")&&{type:pe.responseTypes.ERROR,errorMessage:(0,f.__)("Sorry, this order requires a shipping option.","woocommerce")}:E?{type:pe.responseTypes.ERROR,errorMessage:(0,f.__)("There was a problem with your payment option.","woocommerce"),context:"wc/checkout/payments"}:!k.hasError||{type:pe.responseTypes.ERROR,errorMessage:(0,f.__)("There was a problem with your shipping option.","woocommerce"),context:"wc/checkout/shipping-methods"}),[g,E,k.hasError]);(0,d.useEffect)((()=>{let t;return P||(t=e(U,0)),()=>{P||"function"!=typeof t||t()}}),[e,U,P]),(0,d.useEffect)((()=>{window.localStorage.removeItem("WOOCOMMERCE_CHECKOUT_IS_CUSTOMER_DATA_DIRTY"),B.current&&(window.location.href=B.current)}),[i]);const $=(0,d.useCallback)((async()=>{if(D)return;O(!0),(()=>{const e=(0,_.select)("wc/store/store-notices").getRegisteredContainers(),{removeNotice:t}=(0,_.dispatch)(Xe.store),{getNotices:o}=(0,_.select)(Xe.store);e.forEach((e=>{o(e).forEach((o=>{t(o.id,e)}))}))})();const e=x?{payment_method:F,payment_data:lt(C,A,j)}:{},c=xe(T.current),n=y?c:xe(M.current),i={additional_fields:t,billing_address:c,create_account:m,customer_note:l,customer_password:s,extensions:{...r},shipping_address:v?n:void 0,...e};(0,Y.clearCheckoutPutRequests)(),it()({path:"/wc/store/v1/checkout",method:"POST",data:i,cache:"no-store",parse:!1}).then((e=>{if((0,pe.assertResponseIsValid)(e),dt(e.headers),!e.ok)throw e;return e.json()})).then((e=>{h(e),O(!1)})).catch((e=>{dt(e?.headers);try{e.json().then((e=>e)).then((e=>{e.data?.cart&&S(e.data.cart),(0,Y.processErrorResponse)(e),h(e)}))}catch{let e=(0,f.__)("Something went wrong when placing the order. Check your email for order updates before retrying.","woocommerce");0!==o&&(e=(0,f.__)("Something went wrong when placing the order. Check your account's order history or your email for order updates before retrying.","woocommerce")),(0,Y.processErrorResponse)({code:"unknown_error",message:e,data:null})}u(!0),O(!1)}))}),[D,x,F,C,A,j,l,m,o,s,r,t,v,S,u,h,y]);return(0,d.useEffect)((()=>{V&&!D&&$()}),[$,V,D]),null},ht=({children:e,redirectUrl:t})=>(0,c.jsx)(ct,{redirectUrl:t,children:(0,c.jsx)(Ze,{children:(0,c.jsxs)(te,{children:[e,(0,c.jsx)(W,{renderError:x.CURRENT_USER_IS_ADMIN?null:()=>null,children:(0,c.jsx)(y.PluginArea,{scope:"woocommerce-checkout"})}),(0,c.jsx)(ut,{})]})})}),gt={currency_code:x.SITE_CURRENCY.code,currency_symbol:x.SITE_CURRENCY.symbol,currency_minor_unit:x.SITE_CURRENCY.minorUnit,currency_decimal_separator:x.SITE_CURRENCY.decimalSeparator,currency_thousand_separator:x.SITE_CURRENCY.thousandSeparator,currency_prefix:x.SITE_CURRENCY.prefix,currency_suffix:x.SITE_CURRENCY.suffix},_t=(e,t=2)=>{const o=x.SITE_CURRENCY.minorUnit;if(o===t||!e)return e;const s=Math.pow(10,o);return(Math.round(parseInt(e,10)/Math.pow(10,t))*s).toString()},kt=(0,x.getSetting)("localPickupEnabled",!1),bt=(0,x.getSetting)("localPickupText",(0,f.__)("Local pickup","woocommerce")),wt=(0,x.getSetting)("localPickupCost",""),yt=kt?(0,x.getSetting)("localPickupLocations",[]):[],xt=yt?Object.values(yt).map(((e,t)=>({...gt,name:`${bt} (${e.name})`,description:"",delivery_time:"",price:_t(wt,0)||"0",taxes:"0",rate_id:`pickup_location:${t+1}`,instance_id:t+1,meta_data:[{key:"pickup_location",value:e.name},{key:"pickup_address",value:e.formatted_address},{key:"pickup_details",value:e.details}],method_id:"pickup_location",selected:!1}))):[],ft=[{destination:{address_1:"",address_2:"",city:"",state:"",postcode:"",country:""},package_id:0,name:(0,f.__)("Shipping","woocommerce"),items:[{key:"33e75ff09dd601bbe69f351039152189",name:(0,f._x)("Beanie with Logo","example product in Cart Block","woocommerce"),quantity:2},{key:"6512bd43d9caa6e02c990b0a82652dca",name:(0,f._x)("Beanie","example product in Cart Block","woocommerce"),quantity:1}],shipping_rates:[{...gt,name:(0,f.__)("Flat rate shipping","woocommerce"),description:"",delivery_time:"",price:_t("500"),taxes:"0",rate_id:"flat_rate:0",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!1},{...gt,name:(0,f.__)("Free shipping","woocommerce"),description:"",delivery_time:"",price:"0",taxes:"0",rate_id:"free_shipping:1",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!0},...xt]}],vt=(0,x.getSetting)("displayCartPricesIncludingTax",!1),St={coupons:[],shipping_rates:(0,x.getSetting)("shippingMethodsExist",!1)||(0,x.getSetting)("localPickupEnabled",!1)?ft:[],items:[{key:"1",id:1,type:"simple",quantity:2,catalog_visibility:"visible",name:(0,f.__)("Beanie","woocommerce"),summary:(0,f.__)("Beanie","woocommerce"),short_description:(0,f.__)("Warm hat for winter","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-beanie",permalink:"https://example.org",low_stock_remaining:2,backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:10,src:S+"previews/beanie.jpg",thumbnail:S+"previews/beanie.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,f.__)("Color","woocommerce"),value:(0,f.__)("Yellow","woocommerce")},{attribute:(0,f.__)("Size","woocommerce"),value:(0,f.__)("Small","woocommerce")}],prices:{...gt,price:_t(vt?"12000":"10000"),regular_price:_t(vt?"120":"100"),sale_price:_t(vt?"12000":"10000"),price_range:null,raw_prices:{precision:6,price:vt?"12000000":"10000000",regular_price:vt?"12000000":"10000000",sale_price:vt?"12000000":"10000000"}},totals:{...gt,line_subtotal:_t("2000"),line_subtotal_tax:_t("400"),line_total:_t("2000"),line_total_tax:_t("400")},extensions:{},item_data:[]},{key:"2",id:2,type:"simple",quantity:1,catalog_visibility:"visible",name:(0,f.__)("Cap","woocommerce"),summary:(0,f.__)("Cap","woocommerce"),short_description:(0,f.__)("Lightweight baseball cap","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-cap",low_stock_remaining:null,permalink:"https://example.org",backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:11,src:S+"previews/cap.jpg",thumbnail:S+"previews/cap.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,f.__)("Color","woocommerce"),value:(0,f.__)("Orange","woocommerce")}],prices:{...gt,price:_t(vt?"2400":"2000"),regular_price:_t(vt?"2400":"2000"),sale_price:_t(vt?"2400":"2000"),price_range:null,raw_prices:{precision:6,price:vt?"24000000":"20000000",regular_price:vt?"24000000":"20000000",sale_price:vt?"24000000":"20000000"}},totals:{...gt,line_subtotal:_t("2000"),line_subtotal_tax:_t("400"),line_total:_t("2000"),line_total_tax:_t("400")},extensions:{},item_data:[]}],cross_sells:[{id:1,name:(0,f.__)("Polo","woocommerce"),slug:"polo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-polo",short_description:(0,f.__)("Polo","woocommerce"),description:(0,f.__)("Polo","woocommerce"),on_sale:!1,prices:{...gt,price:_t(vt?"24000":"20000"),regular_price:_t(vt?"24000":"20000"),sale_price:_t(vt?"12000":"10000"),price_range:null},price_html:"",average_rating:"4.5",review_count:2,images:[{id:17,src:S+"previews/polo.jpg",thumbnail:S+"previews/polo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:2,name:(0,f.__)("Long Sleeve Tee","woocommerce"),slug:"long-sleeve-tee",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-long-sleeve-tee",short_description:(0,f.__)("Long Sleeve Tee","woocommerce"),description:(0,f.__)("Long Sleeve Tee","woocommerce"),on_sale:!1,prices:{...gt,price:_t(vt?"30000":"25000"),regular_price:_t(vt?"30000":"25000"),sale_price:_t(vt?"30000":"25000"),price_range:null},price_html:"",average_rating:"4",review_count:2,images:[{id:17,src:S+"previews/long-sleeve-tee.jpg",thumbnail:S+"previews/long-sleeve-tee.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:3,name:(0,f.__)("Hoodie with Zipper","woocommerce"),slug:"hoodie-with-zipper",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-zipper",short_description:(0,f.__)("Hoodie with Zipper","woocommerce"),description:(0,f.__)("Hoodie with Zipper","woocommerce"),on_sale:!0,prices:{...gt,price:_t(vt?"15000":"12500"),regular_price:_t(vt?"30000":"25000"),sale_price:_t(vt?"15000":"12500"),price_range:null},price_html:"",average_rating:"1",review_count:2,images:[{id:17,src:S+"previews/hoodie-with-zipper.jpg",thumbnail:S+"previews/hoodie-with-zipper.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:4,name:(0,f.__)("Hoodie with Logo","woocommerce"),slug:"hoodie-with-logo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-logo",short_description:(0,f.__)("Polo","woocommerce"),description:(0,f.__)("Polo","woocommerce"),on_sale:!1,prices:{...gt,price:_t(vt?"4500":"4250"),regular_price:_t(vt?"4500":"4250"),sale_price:_t(vt?"4500":"4250"),price_range:null},price_html:"",average_rating:"5",review_count:2,images:[{id:17,src:S+"previews/hoodie-with-logo.jpg",thumbnail:S+"previews/hoodie-with-logo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:5,name:(0,f.__)("Hoodie with Pocket","woocommerce"),slug:"hoodie-with-pocket",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-pocket",short_description:(0,f.__)("Hoodie with Pocket","woocommerce"),description:(0,f.__)("Hoodie with Pocket","woocommerce"),on_sale:!0,prices:{...gt,price:_t(vt?"3500":"3250"),regular_price:_t(vt?"4500":"4250"),sale_price:_t(vt?"3500":"3250"),price_range:null},price_html:"",average_rating:"3.75",review_count:4,images:[{id:17,src:S+"previews/hoodie-with-pocket.jpg",thumbnail:S+"previews/hoodie-with-pocket.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:6,name:(0,f.__)("T-Shirt","woocommerce"),slug:"t-shirt",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-t-shirt",short_description:(0,f.__)("T-Shirt","woocommerce"),description:(0,f.__)("T-Shirt","woocommerce"),on_sale:!1,prices:{...gt,price:_t(vt?"1800":"1500"),regular_price:_t(vt?"1800":"1500"),sale_price:_t(vt?"1800":"1500"),price_range:null},price_html:"",average_rating:"3",review_count:2,images:[{id:17,src:S+"previews/tshirt.jpg",thumbnail:S+"previews/tshirt.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}}],fees:[{id:"fee",name:(0,f.__)("Fee","woocommerce"),totals:{...gt,total:_t("100"),total_tax:_t("20")}}],items_count:3,items_weight:0,needs_payment:!0,needs_shipping:I,has_calculated_shipping:!0,shipping_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},billing_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",email:"",phone:""},totals:{...gt,total_items:_t("4000"),total_items_tax:_t("800"),total_fees:_t("100"),total_fees_tax:_t("20"),total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_tax:_t("820"),total_price:_t("4920"),tax_lines:[{name:(0,f.__)("Sales tax","woocommerce"),rate:"20%",price:_t("820")}]},errors:[],payment_methods:["cod","bacs","cheque"],payment_requirements:["products"],extensions:{}},jt={cc:[{method:{gateway:"credit-card",last4:"5678",brand:"Visa"},expires:"12/20",is_default:!1,tokenId:"1"}]},Ct=window.wc.blocksCheckout,Pt=window.wp.coreData;var Et=o(4782);const Nt=(0,d.forwardRef)((({children:e,className:t=""},o)=>(0,c.jsx)("div",{ref:o,className:(0,s.A)("wc-block-components-main",t),children:e}))),At={showOrderNotes:!0,showPolicyLinks:!0,showReturnToCart:!0,cartPageId:0,showRateAfterTaxName:!1,showFormStepNumbers:!1,defaultFields:x.defaultFields},It=(0,d.createContext)(At),Rt=()=>{const e=(0,d.useContext)(It);return{...At,...e}},Tt=["core/paragraph","core/image","core/separator"],Mt=e=>{const t=(0,Ct.applyCheckoutFilter)({filterName:"additionalCartCheckoutInnerBlockTypes",defaultValue:[],extensions:(0,_.select)(Y.cartStore).getCartData().extensions,arg:{block:e},validation:e=>{if(Array.isArray(e)&&e.every((e=>"string"==typeof e)))return!0;throw new Error("allowedBlockTypes filters must return an array of strings.")}});return Array.from(new Set([...(0,a.getBlockTypes)().filter((t=>(t?.parent||[]).includes(e))).map((({name:e})=>e)),...Tt,...t]))},Bt=({clientId:e,registeredBlocks:t,defaultTemplate:o=[]})=>{const s=(0,d.useRef)(t),r=(0,d.useRef)(o),c=(0,_.useRegistry)(),{isPreview:n}=b();(0,d.useEffect)((()=>{let t=!1;if(n)return;const{replaceInnerBlocks:o}=(0,_.dispatch)("core/block-editor");return c.subscribe((()=>{if(!c.select("core/block-editor").getBlock(e))return;const n=c.select("core/block-editor").getBlocks(e);if(0===n.length&&r.current.length>0&&!t){const s=(0,a.createBlocksFromInnerBlocksTemplate)(r.current);if(0!==s.length)return t=!0,void o(e,s)}const i=s.current.map((e=>(0,a.getBlockType)(e))),l=((e,t)=>{const o=t.filter((e=>e&&(({attributes:e})=>Boolean(e.lock?.remove||e.lock?.default?.remove))(e))),s=[];return o.forEach((t=>{if(void 0===t)return;const o=e.find((e=>e.name===t.name));o||s.push(t)})),s})(n,i);if(0===l.length)return;let d=-1;const p=l.map((e=>{const t=r.current.findIndex((([t])=>t===e.name)),o=(0,a.createBlock)(e.name);return-1===d&&(d=(({defaultTemplatePosition:e,innerBlocks:t,currentDefaultTemplate:o})=>{switch(e){case-1:return t.length;case 0:return 0;default:const s=o.current[e-1],r=t.findIndex((({name:e})=>e===s[0]));return-1===r?e:r+1}})({defaultTemplatePosition:t,innerBlocks:n,currentDefaultTemplate:r})),o}));c.batch((()=>{c.dispatch("core/block-editor").insertBlocks(p,d,e)}))}),"core/block-editor")}),[e,n,c])};o(4793);const Dt=window.wp.components,Ot=()=>{const{defaultFields:e}=Rt(),t=(e,t)=>{["phone","company","address_2"].includes(e)&&["optional","required","hidden"].includes(t)&&(0,_.dispatch)(Pt.store).editEntityRecord("root","site",void 0,{[`woocommerce_checkout_${e}_field`]:t})},o=[{label:(0,f.__)("Optional","woocommerce"),value:"false"},{label:(0,f.__)("Required","woocommerce"),value:"true"}];return(0,c.jsx)(l.InspectorControls,{children:(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Address Fields","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("Show or hide fields in the checkout address forms.","woocommerce")}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Company","woocommerce"),checked:!e.company.hidden,onChange:()=>{e.company.hidden?t("company","optional"):t("company","hidden")}}),!e.company.hidden&&(0,c.jsx)(Dt.RadioControl,{selected:e.company.required?"true":"false",options:o,onChange:e=>{t("company","true"===e?"required":"optional")},className:"components-base-control--nested wc-block-components-require-company-field"}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Address line 2","woocommerce"),checked:!e.address_2.hidden,onChange:()=>{e.address_2.hidden?t("address_2","optional"):t("address_2","hidden")}}),!e.address_2.hidden&&(0,c.jsx)(Dt.RadioControl,{selected:e.address_2.required?"true":"false",options:o,onChange:e=>{t("address_2","true"===e?"required":"optional")},className:"components-base-control--nested wc-block-components-require-address_2-field"}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Phone","woocommerce"),checked:!e.phone.hidden,onChange:()=>{e.phone.hidden?t("phone","optional"):t("phone","hidden")}}),!e.phone.hidden&&(0,c.jsx)(Dt.RadioControl,{selected:e.phone.required?"true":"false",options:o,onChange:e=>{t("phone","true"===e?"required":"optional")},className:"components-base-control--nested wc-block-components-require-phone-field"})]})})};(0,a.registerBlockType)("woocommerce/checkout-fields-block",{icon:{src:(0,c.jsx)(i.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e,attributes:t})=>{const o=(0,l.useBlockProps)({className:(0,s.A)("wc-block-checkout__main",t?.className)}),r=Mt(Ct.innerBlockAreas.CHECKOUT_FIELDS),{showFormStepNumbers:n}=Rt(),i=[["woocommerce/checkout-express-payment-block",{},[]],["woocommerce/checkout-contact-information-block",{},[]],["woocommerce/checkout-shipping-method-block",{},[]],["woocommerce/checkout-pickup-options-block",{},[]],["woocommerce/checkout-shipping-address-block",{},[]],["woocommerce/checkout-billing-address-block",{},[]],["woocommerce/checkout-shipping-methods-block",{},[]],["woocommerce/checkout-payment-block",{},[]],["woocommerce/checkout-additional-information-block",{},[]],["woocommerce/checkout-order-note-block",{},[]],["woocommerce/checkout-terms-block",{},[]],["woocommerce/checkout-actions-block",{},[]]].filter(Boolean);return Bt({clientId:e,registeredBlocks:r,defaultTemplate:i}),(0,c.jsxs)(Nt,{...o,children:[(0,c.jsx)(Ot,{}),(0,c.jsx)("form",{className:(0,s.A)("wc-block-components-form wc-block-checkout__form",{"wc-block-checkout__form--with-step-numbers":n}),children:(0,c.jsx)(l.InnerBlocks,{allowedBlocks:r,templateLock:!1,template:i,renderAppender:l.InnerBlocks.ButtonBlockAppender})})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(l.InnerBlocks.Content,{})})});const Ft=(0,d.forwardRef)((({children:e,className:t=""},o)=>(0,c.jsx)("div",{ref:o,className:(0,s.A)("wc-block-components-sidebar",t),children:e})));o(6811),(0,a.registerBlockType)("woocommerce/checkout-totals-block",{icon:{src:(0,c.jsx)(i.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e,attributes:t})=>{const o=(0,l.useBlockProps)({className:(0,s.A)("wc-block-checkout__sidebar",t?.className)}),r=Mt(Ct.innerBlockAreas.CHECKOUT_TOTALS),n=[["woocommerce/checkout-order-summary-block",{},[]]];return Bt({clientId:e,registeredBlocks:r,defaultTemplate:n}),(0,c.jsx)(Ft,{...o,children:(0,c.jsx)(l.InnerBlocks,{allowedBlocks:r,templateLock:!1,template:n,renderAppender:l.InnerBlocks.ButtonBlockAppender})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(l.InnerBlocks.Content,{})})});var Lt=o(2216);const Vt=window.wc.blocksComponents,Ut=({children:e,stepHeadingContent:t})=>(0,c.jsxs)("div",{className:"wc-block-components-checkout-step__heading",children:[(0,c.jsx)(Vt.Title,{"aria-hidden":"true",className:"wc-block-components-checkout-step__title",headingLevel:"2",children:e}),!!t&&(0,c.jsx)("span",{className:"wc-block-components-checkout-step__heading-content",children:t})]}),$t=({attributes:e,setAttributes:t,className:o="",children:r})=>{const{showFormStepNumbers:n}=Rt(),{title:i="",description:a=""}=e,d=(0,l.useBlockProps)({className:(0,s.A)("wc-block-components-checkout-step",o,{"wc-block-components-checkout-step--with-step-number":n})});return(0,c.jsxs)("div",{...d,children:[(0,c.jsx)(Ut,{children:(0,c.jsx)(l.PlainText,{className:"",value:i,onChange:e=>t({title:e}),style:{backgroundColor:"transparent"}})}),(0,c.jsxs)("div",{className:"wc-block-components-checkout-step__container",children:[(0,c.jsx)("p",{className:"wc-block-components-checkout-step__description",children:(0,c.jsx)(l.PlainText,{className:a?"":"wc-block-components-checkout-step__description-placeholder",value:a,placeholder:(0,f.__)("Optional text for this form step.","woocommerce"),onChange:e=>t({description:e}),style:{backgroundColor:"transparent"}})}),(0,c.jsx)("div",{className:"wc-block-components-checkout-step__content",children:r})]})]})};o(2862);const Ht=({block:e})=>{const{"data-block":t}=(0,l.useBlockProps)(),o=Mt(e);return Bt({clientId:t,registeredBlocks:o}),(0,c.jsx)("div",{className:"wc-block-checkout__additional_fields",children:(0,c.jsx)(l.InnerBlocks,{allowedBlocks:o})})},qt=()=>(0,c.jsx)(l.InnerBlocks.Content,{});var zt=o(5929),Wt=o(8107),Yt=o(4347);const Gt=["BUTTON","FIELDSET","INPUT","OPTGROUP","OPTION","SELECT","TEXTAREA","A"],Kt=({children:e,style:t={},...o})=>{const s=(0,d.useRef)(null),r=()=>{s.current&&Wt.focus.focusable.find(s.current).forEach((e=>{Gt.includes(e.nodeName)&&e.setAttribute("tabindex","-1"),e.hasAttribute("contenteditable")&&e.setAttribute("contenteditable","false")}))},n=(0,Yt.YQ)(r,0,{leading:!0});return(0,d.useLayoutEffect)((()=>{let e;return r(),s.current&&(e=new window.MutationObserver(n),e.observe(s.current,{childList:!0,attributes:!0,subtree:!0})),()=>{e&&e.disconnect(),n.cancel()}}),[n]),(0,c.jsx)("div",{ref:s,"aria-disabled":"true",style:{userSelect:"none",pointerEvents:"none",cursor:"normal",...t},...o,children:e})};o(4249);var Zt=o(2174);o(8306);const Jt=e=>{const{onChange:t,options:o,label:r,value:n="",className:a,size:l,errorId:p,required:m,errorMessage:u=(0,f.__)("Please select a valid option","woocommerce"),placeholder:h,...g}=e,k=(0,d.useCallback)((e=>{t(e.target.value)}),[t]),b=(0,Ct.getFieldLabel)(r),w=(0,d.useMemo)((()=>({value:"",label:null!=h?h:(0,f.sprintf)(
// translators: %s will be label of the field. For example "country/region".
// translators: %s will be label of the field. For example "country/region".
(0,f.__)("Select a %s","woocommerce"),b),disabled:!!m})),[h,m,b]),y=(0,d.useId)(),x=g.id||`wc-blocks-components-select-${y}`,v=p||x,S=(0,d.useMemo)((()=>m&&n?o:[w].concat(o)),[m,n,w,o]),{setValidationErrors:j,clearValidationError:C}=(0,_.useDispatch)(Y.validationStore),{error:P,validationErrorId:E}=(0,_.useSelect)((e=>{const t=e(Y.validationStore);return{error:t.getValidationError(v),validationErrorId:t.getValidationErrorId(v)}}),[v]);(0,d.useEffect)((()=>(!m||n?C(v):j({[v]:{message:u,hidden:!0}}),()=>{C(v)})),[C,n,v,u,m,j]);const N=(0,_.useSelect)((e=>e(Y.validationStore).getValidationError(v||"")||{hidden:!0}),[v]);return(0,c.jsxs)("div",{className:(0,s.A)(a,{"has-error":!N.hidden}),children:[(0,c.jsx)("div",{className:"wc-blocks-components-select",children:(0,c.jsxs)("div",{className:"wc-blocks-components-select__container",children:[(0,c.jsx)("label",{htmlFor:x,className:"wc-blocks-components-select__label",children:r}),(0,c.jsx)("select",{className:"wc-blocks-components-select__select",id:x,size:void 0!==l?l:1,onChange:k,value:n,"aria-invalid":!(!P?.message||P?.hidden),"aria-errormessage":E,...g,children:S.map((e=>(0,c.jsx)("option",{value:e.value,"data-alternate-values":`[${e.label}]`,disabled:void 0!==e.disabled&&e.disabled,children:e.label},e.value)))}),(0,c.jsx)(i.A,{className:"wc-blocks-components-select__expand",icon:Zt.A})]})}),(0,c.jsx)(Vt.ValidationInputError,{propertyName:v})]})},Xt=({className:e,countries:t,id:o,errorId:r,label:n,onChange:i,value:a="",autoComplete:l="off",required:p=!1})=>{const m=(0,d.useMemo)((()=>Object.entries(t).map((([e,t])=>({value:e,label:(0,_e.decodeEntities)(t)})))),[t]);return(0,c.jsx)(Jt,{className:(0,s.A)(e,"wc-block-components-country-input"),id:o,errorId:r,label:n||"",onChange:i,options:m,value:a,required:p,autoComplete:l})},Qt=e=>{const{...t}=e;return(0,c.jsx)(Xt,{countries:D,...t})},eo=e=>(0,c.jsx)(Xt,{countries:D,...e});o(3930);const to=(e,t)=>{const o=t.find((t=>t.label.toLocaleUpperCase()===e.toLocaleUpperCase()||t.value.toLocaleUpperCase()===e.toLocaleUpperCase()));return o?o.value:""},oo=({className:e,id:t,states:o,country:r,label:n,onChange:i,autoComplete:a="off",value:l="",required:p=!1})=>{const m=o[r],u=(0,d.useMemo)((()=>m&&Object.keys(m).length>0?Object.keys(m).map((e=>({value:e,label:(0,_e.decodeEntities)(m[e])}))):[]),[m]),h=(0,d.useCallback)((e=>{const t=u.length>0?to(e,u):e;t!==l&&i(t)}),[i,u,l]),g=(0,d.useRef)(l);return(0,d.useEffect)((()=>{g.current!==l&&(g.current=l)}),[l]),(0,d.useEffect)((()=>{if(u.length>0&&g.current){const e=to(g.current,u);e!==g.current&&h(e)}}),[u,h]),u.length>0?(0,c.jsx)(Jt,{className:(0,s.$)(e,"wc-block-components-state-input"),options:u,label:n||"",id:t,onChange:h,value:l,autoComplete:a,required:p}):(0,c.jsx)(Vt.ValidatedTextInput,{className:e,id:t,label:n,onChange:h,autoComplete:a,value:l,required:p})},so=e=>{const{...t}=e;return(0,c.jsx)(oo,{states:O,...t})},ro=e=>(0,c.jsx)(oo,{states:O,...e});function co(e){const t=(0,d.useRef)(e);return Ue()(e,t.current)||(t.current=e),t.current}var no=o(111);o(2770);const io=({field:e,props:t,onChange:o,value:s})=>{var r;const n=null!==(r=e?.required)&&void 0!==r&&r,i=Je(n),[a,l]=(0,d.useState)((()=>Boolean(s)||n)),p=(0,Ct.getFieldLabel)(e.label);(0,d.useEffect)((()=>{i!==n&&l(Boolean(s)||n)}),[s,i,n]);const m=(0,d.useCallback)((e=>{o(e),l(!0)}),[o]);return(0,c.jsx)(d.Fragment,{children:a?(0,c.jsx)(Vt.ValidatedTextInput,{...t,type:e.type,label:n?e.label:e.optionalLabel,className:"wc-block-components-address-form__address_2",value:s,onChange:e=>o(e)}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(no.$,{render:(0,c.jsx)("span",{}),className:"wc-block-components-address-form__address_2-toggle",onClick:()=>l(!0),children:(0,f.sprintf)(
// translators: %s: address 2 field label.
// translators: %s: address 2 field label.
(0,f.__)("+ Add %s","woocommerce"),p)}),(0,c.jsx)("input",{type:"text",tabIndex:-1,className:"wc-block-components-address-form__address_2-hidden-input","aria-hidden":"true","aria-label":e.label,autoComplete:e.autocomplete,id:t?.id,value:s,onChange:e=>m(e.target.value)})]})})},ao=(e,t,o)=>({id:`${t}-${e?.key}`.replaceAll("/","-"),errorId:`${o}_${e?.key}`,label:(e?.required?e?.label:e?.optionalLabel)||"",autoCapitalize:e?.autocapitalize,autoComplete:e?.autocomplete,errorMessage:e?.errorMessage||"",required:e?.required,placeholder:e?.placeholder,className:`wc-block-components-address-form__${e?.key}`.replaceAll("/","-"),...e?.attributes}),lo=(e,t,o)=>{const s=t.find((t=>t.key===e)),r=(0,pe.objectHasProp)(o,e)?o[e]:"";return s?{field:{...s,key:e},value:r}:null},po=(e,t)=>(0,pe.isObject)(e[t])&&Object.keys(e[t]).length>0,mo=({formId:e,address1:t,address2:o,addressType:s,onChange:r})=>{const n=ao(t.field,e,s),i=ao(o.field,e,s);return(0,c.jsxs)(c.Fragment,{children:[t&&(0,c.jsx)(Vt.ValidatedTextInput,{...n,type:t.field.type,label:t.field.label,className:"wc-block-components-address-form__address_1",value:t.value,onChange:e=>r("address_1",e)}),o.field&&!o.field.hidden&&(0,c.jsx)(io,{field:o.field,props:i,onChange:e=>r("address_2",e),value:o.value})]})};var uo=o(7740);const ho=e=>((e,t)=>Object.entries(e).reduce(((e,[o,s])=>({...e,[t(0,o)]:s})),{}))(e,((e,t)=>(0,uo.L)(t))),go=e=>{const t=(e=>{const t=(0,d.useRef)({cart:{},checkout:{},customer:{}}),o=(0,_.useSelect)((t=>{const o=t(Y.cartStore),s=t(Y.checkoutStore),r=t(Y.paymentStore),c=o.getCartData(),{coupons:n,shippingRates:i,shippingAddress:a,billingAddress:l,items:d,itemsCount:p,itemsWeight:m,needsShipping:u,totals:h}=c,g={cart:{coupons:n.map((e=>e.code)),shippingRates:[...new Set(i.map((e=>e.shipping_rates.find((e=>e.selected))?.rate_id)).filter(Boolean))],items:d.map((e=>Array(e.quantity).fill(e.id))).flat(),itemsType:[...new Set(d.map((e=>e.type)))],itemsCount:p,itemsWeight:m,needsShipping:u,prefersCollection:"boolean"==typeof s.prefersCollection()&&s.prefersCollection(),totals:{totalPrice:Number(h.total_price),totalTax:Number(h.total_tax)},extensions:c.extensions},checkout:{createAccount:s.getShouldCreateAccount(),customerNote:s.getOrderNotes(),additionalFields:s.getAdditionalFields(),paymentMethod:r.getActivePaymentMethod()},customer:{id:s.getCustomerId(),billingAddress:l,shippingAddress:a,..."billing"===e||"shipping"===e?{address:"billing"===e?l:a}:{}}};return{cart:ho(g.cart),checkout:ho(g.checkout),customer:ho(g.customer)}}),[e]);return t.current&&ge()(t.current,o)||(t.current=o),t.current})(e);return window.schemaParser?{parser:window.schemaParser,data:t}:{parser:null,data:t}},_o=(e,t,o,s="")=>{const r=(0,d.useRef)([]),{parser:c,data:n}=go(o),i=be(e,t,s).map((e=>{const o=t[e.key]||{};if(c){if(po(o,"required")){let t={};t=Object.keys(o.required).some((e=>"cart"===e||"checkout"===e||"customer"===e))?{type:"object",properties:o.required}:o.required;try{const o=c.validate(t,n);e.required=o}catch(e){x.CURRENT_USER_IS_ADMIN&&console.error(e)}}if(po(o,"hidden")){const t={type:"object",properties:o.hidden};try{const o=c.validate(t,n);e.hidden=o}catch(e){x.CURRENT_USER_IS_ADMIN&&console.error(e)}}}return e}));if(!r.current||!ge()(r.current,i)){const e=i.map((e=>({...e,hidden:"boolean"==typeof e.hidden&&e.hidden,required:"boolean"==typeof e.required&&e.required})));r.current=e}return r.current},ko={};function bo(e){let t=e;return function(e){const o=t;return t=e,o}}const wo=bo(),yo=bo(),xo=({id:e="",fields:t,onChange:o,addressType:r="shipping",values:n,children:i,isEditing:a,ariaDescribedBy:l=""})=>{const m=(0,p.useInstanceId)(xo),u=(0,d.useRef)(!0),{defaultFields:h}=mt(),g=co(t),k=co("country"in n?n.country:""),b=_o(g,h,r,k),w=Je(b),y=Je(a),x=Je(n),v=(0,d.useRef)({}),{errors:S,previousErrors:j}=((e,t,o)=>{const{parser:s,data:r}=go(t),c=(0,d.useRef)(ko),n=Je(c.current);if(!r)return{errors:c.current,previousErrors:void 0};let i;if(o)i=o;else switch(t){case"billing":case"shipping":i=r.customer.address||{};break;case"contact":case"order":i=r.checkout.additional_fields||{};break;default:i={}}const a=e.reduce(((e,t)=>(po(t,"validation")&&!t.hidden&&(t.required||i[t.key])&&(e[t.key]=t.validation),e)),{});let l=ko;if(Object.keys(a).length>0&&s){const o={type:"object",properties:{}};switch(t){case"shipping":o.properties={customer:{type:"object",properties:{shipping_address:{type:"object",properties:a}}}};break;case"billing":o.properties={customer:{type:"object",properties:{billing_address:{type:"object",properties:a}}}};break;default:o.properties={checkout:{type:"object",properties:{additional_fields:{type:"object",properties:a}}}}}const c=s.compile(o),n=c(r);l=!n&&c.errors?((e,t)=>e.reduce(((e,o)=>{var s;const r=(c=o.instancePath,c.split("/").pop()?.replace("~1","/"));var c;const n=t.find((e=>e.key===r));if(!n||!r)return e;const i=(0,Ct.getFieldLabel)(n.label),a=(0,f.sprintf)(
// translators: %s is the label of the field.
// translators: %s is the label of the field.
(0,f.__)("%s is invalid","woocommerce"),i);if(r)switch(o.keyword){case"errorMessage":e[r]=null!==(s=o.message)&&void 0!==s?s:a;break;case"pattern":e[r]=(0,f.sprintf)(
// translators: %1$s is the label of the field, %2$s is the pattern.
// translators: %1$s is the label of the field, %2$s is the pattern.
(0,f.__)("%1$s must match the pattern %2$s","woocommerce"),i,o.params.pattern);break;default:e[r]=a}return e}),{}))(c.errors,e):ko}const p=e.map((e=>l[e.key]?[e.key,l[e.key]]:e.hidden||!e.required&&!i[e.key]?null:"postcode"===e.key&&"country"in i&&!(0,Ct.isPostcode)({postcode:i.postcode,country:i.country})?[e.key,(0,f.__)("Please enter a valid postcode","woocommerce")]:"email"===e.key&&"email"in i&&!(0,we.isEmail)(i.email)?[e.key,(0,f.__)("Please enter a valid email address","woocommerce")]:null)).filter(pe.nonNullable);return ge()(c.current,Object.fromEntries(p))||(c.current=Object.fromEntries(p)),{errors:c.current,previousErrors:n}})(b,r,"shipping"===r?n:void 0);return(0,d.useEffect)((()=>{if(Object.entries(S).forEach((([e,t])=>{const o=v.current[e];t&&(o?.setErrorMessage(t),(0,_.select)(Y.validationStore).getValidationError(`${r}_${e}`)||(0,_.dispatch)(Y.validationStore).setValidationErrors({[`${r}_${e}`]:{message:t,hidden:!!o?.isFocused()}}))})),j){const e=[];Object.entries(j).forEach((([t])=>{const o=v.current[t];t in S||(e.push(`${r}_${t}`),o?.setErrorMessage(""))})),e.length&&(0,_.dispatch)(Y.validationStore).clearValidationErrors(e)}}),[S,j,r,n]),(0,d.useEffect)((()=>{v.current?.postcode?.revalidate()}),[k]),(0,d.useEffect)((()=>{let t;if(!u.current&&a&&v.current&&y!==a){const o=b.find((e=>!1===e.hidden));if(!o)return;const{id:s}=ao(o,e||`${m}`,r),c=document.getElementById(s);c&&(t=setTimeout((()=>{c.focus()}),300))}return u.current=!1,()=>{clearTimeout(t)}}),[a,b,e,m,r,y]),(0,d.useEffect)((()=>{if(ge()(w,b))return;const e={...n,...Object.fromEntries(b.filter((e=>e.hidden)).map((e=>[e.key,""])))};Ue()(n,e)||o(e)}),[o,b,w,n]),(0,d.useEffect)((()=>{if((!ge()(w,b)||!ge()(x,n))&&("country"in n&&((e,t)=>{const o=`${e}_country`,s=(0,_.select)(Y.validationStore).getValidationError(o),r=t.city||t.state||t.postcode;try{if(!t.country&&r)throw(0,f.__)("Please select your country","woocommerce");if("billing"===e&&t.country&&!Object.keys(M).includes(t.country))throw(0,f.__)("Sorry, we do not allow orders from the selected country","woocommerce");if("shipping"===e&&t.country&&!Object.keys(B).includes(t.country))throw(0,f.__)("Sorry, we do not ship orders to the selected country","woocommerce");s&&(0,_.dispatch)(Y.validationStore).clearValidationError(o)}catch(e){s?(0,_.dispatch)(Y.validationStore).showValidationError(o):(0,_.dispatch)(Y.validationStore).setValidationErrors({[o]:{message:String(e),hidden:!1}})}})(r,n),"state"in n)){const e=b.find((e=>"state"===e.key));e&&((e,t,o)=>{const s=`${e}_state`,r=(0,_.select)(Y.validationStore).getValidationError(s),c=o.required,n="shipping"===e?wo(t):yo(t),i=!!n&&!Ue()(n,t);r?!c||t.state?(0,_.dispatch)(Y.validationStore).clearValidationError(s):i||(0,_.dispatch)(Y.validationStore).showValidationError(s):!r&&c&&!t.state&&t.country&&(0,_.dispatch)(Y.validationStore).setValidationErrors({[s]:{message:(0,f.sprintf)(/* translators: %s will be the state field label in lowercase e.g. "state" */ /* translators: %s will be the state field label in lowercase e.g. "state" */
(0,f.__)("Please select a %s","woocommerce"),o.label.toLowerCase()),hidden:!0}})})(r,n,e)}}),[n,x,r,b,w]),e=e||`${m}`,(0,c.jsxs)("div",{id:e,className:"wc-block-components-address-form",children:[b.map((t=>{var i;if(t.hidden)return null;const a=ao(t,e,r),d=(e=>{const{autoCapitalize:t,autoComplete:o,placeholder:s,...r}=e;return r})(a);if("email"===t.key&&(a.id="email",a.errorId="billing_email"),"checkbox"===t.type){const e=t.key in n&&n[t.key],s={checked:Boolean(e),onChange:e=>{o({...n,[t.key]:e})},...d};return t.required?(0,c.jsx)(Vt.ValidatedCheckboxControl,{...t.errorMessage?{errorMessage:t.errorMessage}:{},...s},t.key):(0,c.jsx)(Vt.CheckboxControl,{...s},t.key)}if("address_1"===t.key&&"address_1"in n){const s=lo("address_1",b,n),i=lo("address_2",b,n);return(0,pe.isNull)(s)||(0,pe.isNull)(i)?null:(0,c.jsx)(mo,{address1:s,address2:i,addressType:r,formId:e,onChange:(e,t)=>{o({...n,[e]:t})}},t.key)}if("address_2"===t.key)return null;if("country"===t.key&&"country"in n){const e="shipping"===r?eo:Qt;return(0,c.jsx)(e,{...a,value:n.country,onChange:e=>{o({...n,country:e,state:"",postcode:""})}},t.key)}if("state"===t.key&&"state"in n&&"country"in n){const e="shipping"===r?ro:so;return(0,c.jsx)(e,{...a,country:n.country,value:n.state,onChange:e=>o({...n,state:e})},t.key)}return"select"===t.type&&"options"in t?void 0===t.options?null:(0,c.jsx)(Jt,{...a,label:a.label||"",className:(0,s.A)("wc-block-components-select-input",`wc-block-components-select-input-${t.key}`.replaceAll("/","-")),value:t.key in n?n[t.key]:"",onChange:e=>{o({...n,[t.key]:e})},options:t.options,required:t.required,errorMessage:a.errorMessage||void 0},t.key):(0,c.jsx)(Vt.ValidatedTextInput,{ref:e=>v.current[t.key]=e,...a,type:t.type,ariaDescribedBy:l,value:null!==(i=(0,_e.decodeEntities)(n[t.key]))&&void 0!==i?i:"",onChange:e=>o({...n,[t.key]:e}),customFormatter:e=>"postcode"===t.key?e.trimStart().toUpperCase():e},t.key)})),i]})},fo=xo;o(1121);const vo=({isEditing:e=!1,addressCard:t,addressForm:o})=>{const r=(0,s.A)("wc-block-components-address-address-wrapper",{"is-editing":e});return(0,c.jsxs)("div",{className:r,children:[(0,c.jsx)("div",{className:"wc-block-components-address-card-wrapper",children:t}),(0,c.jsx)("div",{className:"wc-block-components-address-form-wrapper",children:o})]})},So=e=>(0,pe.isObject)(O[e.country])&&(0,pe.isString)(O[e.country][e.state])?(0,_e.decodeEntities)(O[e.country][e.state]):e.state,jo=e=>(0,pe.isString)(D[e.country])?(0,_e.decodeEntities)(D[e.country]):e.country;o(8796);const Co=({address:e,onEdit:t,target:o,isExpanded:s})=>{const r=(0,x.getSetting)("countryData",{});let n=(0,x.getSetting)("defaultAddressFormat","{name}\n{company}\n{address_1}\n{address_2}\n{city}\n{state}\n{postcode}\n{country}");(0,pe.objectHasProp)(r,e?.country)&&(0,pe.objectHasProp)(r[e.country],"format")&&(0,pe.isString)(r[e.country].format)&&(n=r[e.country].format);const{name:i,address:a}=((e,t)=>{const o=(e=>["{name}","{name_upper}","{first_name} {last_name}","{last_name} {first_name}","{first_name_upper} {last_name_upper}","{last_name_upper} {first_name_upper}","{first_name} {last_name_upper}","{first_name_upper} {last_name}","{last_name} {first_name_upper}","{last_name_upper} {first_name}"].find((t=>e.indexOf(t)>=0))||"")(t),s=t.replace(`${o}\n`,""),r=[["{company}",e?.company||""],["{address_1}",e?.address_1||""],["{address_2}",e?.address_2||""],["{city}",e?.city||""],["{state}",So(e)],["{postcode}",e?.postcode||""],["{country}",jo(e)],["{company_upper}",(e?.company||"").toUpperCase()],["{address_1_upper}",(e?.address_1||"").toUpperCase()],["{address_2_upper}",(e?.address_2||"").toUpperCase()],["{city_upper}",(e?.city||"").toUpperCase()],["{state_upper}",So(e).toUpperCase()],["{state_code}",e?.state||""],["{postcode_upper}",(e?.postcode||"").toUpperCase()],["{country_upper}",jo(e).toUpperCase()]],c=[["{name}",e?.first_name+(e?.first_name&&e?.last_name?" ":"")+e?.last_name],["{name_upper}",(e?.first_name+(e?.first_name&&e?.last_name?" ":"")+e?.last_name).toUpperCase()],["{first_name}",e?.first_name||""],["{last_name}",e?.last_name||""],["{first_name_upper}",(e?.first_name||"").toUpperCase()],["{last_name_upper}",(e?.last_name||"").toUpperCase()]];let n=o;c.forEach((([e,t])=>{n=n.replace(e,t)}));let i=s;r.forEach((([e,t])=>{i=i.replace(e,t)}));const a=i.replace(/^,\s|,\s$/g,"").replace(/\n{2,}/,"\n").split("\n").filter(Boolean);return{name:n,address:a}})(e,n),l="shipping"===o?(0,f.__)("Edit shipping address","woocommerce"):(0,f.__)("Edit billing address","woocommerce");return(0,c.jsxs)("div",{className:"wc-block-components-address-card",children:[(0,c.jsxs)("address",{children:[(0,c.jsx)("span",{className:"wc-block-components-address-card__address-section",children:(0,_e.decodeEntities)(i)}),(0,c.jsx)("div",{className:"wc-block-components-address-card__address-section",children:a.filter((e=>!!e)).map(((e,t)=>(0,c.jsx)("span",{children:(0,_e.decodeEntities)(e)},"address-"+t)))}),e.phone?(0,c.jsx)("div",{className:"wc-block-components-address-card__address-section",children:e.phone},"address-phone"):""]}),t&&(0,c.jsx)(no.$,{render:(0,c.jsx)("span",{}),className:"wc-block-components-address-card__edit","aria-controls":o,"aria-expanded":s,"aria-label":l,onClick:e=>{e.preventDefault(),t()},type:"button",children:(0,f.__)("Edit","woocommerce")})]})},Po=()=>{const{shippingAddress:e,setShippingAddress:t,setBillingAddress:o,useShippingAsBilling:s,editingShippingAddress:r,setEditingShippingAddress:n}=mt(),{dispatchCheckoutEvent:i}=He(),{hasValidationErrors:a,invalidProps:l}=(0,_.useSelect)((t=>{const o=t(Y.validationStore);return{hasValidationErrors:o.hasValidationErrors(),invalidProps:Object.keys(e).filter((e=>void 0!==o.getValidationError("shipping_"+e))).filter(Boolean)}}),[e]);(0,d.useEffect)((()=>{l.length>0&&!1===r&&n(!0)}),[r,a,l.length,n]);const p=(0,d.useCallback)((e=>{t(e),s&&(o(e),i("set-billing-address")),i("set-shipping-address")}),[i,o,t,s]);return(0,c.jsx)(vo,{isEditing:r,addressCard:(0,c.jsx)(Co,{address:e,target:"shipping",onEdit:()=>{n(!0)},isExpanded:r}),addressForm:(0,c.jsx)(fo,{id:"shipping",addressType:"shipping",onChange:p,values:e,fields:V,isEditing:r})})},Eo=()=>{const{defaultFields:e,setBillingAddress:t,shippingAddress:o,billingAddress:s,useShippingAsBilling:r,setUseShippingAsBilling:n,setEditingBillingAddress:i}=mt(),{isEditor:a}=b(),l=0===(0,x.getSetting)("currentUserId"),p=()=>{const r={...o};e?.phone?.hidden&&delete r.phone,e?.company?.hidden&&delete r.company,(Object.keys(r).length!==Object.keys(s).length||!Object.keys(r).every((e=>r[e]===s[e])))&&t(r)};(0,zt.Su)((()=>{r&&p()}));const m=a?Kt:d.Fragment,u=r?[me.SHIPPING_ADDRESS,me.BILLING_ADDRESS]:[me.SHIPPING_ADDRESS],{cartDataLoaded:h}=(0,_.useSelect)((e=>({cartDataLoaded:e(Y.cartStore).hasFinishedResolution("getCartData")})));return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:u}),(0,c.jsx)(m,{children:h?(0,c.jsx)(Po,{}):null}),(0,c.jsx)(Vt.CheckboxControl,{className:"wc-block-checkout__use-address-for-billing",label:(0,f.__)("Use same address for billing","woocommerce"),checked:r,onChange:e=>{n(e),e?p():(i(!0),(e=>{if(!e||!l)return;const o=(e=>{const t=be(V,x.defaultFields,e.country),o=Object.assign({},e);return t.forEach((({key:t})=>{"country"!==t&&"state"!==t&&ye(t,e)&&(o[t]="")})),o})(e);t(o)})(s))}})]})},No=({defaultTitle:e=(0,f.__)("Step","woocommerce"),defaultDescription:t=(0,f.__)("Step description text.","woocommerce"),defaultShowStepNumber:o=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:o}}),Ao={...No({defaultTitle:(0,f.__)("Shipping address","woocommerce"),defaultDescription:(0,f.__)("Enter the address where you want your order delivered.","woocommerce")}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};(0,a.registerBlockType)("woocommerce/checkout-shipping-address-block",{icon:{src:(0,c.jsx)(i.A,{icon:Lt.A,className:"wc-block-editor-components-block-icon"})},attributes:Ao,edit:({attributes:e,setAttributes:t})=>{const{showShippingFields:o}=mt();return o?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Ot,{}),(0,c.jsxs)($t,{setAttributes:t,attributes:e,className:(0,s.A)("wc-block-checkout__shipping-fields",e?.className),children:[(0,c.jsx)(Eo,{}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.SHIPPING_ADDRESS})]})]}):null},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});var Io=o(7223);o(6223);const Ro=P?`<a href="${P}" target="_blank">${(0,f.__)("Terms and Conditions","woocommerce")}</a>`:(0,f.__)("Terms and Conditions","woocommerce"),To=C?`<a href="${C}" target="_blank">${(0,f.__)("Privacy Policy","woocommerce")}</a>`:(0,f.__)("Privacy Policy","woocommerce"),Mo=(0,f.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,f.__)("By proceeding with your purchase you agree to our %1$s and %2$s","woocommerce"),Ro,To),Bo=(0,f.sprintf)(/* translators: %1$s terms page link, %2$s privacy page link. */ /* translators: %1$s terms page link, %2$s privacy page link. */
(0,f.__)("You must accept our %1$s and %2$s to continue with your purchase.","woocommerce"),Ro,To);o(5763),(0,a.registerBlockType)("woocommerce/checkout-terms-block",{icon:{src:(0,c.jsx)(i.A,{icon:Io.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:{checkbox:e,text:t,showSeparator:o},setAttributes:r})=>{const n=(0,l.useBlockProps)(),i=t||(e?Bo:Mo);return(0,c.jsxs)("div",{...n,children:[(0,c.jsxs)(l.InspectorControls,{children:[(!P||!C)&&(0,c.jsxs)(Dt.Notice,{className:"wc-block-checkout__terms_notice",status:"warning",isDismissible:!1,children:[(0,f.__)("Link to your store's Terms and Conditions and Privacy Policy pages by creating pages for them.","woocommerce"),(0,c.jsx)("br",{}),!P&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("br",{}),(0,c.jsx)(Dt.ExternalLink,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=advanced`,children:(0,f.__)("Setup a Terms and Conditions page","woocommerce")})]}),!C&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("br",{}),(0,c.jsx)(Dt.ExternalLink,{href:`${x.ADMIN_URL}options-privacy.php`,children:(0,f.__)("Setup a Privacy Policy page","woocommerce")})]})]}),P&&C&&!(i.includes(P)&&i.includes(C))&&(0,c.jsx)(Dt.Notice,{className:"wc-block-checkout__terms_notice",status:"warning",isDismissible:!1,actions:Mo!==t?[{label:(0,f.__)("Restore default text","woocommerce"),onClick:()=>r({text:""})}]:[],children:(0,c.jsx)("p",{children:(0,f.__)("Ensure you add links to your policy pages in this section.","woocommerce")})}),(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Display options","woocommerce"),children:[(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Require checkbox","woocommerce"),checked:e,onChange:()=>r({checkbox:!e})}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Show separator","woocommerce"),checked:o,onChange:()=>r({showSeparator:!o})})]})]}),(0,c.jsx)("div",{className:(0,s.A)("wc-block-checkout__terms",{"wc-block-checkout__terms--with-separator":o}),children:e?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.CheckboxControl,{id:"terms-condition",checked:!1}),(0,c.jsx)(l.RichText,{value:i,onChange:e=>r({text:e})})]}):(0,c.jsx)(l.RichText,{tagName:"span",value:i,onChange:e=>r({text:e})})})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});var Do=o(5634),Oo=o(3558);o(6713);const Fo=[(0,f.__)("Too weak","woocommerce"),(0,f.__)("Weak","woocommerce"),(0,f.__)("Medium","woocommerce"),(0,f.__)("Strong","woocommerce"),(0,f.__)("Very strong","woocommerce")],Lo=({password:e="",onChange:t})=>{var o;const r=(0,p.useInstanceId)(Lo,"woocommerce-password-strength-meter");let n=-1;e.length>0&&(n=(e=>void 0===window.zxcvbn?(0,Oo.Bi)(e,[{id:0,value:Fo[0],minDiversity:0,minLength:0},{id:1,value:Fo[1],minDiversity:1,minLength:4},{id:2,value:Fo[2],minDiversity:2,minLength:8},{id:3,value:Fo[3],minDiversity:4,minLength:12},{id:4,value:Fo[4],minDiversity:4,minLength:20}]).id:window.zxcvbn(e).score)(e));const i=Je(n);return(0,d.useEffect)((()=>{n!==i&&t&&t(n)}),[n,i,t]),(0,c.jsxs)("div",{id:r,className:(0,s.A)("wc-block-components-password-strength",{hidden:-1===n}),children:[(0,c.jsx)("label",{htmlFor:r+"-meter",className:"screen-reader-text",children:(0,f.__)("Password strength","woocommerce")}),(0,c.jsx)("meter",{id:r+"-meter",className:"wc-block-components-password-strength__meter",min:0,max:4,value:n>-1?n:0,children:null!==(o=Fo[n])&&void 0!==o?o:""}),!!Fo[n]&&(0,c.jsxs)("div",{id:r+"-result",className:"wc-block-components-password-strength__result",children:[(0,c.jsx)("span",{className:"screen-reader-text","aria-live":"polite",children:(0,f.sprintf)(/* translators: %s: Password strength */ /* translators: %s: Password strength */
(0,f.__)("Password strength: %1$s (%2$d characters long)","woocommerce"),Fo[n],e.length)})," ",(0,c.jsx)("span",{"aria-hidden":!0,children:Fo[n]})]})]})},Vo=Lo,Uo=()=>{const[e,t]=(0,d.useState)(0),{customerPassword:o}=(0,_.useSelect)((e=>({customerPassword:e(Y.checkoutStore).getCustomerPassword()})),[]),{__internalSetCustomerPassword:s}=(0,_.useDispatch)(Y.checkoutStore),{setValidationErrors:r,clearValidationError:n}=(0,_.useDispatch)(Y.validationStore);return(0,c.jsx)(Vt.ValidatedTextInput,{type:"password",label:(0,f.__)("Create a password","woocommerce"),className:"wc-block-components-address-form__password",value:o,required:!0,errorId:"account-password",onChange:t=>{s(t),t?e<2?r({"account-password":{message:(0,f.__)("Please create a stronger password","woocommerce"),hidden:!0}}):n("account-password"):r({"account-password":{message:(0,f.__)("Please enter a valid password","woocommerce"),hidden:!0}})},feedback:(0,c.jsx)(Vo,{password:o,onChange:e=>t(e)})})},$o="wc-guest-checkout-notice",Ho=()=>{const{shouldCreateAccount:e}=(0,_.useSelect)((e=>({shouldCreateAccount:e(Y.checkoutStore).getShouldCreateAccount()}))),{__internalSetShouldCreateAccount:t,__internalSetCustomerPassword:o}=(0,_.useDispatch)(Y.checkoutStore),s=(0,x.getSetting)("checkoutAllowsGuest",!1),r=(0,x.getSetting)("checkoutAllowsSignup",!1),n=s&&r,i=!(0,x.getSetting)("generatePassword",!1)&&(n&&e||!s);return s||n||i?(0,c.jsxs)(c.Fragment,{children:[s&&(0,c.jsx)("p",{id:$o,className:"wc-block-checkout__guest-checkout-notice",children:(0,f.__)("You are currently checking out as a guest.","woocommerce")}),n&&(0,c.jsx)(Vt.CheckboxControl,{className:"wc-block-checkout__create-account",label:(0,f.sprintf)(/* translators: Store name */ /* translators: Store name */
(0,f.__)("Create an account with %s","woocommerce"),(0,x.getSetting)("siteTitle","")),checked:e,onChange:e=>{t(e),o("")}}),i&&(0,c.jsx)(Uo,{})]}):null},qo=()=>{const{additionalFields:e,customerId:t}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId()}})),{setAdditionalFields:o}=(0,_.useDispatch)(Y.checkoutStore),{billingAddress:s,setEmail:r}=mt(),{dispatchCheckoutEvent:n}=He(),i={email:s.email,...e};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:me.CONTACT_INFORMATION}),(0,c.jsx)(fo,{id:"contact",addressType:"contact",ariaDescribedBy:$o,onChange:e=>{const{email:t,...s}=e;r(t),n("set-email-address"),o(s)},values:i,fields:U,children:!t&&(0,c.jsx)(Ho,{})})]})},zo={...No({defaultTitle:(0,f.__)("Contact information","woocommerce"),defaultDescription:(0,f.__)("We'll use this email to send you details and updates about your order.","woocommerce")}),className:{type:"string",default:""},lock:{type:"object",default:{remove:!0,move:!0}}};(0,a.registerBlockType)("woocommerce/checkout-contact-information-block",{icon:{src:(0,c.jsx)(i.A,{icon:Do.A,className:"wc-block-editor-components-block-icon"})},attributes:zo,edit:({attributes:e,setAttributes:t})=>(0,c.jsxs)($t,{attributes:e,setAttributes:t,className:(0,s.A)("wc-block-checkout__contact-fields",e?.className),children:[(0,c.jsx)(l.InspectorControls,{children:(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Account creation and guest checkout","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("Account creation and guest checkout settings can be managed in your store settings.","woocommerce")}),(0,c.jsx)(Dt.ExternalLink,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=account`,children:(0,f.__)("Manage account settings","woocommerce")})]})}),(0,c.jsx)(Kt,{children:(0,c.jsx)(qo,{})}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.CONTACT_INFORMATION})]}),save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});const Wo=()=>{const{billingAddress:e,setShippingAddress:t,setBillingAddress:o,useBillingAsShipping:s,editingBillingAddress:r,setEditingBillingAddress:n}=mt(),{dispatchCheckoutEvent:i}=He(),{hasValidationErrors:a,invalidProps:l}=(0,_.useSelect)((t=>{const o=t(Y.validationStore);return{hasValidationErrors:o.hasValidationErrors(),invalidProps:Object.keys(e).filter((e=>"email"!==e&&void 0!==o.getValidationError("billing_"+e))).filter(Boolean)}}),[e]);(0,d.useEffect)((()=>{l.length>0&&!1===r&&n(!0)}),[r,a,l.length,n]);const p=(0,d.useCallback)((e=>{o(e),s&&(t(e),i("set-shipping-address")),i("set-billing-address")}),[i,o,t,s]);return(0,c.jsx)(vo,{isEditing:r,addressCard:(0,c.jsx)(Co,{address:e,target:"billing",onEdit:()=>{n(!0)},isExpanded:r}),addressForm:(0,c.jsx)(fo,{id:"billing",addressType:"billing",onChange:p,values:e,fields:V,isEditing:r})})},Yo=()=>{const{defaultFields:e,billingAddress:t,setShippingAddress:o,useBillingAsShipping:s}=mt(),{isEditor:r}=b();(0,zt.Su)((()=>{if(s){const{email:s,...r}=t,c={...r};e?.phone?.hidden&&delete c.phone,e?.company?.hidden&&delete c.company,o(c)}}));const n=r?Kt:d.Fragment,i=s?[me.BILLING_ADDRESS,me.SHIPPING_ADDRESS]:[me.BILLING_ADDRESS],{cartDataLoaded:a}=(0,_.useSelect)((e=>({cartDataLoaded:e(Y.cartStore).hasFinishedResolution("getCartData")})));return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:i}),(0,c.jsx)(n,{children:a?(0,c.jsx)(Wo,{}):null})]})},Go=(0,f.__)("Billing address","woocommerce"),Ko=(0,f.__)("Enter the billing address that matches your payment method.","woocommerce"),Zo=(0,f.__)("Billing and shipping address","woocommerce"),Jo=(0,f.__)("Enter the billing and shipping address that matches your payment method.","woocommerce"),Xo={...No({defaultTitle:Go,defaultDescription:Ko}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};(0,a.registerBlockType)("woocommerce/checkout-billing-address-block",{icon:{src:(0,c.jsx)(i.A,{icon:Lt.A,className:"wc-block-editor-components-block-icon"})},attributes:Xo,edit:({attributes:e,setAttributes:t})=>{const{showBillingFields:o,forcedBillingAddress:r,useBillingAsShipping:n}=mt();return o||n?(e.title=((e,t)=>t?e===Go?Zo:e:e===Zo?Go:e)(e.title,r),e.description=((e,t)=>t?e===Ko?Jo:e:e===Jo?Ko:e)(e.description,r),(0,c.jsxs)($t,{setAttributes:t,attributes:e,className:(0,s.A)("wc-block-checkout__billing-fields",e?.className),children:[(0,c.jsx)(Ot,{}),(0,c.jsx)(Yo,{}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.BILLING_ADDRESS})]})):null},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});var Qo=o(6012);const es=(0,f.__)("Place Order","woocommerce"),ts=(0,f.__)("Return to Cart","woocommerce"),os={placeOrderButtonLabel:{type:"string",default:es},returnToCartButtonLabel:{type:"string",default:ts}},ss=(e,t)=>{if(!e.title.raw)return e.slug;const o=1===t.filter((t=>t.title.raw===e.title.raw)).length;return e.title.raw+(o?"":` - ${e.slug}`)},rs=({setPageId:e,pageId:t,labels:o})=>{const s=(0,_.useSelect)((e=>e("core").getEntityRecords("postType","page",{status:"publish",orderby:"title",order:"asc",per_page:100})),[])||null;return s?(0,c.jsx)(Dt.PanelBody,{title:o.title,children:(0,c.jsx)(Dt.SelectControl,{label:(0,f.__)("Link to","woocommerce"),value:t,options:[{label:o.default,value:0},...s.map((e=>({label:ss(e,s),value:parseInt(e.id,10)})))],onChange:t=>e(parseInt(t,10))})}):null};var cs=o(5181);o(2840);const ns=({href:e,children:t,element:o="a"})=>{const s=e||N;if(!s)return null;const r=o;return(0,c.jsxs)(r,{..."a"===o?{href:s}:{},className:"wc-block-components-checkout-return-to-cart-button",children:[(0,c.jsx)(i.A,{icon:cs.A}),t]})};o(6882);const is=(0,d.forwardRef)(((e,t)=>{const{className:o,children:r,variant:n="contained",removeTextWrap:i=!1,...a}=e,l=(0,s.A)("wc-block-components-button","wp-element-button",o,n);if("href"in e)return(0,c.jsx)(no.$,{render:(0,c.jsx)("a",{ref:t,href:e.href,children:(0,c.jsx)("div",{className:"wc-block-components-button__text",children:r})}),className:l,...a});const d=i?e.children:(0,c.jsx)("div",{className:"wc-block-components-button__text",children:e.children});return(0,c.jsx)(no.$,{ref:t,className:l,...a,children:d})})),as=({onChange:e,placeholder:t,value:o,children:s,...r})=>(0,c.jsxs)(is,{...r,children:[(0,c.jsx)(l.RichText,{multiline:!1,allowedFormats:[],value:o,placeholder:t,onChange:e}),s]}),ls=window.wc.priceFormat;o(8599);const ds=JSON.parse('{"uK":{"lock":{"type":"object","default":{"remove":true,"move":true}},"cartPageId":{"type":"number","default":0},"showReturnToCart":{"type":"boolean","default":true},"className":{"type":"string","default":""},"priceSeparator":{"type":"string","default":"·"}}}');o(7883);const ps={example:{attributes:{showPrice:!0,placeOrderButtonLabel:(0,f.__)("Place Order","woocommerce"),showReturnToCart:!1}},icon:{src:(0,c.jsx)(i.A,{icon:Qo.A,className:"wc-block-editor-components-block-icon"})},attributes:{...os,...ds.uK},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()}),edit:({attributes:e,setAttributes:t})=>{const o=(0,l.useBlockProps)(),{cartPageId:r=0,showReturnToCart:n=!1,placeOrderButtonLabel:i,returnToCartButtonLabel:a}=e,{cartTotals:p}=Te(),m=(0,ls.getCurrencyFromPriceResponse)(p),{current:u}=(0,d.useRef)(r),h=(0,_.useSelect)((e=>u||e("core/editor").getCurrentPostId()),[u]),g=o.className.includes("is-style-with-price");return(0,c.jsxs)("div",{...o,children:[(0,c.jsxs)(l.InspectorControls,{children:[(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Options","woocommerce"),children:[(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)('Show a "Return to Cart" link',"woocommerce"),help:(0,f.__)("Recommended to enable only if there is no Cart link in the header.","woocommerce"),checked:n,onChange:()=>t({showReturnToCart:!n})}),g&&(0,c.jsx)(Dt.TextControl,{label:(0,f.__)("Price separator","woocommerce"),id:"price-separator",value:e.priceSeparator,onChange:e=>{t({priceSeparator:e})}})]}),n&&!(h===j&&0===u)&&(0,c.jsx)(rs,{pageId:r,setPageId:e=>t({cartPageId:e}),labels:{title:(0,f.__)("Return to Cart button","woocommerce"),default:(0,f.__)("WooCommerce Cart Page","woocommerce")}})]}),(0,c.jsx)("div",{className:"wc-block-checkout__actions",children:(0,c.jsxs)("div",{className:"wc-block-checkout__actions_row",children:[n&&(0,c.jsx)(ns,{element:"span",children:(0,c.jsx)(l.RichText,{multiline:!1,allowedFormats:[],value:a,placeholder:ts,onChange:e=>{t({returnToCartButtonLabel:e})}})}),(0,c.jsx)(as,{className:(0,s.A)("wc-block-cart__submit-button","wc-block-components-checkout-place-order-button",{"wc-block-components-checkout-place-order-button--full-width":!n}),value:i,placeholder:es,onChange:e=>{t({placeOrderButtonLabel:e})},children:g&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{children:`.wp-block-woocommerce-checkout-actions-block {\n\t\t\t\t\t\t\t\t\t\t.wc-block-components-checkout-place-order-button__separator {\n\t\t\t\t\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\t\t\t\t\tcontent: "${e.priceSeparator}";\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}`}),(0,c.jsx)("div",{className:"wc-block-components-checkout-place-order-button__separator"}),(0,c.jsx)("div",{className:"wc-block-components-checkout-place-order-button__price",children:(0,c.jsx)(Vt.FormattedMonetaryAmount,{value:p.total_price,currency:m})})]})})]})})]})}};(0,a.registerBlockType)("woocommerce/checkout-actions-block",ps);const ms=()=>{const{additionalFields:e}=(0,_.useSelect)((e=>({additionalFields:e(Y.checkoutStore).getAdditionalFields()})),[]),{isEditor:t}=b(),{setAdditionalFields:o}=(0,_.useDispatch)(Y.checkoutStore),s={...e},r=t?Kt:d.Fragment;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:me.ORDER_INFORMATION}),(0,c.jsx)(r,{children:(0,c.jsx)(fo,{id:"order",addressType:"order",onChange:e=>{o(e)},fields:$,values:s})})]})},us={...No({defaultTitle:(0,f.__)("Additional order information","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!1,remove:!0}}};(0,a.registerBlockType)("woocommerce/checkout-additional-information-block",{attributes:us,icon:{src:(0,c.jsx)(i.A,{icon:Io.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{defaultFields:o}=mt(),r=_o($,o,"order");return 0===r.length||r.every((e=>!!e.hidden))?null:(0,c.jsx)($t,{setAttributes:t,attributes:e,className:(0,s.A)("wc-block-checkout__additional-information-fields",e?.className),children:(0,c.jsx)(ms,{})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});var hs=o(7715);const gs=({disabled:e,onChange:t,placeholder:o,value:s})=>{const[r,n]=(0,d.useState)(""!==s),[i,a]=(0,d.useState)("");return(0,c.jsxs)("div",{className:"wc-block-checkout__add-note",children:[(0,c.jsx)(Vt.CheckboxControl,{disabled:e,label:(0,f.__)("Add a note to your order","woocommerce"),checked:r,onChange:e=>{n(e),e?s!==i&&t(i):(t(""),a(s))}}),r&&(0,c.jsx)(Vt.Textarea,{disabled:e,onTextChange:t,placeholder:o,value:s})]})},_s=({className:e})=>{const{needsShipping:t}=qe(),{isProcessing:o,orderNotes:r}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{isProcessing:t.isProcessing(),orderNotes:t.getOrderNotes()}})),{__internalSetOrderNotes:n}=(0,_.useDispatch)(Y.checkoutStore);return(0,c.jsx)(Vt.FormStep,{id:"order-notes",showStepNumber:!1,className:(0,s.A)("wc-block-checkout__order-notes",e),disabled:o,children:(0,c.jsx)(gs,{disabled:o,onChange:n,placeholder:t?(0,f.__)("Notes about your order, e.g. special notes for delivery.","woocommerce"):(0,f.__)("Notes about your order.","woocommerce"),value:r})})};o(9003),o(4255),(0,a.registerBlockType)("woocommerce/checkout-order-note-block",{icon:{src:(0,c.jsx)(i.A,{icon:hs.A,className:"wc-block-editor-components-block-icon"})},edit:()=>{const e=(0,l.useBlockProps)();return(0,c.jsx)("div",{...e,children:(0,c.jsx)(Kt,{children:(0,c.jsx)(_s,{})})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});const ks=(0,c.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",fill:"none",children:[(0,c.jsx)("path",{stroke:"currentColor",strokeWidth:"1.5",fill:"none",d:"M6 3.75h12c.69 0 1.25.56 1.25 1.25v14c0 .69-.56 1.25-1.25 1.25H6c-.69 0-1.25-.56-1.25-1.25V5c0-.69.56-1.25 1.25-1.25z"}),(0,c.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.9 7.5A1.1 1.1 0 018 6.4h8a1.1 1.1 0 011.1 1.1v2a1.1 1.1 0 01-1.1 1.1H8a1.1 1.1 0 01-1.1-1.1v-2zm1.2.1v1.8h7.8V7.6H8.1z",clipRule:"evenodd"}),(0,c.jsx)("path",{fill:"currentColor",d:"M8.5 12h1v1h-1v-1zM8.5 14h1v1h-1v-1zM8.5 16h1v1h-1v-1zM11.5 12h1v1h-1v-1zM11.5 14h1v1h-1v-1zM11.5 16h1v1h-1v-1zM14.5 12h1v1h-1v-1zM14.5 14h1v1h-1v-1zM14.5 16h1v1h-1v-1z"})]});o(9961);const bs=({children:e,className:t,screenReaderLabel:o,showSpinner:r=!1,isLoading:n=!0})=>(0,c.jsxs)("div",{className:(0,s.A)(t,{"wc-block-components-loading-mask":n}),children:[n&&r&&(0,c.jsx)(Vt.Spinner,{}),(0,c.jsx)("div",{className:(0,s.A)({"wc-block-components-loading-mask__children":n}),"aria-hidden":n,children:e}),n&&(0,c.jsx)("span",{className:"screen-reader-text",children:o||(0,f.__)("Loading…","woocommerce")})]});o(1962);const ws=({instanceId:e,isLoading:t=!1,onSubmit:o,displayCouponForm:r=!1})=>{const[n,i]=(0,d.useState)(""),[a,l]=(0,d.useState)(r),p=`wc-block-components-totals-coupon__input-${e}`,{validationErrorId:m}=(0,_.useSelect)((t=>({validationErrorId:t(Y.validationStore).getValidationErrorId(e)})),[e]),u=(0,d.useRef)(null);return(0,c.jsx)(Vt.Panel,{className:"wc-block-components-totals-coupon",initialOpen:a,hasBorder:!1,headingLevel:2,title:(0,f.__)("Add a coupon","woocommerce"),state:[a,l],children:(0,c.jsx)(bs,{screenReaderLabel:(0,f.__)("Applying coupon…","woocommerce"),isLoading:t,showSpinner:!1,children:(0,c.jsxs)("div",{className:"wc-block-components-totals-coupon__content",children:[(0,c.jsxs)("form",{className:"wc-block-components-totals-coupon__form",id:"wc-block-components-totals-coupon__form",children:[(0,c.jsx)(Vt.ValidatedTextInput,{id:p,errorId:"coupon",className:"wc-block-components-totals-coupon__input",label:(0,f.__)("Enter code","woocommerce"),value:n,ariaDescribedBy:m||"",onChange:e=>{i(e)},focusOnMount:!0,validateOnMount:!1,showError:!1,ref:u}),(0,c.jsxs)(is,{className:(0,s.A)("wc-block-components-totals-coupon__button",{"wc-block-components-totals-coupon__button--loading":t}),disabled:t||!n,onClick:e=>{e.preventDefault(),void 0!==o?o(n)?.then((e=>{e?(i(""),l(!1)):u.current?.focus&&u.current.focus()})):(i(""),l(!0))},type:"submit",children:[t&&(0,c.jsx)(Vt.Spinner,{}),(0,f.__)("Apply","woocommerce")]})]}),(0,c.jsx)(Vt.ValidationInputError,{propertyName:"coupon",elementId:e})]})})})};o(619);const ys={context:"summary"},xs=({cartCoupons:e=[],currency:t,isRemovingCoupon:o,removeCoupon:s,values:r})=>{const{total_discount:n,total_discount_tax:i}=r,a=parseInt(n,10),l=(0,Ct.applyCheckoutFilter)({arg:ys,filterName:"coupons",defaultValue:e});if(!a&&0===l.length)return null;const d=parseInt(i,10),p=(0,x.getSetting)("displayCartPricesIncludingTax",!1)?a+d:a;return(0,c.jsx)(Vt.TotalsItem,{className:"wc-block-components-totals-discount",currency:t,description:0!==l.length&&(0,c.jsx)(bs,{screenReaderLabel:(0,f.__)("Removing coupon…","woocommerce"),isLoading:o,showSpinner:!1,children:(0,c.jsx)("ul",{className:"wc-block-components-totals-discount__coupon-list",children:l.map((e=>(0,c.jsx)(Vt.RemovableChip,{className:"wc-block-components-totals-discount__coupon-list-item",text:e.label,screenReaderText:(0,f.sprintf)(/* translators: %s Coupon code. */ /* translators: %s Coupon code. */
(0,f.__)("Coupon: %s","woocommerce"),e.label),disabled:o,onRemove:()=>{s(e.code)},radius:"large",ariaLabel:(0,f.sprintf)(/* translators: %s is a coupon code. */ /* translators: %s is a coupon code. */
(0,f.__)('Remove coupon "%s"',"woocommerce"),e.label)},"coupon-"+e.code)))})}),label:p?(0,f.__)("Discount","woocommerce"):(0,f.__)("Coupons","woocommerce"),value:p?-1*p:"-"})};o(8413);const fs=({currency:e,values:t,className:o})=>{const r=(0,x.getSetting)("taxesEnabled",!0)&&(0,x.getSetting)("displayCartPricesIncludingTax",!1),{total_price:n,total_tax:i,tax_lines:a}=t,{receiveCart:l,...p}=Te(),m=(0,Ct.applyCheckoutFilter)({filterName:"totalLabel",defaultValue:(0,f.__)("Total","woocommerce"),extensions:p.extensions,arg:{cart:p}}),u=(0,Ct.applyCheckoutFilter)({filterName:"totalValue",defaultValue:"<price/>",extensions:p.extensions,arg:{cart:p},validation:Ct.productPriceValidation}),h=(0,c.jsx)(Vt.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:parseInt(n,10)}),g=(0,d.createInterpolateElement)(u,{price:h}),_=parseInt(i,10),k=a&&a.length>0?(0,f.sprintf)(/* translators: %s is a list of tax rates */ /* translators: %s is a list of tax rates */
(0,f.__)("Including %s","woocommerce"),a.map((({name:t,price:o})=>`${(0,ls.formatPrice)(o,e)} ${t}`)).join(", ")):(0,f.__)("Including <TaxAmount/> in taxes","woocommerce");return(0,c.jsx)(Vt.TotalsItem,{className:(0,s.A)("wc-block-components-totals-footer-item",o),currency:e,label:m,value:g,description:r&&0!==_&&(0,c.jsx)("p",{className:"wc-block-components-totals-footer-item-tax",children:(0,d.createInterpolateElement)(k,{TaxAmount:(0,c.jsx)(Vt.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:_})})})})},vs=()=>{const{shippingRates:e}=Te(),t=(e=>e.flatMap((e=>e.shipping_rates.filter((e=>e.selected)).flatMap((e=>e.name)))))(e);return t?(0,c.jsx)("div",{className:"wc-block-components-totals-shipping__via",children:(0,_e.decodeEntities)(t.filter(((e,o)=>t.indexOf(e)===o)).join(", "))}):null};let Ss=null;o(8349);const js=({address:e,onUpdate:t,onCancel:o,addressFields:s})=>{const[r,n]=(0,d.useState)(e),{showAllValidationErrors:i}=(0,_.useDispatch)(Y.validationStore),a=function(e){const t=(0,d.useRef)(null),o=(0,d.useRef)(null),s=(0,d.useRef)(e);return(0,d.useEffect)((()=>{s.current=e}),[e]),(0,d.useCallback)((e=>{if(e)t.current=e,o.current=e.ownerDocument.activeElement;else if(o.current){const e=t.current?.contains(t.current?.ownerDocument.activeElement);var r;if(t.current?.isConnected&&!e&&(null!==(r=Ss)&&void 0!==r||(Ss=o.current)),s.current)s.current();else{const e=o.current;(e?.isConnected?e:Ss)?.focus()}Ss=null}}),[])}(),{hasValidationErrors:l,isCustomerDataUpdating:p}=(0,_.useSelect)((e=>({hasValidationErrors:e(Y.validationStore).hasValidationErrors(),isCustomerDataUpdating:e(Y.cartStore).isCustomerDataUpdating()})),[]),{defaultFields:m}=mt(),u=_o(s,m,"shipping",r.country),h=(0,d.useCallback)((()=>{for(const e of u)if(e.required&&!e.hidden){const t=r[e.key];if("string"==typeof t){if(""===t.trim())return!1;continue}return!1}return!0}),[u,r]),g=(0,d.useCallback)((c=>{if(c.preventDefault(),i(),!l&&h()){if(Ue()(r,e))return o();const c=Object.fromEntries(s.filter((e=>void 0!==r[e])).map((e=>[e,r[e]])));t(c)}}),[i,l,h,r,e,s,o,t]);return(0,c.jsxs)("form",{className:"wc-block-components-shipping-calculator-address",ref:a,children:[(0,c.jsx)(fo,{fields:s,onChange:n,values:r}),(0,c.jsx)(is,{className:"wc-block-components-shipping-calculator-address__button",disabled:p,variant:"outlined",onClick:g,type:"submit",children:(0,f.__)("Check delivery options","woocommerce")})]})},Cs=(0,d.createContext)({shippingCalculatorID:"",showCalculator:!1,isShippingCalculatorOpen:!1,setIsShippingCalculatorOpen:()=>{}}),Ps=({onUpdate:e=()=>{},onCancel:t=()=>{},addressFields:o=["country","state","city","postcode"]})=>{const{shippingCalculatorID:s,showCalculator:r,setIsShippingCalculatorOpen:n}=(0,d.useContext)(Cs),{shippingAddress:i}=pt(),a="wc/cart/shipping-calculator",l=(0,d.useCallback)((()=>{n(!1),t()}),[n,t]),p=(0,d.useCallback)((t=>{(0,_.dispatch)(Y.cartStore).updateCustomerData({shipping_address:t},!1).then((()=>{(e=>{const{removeNotice:t}=(0,_.dispatch)(Xe.store),{getNotices:o}=(0,_.select)(Xe.store);o(e).forEach((o=>{t(o.id,e)}))})(a),n(!1),e(t)})).catch((e=>{(0,Y.processErrorResponse)(e,a)}))}),[e,n]);return r?(0,c.jsxs)("div",{className:"wc-block-components-shipping-calculator",id:s,children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:a}),(0,c.jsx)(js,{address:i,addressFields:o,onCancel:l,onUpdate:p})]}):null},Es=({title:e})=>{const{isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:o}=(0,d.useContext)(Cs);return(0,c.jsx)(Vt.Panel,{className:"wc-block-components-totals-shipping-panel",initialOpen:!1,hasBorder:!1,title:e,state:[t,o],children:(0,c.jsx)(Ps,{})})},Ns=e=>{const t=(e=>(0,x.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.total_shipping,10)+parseInt(e.total_shipping_tax,10):parseInt(e.total_shipping,10))(e);return 0===t?(0,c.jsx)("strong",{children:(0,f.__)("Free","woocommerce")}):t},As=()=>{const{shippingRates:e,shippingAddress:t}=Te(),o=(0,_.useSelect)((e=>e(Y.checkoutStore).prefersCollection())),s=Fe(e),{showCalculator:r}=(0,d.useContext)(Cs),n=o?(e=>{const t=(e||[]).flatMap((e=>e.shipping_rates)).find((e=>e.selected&&De(e)));if((0,pe.isObject)(t)&&(0,pe.objectHasProp)(t,"meta_data")){const e=t.meta_data.find((e=>"pickup_address"===e.key));if((0,pe.isObject)(e)&&(0,pe.objectHasProp)(e,"value")&&e.value)return e.value}return""})(e):(e=>{if(0===Object.values(e).length)return null;const t=(0,pe.isString)(D[e.country])?(0,_e.decodeEntities)(D[e.country]):"",o=(0,pe.isObject)(O[e.country])&&(0,pe.isString)(O[e.country][e.state])?(0,_e.decodeEntities)(O[e.country][e.state]):e.state,s=[];return s.push(e.postcode.toUpperCase()),s.push(e.city),s.push(o),s.push(t),s.filter(Boolean).join(", ")||null})(t),i=s?
// Translators: <address/> is the formatted shipping address.
// Translators: <address/> is the formatted shipping address.
(0,f.__)("Delivers to <address/>","woocommerce"):
// Translators: <address/> is the formatted shipping address.
// Translators: <address/> is the formatted shipping address.
(0,f.__)("No delivery options available for <address/>","woocommerce"),a=fe(t,["state","city","country","postcode"]),l=(0,x.getSetting)("shippingCostRequiresAddress",!1)&&!a,p=o?
// Translators: <address/> is the pickup location.
// Translators: <address/> is the pickup location.
(0,f.__)("Collection from <address/>","woocommerce"):i,m=(0,c.jsx)("p",{className:"wc-block-components-totals-shipping-address-summary",children:n&&!l?(0,d.createInterpolateElement)(p,{address:(0,c.jsx)("strong",{children:n})}):(0,c.jsx)(c.Fragment,{children:(0,f.__)("Enter address to check delivery options","woocommerce")})});return(0,c.jsx)("div",{className:"wc-block-components-shipping-address",children:r&&(0,c.jsx)(Es,{title:m})})};o(6562);const Is=({label:e=(0,f.__)("Shipping","woocommerce"),placeholder:t=null,collaterals:o=null})=>{const{cartTotals:s,shippingRates:r}=Te(),n=Fe(r);return(0,c.jsx)("div",{className:"wc-block-components-totals-shipping",children:(0,c.jsx)(Vt.TotalsItem,{label:e,value:n?Ns(s):t,description:(0,c.jsxs)(c.Fragment,{children:[!!n&&(0,c.jsx)(vs,{}),(0,c.jsx)(As,{}),o&&(0,c.jsx)("div",{className:"wc-block-components-totals-shipping__collaterals",children:o})]}),currency:(0,ls.getCurrencyFromPriceResponse)(s)})})};var Rs=o(559);const Ts=()=>{const{extensions:e,receiveCart:t,...o}=Te(),s={extensions:e,cart:o,context:"woocommerce/checkout"};return(0,c.jsx)(Ct.ExperimentalOrderMeta.Slot,{...s})},{Fill:Ms,Slot:Bs}=(0,Ct.createSlotFill)("checkoutOrderSummaryActionArea"),Ds=JSON.parse('{"xY":{"align":false,"html":false,"multiple":false,"reusable":false,"inserter":false,"lock":false},"uK":{"lock":{"type":"object","default":{"remove":true}}}}'),Os=[{attributes:Ds.uK,save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(l.InnerBlocks.Content,{})}),supports:Ds.xY,migrate:({attributes:e})=>[e,[(0,a.createBlock)("woocommerce/checkout-order-summary-cart-items-block",{},[]),(0,a.createBlock)("woocommerce/checkout-order-summary-coupon-form-block",{},[]),(0,a.createBlock)("woocommerce/checkout-order-summary-totals-block",{},[(0,a.createBlock)("woocommerce/checkout-order-summary-subtotal-block",{},[]),(0,a.createBlock)("woocommerce/checkout-order-summary-fee-block",{},[]),(0,a.createBlock)("woocommerce/checkout-order-summary-discount-block",{},[]),(0,a.createBlock)("woocommerce/checkout-order-summary-shipping-block",{},[]),(0,a.createBlock)("woocommerce/checkout-order-summary-taxes-block",{},[])])]],isEligible:(e,t)=>!t.some((e=>"woocommerce/checkout-order-summary-totals-block"===e.name))}],Fs=Os;o(6229),(0,a.registerBlockType)("woocommerce/checkout-order-summary-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},attributes:{className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}},edit:({clientId:e})=>{const t=(0,l.useBlockProps)(),{cartTotals:o}=Te(),r=(0,ls.getCurrencyFromPriceResponse)(o),n=parseInt(o.total_price,10),i=Mt(Ct.innerBlockAreas.CHECKOUT_ORDER_SUMMARY),{isLarge:a}=u(),[p,m]=(0,d.useState)(!1),h=(0,d.useId)(),g=a?{}:{role:"button",onClick:()=>m(!p),"aria-expanded":p,"aria-controls":h,tabIndex:0,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||m(!p)}},_=[["woocommerce/checkout-order-summary-cart-items-block",{},[]],["woocommerce/checkout-order-summary-coupon-form-block",{},[]],["woocommerce/checkout-order-summary-totals-block",{},[]]];return Bt({clientId:e,registeredBlocks:i,defaultTemplate:_}),(0,c.jsxs)("div",{...t,children:[(0,c.jsxs)("div",{className:"wc-block-components-checkout-order-summary__title",...g,children:[(0,c.jsx)("p",{className:"wc-block-components-checkout-order-summary__title-text",role:"heading",children:(0,f.__)("Order summary","woocommerce")}),!a&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:r,value:n}),(0,c.jsx)(Dt.Icon,{icon:p?Rs.A:Zt.A})]})]}),(0,c.jsxs)("div",{className:(0,s.A)("wc-block-components-checkout-order-summary__content",{"is-open":p}),id:h,children:[(0,c.jsx)(l.InnerBlocks,{allowedBlocks:i,template:_}),(0,c.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,c.jsx)(fs,{currency:r,values:o})}),(0,c.jsx)(Ts,{})]})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(l.InnerBlocks.Content,{})}),deprecated:Fs});var Ls=o(6208),Vs=o(7035),Us=o(3240),$s=o.n(Us);const Hs=["a","b","em","i","strong","p","br"],qs=["target","href","rel","name","download"],zs=(e,t)=>{const o=t?.tags||Hs,s=t?.attr||qs;return $s().sanitize(e,{ALLOWED_TAGS:o,ALLOWED_ATTR:s})},Ws={warning:"#F0B849",error:"#CC1818",success:"#46B450",info:"#0073AA"},Ys=({status:e="warning",...t})=>(0,c.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",...t,children:[(0,c.jsx)("path",{d:"M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z",stroke:Ws[e],strokeWidth:"1.5"}),(0,c.jsx)("path",{d:"M13 7H11V13H13V7Z",fill:Ws[e]}),(0,c.jsx)("path",{d:"M13 15H11V17H13V15Z",fill:Ws[e]})]});o(4459);const Gs=({href:e,title:t,description:o,warning:s})=>(0,c.jsxs)("a",{href:e,className:"wc-block-editor-components-external-link-card",target:"_blank",rel:"noreferrer",children:[(0,c.jsxs)("span",{className:"wc-block-editor-components-external-link-card__content",children:[(0,c.jsx)("strong",{className:"wc-block-editor-components-external-link-card__title",children:t}),o&&(0,c.jsx)("span",{className:"wc-block-editor-components-external-link-card__description",dangerouslySetInnerHTML:{__html:zs(o)}}),s?(0,c.jsxs)("span",{className:"wc-block-editor-components-external-link-card__warning",children:[(0,c.jsx)(i.A,{icon:(0,c.jsx)(Ys,{status:"error"})}),(0,c.jsx)("span",{children:s})]}):null]}),(0,c.jsx)(Dt.VisuallyHidden,{as:"span",children:/* translators: accessibility text */ /* translators: accessibility text */
(0,f.__)("(opens in a new tab)","woocommerce")}),(0,c.jsx)(i.A,{icon:Vs.A,className:"wc-block-editor-components-external-link-card__icon"})]}),Ks=window.wp.autop,Zs=e=>e.replace(/<\/?[a-z][^>]*?>/gi,""),Js=(e,t)=>e.replace(/[\s|\.\,]+$/i,"")+t,Xs=(e,t,o="&hellip;",s=!0)=>{const r=Zs(e),c=r.split(" ").splice(0,t).join(" ");return c===r?s?(0,Ks.autop)(r):r:s?(0,Ks.autop)(Js(c,o)):Js(c,o)},Qs=(e,t,o=!0,s="&hellip;",r=!0)=>{const c=Zs(e),n=c.slice(0,t);if(n===c)return r?(0,Ks.autop)(c):c;if(o)return(0,Ks.autop)(Js(n,s));const i=n.match(/([\s]+)/g),a=i?i.length:0,l=c.slice(0,t+a);return r?(0,Ks.autop)(Js(l,s)):Js(l,s)};var er=o(5614),tr=(o(7575),o(8034)),or=o(2624),sr=o(4144);const rr=e=>{switch(e){case"success":case"warning":case"info":case"default":return"polite";default:return"assertive"}},cr=e=>{switch(e){case"success":return tr.A;case"warning":case"info":case"error":return or.A;default:return sr.A}};var nr=o(195);const ir=({className:e,status:t="default",children:o,spokenMessage:r=o,onRemove:n=()=>{},isDismissible:a=!0,politeness:l=rr(t),summary:p})=>(((e,t)=>{const o="string"==typeof e?e:(0,d.renderToString)(e);(0,d.useEffect)((()=>{o&&(0,nr.speak)(o,t)}),[o,t])})(r,l),(0,c.jsxs)("div",{className:(0,s.A)(e,"wc-block-components-notice-banner","is-"+t,{"is-dismissible":a}),children:[(0,c.jsx)(i.A,{icon:cr(t)}),(0,c.jsxs)("div",{className:"wc-block-components-notice-banner__content",children:[p&&(0,c.jsx)("p",{className:"wc-block-components-notice-banner__summary",children:p}),o]}),!!a&&(0,c.jsx)(is,{className:"wc-block-components-notice-banner__dismiss","aria-label":(0,f.__)("Dismiss this notice","woocommerce"),onClick:e=>{"function"==typeof e?.preventDefault&&e.preventDefault&&e.preventDefault(),n()},removeTextWrap:!0,children:(0,c.jsx)(i.A,{icon:er.A})})]}));o(4147);const ar=()=>(0,c.jsx)(ir,{isDismissible:!1,className:"wc-block-checkout__no-payment-methods-notice",status:"error",children:(0,f.__)("There are no payment methods available. This may be an error on our side. Please contact us if you need any help placing your order.","woocommerce")}),lr=(0,c.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsxs)("g",{fill:"none",fillRule:"evenodd",children:[(0,c.jsx)("path",{d:"M0 0h24v24H0z"}),(0,c.jsx)("path",{fill:"#000",fillRule:"nonzero",d:"M17.3 8v1c1 .2 1.4.9 1.4 1.7h-1c0-.6-.3-1-1-1-.8 0-1.3.4-1.3.9 0 .4.3.6 1.4 1 1 .2 2 .6 2 1.9 0 .9-.6 1.4-1.5 1.5v1H16v-1c-.9-.1-1.6-.7-1.7-1.7h1c0 .6.4 1 1.3 1 1 0 1.2-.5 1.2-.8 0-.4-.2-.8-1.3-1.1-1.3-.3-2.1-.8-2.1-1.8 0-.9.7-1.5 1.6-1.6V8h1.3zM12 10v1H6v-1h6zm2-2v1H6V8h8zM2 4v16h20V4H2zm2 14V6h16v12H4z"}),(0,c.jsx)("path",{stroke:"#000",strokeLinecap:"round",d:"M6 16c2.6 0 3.9-3 1.7-3-2 0-1 3 1.5 3 1 0 1-.8 2.8-.8"})]})});var dr=o(6600),pr=o(8486);o(9287);const mr={bank:dr.A,bill:pr.A,card:Ls.A,checkPayment:lr},ur=({icon:e="",text:t=""})=>{const o=!!e,r=(0,d.useCallback)((e=>o&&(0,pe.isString)(e)&&(0,pe.objectHasProp)(mr,e)),[o]),n=(0,s.A)("wc-block-components-payment-method-label",{"wc-block-components-payment-method-label--with-icon":o});return(0,c.jsxs)("span",{className:n,children:[r(e)?(0,c.jsx)(i.A,{icon:mr[e]}):e,t]})},hr=e=>`wc-block-components-payment-method-icon wc-block-components-payment-method-icon--${e}`,gr=({id:e,src:t=null,alt:o=""})=>t?(0,c.jsx)("img",{className:hr(e),src:t,alt:o}):null,_r=[{id:"alipay",alt:"Alipay",src:S+"payment-methods/alipay.svg"},{id:"amex",alt:"American Express",src:S+"payment-methods/amex.svg"},{id:"bancontact",alt:"Bancontact",src:S+"payment-methods/bancontact.svg"},{id:"diners",alt:"Diners Club",src:S+"payment-methods/diners.svg"},{id:"discover",alt:"Discover",src:S+"payment-methods/discover.svg"},{id:"eps",alt:"EPS",src:S+"payment-methods/eps.svg"},{id:"giropay",alt:"Giropay",src:S+"payment-methods/giropay.svg"},{id:"ideal",alt:"iDeal",src:S+"payment-methods/ideal.svg"},{id:"jcb",alt:"JCB",src:S+"payment-methods/jcb.svg"},{id:"laser",alt:"Laser",src:S+"payment-methods/laser.svg"},{id:"maestro",alt:"Maestro",src:S+"payment-methods/maestro.svg"},{id:"mastercard",alt:"Mastercard",src:S+"payment-methods/mastercard.svg"},{id:"multibanco",alt:"Multibanco",src:S+"payment-methods/multibanco.svg"},{id:"p24",alt:"Przelewy24",src:S+"payment-methods/p24.svg"},{id:"sepa",alt:"Sepa",src:S+"payment-methods/sepa.svg"},{id:"sofort",alt:"Sofort",src:S+"payment-methods/sofort.svg"},{id:"unionpay",alt:"Union Pay",src:S+"payment-methods/unionpay.svg"},{id:"visa",alt:"Visa",src:S+"payment-methods/visa.svg"},{id:"wechat",alt:"WeChat",src:S+"payment-methods/wechat.svg"}];o(6983);const kr=({icons:e=[],align:t="center",className:o})=>{const r=(e=>{const t={};return e.forEach((e=>{let o={};"string"==typeof e&&(o={id:e,alt:e,src:null}),"object"==typeof e&&(o={id:e.id||"",alt:e.alt||"",src:e.src||null}),o.id&&(0,pe.isString)(o.id)&&!t[o.id]&&(t[o.id]=o)})),Object.values(t)})(e);if(0===r.length)return null;const n=(0,s.A)("wc-block-components-payment-method-icons",{"wc-block-components-payment-method-icons--align-left":"left"===t,"wc-block-components-payment-method-icons--align-right":"right"===t},o);return(0,c.jsx)("div",{className:n,children:r.map((e=>{const t={...e,...(o=e.id,_r.find((e=>e.id===o))||{})};var o;return(0,c.jsx)(gr,{...t},"payment-method-icon-"+e.id)}))})},br=(e="")=>{const{cartCoupons:t,cartIsLoading:o}=Te(),{createErrorNotice:s}=(0,_.useDispatch)("core/notices"),{createNotice:r}=(0,_.useDispatch)("core/notices"),{setValidationErrors:c}=(0,_.useDispatch)(Y.validationStore),{isApplyingCoupon:n,isRemovingCoupon:i}=(0,_.useSelect)((e=>{const t=e(Y.cartStore);return{isApplyingCoupon:t.isApplyingCoupon(),isRemovingCoupon:t.isRemovingCoupon()}})),{applyCoupon:a,removeCoupon:l}=(0,_.useDispatch)(Y.cartStore),d=(0,_.useSelect)((e=>e(Y.checkoutStore).getOrderId()));return{appliedCoupons:t,isLoading:o,applyCoupon:t=>a(t).then((()=>((0,Ct.applyCheckoutFilter)({filterName:"showApplyCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&r("info",(0,f.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,f.__)('Coupon code "%s" has been applied to your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((e=>{const t=(e=>d&&d>0&&e?.data?.details?.checkout?e.data.details.checkout:e?.data?.details?.cart?e.data.details.cart:e.message)(e);return c({coupon:{message:(0,_e.decodeEntities)(t),hidden:!1}}),Promise.resolve(!1)})),removeCoupon:t=>l(t).then((()=>((0,Ct.applyCheckoutFilter)({filterName:"showRemoveCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&r("info",(0,f.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,f.__)('Coupon code "%s" has been removed from your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((t=>(s(t.message,{id:"coupon-form",context:e}),Promise.resolve(!1)))),isApplyingCoupon:n,isRemovingCoupon:i}},wr=(e,t)=>{const o=[],s=(t,o)=>{const s=o+"_tax",r=(0,pe.objectHasProp)(e,o)&&(0,pe.isString)(e[o])?parseInt(e[o],10):0;return{key:o,label:t,value:r,valueWithTax:r+((0,pe.objectHasProp)(e,s)&&(0,pe.isString)(e[s])?parseInt(e[s],10):0)}};return o.push(s((0,f.__)("Subtotal:","woocommerce"),"total_items")),o.push(s((0,f.__)("Fees:","woocommerce"),"total_fees")),o.push(s((0,f.__)("Discount:","woocommerce"),"total_discount")),o.push({key:"total_tax",label:(0,f.__)("Taxes:","woocommerce"),value:parseInt(e.total_tax,10),valueWithTax:parseInt(e.total_tax,10)}),t&&o.push(s((0,f.__)("Shipping:","woocommerce"),"total_shipping")),o},yr=()=>{const{onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutAfterProcessingWithSuccess:o,onCheckoutAfterProcessingWithError:s,onSubmit:r}=(0,d.useContext)(rt),{onCheckoutValidation:c,onCheckoutSuccess:n,onCheckoutFail:i}=Qe.checkoutEvents,{isCalculating:a,isComplete:l,isIdle:p,isProcessing:m,customerId:u}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{isComplete:t.isComplete(),isIdle:t.isIdle(),isProcessing:t.isProcessing(),customerId:t.getCustomerId(),isCalculating:t.isCalculating()}})),{paymentStatus:h,activePaymentMethod:g,shouldSavePayment:k}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{paymentStatus:{get isPristine(){return K()("isPristine",{since:"9.6.0",alternative:"isIdle",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentIdle()},isIdle:t.isPaymentIdle(),isStarted:t.isExpressPaymentStarted(),isProcessing:t.isPaymentProcessing(),get isFinished(){return K()("isFinished",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()||t.isPaymentReady()},hasError:t.hasPaymentError(),get hasFailed(){return K()("hasFailed",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()},get isSuccessful(){return K()("isSuccessful",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentReady()},isReady:t.isPaymentReady(),isDoingExpressPayment:t.isExpressPaymentMethodActive()},activePaymentMethod:t.getActivePaymentMethod(),shouldSavePayment:t.getShouldSavePaymentMethod()}})),{__internalSetExpressPaymentError:b}=(0,_.useDispatch)(Y.paymentStore),{onPaymentProcessing:w,onPaymentSetup:y}=(0,d.useContext)(ee),{shippingErrorStatus:v,shippingErrorTypes:S,onShippingRateSuccess:j,onShippingRateFail:C,onShippingRateSelectSuccess:P,onShippingRateSelectFail:E}=Ke(),{shippingRates:N,isLoadingRates:A,selectedRates:I,isSelectingRate:R,selectShippingRate:T,needsShipping:M}=qe(),{billingAddress:B,shippingAddress:D}=(0,_.useSelect)((e=>e(Y.cartStore).getCustomerData())),{setShippingAddress:O}=(0,_.useDispatch)(Y.cartStore),{cartItems:F,cartFees:L,cartTotals:V,extensions:U}=Te(),{appliedCoupons:$}=br(),H=(0,d.useRef)(wr(V,M)),q=(0,d.useRef)({label:(0,f.__)("Total","woocommerce"),value:parseInt(V.total_price,10)});(0,d.useEffect)((()=>{H.current=wr(V,M),q.current={label:(0,f.__)("Total","woocommerce"),value:parseInt(V.total_price,10)}}),[V,M]);const z=(0,d.useCallback)(((e="")=>{K()("setExpressPaymentError should only be used by Express Payment Methods (using the provided onError handler).",{alternative:"",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),b(e)}),[b]);return{activePaymentMethod:g,billing:{appliedCoupons:$,billingAddress:B,billingData:B,cartTotal:q.current,cartTotalItems:H.current,currency:(0,ls.getCurrencyFromPriceResponse)(V),customerId:u,displayPricesIncludingTax:(0,x.getSetting)("displayCartPricesIncludingTax",!1)},cartData:{cartItems:F,cartFees:L,extensions:U},checkoutStatus:{isCalculating:a,isComplete:l,isIdle:p,isProcessing:m},components:{LoadingMask:bs,PaymentMethodIcons:kr,PaymentMethodLabel:ur,ValidationInputError:Vt.ValidationInputError},emitResponse:{noticeContexts:me,responseTypes:pe.responseTypes},eventRegistration:{onCheckoutAfterProcessingWithError:s,onCheckoutAfterProcessingWithSuccess:o,onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutSuccess:n,onCheckoutFail:i,onCheckoutValidation:c,onPaymentProcessing:w,onPaymentSetup:y,onShippingRateFail:C,onShippingRateSelectFail:E,onShippingRateSelectSuccess:P,onShippingRateSuccess:j},onSubmit:r,paymentStatus:h,setExpressPaymentError:z,shippingData:{isSelectingRate:R,needsShipping:M,selectedRates:I,setSelectedRates:T,setShippingAddress:O,shippingAddress:D,shippingRates:N,shippingRatesLoading:A},shippingStatus:{shippingErrorStatus:v,shippingErrorTypes:S},shouldSavePayment:k}};class xr extends d.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return{errorMessage:e.message,hasError:!0}}render(){const{hasError:e,errorMessage:t}=this.state,{isEditor:o}=this.props;if(e){let e=(0,f.__)("We are experiencing difficulties with this payment method. Please contact us for assistance.","woocommerce");(o||x.CURRENT_USER_IS_ADMIN)&&(e=t||(0,f.__)("There was an error with this payment method. Please verify it's configured correctly.","woocommerce"));const s=[{id:"0",content:e,isDismissible:!1,status:"error"}];return(0,c.jsx)(Vt.StoreNoticesContainer,{additionalNotices:s,context:me.PAYMENTS})}return this.props.children}}const fr=xr,vr=({children:e,showSaveOption:t})=>{const{isEditor:o}=b(),{shouldSavePaymentMethod:s,customerId:r,shouldCreateAccount:n}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore),o=e(Y.checkoutStore);return{shouldSavePaymentMethod:t.getShouldSavePaymentMethod(),customerId:o.getCustomerId(),shouldCreateAccount:o.getShouldCreateAccount()}}),[]),{__internalSetShouldSavePaymentMethod:i}=(0,_.useDispatch)(Y.paymentStore),a=(0,x.getSetting)("checkoutAllowsGuest",!1),l=r>0||n||!a;return(0,d.useEffect)((()=>{!l&&s&&i(!1)}),[l,s,i]),(0,c.jsxs)(fr,{isEditor:o,children:[e,l&&t&&(0,c.jsx)(Vt.CheckboxControl,{className:"wc-block-components-payment-methods__save-card-info",label:(0,f.__)("Save payment information to my account for future purchases.","woocommerce"),checked:s,onChange:()=>i(!s)})]})},Sr=()=>{const{activeSavedToken:e,activePaymentMethod:t,isExpressPaymentMethodActive:o,savedPaymentMethods:r,availablePaymentMethods:n}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{activeSavedToken:t.getActiveSavedToken(),activePaymentMethod:t.getActivePaymentMethod(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),savedPaymentMethods:t.getSavedPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),{__internalSetActivePaymentMethod:i}=(0,_.useDispatch)(Y.paymentStore),a=(0,at.getPaymentMethods)(),{...l}=yr(),{removeNotice:p}=(0,_.useDispatch)("core/notices"),{dispatchCheckoutEvent:m}=He(),{isEditor:u}=b(),h=Object.keys(n).map((e=>{const{edit:t,content:o,label:s,supports:r}=a[e],n=u?t:o;return{value:e,label:"string"==typeof s?s:(0,d.cloneElement)(s,{components:l.components}),name:`wc-saved-payment-method-token-${e}`,content:(0,c.jsx)(vr,{showSaveOption:r.showSaveOption,children:(0,d.cloneElement)(n,{__internalSetActivePaymentMethod:i,...l})})}})),g=(0,d.useCallback)((e=>{i(e),p("wc-payment-error",me.PAYMENTS),m("set-active-payment-method",{paymentMethodSlug:e})}),[m,p,i]),k=0===Object.keys(r).length&&1===Object.keys(a).length,w=(0,s.A)({"disable-radio-control":k});return o?null:(0,c.jsx)(Vt.RadioControlAccordion,{highlightChecked:!0,id:"wc-payment-method-options",className:w,selected:e?null:t,onChange:g,options:h})},jr="wc/store/cart",Cr=((0,f.__)("Unable to get cart data from the API.","woocommerce"),[]),Pr=[],Er={},Nr={};V.forEach((e=>{Nr[e]=""}));const Ar={};V.forEach((e=>{Ar[e]=""})),Ar.email="";const Ir={cartItemsPendingQuantity:[],cartItemsPendingDelete:[],productsPendingAdd:[],cartData:{coupons:[],shippingRates:[],shippingAddress:Nr,billingAddress:Ar,items:[],itemsCount:0,itemsWeight:0,crossSells:[],needsShipping:!0,needsPayment:!1,hasCalculatedShipping:!0,fees:[],totals:{currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:".",currency_thousand_separator:",",currency_prefix:"",currency_suffix:"",total_items:"0",total_items_tax:"0",total_fees:"0",total_fees_tax:"0",total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_price:"0",total_tax:"0",tax_lines:[]},errors:Cr,paymentMethods:[],paymentRequirements:[],extensions:Er},metaData:{updatingCustomerData:!1,updatingSelectedRate:!1,applyingCoupon:"",removingCoupon:"",isCartDataStale:!1},errors:Pr},Rr=({method:e,expires:t})=>{var o,s;return(0,f.sprintf)(/* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card, %3$s is referring to the expiry date.  */ /* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card, %3$s is referring to the expiry date.  */
(0,f.__)("%1$s ending in %2$s (expires %3$s)","woocommerce"),null!==(o=null!==(s=e?.display_brand)&&void 0!==s?s:e?.networks?.preferred)&&void 0!==o?o:e.brand,e.last4,t)},Tr=({method:e})=>e.brand&&e.last4?(0,f.sprintf)(/* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card. */ /* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card. */
(0,f.__)("%1$s ending in %2$s","woocommerce"),e.brand,e.last4):(0,f.sprintf)(/* translators: %s is the name of the payment method gateway. */ /* translators: %s is the name of the payment method gateway. */
(0,f.__)("Saved token for %s","woocommerce"),e.gateway),Mr=()=>{const{activeSavedToken:e,activePaymentMethod:t,savedPaymentMethods:o}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{activeSavedToken:t.getActiveSavedToken(),activePaymentMethod:t.getActivePaymentMethod(),savedPaymentMethods:t.getSavedPaymentMethods()}})),{__internalSetActivePaymentMethod:s}=(0,_.useDispatch)(Y.paymentStore),r=(()=>{let e;if((0,_.select)("core/editor")){const t={cartCoupons:St.coupons,cartItems:St.items,crossSellsProducts:St.cross_sells,cartFees:St.fees,cartItemsCount:St.items_count,cartItemsWeight:St.items_weight,cartNeedsPayment:St.needs_payment,cartNeedsShipping:St.needs_shipping,cartItemErrors:Cr,cartTotals:St.totals,cartIsLoading:!1,cartErrors:Pr,billingData:Ir.cartData.billingAddress,billingAddress:Ir.cartData.billingAddress,shippingAddress:Ir.cartData.shippingAddress,extensions:Er,shippingRates:St.shipping_rates,isLoadingRates:!1,cartHasCalculatedShipping:St.has_calculated_shipping,paymentRequirements:St.payment_requirements,receiveCart:()=>{}};e={cart:t,cartTotals:t.cartTotals,cartNeedsShipping:t.cartNeedsShipping,billingData:t.billingAddress,billingAddress:t.billingAddress,shippingAddress:t.shippingAddress,selectedShippingMethods:Le(t.shippingRates),paymentMethods:St.payment_methods,paymentRequirements:t.paymentRequirements}}else{const t=(0,_.select)(jr),o=t.getCartData(),s=t.getCartErrors(),r=t.getCartTotals(),c=!t.hasFinishedResolution("getCartData"),n=t.isCustomerDataUpdating(),i=Le(o.shippingRates);e={cart:{cartCoupons:o.coupons,cartItems:o.items,crossSellsProducts:o.crossSells,cartFees:o.fees,cartItemsCount:o.itemsCount,cartItemsWeight:o.itemsWeight,cartNeedsPayment:o.needsPayment,cartNeedsShipping:o.needsShipping,cartItemErrors:o.errors,cartTotals:r,cartIsLoading:c,cartErrors:s,billingData:xe(o.billingAddress),billingAddress:xe(o.billingAddress),shippingAddress:xe(o.shippingAddress),extensions:o.extensions,shippingRates:o.shippingRates,isLoadingRates:n,cartHasCalculatedShipping:o.hasCalculatedShipping,paymentRequirements:o.paymentRequirements,receiveCart:(0,_.dispatch)(jr).receiveCart},cartTotals:o.totals,cartNeedsShipping:o.needsShipping,billingData:o.billingAddress,billingAddress:o.billingAddress,shippingAddress:o.shippingAddress,selectedShippingMethods:i,paymentMethods:o.paymentMethods,paymentRequirements:o.paymentRequirements}}return e})(),n=(0,at.getPaymentMethods)(),i=yr(),{removeNotice:a}=(0,_.useDispatch)("core/notices"),{dispatchCheckoutEvent:l}=He(),p=(0,d.useMemo)((()=>{const e=Object.keys(o),t=new Set(e.flatMap((e=>o[e].map((e=>e.method.gateway))))),c=Array.from(t).filter((e=>n[e]?.canMakePayment(r)));return e.flatMap((e=>o[e].map((t=>{if(!c.includes(t.method.gateway))return;const o="cc"===e||"echeck"===e,r=t.method.gateway;return{name:`wc-saved-payment-method-token-${r}`,label:o?Rr(t):Tr(t),value:t.tokenId.toString(),onChange:e=>{const t=`wc-${r}-payment-token`;s(r,{token:e,payment_method:r,[t]:e.toString(),isSavedToken:!0}),a("wc-payment-error",me.PAYMENTS),l("set-active-payment-method",{paymentMethodSlug:r})}}})))).filter((e=>void 0!==e))}),[o,n,s,a,l,r]),m=e&&n[t]&&void 0!==n[t]?.savedTokenComponent&&!(0,pe.isNull)(n[t].savedTokenComponent)?(0,d.cloneElement)(n[t].savedTokenComponent,{token:e,...i}):null;return p.length>0?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.RadioControl,{highlightChecked:!0,id:"wc-payment-method-saved-tokens",selected:e,options:p,onChange:()=>{}}),m]}):null};o(7215);const Br=({noPaymentMethods:e=(0,c.jsx)(ar,{})})=>{const{paymentMethodsInitialized:t,availablePaymentMethods:o,savedPaymentMethods:s}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),availablePaymentMethods:t.getAvailablePaymentMethods(),savedPaymentMethods:t.getSavedPaymentMethods()}}));return t&&0===Object.keys(o).length?e:(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Mr,{}),Object.keys(s).length>0&&(0,c.jsx)(Vt.Label,{label:(0,f.__)("Use another payment method.","woocommerce"),screenReaderLabel:(0,f.__)("Other available payment methods","woocommerce"),wrapperElement:"p",wrapperProps:{className:["wc-block-components-checkout-step__description wc-block-components-checkout-step__description-payments-aligned"]}}),(0,c.jsx)(Sr,{})]})},Dr=({noPaymentMethods:e})=>(0,c.jsx)(Br,{noPaymentMethods:e});var Or=o(9143),Fr=o(5194);const Lr=({label:e,description:t,buttonLabel:o,buttonHref:s,icon:r=Or.A})=>(0,c.jsxs)(Dt.Placeholder,{icon:(0,c.jsx)(i.A,{icon:r}),label:e,className:"wc-block-checkout__configure-placeholder",children:[(0,c.jsx)("span",{className:"wc-block-checkout__configure-placeholder-description",children:t}),(0,c.jsx)(Dt.Button,{variant:"primary",href:s,target:"_blank",rel:"noopener noreferrer",style:{backgroundColor:Fr.T.fN2,color:Fr.T.cai,pointerEvents:"all"},children:o})]}),Vr={...No({defaultTitle:(0,f.__)("Payment options","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};(0,a.registerBlockType)("woocommerce/checkout-payment-block",{icon:{src:(0,c.jsx)(i.A,{icon:Ls.A,className:"wc-block-editor-components-block-icon"})},attributes:Vr,edit:({attributes:e,setAttributes:t})=>{const o=(0,x.getSetting)("globalPaymentMethods"),r=(0,_.select)(Y.paymentStore).getIncompatiblePaymentMethods(),n=(0,f.__)("Incompatible with block-based checkout","woocommerce"),i=v.wordCountType;return(0,c.jsxs)($t,{attributes:e,setAttributes:t,className:(0,s.A)("wc-block-checkout__payment-method",e?.className),children:[(0,c.jsx)(l.InspectorControls,{children:o.length>0&&(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Methods","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("You currently have the following payment integrations active.","woocommerce")}),o.map((e=>{const t=!!r[e.id];let o;return o="words"===i?Xs(e.description,30,void 0,!1):Qs(e.description,30,"characters_including_spaces"===i,void 0,!1),(0,c.jsx)(Gs,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=checkout&section=${e.id}`,title:e.title,description:o,...t?{warning:n}:{}},e.id)})),(0,c.jsx)(Dt.ExternalLink,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=checkout`,children:(0,f.__)("Manage payment methods","woocommerce")})]})}),(0,c.jsx)(Kt,{children:(0,c.jsx)(Dr,{noPaymentMethods:(0,c.jsx)(Lr,{icon:Ls.A,label:(0,f.__)("Payment options","woocommerce"),description:(0,f.__)("Your store does not have any payment methods that support the Checkout block. Once you have configured a compatible payment method it will be displayed here.","woocommerce"),buttonLabel:(0,f.__)("Configure Payment Options","woocommerce"),buttonHref:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=checkout`})})}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.PAYMENT_METHODS})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});const Ur=(0,c.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"currentColor",viewBox:"0 0 24 24",children:[(0,c.jsx)("path",{stroke:"#1E1E1E",strokeLinejoin:"round",strokeWidth:"1.5",d:"M18.25 12a6.25 6.25 0 1 1-12.5 0 6.25 6.25 0 0 1 12.5 0Z"}),(0,c.jsx)("path",{fill:"#1E1E1E",d:"M10 3h4v3h-4z"}),(0,c.jsx)("rect",{width:"1.5",height:"5",x:"11.25",y:"8",fill:"#1E1E1E",rx:".75"}),(0,c.jsx)("path",{fill:"#1E1E1E",d:"m15.7 4.816 1.66 1.078-1.114 1.718-1.661-1.078z"})]}),$r=()=>((e=!1)=>{const{paymentMethodsInitialized:t,expressPaymentMethodsInitialized:o,availablePaymentMethods:s,availableExpressPaymentMethods:r}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),c=Object.values(s).map((({name:e})=>e)),n=Object.values(r).map((({name:e})=>e)),i=(0,at.getPaymentMethods)(),a=(0,at.getExpressPaymentMethods)(),l=Object.keys(i).reduce(((e,t)=>(c.includes(t)&&(e[t]=i[t]),e)),{}),d=Object.keys(a).reduce(((e,t)=>(n.includes(t)&&(e[t]=a[t]),e)),{}),p=co(l),m=co(d);return{paymentMethods:e?m:p,isInitialized:e?o:t}})(!0),Hr=["height","borderRadius"],qr=e=>{const t=(0,_.select)(Y.paymentStore).getAvailableExpressPaymentMethods();return Object.values(t).reduce(((t,o)=>t||o?.supportsStyle.some((t=>e.includes(t)))),!1)},zr=({attributes:e,setAttributes:t})=>{const{buttonHeight:o,buttonBorderRadius:s}=e;return(0,c.jsxs)(c.Fragment,{children:[qr(["height"])&&(0,c.jsx)(Dt.RadioControl,{label:(0,f.__)("Button height","woocommerce"),selected:o,options:[{label:(0,f.__)("Small (40px)","woocommerce"),value:"40"},{label:(0,f.__)("Medium (48px)","woocommerce"),value:"48"},{label:(0,f.__)("Large (55px)","woocommerce"),value:"55"}],onChange:e=>t({buttonHeight:e})}),qr(["borderRadius"])&&(0,c.jsx)("div",{className:"border-radius-control-container",children:(0,c.jsx)(l.HeightControl,{label:(0,f.__)("Button border radius","woocommerce"),value:s,onChange:e=>{const o=e.replace("px","");t({buttonBorderRadius:o})}})})]})},Wr=({attributes:e,setAttributes:t})=>e.showButtonStyles?(0,c.jsx)(zr,{attributes:e,setAttributes:t}):null,Yr=()=>{const e=(0,_.select)(Y.paymentStore).getAvailableExpressPaymentMethods();return Object.entries(e).length<1?(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("You currently have no express payment integrations active.","woocommerce")}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("You currently have the following express payment integrations active.","woocommerce")}),Object.values(e).map((e=>(0,c.jsx)(Gs,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=checkout&section=${encodeURIComponent(e.gatewayId)}`,title:e.title,description:e.description},e.name)))]})},Gr=(0,c.jsxs)(c.Fragment,{children:[(0,f.__)("Apply uniform styles","woocommerce")," ",(0,c.jsx)("span",{className:"express-payment-styles-beta-badge",children:"Beta"})]}),Kr=({attributes:e,setAttributes:t})=>(0,c.jsxs)(l.InspectorControls,{children:[qr(Hr)&&(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Button Settings","woocommerce"),className:"express-payment-button-settings",children:[(0,c.jsx)(Dt.ToggleControl,{label:Gr,checked:e.showButtonStyles,onChange:()=>t({showButtonStyles:!e.showButtonStyles}),help:(0,f.__)("Sets a consistent style for express payment buttons.","woocommerce")}),(0,c.jsxs)(Dt.Notice,{status:"warning",isDismissible:!1,className:"wc-block-checkout__notice express-payment-styles-notice",children:[(0,c.jsxs)("strong",{children:[(0,f.__)("Note","woocommerce"),":"]})," ",(0,f.__)("Some payment methods might not yet support all style controls","woocommerce")]}),(0,c.jsx)(Wr,{attributes:e,setAttributes:t})]}),(0,c.jsx)(Dt.PanelBody,{title:(0,f.__)("Express Payment Methods","woocommerce"),children:(0,c.jsx)(Yr,{})})]}),Zr="wc/store/payment",Jr=(0,d.createContext)({showButtonStyles:!1,buttonHeight:"48",buttonBorderRadius:"4"}),Xr=()=>{const{isEditor:e}=b(),{showButtonStyles:t,buttonHeight:o,buttonBorderRadius:s}=(0,d.useContext)(Jr),r=t?{height:o,borderRadius:s}:void 0,{activePaymentMethod:n,paymentMethodData:i}=(0,_.useSelect)((e=>{const t=e(Zr);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData()}})),{__internalSetActivePaymentMethod:a,__internalSetExpressPaymentStarted:l,__internalSetPaymentIdle:p,__internalSetPaymentError:m,__internalSetPaymentMethodData:u,__internalSetExpressPaymentError:h}=(0,_.useDispatch)(Zr),{paymentMethods:g}=$r(),k=yr(),w=(0,d.useRef)(n),y=(0,d.useRef)(i),x=(0,d.useCallback)((e=>()=>{w.current=n,y.current=i,l(),a(e)}),[n,i,a,l]),v=(0,d.useCallback)((()=>{p(),a(w.current,y.current)}),[a,p]),S=(0,d.useCallback)((e=>{m(),u(e),h(e),a(w.current,y.current)}),[a,m,u,h]),j=(0,d.useCallback)(((e="")=>{K()("Express Payment Methods should use the provided onError handler instead.",{alternative:"onError",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),e?S(e):h("")}),[h,S]),C=Object.entries(g),P=C.length>0?C.map((([t,o])=>{const s=e?o.edit:o.content;return(0,d.isValidElement)(s)?(0,c.jsx)("li",{id:`express-payment-method-${t}`,children:(0,d.cloneElement)(s,{...k,onClick:x(t),onClose:v,onError:S,setExpressPaymentError:j,buttonAttributes:r})},t):null})):(0,c.jsx)("li",{children:(0,f.__)("No registered Payment Methods","woocommerce")},"noneRegistered");return(0,c.jsx)(fr,{isEditor:e,children:(0,c.jsx)("ul",{className:"wc-block-components-express-payment__event-buttons",children:P})})};o(2831);const Qr=()=>{const{isCalculating:e,isProcessing:t,isAfterProcessing:o,isBeforeProcessing:s,isComplete:r,hasError:n}=(0,_.useSelect)((e=>{const t=e(Y.checkoutStore);return{isCalculating:t.isCalculating(),isProcessing:t.isProcessing(),isAfterProcessing:t.isAfterProcessing(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),hasError:t.hasError()}})),{availableExpressPaymentMethods:i,expressPaymentMethodsInitialized:a,isExpressPaymentMethodActive:l}=(0,_.useSelect)((e=>{const t=e(Y.paymentStore);return{availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive()}})),{isEditor:d}=b();if(!a||a&&0===Object.keys(i).length)return d||x.CURRENT_USER_IS_ADMIN?(0,c.jsx)(Vt.StoreNoticesContainer,{context:me.EXPRESS_PAYMENTS}):null;const p=t||o||s||r&&!n;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(bs,{isLoading:e||p||l,children:(0,c.jsxs)("div",{className:"wc-block-components-express-payment wc-block-components-express-payment--checkout",children:[(0,c.jsx)("div",{className:"wc-block-components-express-payment__title-container",children:(0,c.jsx)(Vt.Title,{className:"wc-block-components-express-payment__title",headingLevel:"2",children:(0,f.__)("Express Checkout","woocommerce")})}),(0,c.jsxs)("div",{className:"wc-block-components-express-payment__content",children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:me.EXPRESS_PAYMENTS}),(0,c.jsx)(Xr,{})]})]})}),(0,c.jsx)("div",{className:"wc-block-components-express-payment-continue-rule wc-block-components-express-payment-continue-rule--checkout",children:(0,f.__)("Or continue below","woocommerce")})]})},ec=({className:e})=>{const{cartNeedsPayment:t}=Te();return t?(0,c.jsx)("div",{className:e,children:(0,c.jsx)(Qr,{})}):null};o(6715),(0,a.registerBlockType)("woocommerce/checkout-express-payment-block",{icon:{src:(0,c.jsx)(i.A,{style:{fill:"none"},icon:Ur,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{paymentMethods:o,isInitialized:r}=$r(),n=Object.keys(o).length>0,i=(0,l.useBlockProps)({className:(0,s.A)({"wp-block-woocommerce-checkout-express-payment-block--has-express-payment-methods":n},e?.className),attributes:e});if(!r||!n)return null;const{buttonHeight:a,buttonBorderRadius:d,showButtonStyles:p}=e;return(0,c.jsxs)("div",{...i,children:[(0,c.jsx)(Kr,{attributes:e,setAttributes:t}),(0,c.jsx)(Jr.Provider,{value:{showButtonStyles:p,buttonHeight:a,buttonBorderRadius:d},children:(0,c.jsx)(ec,{})})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});var tc=o(4970),oc=o(8415);const sc=({minRate:e,maxRate:t,multiple:o=!1})=>{if(void 0===e||void 0===t)return null;const s=(0,x.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),r=(0,x.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(t.price,10)+parseInt(t.taxes,10):parseInt(t.price,10),n=0===s?(0,c.jsx)("em",{children:(0,f.__)("free","woocommerce")}):(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:(0,ls.getCurrencyFromPriceResponse)(e),value:s});return(0,c.jsx)("span",{className:"wc-block-checkout__shipping-method-option-price",children:s!==r||o?(0,d.createInterpolateElement)(0===s&&0===r?"<price />":(0,f.__)("from <price />","woocommerce"),{price:n}):n})};function rc(e){return e?{min:e.reduce(((e,t)=>Oe(t.method_id)?e:void 0===e||parseInt(t.price,10)<parseInt(e.price,10)?t:e),void 0),max:e.reduce(((e,t)=>Oe(t.method_id)?e:void 0===e||parseInt(t.price,10)>parseInt(e.price,10)?t:e),void 0)}:{min:void 0,max:void 0}}function cc(e){return e?{min:e.reduce(((e,t)=>Oe(t.method_id)&&(void 0===e||t.price<e.price)?t:e),void 0),max:e.reduce(((e,t)=>Oe(t.method_id)&&(void 0===e||t.price>e.price)?t:e),void 0)}:{min:void 0,max:void 0}}o(6336);const nc=(0,f.__)("Pickup","woocommerce"),ic=(0,f.__)("Ship","woocommerce"),ac=({checked:e,rate:t,showPrice:o,showIcon:r,toggleText:n,setAttributes:a,onClick:d})=>(0,c.jsxs)(no.$,{render:(0,c.jsx)("div",{}),className:(0,s.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"pickup"===e}),onClick:d,children:[!0===r&&(0,c.jsx)(i.A,{icon:oc.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,c.jsx)(l.RichText,{value:n,placeholder:nc,tagName:"span",className:"wc-block-checkout__shipping-method-option-title",onChange:e=>a({localPickupText:e}),__unstableDisableFormats:!0,preserveWhiteSpace:!0}),!0===o&&(0,c.jsx)(sc,{minRate:t.min,maxRate:t.max})]}),lc=({checked:e,rate:t,showPrice:o,showIcon:r,toggleText:n,setAttributes:a,onClick:d})=>{const p=void 0===t.min?(0,c.jsx)("span",{className:"wc-block-checkout__shipping-method-option-price",children:(0,f.__)("calculated with an address","woocommerce")}):(0,c.jsx)(sc,{minRate:t.min,maxRate:t.max});return(0,c.jsxs)(no.$,{render:(0,c.jsx)("div",{}),className:(0,s.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"shipping"===e}),onClick:d,children:[!0===r&&(0,c.jsx)(i.A,{icon:tc.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,c.jsx)(l.RichText,{value:n,placeholder:ic,tagName:"span",className:"wc-block-checkout__shipping-method-option-title",onChange:e=>a({shippingText:e}),__unstableDisableFormats:!0,preserveWhiteSpace:!0}),!0===o&&p]})},dc={...No({defaultTitle:(0,f.__)("Delivery","woocommerce"),defaultDescription:(0,f.__)("Select how you would like to receive your order.","woocommerce")}),className:{type:"string",default:""},showIcon:{type:"boolean",default:!0},showPrice:{type:"boolean",default:!1},localPickupText:{type:"string",default:nc},shippingText:{type:"string",default:ic},lock:{type:"object",default:{move:!0,remove:!0}}};(0,a.registerBlockType)("woocommerce/checkout-shipping-method-block",{icon:{src:(0,c.jsx)(i.A,{icon:tc.A,className:"wc-block-editor-components-block-icon"})},attributes:dc,edit:({attributes:e,setAttributes:t})=>{(0,d.useEffect)((()=>{const o=(0,x.getSetting)("localPickupText",e.localPickupText);t({localPickupText:o})}),[t]);const{setPrefersCollection:o}=(0,_.useDispatch)(Y.checkoutStore),{prefersCollection:r}=(0,_.useSelect)((e=>({prefersCollection:e(Y.checkoutStore).prefersCollection()}))),{showPrice:n,showIcon:i,className:a,localPickupText:p,shippingText:m}=e,{shippingRates:u,needsShipping:h,hasCalculatedShipping:g,isCollectable:k}=qe();if(!(h&&g&&u&&k&&A))return null;const b=e=>{o("pickup"===e)};return(0,c.jsxs)($t,{attributes:e,setAttributes:t,className:(0,s.A)("wc-block-checkout__shipping-method",a),children:[(0,c.jsxs)(l.InspectorControls,{children:[(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Appearance","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("Choose how this block is displayed to your customers.","woocommerce")}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Show icon","woocommerce"),checked:i,onChange:()=>t({showIcon:!i})}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Show costs","woocommerce"),checked:n,onChange:()=>t({showPrice:!n})})]}),(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Shipping Methods","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("Methods can be made managed in your store settings.","woocommerce")}),(0,c.jsx)(Gs,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`,title:(0,f.__)("Shipping","woocommerce"),description:(0,f.__)("Manage your shipping zones, methods, and rates.","woocommerce")},"shipping_methods"),(0,c.jsx)(Gs,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=pickup_location`,title:(0,f.__)("Pickup","woocommerce"),description:(0,f.__)("Allow customers to choose a local pickup location during checkout.","woocommerce")},"pickup_location")]})]}),(0,c.jsxs)("div",{id:"shipping-method",className:"wc-block-checkout__shipping-method-container",role:"radiogroup",children:[(0,c.jsx)(lc,{checked:r?"pickup":"shipping",rate:rc(u[0]?.shipping_rates),onClick:()=>{b("shipping")},showPrice:n,showIcon:i,setAttributes:t,toggleText:m}),(0,c.jsx)(ac,{checked:r?"pickup":"shipping",rate:cc(u[0]?.shipping_rates),showPrice:n,onClick:()=>{b("pickup")},showIcon:i,setAttributes:t,toggleText:p})]}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.SHIPPING_METHOD})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});const pc=e=>{const t=(0,x.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10);let o=(0,c.jsxs)(c.Fragment,{children:[Number.isFinite(t)&&(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:(0,ls.getCurrencyFromPriceResponse)(e),value:t}),(0,c.jsxs)("span",{className:"wc-block-components-shipping-rates-control__package__delivery_time",children:[Number.isFinite(t)&&e.delivery_time?" — ":null,(0,_e.decodeEntities)(e.delivery_time)]})]});return 0===t&&(o=(0,c.jsxs)("span",{className:"wc-block-components-shipping-rates-control__package__description--free",children:[(0,f.__)("Free","woocommerce"),(0,c.jsx)("span",{className:"wc-block-components-shipping-rates-control__package__delivery_time",children:e.delivery_time&&" — "+(0,_e.decodeEntities)(e.delivery_time)})]})),{label:(0,_e.decodeEntities)(e.name),value:e.rate_id,description:o}},mc=({className:e="",noResultsMessage:t,onSelectRate:o,rates:s,renderOption:r=pc,selectedRate:n,disabled:i=!1,highlightChecked:a=!1})=>{const l=n?.rate_id||"",p=Je(l),[m,u]=(0,d.useState)(null!=l?l:"");return(0,d.useEffect)((()=>{l&&l!==p&&l!==m&&u(l)}),[l,m,p]),(0,d.useEffect)((()=>{!m&&s.length>0&&(u(s[0].rate_id),o(s[0].rate_id))}),[o,s,m]),0===s.length?t:(0,c.jsx)(Vt.RadioControl,{className:e,onChange:e=>{u(e),o(e)},highlightChecked:a,disabled:i,selected:m,options:s.map(r),descriptionStackingDirection:"column"})},uc=({packageData:e})=>(0,c.jsx)("ul",{className:"wc-block-components-shipping-rates-control__package-items",children:Object.values(e.items).map((e=>{const t=(0,_e.decodeEntities)(e.name),o=e.quantity;return(0,c.jsx)("li",{className:"wc-block-components-shipping-rates-control__package-item",children:(0,c.jsx)(Vt.Label,{label:o>1?`${t} × ${o}`:`${t}`,allowHTML:!0,screenReaderLabel:(0,f.sprintf)(/* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */ /* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */
(0,f._n)("%1$s (%2$d unit)","%1$s (%2$d units)",o,"woocommerce"),t,o)})},e.key)}))});o(2793);const hc=({packageId:e,className:t="",noResultsMessage:o,renderOption:r,packageData:n,collapsible:i,showItems:a,highlightChecked:l=!1})=>{const{selectShippingRate:p,isSelectingRate:m,shippingRates:u}=qe(),h=u?.length||1,[g,_]=(0,d.useState)(0),k=h>1||g>1;(0,d.useEffect)((()=>{const e=()=>{_(document.querySelectorAll(".wc-block-components-shipping-rates-control__package").length)};e();const t=new MutationObserver(e);return t.observe(document.body,{childList:!0,subtree:!0}),()=>{t.disconnect()}}),[]);const b=null!=a?a:k,w=null!=i?i:k,{selectedOptionNumber:y,selectedOption:x}=(0,d.useMemo)((()=>({selectedOptionNumber:n?.shipping_rates?.findIndex((e=>e?.selected)),selectedOption:n?.shipping_rates?.find((e=>e?.selected))})),[n?.shipping_rates]),f=w||b?(0,c.jsxs)("div",{className:"wc-block-components-shipping-rates-control__package-header",children:[(0,c.jsx)("div",{className:"wc-block-components-shipping-rates-control__package-title",dangerouslySetInnerHTML:{__html:zs(n.name)}}),w&&(0,c.jsx)("div",{className:"wc-block-components-totals-shipping__via",children:(0,_e.decodeEntities)(x?.name)}),b&&(0,c.jsx)(uc,{packageData:n})]}):null,v=(0,d.useCallback)((t=>{p(t,e)}),[e,p]),S={className:t,noResultsMessage:o,rates:n.shipping_rates,onSelectRate:v,selectedRate:n.shipping_rates.find((e=>e.selected)),renderOption:r,disabled:m,highlightChecked:l};return w?(0,c.jsx)(Vt.Panel,{className:(0,s.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":m}),initialOpen:!1,title:f,children:(0,c.jsx)(mc,{...S})}):(0,c.jsxs)("div",{className:(0,s.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":m,"wc-block-components-shipping-rates-control__package--first-selected":!m&&0===y,"wc-block-components-shipping-rates-control__package--last-selected":!m&&y===n?.shipping_rates?.length-1}),children:[f,(0,c.jsx)(mc,{...S})]})},gc=({packages:e,showItems:t,collapsible:o,noResultsMessage:s,renderOption:r,context:n=""})=>e.length?(0,c.jsx)(c.Fragment,{children:e.map((({package_id:e,...i})=>(0,c.jsx)(hc,{highlightChecked:"woocommerce/cart"!==n,packageId:e,packageData:i,collapsible:o,showItems:t,noResultsMessage:s,renderOption:r},e)))}):null,_c=({shippingRates:e,isLoadingRates:t,className:o,collapsible:s,showItems:r,noResultsMessage:n=(0,c.jsx)(c.Fragment,{}),renderOption:i,context:a})=>{const l=(e=>e.reduce((function(e,t){return e+t.shipping_rates.length}),0))(e),p=Me(e),m=Je(l),u=Je(p);(0,d.useEffect)((()=>{var e,o;t||m===l&&u===p||(o=l,1===(e=p)?(0,nr.speak)((0,f.sprintf)(/* translators: %d number of shipping options found. */ /* translators: %d number of shipping options found. */
(0,f._n)("%d shipping option was found.","%d shipping options were found.",o,"woocommerce"),o)):(0,nr.speak)((0,f.sprintf)(/* translators: %d number of shipping packages packages. */ /* translators: %d number of shipping packages packages. */
(0,f._n)("Shipping option searched for %d package.","Shipping options searched for %d packages.",e,"woocommerce"),e)+" "+(0,f.sprintf)(/* translators: %d number of shipping options available. */ /* translators: %d number of shipping options available. */
(0,f._n)("%d shipping option was found","%d shipping options were found",o,"woocommerce"),o)))}),[t,l,p,m,u]);const{extensions:h,receiveCart:g,..._}=Te(),k={className:o,collapsible:s,showItems:r,noResultsMessage:n,renderOption:i,extensions:h,cart:_,components:{ShippingRatesControlPackage:hc},context:a},{isEditor:w}=b(),{hasSelectedLocalPickup:y,selectedRates:x}=qe(),v=(0,pe.isObject)(x)?Object.values(x):[],S=v.every((e=>e===v[0]));return(0,c.jsxs)(bs,{isLoading:t,screenReaderLabel:(0,f.__)("Loading shipping rates…","woocommerce"),showSpinner:!0,children:[y&&"woocommerce/cart"===a&&e.length>1&&!S&&!w&&(0,c.jsx)(ir,{className:"wc-block-components-notice",isDismissible:!1,status:"warning",children:(0,f.__)("Multiple shipments must have the same pickup location","woocommerce")}),(0,c.jsx)(Ct.ExperimentalOrderShippingPackages.Slot,{...k}),(0,c.jsx)(Ct.ExperimentalOrderShippingPackages,{children:(0,c.jsx)(gc,{packages:e,noResultsMessage:n,renderOption:i})})]})},kc=e=>{const t=(0,x.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),o=0===t?(0,c.jsx)("span",{className:"wc-block-checkout__shipping-option--free",children:(0,f.__)("Free","woocommerce")}):(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:(0,ls.getCurrencyFromPriceResponse)(e),value:t});return{label:(0,_e.decodeEntities)(e.name),value:e.rate_id,description:(0,_e.decodeEntities)(e.delivery_time),secondaryLabel:o,secondaryDescription:(0,_e.decodeEntities)(e.description)}},bc=()=>(0,c.jsx)("p",{role:"status","aria-live":"polite",className:"wc-block-components-shipping-rates-control__no-shipping-address-message",children:(0,f.__)("Enter a shipping address to view shipping options.","woocommerce")}),wc=({noShippingPlaceholder:e=null})=>{const{isEditor:t}=b(),{shippingRates:o,needsShipping:s,isLoadingRates:r,hasCalculatedShipping:n,isCollectable:i}=qe(),{shippingAddress:a}=pt(),l=(0,d.useMemo)((()=>i?o.map((e=>({...e,shipping_rates:e.shipping_rates.filter((e=>!Oe(e.method_id)))}))):o),[o,i]);if(!s)return null;const p=Me(o);if(!n&&!p)return(0,c.jsx)(bc,{});const m=fe(a);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.StoreNoticesContainer,{context:me.SHIPPING_METHODS}),t&&!p?e:(0,c.jsx)(_c,{noResultsMessage:(0,c.jsx)(c.Fragment,{children:m?(0,c.jsx)(ir,{isDismissible:!1,className:"wc-block-components-shipping-rates-control__no-results-notice",status:"warning",children:(0,f.__)("No shipping options are available for this address. Please verify the address is correct or try a different address.","woocommerce")}):(0,c.jsx)(bc,{})}),renderOption:kc,collapsible:!1,shippingRates:l,isLoadingRates:r,context:"woocommerce/checkout"})]})};o(1393);const yc={...No({defaultTitle:(0,f.__)("Shipping options","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};o(7693),(0,a.registerBlockType)("woocommerce/checkout-shipping-methods-block",{icon:{src:(0,c.jsx)(i.A,{icon:tc.A,className:"wc-block-editor-components-block-icon"})},attributes:yc,edit:({attributes:e,setAttributes:t})=>{const o=(0,x.getSetting)("globalShippingMethods"),r=(0,x.getSetting)("activeShippingZones"),{showShippingMethods:n}=mt();return n?(0,c.jsxs)($t,{attributes:e,setAttributes:t,className:(0,s.A)("wc-block-checkout__shipping-option",e?.className),children:[(0,c.jsxs)(l.InspectorControls,{children:[(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Shipping Calculations","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("Options that control shipping can be managed in your store settings.","woocommerce")}),(0,c.jsx)(Dt.ExternalLink,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=options`,children:(0,f.__)("Manage shipping options","woocommerce")})," "]}),o.length>0&&(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Methods","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("The following shipping integrations are active on your store.","woocommerce")}),o.map((e=>(0,c.jsx)(Gs,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=${e.id}`,title:e.title,description:e.description},e.id))),(0,c.jsx)(Dt.ExternalLink,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`,children:(0,f.__)("Manage shipping methods","woocommerce")})]}),r.length&&(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Shipping Zones","woocommerce"),children:[(0,c.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,f.__)("Shipping Zones can be made managed in your store settings.","woocommerce")}),r.map((e=>(0,c.jsx)(Gs,{href:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&zone_id=${e.id}`,title:e.title,description:e.description},e.id)))]})]}),(0,c.jsx)(Kt,{children:(0,c.jsx)(wc,{noShippingPlaceholder:(0,c.jsx)(Lr,{icon:tc.A,label:(0,f.__)("Shipping options","woocommerce"),description:(0,f.__)("Your store does not have any Shipping Options configured. Once you have added your Shipping Options they will appear here.","woocommerce"),buttonLabel:(0,f.__)("Configure Shipping Options","woocommerce"),buttonHref:`${x.ADMIN_URL}admin.php?page=wc-settings&tab=shipping`})})}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.SHIPPING_METHODS})]}):null},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});const xc=({title:e,setSelectedOption:t,selectedOption:o,pickupLocations:s,onSelectRate:r,renderPickupLocation:n,packageCount:i})=>{const{shippingRates:a}=qe(),l=(a?.length||1)>1||document.querySelectorAll(".wc-block-components-local-pickup-select .wc-block-components-radio-control").length>1;return(0,c.jsxs)("div",{className:"wc-block-components-local-pickup-select",children:[!(!l||!e)&&(0,c.jsx)("div",{children:e}),(0,c.jsx)(Vt.RadioControl,{onChange:e=>{t(e),r(e)},highlightChecked:!0,selected:o,options:s.map((e=>n(e,i)))})]})};function fc(e){let t,o,s,r=[];for(let c=0;c<e.length;c++)t=e.substring(c),o=t.match(/^&[a-z0-9#]+;/),o?(s=o[0],r.push(s),c+=s.length-1):r.push(e[c]);return r}const vc=(e,t,o="...")=>{const s=function(e,t){const o=(t=t||{}).limit||100,s=void 0===t.preserveTags||t.preserveTags,r=void 0!==t.wordBreak&&t.wordBreak,c=t.suffix||"...",n=t.moreLink||"",i=t.moreText||"»",a=t.preserveWhiteSpace||!1,l=e.replace(/</g,"\n<").replace(/>/g,">\n").replace(/\n\n/g,"\n").replace(/^\n/g,"").replace(/\n$/g,"").split("\n");let d,p,m,u,h,g,_=0,k=[],b=!1;for(let e=0;e<l.length;e++){if(d=l[e],u=a?d:d.replace(/[ ]+/g," "),!d.length)continue;const t=fc(u);if("<"!==d[0])if(_>=o)d="";else if(_+t.length>=o){if(p=o-_," "===t[p-1])for(;p&&(p-=1," "===t[p-1]););else m=t.slice(p).indexOf(" "),r||(-1!==m?p+=m:p=d.length);if(d=t.slice(0,p).join("")+c,n){const e=document.createElement("a");e.href=n,e.style.display="inline",e.textContent=i,d+=e.outerHTML}_=o,b=!0}else _+=t.length;else if(s){if(_>=o)if(h=d.match(/[a-zA-Z]+/),g=h?h[0]:"",g)if("</"!==d.substring(0,2))k.push(g),d="";else{for(;k[k.length-1]!==g&&k.length;)k.pop();k.length&&(d=""),k.pop()}else d=""}else d="";l[e]=d}return{html:l.join("\n").replace(/\n/g,""),more:b}}(e,{suffix:o,limit:t});return s.html},Sc=(e,t,o)=>(t<=o?e.start=e.middle+1:e.end=e.middle-1,e),jc=(e,t,o,s)=>{const r=((e,t,o)=>{let s={start:0,middle:0,end:e.length};for(;s.start<=s.end;)s.middle=Math.floor((s.start+s.end)/2),t.innerHTML=vc(e,s.middle),s=Sc(s,t.clientHeight,o);return s.middle})(e,t,o);return vc(e,r-s.length,s)},Cc={className:"read-more-content",ellipsis:"&hellip;",lessText:(0,f.__)("Read less","woocommerce"),maxLines:3,moreText:(0,f.__)("Read more","woocommerce")};class Pc extends d.Component{static defaultProps=Cc;constructor(e){super(e),this.state={isExpanded:!1,clampEnabled:null,content:e.children,summary:"."},this.reviewContent=(0,d.createRef)(),this.reviewSummary=(0,d.createRef)(),this.getButton=this.getButton.bind(this),this.onClick=this.onClick.bind(this)}componentDidMount(){this.setSummary()}componentDidUpdate(e){e.maxLines===this.props.maxLines&&e.children===this.props.children||this.setState({clampEnabled:null,summary:"."},this.setSummary)}setSummary(){if(this.props.children){const{maxLines:e,ellipsis:t}=this.props;if(!this.reviewSummary.current||!this.reviewContent.current)return;const o=(this.reviewSummary.current.clientHeight+1)*e+1,s=this.reviewContent.current.clientHeight+1>o;this.setState({clampEnabled:s}),s&&this.setState({summary:jc(this.reviewContent.current.innerHTML,this.reviewSummary.current,o,t)})}}getButton(){const{isExpanded:e}=this.state,{className:t,lessText:o,moreText:s}=this.props,r=e?o:s;if(r)return(0,c.jsx)("a",{href:"#more",className:t+"__read_more",onClick:this.onClick,"aria-expanded":!e,role:"button",children:r})}onClick(e){e.preventDefault();const{isExpanded:t}=this.state;this.setState({isExpanded:!t})}render(){const{className:e}=this.props,{content:t,summary:o,clampEnabled:s,isExpanded:r}=this.state;return t?!1===s?(0,c.jsx)("div",{className:e,children:(0,c.jsx)("div",{ref:this.reviewContent,children:t})}):(0,c.jsxs)("div",{className:e,children:[(!r||null===s)&&(0,c.jsx)("div",{ref:this.reviewSummary,"aria-hidden":r,dangerouslySetInnerHTML:{__html:o}}),(r||null===s)&&(0,c.jsx)("div",{ref:this.reviewContent,"aria-hidden":!r,children:t}),this.getButton()]}):null}}const Ec=Pc,Nc=(e,t)=>{const o=(0,x.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):e.price,s=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_location"===e.key));return t?t.value:""}return""})(e),r=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_address"===e.key));return t?t.value:""}return""})(e),n=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_details"===e.key));return t?t.value:""}return""})(e);let a=(0,c.jsx)("em",{children:(0,f.__)("free","woocommerce")});return parseInt(o,10)>0&&(a=1===t?(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:(0,ls.getCurrencyFromPriceResponse)(e),value:o}):(0,d.createInterpolateElement)(/* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */ /* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */
(0,f._n)("<price/> x <packageCount/> package","<price/> x <packageCount/> packages",t,"woocommerce"),{price:(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:(0,ls.getCurrencyFromPriceResponse)(e),value:o}),packageCount:(0,c.jsx)(c.Fragment,{children:t})})),{value:e.rate_id,label:s?(0,_e.decodeEntities)(s):(0,_e.decodeEntities)(e.name),secondaryLabel:a,description:r?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(i.A,{icon:Lt.A,className:"wc-block-editor-components-block-icon"}),(0,_e.decodeEntities)(r)]}):void 0,secondaryDescription:n?(0,c.jsx)(Ec,{maxLines:2,children:(0,_e.decodeEntities)(n)}):void 0}},Ac=()=>{const{shippingRates:e,selectShippingRate:t}=qe(),o=(0,d.useMemo)((()=>(e[0]?.shipping_rates||[]).filter(De)),[e]),[s,r]=(0,d.useState)((()=>o.find((e=>e.selected))?.rate_id||"")),n=(0,d.useCallback)((e=>{t(e)}),[t]),{extensions:i,receiveCart:a,...l}=Te(),p={extensions:i,cart:l,components:{ShippingRatesControlPackage:hc,LocalPickupSelect:xc},renderPickupLocation:Nc};(0,d.useEffect)((()=>{!s&&o[0]&&s!==o[0].rate_id&&(r(o[0].rate_id),n(o[0].rate_id))}),[o,s]);const m=Me(e);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Ct.ExperimentalOrderLocalPickupPackages.Slot,{...p}),(0,c.jsx)(Ct.ExperimentalOrderLocalPickupPackages,{children:(0,c.jsx)(xc,{title:e[0].name,setSelectedOption:r,onSelectRate:n,selectedOption:s,renderPickupLocation:Nc,pickupLocations:o,packageCount:m})})]})},Ic={...No({defaultTitle:(0,f.__)("Pickup locations","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};o(5991),(0,a.registerBlockType)("woocommerce/checkout-pickup-options-block",{icon:{src:(0,c.jsx)(i.A,{icon:oc.A,className:"wc-block-editor-components-block-icon"})},attributes:Ic,edit:({attributes:e,setAttributes:t})=>{const{prefersCollection:o}=(0,_.useSelect)((e=>({prefersCollection:e(Y.checkoutStore).prefersCollection()}))),{className:r}=e;return o&&A?(0,c.jsxs)($t,{attributes:e,setAttributes:t,className:(0,s.A)("wc-block-checkout__shipping-method",r),children:[(0,c.jsx)(Dt.Disabled,{children:(0,c.jsx)(Ac,{})}),(0,c.jsx)(Ht,{block:Ct.innerBlockAreas.PICKUP_LOCATION})]}):null},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(qt,{})})});const Rc=({className:e=""})=>{const{cartTotals:t}=Te(),o=(0,ls.getCurrencyFromPriceResponse)(t);return(0,c.jsx)(Vt.TotalsWrapper,{className:e,children:(0,c.jsx)(Vt.Subtotal,{currency:o,values:t})})};(0,a.registerBlockType)("woocommerce/checkout-order-summary-subtotal-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,l.useBlockProps)();return(0,c.jsx)("div",{...o,children:(0,c.jsx)(Rc,{className:t})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});const Tc=({className:e=""})=>{const{cartFees:t,cartTotals:o}=Te(),s=(0,ls.getCurrencyFromPriceResponse)(o);return(0,c.jsx)(Vt.TotalsWrapper,{className:e,children:(0,c.jsx)(Vt.TotalsFees,{currency:s,cartFees:t})})};(0,a.registerBlockType)("woocommerce/checkout-order-summary-fee-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,l.useBlockProps)();return(0,c.jsx)("div",{...o,children:(0,c.jsx)(Tc,{className:t})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});const Mc=()=>{const{extensions:e,receiveCart:t,...o}=Te(),s={extensions:e,cart:o,context:"woocommerce/checkout"};return(0,c.jsx)(Ct.ExperimentalDiscountsMeta.Slot,{...s})},Bc=({className:e=""})=>{const{cartTotals:t,cartCoupons:o}=Te(),{removeCoupon:s,isRemovingCoupon:r}=br("wc/checkout"),n=(0,ls.getCurrencyFromPriceResponse)(t);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Vt.TotalsWrapper,{className:e,children:(0,c.jsx)(xs,{cartCoupons:o,currency:n,isRemovingCoupon:r,removeCoupon:s,values:t})}),(0,c.jsx)(Mc,{})]})};(0,a.registerBlockType)("woocommerce/checkout-order-summary-discount-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,l.useBlockProps)();return(0,c.jsx)("div",{...o,children:(0,c.jsx)(Bc,{className:t})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});const Dc=({className:e=""})=>{const{cartNeedsShipping:t,shippingRates:o,shippingAddress:s}=Te(),r=(0,_.useSelect)((e=>e(Y.checkoutStore).prefersCollection()));if(!t)return null;const n=(e=>!!Fe(e)&&e.every((e=>e.shipping_rates.every((e=>!e.selected||De(e))))))(((e,t)=>e.map((e=>({...e,shipping_rates:e.shipping_rates.filter((e=>{const o=Oe(e.method_id);return t?o:!o}))}))))(o,null!=r&&r)),i=fe(s,["state","country","postcode","city"]);return(0,c.jsx)(Ct.TotalsWrapper,{className:e,children:(0,c.jsx)(Is,{label:n?(0,f.__)("Pickup","woocommerce"):(0,f.__)("Delivery","woocommerce"),placeholder:(0,c.jsx)("span",{className:"wc-block-components-shipping-placeholder__value",children:i?(0,f.__)("No available delivery option","woocommerce"):(0,f.__)("Enter address to calculate","woocommerce")})})})};(0,a.registerBlockType)("woocommerce/checkout-order-summary-shipping-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,l.useBlockProps)();return(0,c.jsx)("div",{...o,children:(0,c.jsx)(Kt,{children:(0,c.jsx)(Dc,{className:t})})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});var Oc=o(1686);const Fc=({className:e=""})=>{const t=(0,x.getSetting)("couponsEnabled",!0),{applyCoupon:o,isApplyingCoupon:s}=br("wc/checkout");return t?(0,c.jsx)(Vt.TotalsWrapper,{className:e,children:(0,c.jsx)(ws,{onSubmit:o,isLoading:s,instanceId:"coupon"})}):null};(0,a.registerBlockType)("woocommerce/checkout-order-summary-coupon-form-block",{icon:{src:(0,c.jsx)(i.A,{icon:Oc.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,o=(0,l.useBlockProps)();return(0,c.jsx)("div",{...o,children:(0,c.jsx)(Kt,{children:(0,c.jsx)(Fc,{className:t})})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});const Lc=({className:e,showRateAfterTaxName:t})=>{const{cartTotals:o}=Te();if((0,x.getSetting)("displayCartPricesIncludingTax",!1)||parseInt(o.total_tax,10)<=0)return null;const s=(0,ls.getCurrencyFromPriceResponse)(o);return(0,c.jsx)(Vt.TotalsWrapper,{className:e,children:(0,c.jsx)(Vt.TotalsTaxes,{showRateAfterTaxName:t,currency:s,values:o})})},Vc={showRateAfterTaxName:{type:"boolean",default:(0,x.getSetting)("displayCartPricesIncludingTax",!1)},lock:{type:"object",default:{remove:!0,move:!0}}};(0,a.registerBlockType)("woocommerce/checkout-order-summary-taxes-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},attributes:Vc,edit:({attributes:e,setAttributes:t})=>{const{className:o,showRateAfterTaxName:s}=e,r=(0,l.useBlockProps)(),n=(0,x.getSetting)("taxesEnabled"),i=(0,x.getSetting)("displayItemizedTaxes",!1),a=(0,x.getSetting)("displayCartPricesIncludingTax",!1);return(0,c.jsxs)("div",{...r,children:[(0,c.jsx)(l.InspectorControls,{children:n&&i&&!a&&(0,c.jsx)(Dt.PanelBody,{title:(0,f.__)("Taxes","woocommerce"),children:(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Show rate after tax name","woocommerce"),help:(0,f.__)("Show the percentage rate alongside each tax line in the summary.","woocommerce"),checked:s,onChange:()=>t({showRateAfterTaxName:!s})})})}),(0,c.jsx)(Lc,{className:o,showRateAfterTaxName:s})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})});const Uc=(0,c.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,c.jsx)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,c.jsx)("path",{d:"M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45zM6.16 6h12.15l-2.76 5H8.53L6.16 6zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"})]});o(8501);const $c=({currency:e,maxPrice:t,minPrice:o,priceClassName:r,priceStyle:n={}})=>(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("span",{className:"screen-reader-text",children:(0,f.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,f.__)("Price between %1$s and %2$s","woocommerce"),(0,ls.formatPrice)(o),(0,ls.formatPrice)(t))}),(0,c.jsxs)("span",{"aria-hidden":!0,children:[(0,c.jsx)(Vt.FormattedMonetaryAmount,{className:(0,s.A)("wc-block-components-product-price__value",r),currency:e,value:o,style:n})," — ",(0,c.jsx)(Vt.FormattedMonetaryAmount,{className:(0,s.A)("wc-block-components-product-price__value",r),currency:e,value:t,style:n})]})]}),Hc=({currency:e,regularPriceClassName:t,regularPriceStyle:o,regularPrice:r,priceClassName:n,priceStyle:i,price:a})=>(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("span",{className:"screen-reader-text",children:(0,f.__)("Previous price:","woocommerce")}),(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,c.jsx)("del",{className:(0,s.A)("wc-block-components-product-price__regular",t),style:o,children:e}),value:r}),(0,c.jsx)("span",{className:"screen-reader-text",children:(0,f.__)("Discounted price:","woocommerce")}),(0,c.jsx)(Vt.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,c.jsx)("ins",{className:(0,s.A)("wc-block-components-product-price__value","is-discounted",n),style:i,children:e}),value:a})]}),qc=({align:e,className:t,currency:o,format:r="<price/>",maxPrice:n,minPrice:i,price:a,priceClassName:l,priceStyle:p,regularPrice:m,regularPriceClassName:u,regularPriceStyle:h,style:g})=>{const _=(0,s.A)(t,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});r.includes("<price/>")||(r="<price/>",console.error("Price formats need to include the `<price/>` tag."));const k=m&&a&&a<m;let b=(0,c.jsx)("span",{className:(0,s.A)("wc-block-components-product-price__value",l)});return k?b=(0,c.jsx)(Hc,{currency:o,price:a,priceClassName:l,priceStyle:p,regularPrice:m,regularPriceClassName:u,regularPriceStyle:h}):void 0!==i&&void 0!==n?b=(0,c.jsx)($c,{currency:o,maxPrice:n,minPrice:i,priceClassName:l,priceStyle:p}):a&&(b=(0,c.jsx)(Vt.FormattedMonetaryAmount,{className:(0,s.A)("wc-block-components-product-price__value",l),currency:o,value:a,style:p})),(0,c.jsx)("span",{className:_,style:g,children:(0,d.createInterpolateElement)(r,{price:b})})};o(959);const zc=({className:e="",disabled:t=!1,name:o,permalink:r="",target:n,rel:i,style:a,onClick:l,disabledTagName:d="span",...p})=>{const m=(0,s.A)("wc-block-components-product-name",e),u=d;if(t){const e=p;return(0,c.jsx)(u,{className:m,...e,dangerouslySetInnerHTML:{__html:o}})}return(0,c.jsx)("a",{className:m,href:r,target:n,...p,dangerouslySetInnerHTML:{__html:o},style:a})};var Wc=o(6513);o(7605);const Yc=({children:e,className:t})=>(0,c.jsx)("div",{className:(0,s.A)("wc-block-components-product-badge",t),children:e}),Gc=()=>(0,c.jsx)(Yc,{className:"wc-block-components-product-backorder-badge",children:(0,f.__)("Available on backorder","woocommerce")}),Kc=({image:e={},fallbackAlt:t=""})=>{const o=e.thumbnail?{src:e.thumbnail,alt:(0,_e.decodeEntities)(e.alt)||t||"Product Image"}:{src:x.PLACEHOLDER_IMG_SRC,alt:""};return(0,c.jsx)("img",{...o,alt:o.alt})},Zc=({lowStockRemaining:e})=>e?(0,c.jsx)(Yc,{className:"wc-block-components-product-low-stock-badge",children:(0,f.sprintf)(/* translators: %d stock amount (number of items in stock for product) */ /* translators: %d stock amount (number of items in stock for product) */
(0,f.__)("%d left in stock","woocommerce"),e)}):null;var Jc=o(7356);o(3692);const Xc=({details:e=[]})=>{if(!Array.isArray(e))return null;if(0===(e=e.filter((e=>!e.hidden))).length)return null;let t="ul",o="li";return 1===e.length&&(t="div",o="div"),(0,c.jsx)(t,{className:"wc-block-components-product-details",children:e.map((e=>{const t=e?.key||e.name||"",s=e?.className||(t?`wc-block-components-product-details__${(0,Jc.c)(t)}`:"");return(0,c.jsxs)(o,{className:s,children:[t&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("span",{className:"wc-block-components-product-details__name",children:[(0,_e.decodeEntities)(t),":"]})," "]}),(0,c.jsx)("span",{className:"wc-block-components-product-details__value",children:(0,_e.decodeEntities)(e.display||e.value)})]},t+(e.display||e.value))}))})},Qc=window.wp.wordcount,en=["a","b","em","i","strong","p","br","ul","ol","li","h1","h2","h3","h4","h5","h6","pre","blockquote","img"],tn=["target","href","rel","name","download","src","class","alt","style"],on=({source:e,maxLength:t=15,countType:o="words",className:s="",style:r={}})=>{const n=(0,d.useMemo)((()=>((e,t=15,o="words")=>{const s=(0,Ks.autop)(e);if((0,Qc.count)(s,o)<=t)return s;const r=(e=>{const t=e.indexOf("</p>");return-1===t?e:e.substr(0,t+4)})(s);return(0,Qc.count)(r,o)<=t?r:"words"===o?Xs(r,t):Qs(r,t,"characters_including_spaces"===o)})(e,t,o)),[e,t,o]);return(0,c.jsx)(d.RawHTML,{style:r,className:s,children:zs(n,{tags:en,attr:tn})})},sn=({className:e,shortDescription:t="",fullDescription:o=""})=>{const s=t||o;return s?(0,c.jsx)(on,{className:e,source:s,maxLength:15,countType:v.wordCountType||"words"}):null};o(8879);const rn=({shortDescription:e="",fullDescription:t="",itemData:o=[],variation:s=[]})=>(0,c.jsxs)("div",{className:"wc-block-components-product-metadata",children:[(0,c.jsx)(sn,{className:"wc-block-components-product-metadata__description",shortDescription:e,fullDescription:t}),(0,c.jsx)(Xc,{details:o}),(0,c.jsx)(Xc,{details:s.map((({attribute:e="",value:t})=>({key:e,value:t})))})]}),cn=({cartItem:e,disableProductDescriptions:t})=>{const{images:o,low_stock_remaining:r,show_backorder_badge:n,name:i,permalink:a,prices:l,quantity:p,short_description:m,description:u,item_data:h,variation:g,totals:_,extensions:k}=e,{receiveCart:b,...w}=Te(),y=(0,d.useMemo)((()=>({context:"summary",cartItem:e,cart:w})),[e,w]),v=(0,ls.getCurrencyFromPriceResponse)(l),S=(0,Ct.applyCheckoutFilter)({filterName:"itemName",defaultValue:i,extensions:k,arg:y}),j=(0,Wc.A)({amount:parseInt(l.raw_prices.regular_price,10),precision:(0,pe.isString)(l.raw_prices.precision)?parseInt(l.raw_prices.precision,10):l.raw_prices.precision}).convertPrecision(v.minorUnit).getAmount(),C=(0,Wc.A)({amount:parseInt(l.raw_prices.price,10),precision:(0,pe.isString)(l.raw_prices.precision)?parseInt(l.raw_prices.precision,10):l.raw_prices.precision}).convertPrecision(v.minorUnit).getAmount(),P=(0,ls.getCurrencyFromPriceResponse)(_);let E=parseInt(_.line_subtotal,10);(0,x.getSetting)("displayCartPricesIncludingTax",!1)&&(E+=parseInt(_.line_subtotal_tax,10));const N=(0,Wc.A)({amount:E,precision:P.minorUnit}).getAmount(),A=(0,Ct.applyCheckoutFilter)({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:k,arg:y,validation:Ct.productPriceValidation}),I=(0,Ct.applyCheckoutFilter)({filterName:"cartItemPrice",defaultValue:"<price/>",extensions:k,arg:y,validation:Ct.productPriceValidation}),R=(0,Ct.applyCheckoutFilter)({filterName:"cartItemClass",defaultValue:"",extensions:k,arg:y}),T=t?{itemData:h,variation:g}:{itemData:h,variation:g,shortDescription:m,fullDescription:u};return(0,c.jsxs)("div",{className:(0,s.A)("wc-block-components-order-summary-item",R),children:[(0,c.jsxs)("div",{className:"wc-block-components-order-summary-item__image",children:[(0,c.jsx)("div",{className:"wc-block-components-order-summary-item__quantity",children:(0,c.jsx)(Vt.Label,{label:p.toString(),screenReaderLabel:(0,f.sprintf)(/* translators: %d number of products of the same type in the cart */ /* translators: %d number of products of the same type in the cart */
(0,f._n)("%d item","%d items",p,"woocommerce"),p)})}),(0,c.jsx)(Kc,{image:o.length?o[0]:{},fallbackAlt:S})]}),(0,c.jsxs)("div",{className:"wc-block-components-order-summary-item__description",children:[(0,c.jsx)(zc,{disabled:!0,name:S,permalink:a,disabledTagName:"h3"}),(0,c.jsx)(qc,{currency:v,price:C,regularPrice:j,className:"wc-block-components-order-summary-item__individual-prices",priceClassName:"wc-block-components-order-summary-item__individual-price",regularPriceClassName:"wc-block-components-order-summary-item__regular-individual-price",format:A}),n?(0,c.jsx)(Gc,{}):!!r&&(0,c.jsx)(Zc,{lowStockRemaining:r}),(0,c.jsx)(rn,{...T})]}),(0,c.jsx)("span",{className:"screen-reader-text",children:(0,f.sprintf)(/* translators: %1$d is the number of items, %2$s is the item name and %3$s is the total price including the currency symbol. */ /* translators: %1$d is the number of items, %2$s is the item name and %3$s is the total price including the currency symbol. */
(0,f._n)("Total price for %1$d %2$s item: %3$s","Total price for %1$d %2$s items: %3$s",p,"woocommerce"),p,S,(0,ls.formatPrice)(N,P))}),(0,c.jsx)("div",{className:"wc-block-components-order-summary-item__total-price","aria-hidden":"true",children:(0,c.jsx)(qc,{currency:P,format:I,price:N})})]})};o(6161);const nn=({cartItems:e=[],disableProductDescriptions:t=!1})=>{const{isLarge:o,hasContainerWidth:r}=u();return r?(0,c.jsx)("div",{className:(0,s.A)("wc-block-components-order-summary",{"is-large":o}),children:(0,c.jsx)("div",{className:"wc-block-components-order-summary__content",children:e.map((e=>(0,c.jsx)(cn,{disableProductDescriptions:t,cartItem:e},e.key)))})}):null},an=({className:e="",disableProductDescriptions:t=!1})=>{const{cartItems:o}=Te();return(0,c.jsx)(Vt.TotalsWrapper,{className:e,children:(0,c.jsx)(nn,{cartItems:o,disableProductDescriptions:t})})};(0,a.registerBlockType)("woocommerce/checkout-order-summary-cart-items-block",{icon:{src:(0,c.jsx)(i.A,{icon:Uc,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{className:o="",disableProductDescriptions:s=!1}=e,r=(0,l.useBlockProps)();return(0,c.jsxs)("div",{...r,children:[H()&&(0,c.jsx)(l.InspectorControls,{children:(0,c.jsx)(Dt.PanelBody,{title:(0,f.__)("Settings","woocommerce"),children:(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Disable product descriptions","woocommerce"),help:(0,f.__)("Disable display of product descriptions.","woocommerce"),checked:s,onChange:()=>t({disableProductDescriptions:!s})})})}),(0,c.jsx)(an,{disableProductDescriptions:s,className:o})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save()})}),o(2739),(0,a.registerBlockType)("woocommerce/checkout-order-summary-totals-block",{icon:{src:(0,c.jsx)(i.A,{icon:ks,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,l.useBlockProps)(),o=Mt(Ct.innerBlockAreas.CHECKOUT_ORDER_SUMMARY_TOTALS),s=[["woocommerce/checkout-order-summary-subtotal-block",{},[]],["woocommerce/checkout-order-summary-fee-block",{},[]],["woocommerce/checkout-order-summary-discount-block",{},[]],["woocommerce/checkout-order-summary-shipping-block",{},[]],["woocommerce/checkout-order-summary-taxes-block",{},[]]];return Bt({clientId:e,registeredBlocks:o,defaultTemplate:s}),(0,c.jsx)("div",{...t,children:(0,c.jsx)(l.InnerBlocks,{allowedBlocks:o,template:s})})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save(),children:(0,c.jsx)(l.InnerBlocks.Content,{})})}),o(4259);var ln=o(8558);const dn=(e,t=!0)=>{t?window.document.body.classList.add(e):window.document.body.classList.remove(e)},pn=({attributes:e,setAttributes:t})=>{const{hasDarkControls:o,showFormStepNumbers:s}=e;return(0,c.jsx)(l.InspectorControls,{children:(0,c.jsxs)(Dt.PanelBody,{title:(0,f.__)("Style","woocommerce"),children:[(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Show form step numbers","woocommerce"),checked:s,onChange:()=>t({showFormStepNumbers:!s})}),(0,c.jsx)(Dt.ToggleControl,{label:(0,f.__)("Dark mode inputs","woocommerce"),help:(0,f.__)("Inputs styled specifically for use on dark background colors.","woocommerce"),checked:o,onChange:()=>t({hasDarkControls:!o})})]})})},mn=window.wp.editor;function un({block:e}){const t="checkout"===e?j:E,o="checkout"===e?"woocommerce_checkout_page_id":"woocommerce_cart_page_id",{saveEntityRecord:s}=(0,_.useDispatch)(Pt.store),{editPost:r,savePost:n}=(0,_.useDispatch)(mn.store),{slug:i,postPublished:a,currentPostId:l}=(0,_.useSelect)((o=>{const{getEntityRecord:s}=o(Pt.store),{isCurrentPostPublished:r,getCurrentPostId:c}=o(mn.store);return{slug:s("postType","page",t)?.slug||e,postPublished:r(),currentPostId:c()}}),[]),[p,m]=(0,d.useState)("pristine"),u=(0,d.useCallback)((()=>{m("updating"),Promise.resolve().then((()=>it()({path:`/wc/v3/settings/advanced/${o}`,method:"GET"}))).catch((e=>{"rest_setting_setting_invalid"===e.code&&m("error")})).then((()=>{if(!a)return r({status:"publish"}),n()})).then((()=>it()({path:`/wc/v3/settings/advanced/${o}`,method:"POST",data:{value:l.toString()}}))).then((()=>{if(0!==t)return s("postType","page",{id:t,slug:`${i}-2`})})).then((()=>r({slug:i}))).then((()=>n())).then((()=>m("updated")))}),[a,r,n,o,l,t,s,i]);let h;return h="checkout"===e?(0,d.createInterpolateElement)((0,f.__)("If you would like to use this block as your default checkout, <a>update your page settings</a>.","woocommerce"),{a:(0,c.jsx)("a",{href:"#",onClick:u,children:(0,f.__)("update your page settings","woocommerce")})}):(0,d.createInterpolateElement)((0,f.__)("If you would like to use this block as your default cart, <a>update your page settings</a>.","woocommerce"),{a:(0,c.jsx)("a",{href:"#",onClick:u,children:(0,f.__)("update your page settings","woocommerce")})}),"string"==typeof pagenow&&"site-editor"===pagenow||l===t||"dismissed"===p?null:(0,c.jsx)(Dt.Notice,{className:"wc-default-page-notice",status:"updated"===p?"success":"info",onRemove:()=>m("dismissed"),spokenMessage:"updated"===p?(0,f.__)("Page settings updated","woocommerce"):h,children:"updated"===p?(0,f.__)("Page settings updated","woocommerce"):(0,c.jsx)(c.Fragment,{children:(0,c.jsx)("p",{children:h})})})}o(3650);const hn=[],gn=e=>{const[t,o,s]=(()=>{const e={};(0,x.getSetting)("incompatibleExtensions")&&(0,x.getSetting)("incompatibleExtensions").forEach((t=>{e[t.id]=t.title}));const t=Object.keys(e),o=t.length;return[e,t,o]})(),[r,c,n]=(()=>{const{incompatiblePaymentMethods:e}=(0,_.useSelect)((e=>{const{getIncompatiblePaymentMethods:t}=e(Y.paymentStore);return{incompatiblePaymentMethods:t()}}),[]),t=Object.keys(e);return[e,t,t.length]})(),i={...t,...r},a=[...o,...c],l=s+n,[p,m]=((e,t)=>{const[o,s]=(0,d.useState)((()=>{const o=window.localStorage.getItem(e);if(o)try{return JSON.parse(o)}catch{console.error(`Value for key '${e}' could not be retrieved from localStorage because it can't be parsed.`)}return t}));return(0,d.useEffect)((()=>{try{window.localStorage.setItem(e,JSON.stringify(o))}catch{console.error(`Value for key '${e}' could not be saved in localStorage because it can't be converted into a string.`)}}),[e,o]),[o,s]})("wc-blocks_dismissed_incompatible_extensions_notices",hn),[u,h]=(0,d.useState)(!1),g=p.some((t=>{return Object.keys(t).includes(e)&&(o=t[e],s=a,o.length===s.length&&new Set([...o,...s]).size===o.length);var o,s})),k=0===l||g;return(0,d.useEffect)((()=>{h(!k),k||g||m((t=>t.reduce(((t,o)=>(Object.keys(o).includes(e)||t.push(o),t)),[])))}),[k,g,m,e]),[u,()=>{const t=new Set(p);t.add({[e]:a}),m([...t])},(b=i,Object.fromEntries(Object.entries(b).sort((([,e],[,t])=>e.localeCompare(t))))),l];var b};var kn=o(1244),bn=o.n(kn);bn()("wc-admin:tracks:stats");const wn=bn()("wc-admin:tracks");function yn(e,t){if(wn("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!!window._tkq&&!!window.wcTracks&&!!window.wcTracks.isEnabled}),!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)return!1;window.wcTracks.recordEvent(e,t)}const xn=({blocks:e,findCondition:t})=>{for(const o of e){if(t(o))return o;if(o.innerBlocks){const e=xn({blocks:o.innerBlocks,findCondition:t});if(e)return e}}},fn=({blockType:e="woocommerce/cart"})=>"woocommerce/cart"===e?(0,c.jsx)("p",{children:(0,f.__)("If you continue, the cart block will be replaced with the classic experience powered by shortcodes. This means that you may lose customizations that you made to the cart block.","woocommerce")}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("p",{children:(0,f.__)("If you continue, the checkout block will be replaced with the classic experience powered by shortcodes. This means that you may lose:","woocommerce")}),(0,c.jsxs)("ul",{className:"cross-list",children:[(0,c.jsx)("li",{children:(0,f.__)("Customizations and updates to the block","woocommerce")}),(0,c.jsx)("li",{children:(0,f.__)("Additional local pickup options created for the new checkout","woocommerce")})]})]});function vn({block:e,clientId:t,type:o}){const{createInfoNotice:s}=(0,_.useDispatch)(Xe.store),{replaceBlock:r,selectBlock:n}=(0,_.useDispatch)(l.store),[i,p]=(0,d.useState)(!1),m=()=>p(!1),{undo:u}=(0,_.useDispatch)(Pt.store),[,,h,g]=gn(e),k="woocommerce/cart"===e,b=k?(0,f.__)("Switch to classic cart","woocommerce"):(0,f.__)("Switch to classic checkout","woocommerce"),w=k?(0,f.__)("Switched to classic cart.","woocommerce"):(0,f.__)("Switched to classic checkout.","woocommerce"),y=k?"cart":"checkout",x={shortcode:y,notice:"incompatible"===o?"incompatible_notice":"generic_notice",incompatible_extensions_count:g,incompatible_extensions_names:JSON.stringify(h)},{getBlocks:v}=(0,_.useSelect)((e=>({getBlocks:e(l.store).getBlocks})),[]),S=()=>{u(),yn("switch_to_classic_shortcode_undo",x)};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(Dt.Button,{variant:"secondary",onClick:()=>{yn("switch_to_classic_shortcode_click",x),p(!0)},children:b}),i&&(0,c.jsxs)(Dt.Modal,{size:"medium",title:b,onRequestClose:m,className:"wc-blocks-switch-to-classic-shortcode-modal-content",children:[(0,c.jsx)(fn,{blockType:e}),(0,c.jsxs)(Dt.TabbableContainer,{className:"wc-blocks-switch-to-classic-shortcode-modal-actions",children:[(0,c.jsx)(Dt.Button,{variant:"primary",isDestructive:!0,onClick:()=>{r(t,(0,a.createBlock)("woocommerce/classic-shortcode",{shortcode:y})),yn("switch_to_classic_shortcode_confirm",x),(()=>{const e=xn({blocks:v(),findCondition:e=>"woocommerce/classic-shortcode"===e.name});e&&n(e.clientId)})(),s(w,{actions:[{label:(0,f.__)("Undo","woocommerce"),onClick:S}],type:"snackbar"}),m()},children:(0,f.__)("Switch","woocommerce")})," ",(0,c.jsx)(Dt.Button,{variant:"secondary",onClick:()=>{yn("switch_to_classic_shortcode_cancel",x),m()},children:(0,f.__)("Cancel","woocommerce")})]})]})]})}function Sn({block:e,clientId:t}){const[o,s,r,n]=gn(e);if(!o)return null;const a=(0,c.jsx)(c.Fragment,{children:n>1?(0,d.createInterpolateElement)((0,f.__)("Some active extensions do not yet support this block. This may impact the shopper experience. <a>Learn more</a>","woocommerce"),{a:(0,c.jsx)(Dt.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-store-editing/customizing-cart-and-checkout/#incompatible-extensions/"})}):(0,d.createInterpolateElement)((0,f.sprintf)(
// translators: %s is the name of the extension.
// translators: %s is the name of the extension.
(0,f.__)("<strong>%s</strong> does not yet support this block. This may impact the shopper experience. <a>Learn more</a>","woocommerce"),Object.values(r)[0]),{strong:(0,c.jsx)("strong",{}),a:(0,c.jsx)(Dt.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-store-editing/customizing-cart-and-checkout/#incompatible-extensions/"})})}),l=Object.entries(r),p=l.length-2;return(0,c.jsx)(Dt.Notice,{className:"wc-blocks-incompatible-extensions-notice",status:"warning",onRemove:s,spokenMessage:a,children:(0,c.jsxs)("div",{className:"wc-blocks-incompatible-extensions-notice__content",children:[(0,c.jsx)(i.A,{className:"wc-blocks-incompatible-extensions-notice__warning-icon",icon:(0,c.jsx)(Ys,{})}),(0,c.jsxs)("div",{children:[(0,c.jsx)("p",{children:a}),n>1&&(0,c.jsx)("ul",{children:l.slice(0,2).map((([e,t])=>(0,c.jsx)("li",{className:"wc-blocks-incompatible-extensions-notice__element",children:t},e)))}),l.length>2&&(0,c.jsxs)("details",{children:[(0,c.jsxs)("summary",{children:[(0,c.jsx)("span",{children:(0,f.sprintf)(
// translators: %s is the number of incompatible extensions.
// translators: %s is the number of incompatible extensions.
(0,f._n)("%s more incompatibility","%s more incompatibilities",p,"woocommerce"),p)}),(0,c.jsx)(i.A,{icon:Zt.A})]}),(0,c.jsx)("ul",{children:l.slice(2).map((([e,t])=>(0,c.jsx)("li",{className:"wc-blocks-incompatible-extensions-notice__element",children:t},e)))})]}),(0,c.jsx)(vn,{block:e,clientId:t,type:"incompatible"})]})]})})}o(4490),o(6342);var jn=o(3791);o(4268);const Cn=({text:e,title:t=(0,f.__)("Feedback?","woocommerce"),url:o})=>{const[s,r]=(0,d.useState)(!1);return(0,d.useEffect)((()=>{r(!0)}),[]),(0,c.jsx)(c.Fragment,{children:s&&(0,c.jsxs)("div",{className:"wc-block-feedback-prompt",children:[(0,c.jsx)(i.A,{icon:jn.A}),(0,c.jsx)("h2",{className:"wc-block-feedback-prompt__title",children:t}),(0,c.jsx)("p",{className:"wc-block-feedback-prompt__text",children:e}),(0,c.jsxs)("a",{href:o,className:"wc-block-feedback-prompt__link",rel:"noreferrer noopener",target:"_blank",children:[(0,f.__)("Give us your feedback.","woocommerce"),(0,c.jsx)(i.A,{icon:Vs.A,size:16})]})]})})},Pn=()=>(0,c.jsx)(Cn,{text:(0,f.__)("We are currently working on improving our cart and checkout blocks to provide merchants with the tools and customization options they need.","woocommerce"),url:"https://github.com/woocommerce/woocommerce/discussions/new?category=checkout-flow&labels=type%3A+product%20feedback"}),En=(0,p.createHigherOrderComponent)((e=>t=>{const{clientId:o,name:s,isSelected:r}=t,{isCart:n,isCheckout:i,parentId:a}=(0,_.useSelect)((e=>{const{getBlockParentsByBlockName:t,getBlockName:s}=e(l.store),r=t(o,["woocommerce/cart","woocommerce/checkout"]).reduce(((e,t)=>(e[s(t)]=t,e)),{}),c=s(o),n=Object.keys(r).includes("woocommerce/cart"),i=Object.keys(r).includes("woocommerce/checkout"),a="woocommerce/cart"===c||n,d=a?"woocommerce/cart":"woocommerce/checkout";return{isCart:a,isCheckout:"woocommerce/checkout"===c||i,parentId:c===d?o:r[d]}}));return s.startsWith("woocommerce/")&&r&&(n||i)?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)(l.InspectorControls,{children:[(0,c.jsx)(Sn,{block:n?"woocommerce/cart":"woocommerce/checkout",clientId:a}),(0,c.jsx)(un,{block:i?"checkout":"cart"}),(0,c.jsx)(Pn,{})]}),(0,c.jsx)(e,{...t},"edit")]}):(0,c.jsx)(e,{...t},"edit")}),"withSidebarNotices");(0,$e.hasFilter)("editor.BlockEdit","woocommerce/add/sidebar-compatibility-notice")||(0,$e.addFilter)("editor.BlockEdit","woocommerce/add/sidebar-compatibility-notice",En,11),(0,$e.hasFilter)("blocks.registerBlockType","core/lock/addAttribute")||(0,_.subscribe)((()=>{const e=(0,_.select)(l.store);if(!e)return;const t=e.getSelectedBlock();t&&(dn("wc-lock-selected-block--remove",!!t?.attributes?.lock?.remove),dn("wc-lock-selected-block--move",!!t?.attributes?.lock?.move))}));const Nn=["woocommerce/checkout-fields-block","woocommerce/checkout-totals-block"],An={hasDarkControls:{type:"boolean",default:(0,x.getSetting)("hasDarkEditorStyleSupport",!1)},showRateAfterTaxName:{type:"boolean",default:(0,x.getSetting)("displayCartPricesIncludingTax",!1)}},In={showOrderNotes:{type:"boolean",default:!0},showPolicyLinks:{type:"boolean",default:!0},showReturnToCart:{type:"boolean",default:!0},cartPageId:{type:"number",default:0},showCompanyField:{type:"boolean",default:!1},requireCompanyField:{type:"boolean",default:!1},showApartmentField:{type:"boolean",default:!0},requireApartmentField:{type:"boolean",default:!1},showPhoneField:{type:"boolean",default:!0},requirePhoneField:{type:"boolean",default:!1}},Rn=JSON.parse('{"name":"woocommerce/checkout","version":"1.0.0","title":"Checkout","description":"Display a checkout form so your customers can submit orders.","category":"woocommerce","keywords":["WooCommerce"],"supports":{"align":["wide"],"html":false,"multiple":false},"example":{"attributes":{"isPreview":true},"viewportWidth":800},"attributes":{"isPreview":{"type":"boolean","default":false,"save":false},"align":{"type":"string","default":"wide"},"showFormStepNumbers":{"type":"boolean","default":false}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),Tn={icon:{src:(0,c.jsx)(i.A,{icon:n,className:"wc-block-editor-components-block-icon"})},attributes:{...Rn.attributes,...An,...In},edit:({clientId:e,attributes:t,setAttributes:o})=>{const{showOrderNotes:r,showPolicyLinks:n,showReturnToCart:i,showRateAfterTaxName:p,cartPageId:m,isPreview:u=!1,showFormStepNumbers:h=!1,hasDarkControls:k=!1}=t,b=(0,_.useSelect)((e=>{const t=e(Pt.store).getEditedEntityRecord("root","site");return{...x.defaultFields,...Object.fromEntries(Object.entries({phone:"optional",company:"hidden",address_2:"optional"}).map((([e,o])=>{const s=t[`woocommerce_checkout_${e}_field`]||o;return[e,{...x.defaultFields[e],required:"required"===s,hidden:"hidden"===s}]})))}})),y=(0,d.useRef)((0,we.getQueryArg)(window.location.href,"focus"));(0,d.useEffect)((()=>{"checkout"!==y.current||(0,_.select)("core/block-editor").hasSelectedBlock()||((0,_.dispatch)("core/block-editor").selectBlock(e),(0,_.dispatch)("core/interface").enableComplementaryArea("core/edit-site","edit-site/block-inspector"))}),[e]);const f=((e={})=>{const t=(0,d.useRef)(),o=(0,l.useBlockProps)({ref:t,...e});return(({ref:e})=>{const t=(0,$e.hasFilter)("blocks.registerBlockType","core/lock/addAttribute"),o=e.current;(0,d.useEffect)((()=>{if(o&&!t)return o.addEventListener("keydown",e,{capture:!0,passive:!1}),()=>{o.removeEventListener("keydown",e,{capture:!0})};function e(e){const{keyCode:t,target:o}=e;if(!(o instanceof HTMLElement))return;if(t!==ln.BACKSPACE&&t!==ln.DELETE)return;if((0,Wt.isTextField)(o))return;const s=o;if(void 0===s.dataset.block)return;const r=(e=>{if(!e)return!1;const{getBlock:t}=(0,_.select)(l.store),o=t(e);if("boolean"==typeof o?.attributes?.lock?.remove)return o.attributes.lock.remove;const s=(0,a.getBlockType)(o.name);return"boolean"==typeof s?.attributes?.lock?.default?.remove&&s?.attributes?.lock?.default?.remove})(s.dataset.block);r&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation())}}),[o,t])})({ref:t}),o})();return(0,c.jsxs)("div",{...f,children:[(0,c.jsx)(l.InspectorControls,{children:(0,c.jsx)(pn,{attributes:t,setAttributes:o})}),(0,c.jsx)(w,{isPreview:!!u,previewData:{previewCart:St,previewSavedPaymentMethods:jt,defaultFields:b},children:(0,c.jsx)(Ct.SlotFillProvider,{children:(0,c.jsx)(ht,{children:(0,c.jsx)(g,{className:(0,s.A)("wc-block-checkout",{"has-dark-controls":k}),children:(0,c.jsx)(It.Provider,{value:{showOrderNotes:r,showPolicyLinks:n,showReturnToCart:i,cartPageId:m,showRateAfterTaxName:p,showFormStepNumbers:h,defaultFields:b},children:(0,c.jsx)(l.InnerBlocks,{allowedBlocks:Nn,template:[["woocommerce/checkout-totals-block",{},[]],["woocommerce/checkout-fields-block",{},[]]],templateLock:"insert"})})})})})})]})},save:()=>(0,c.jsx)("div",{...l.useBlockProps.save({className:"wc-block-checkout is-loading"}),children:(0,c.jsx)(l.InnerBlocks.Content,{})}),transforms:{to:[{type:"block",blocks:["woocommerce/classic-shortcode"],transform:e=>(0,a.createBlock)("woocommerce/classic-shortcode",{shortcode:"checkout",align:e.align},[])}]},deprecated:[{attributes:{...Rn.attributes,...An,...In},save:({attributes:e})=>(0,c.jsx)("div",{className:(0,s.A)("is-loading",e.className)}),migrate:e=>{const{showOrderNotes:t,showPolicyLinks:o,showReturnToCart:s,cartPageId:r}=e;return[e,[(0,a.createBlock)("woocommerce/checkout-fields-block",{},[(0,a.createBlock)("woocommerce/checkout-express-payment-block",{},[]),(0,a.createBlock)("woocommerce/checkout-contact-information-block",{},[]),(0,a.createBlock)("woocommerce/checkout-shipping-address-block",{},[]),(0,a.createBlock)("woocommerce/checkout-billing-address-block",{},[]),(0,a.createBlock)("woocommerce/checkout-shipping-methods-block",{},[]),(0,a.createBlock)("woocommerce/checkout-payment-block",{},[]),(0,a.createBlock)("woocommerce/checkout-additional-information-block",{},[]),!!t&&(0,a.createBlock)("woocommerce/checkout-order-note-block",{},[]),!!o&&(0,a.createBlock)("woocommerce/checkout-terms-block",{},[]),(0,a.createBlock)("woocommerce/checkout-actions-block",{showReturnToCart:s,cartPageId:r},[])].filter(Boolean)),(0,a.createBlock)("woocommerce/checkout-totals-block",{})]]},isEligible:(e,t)=>!t.some((e=>"woocommerce/checkout-fields-block"===e.name))},{save:({attributes:e})=>(0,c.jsx)("div",{className:(0,s.A)("is-loading",e.className)}),isEligible:(e,t)=>{const o=t.find((e=>"woocommerce/checkout-fields-block"===e.name));return!!o&&!o.innerBlocks.some((e=>"woocommerce/checkout-additional-information-block"===e.name))},migrate:(e,t)=>{const o=t.findIndex((e=>"woocommerce/checkout-fields-block"===e.name));if(-1===o)return!1;const s=t[o],r=s.innerBlocks.findIndex((e=>"wp-block-woocommerce-checkout-payment-block"===e.name));return-1!==r&&(t[o]=s.innerBlocks.slice(0,r).concat((0,a.createBlock)("woocommerce/checkout-additional-information-block",{},[])).concat(t.slice(r+1,t.length)),[e,t])}}]};(0,a.registerBlockType)(Rn,Tn)},5893:()=>{},6882:()=>{},2770:()=>{},6161:()=>{},6713:()=>{},6983:()=>{},9287:()=>{},7605:()=>{},3692:()=>{},8879:()=>{},2840:()=>{},8349:()=>{},2793:()=>{},1962:()=>{},619:()=>{},8413:()=>{},6562:()=>{},4249:()=>{},9961:()=>{},7575:()=>{},959:()=>{},8501:()=>{},8306:()=>{},9163:()=>{},3930:()=>{},2831:()=>{},4147:()=>{},7215:()=>{},8796:()=>{},1121:()=>{},2862:()=>{},8599:()=>{},7883:()=>{},6715:()=>{},4793:()=>{},9003:()=>{},4255:()=>{},6229:()=>{},2739:()=>{},5991:()=>{},6336:()=>{},1393:()=>{},7693:()=>{},6223:()=>{},5763:()=>{},6811:()=>{},4259:()=>{},3650:()=>{},4459:()=>{},4268:()=>{},6342:()=>{},4490:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},195:e=>{"use strict";e.exports=window.wp.a11y},9491:e=>{"use strict";e.exports=window.wp.compose},1659:e=>{"use strict";e.exports=window.wp.deprecated},8107:e=>{"use strict";e.exports=window.wp.dom},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},8558:e=>{"use strict";e.exports=window.wp.keycodes},5573:e=>{"use strict";e.exports=window.wp.primitives}},r={};function c(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return s[e].call(o.exports,o,o.exports,c),o.exports}c.m=s,e=[],c.O=(t,o,s,r)=>{if(!o){var n=1/0;for(d=0;d<e.length;d++){for(var[o,s,r]=e[d],i=!0,a=0;a<o.length;a++)(!1&r||n>=r)&&Object.keys(c.O).every((e=>c.O[e](o[a])))?o.splice(a--,1):(i=!1,r<n&&(n=r));if(i){e.splice(d--,1);var l=s();void 0!==l&&(t=l)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[o,s,r]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,s){if(1&s&&(e=this(e)),8&s)return e;if("object"==typeof e&&e){if(4&s&&e.__esModule)return e;if(16&s&&"function"==typeof e.then)return e}var r=Object.create(null);c.r(r);var n={};t=t||[null,o({}),o([]),o(o)];for(var i=2&s&&e;"object"==typeof i&&!~t.indexOf(i);i=o(i))Object.getOwnPropertyNames(i).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,c.d(r,n),r},c.d=(e,t)=>{for(var o in t)c.o(t,o)&&!c.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=251,(()=>{var e={251:0};c.O.j=t=>0===e[t];var t=(t,o)=>{var s,r,[n,i,a]=o,l=0;if(n.some((t=>0!==e[t]))){for(s in i)c.o(i,s)&&(c.m[s]=i[s]);if(a)var d=a(c)}for(t&&t(o);l<n.length;l++)r=n[l],c.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return c.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var n=c.O(void 0,[94],(()=>c(9612)));n=c.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{}).checkout=n})();