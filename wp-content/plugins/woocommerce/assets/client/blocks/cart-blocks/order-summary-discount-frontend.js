"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[146],{3185:(e,o,s)=>{s.r(o),s.d(o,{default:()=>i});var n=s(5486),c=s(4656),r=s(910),t=s(5460),a=s(5954),u=s(1e3),p=s(790);const l=()=>{const{extensions:e,receiveCart:o,...s}=(0,t.V)(),n={extensions:e,cart:s,context:"woocommerce/cart"};return(0,p.jsx)(u.ExperimentalDiscountsMeta.Slot,{...n})},i=({className:e})=>{const{cartTotals:o,cartCoupons:s}=(0,t.V)(),{removeCoupon:u,isRemovingCoupon:i}=(0,a.k)("wc/cart"),C=(0,r.getCurrencyFromPriceResponse)(o);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(c.TotalsWrapper,{className:e,children:(0,p.jsx)(n.n$,{cartCoupons:s,currency:C,isRemovingCoupon:i,removeCoupon:u,values:o})}),(0,p.jsx)(l,{})]})}}}]);