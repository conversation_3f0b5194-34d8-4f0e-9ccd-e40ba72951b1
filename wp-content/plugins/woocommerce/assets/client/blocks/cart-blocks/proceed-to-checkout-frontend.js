"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[9936],{8436:(e,t,o)=>{o.r(t),o.d(t,{default:()=>f});var c=o(1616),a=o(4921),r=o(6087),n=o(9874),s=o(8331),u=o(2754),l=o(5703),i=o(7143),d=o(7594),b=o(1e3),k=o(3993),p=o(3224),h=o(4656);const g=(0,o(7723).__)("Proceed to Checkout","woocommerce");var m=o(790);const C={checkoutPageId:{type:"number",default:0},lock:{type:"object",default:{move:!0,remove:!0}},buttonLabel:{type:"string",default:g}},f=(0,c.withFilteredAttributes)(C)((({checkoutPageId:e,className:t,buttonLabel:c})=>{const C=(0,l.getSetting)("page-"+e,!1),f=(0,i.useSelect)((e=>e(d.checkoutStore).isCalculating()),[]),[w,v]=(0,u.E)(),[_,y]=(0,r.useState)(!1);(0,r.useEffect)((()=>{if("function"!=typeof o.g.addEventListener||"function"!=typeof o.g.removeEventListener)return;const e=()=>{y(!1)};return o.g.addEventListener("pageshow",e),()=>{o.g.removeEventListener("pageshow",e)}}),[]);const L=(0,i.useSelect)((e=>e(d.cartStore).getCartData()),[]),S=(0,b.applyCheckoutFilter)({filterName:"proceedToCheckoutButtonLabel",defaultValue:c||g,arg:{cart:L}}),E=(0,b.applyCheckoutFilter)({filterName:"proceedToCheckoutButtonLink",defaultValue:C||s.tn,arg:{cart:L}}),{dispatchOnProceedToCheckout:N}=(0,p.e)(),j=(0,m.jsxs)(n.A,{className:(0,a.A)("wc-block-cart__submit-button",{"wc-block-cart__submit-button--loading":_}),href:E,disabled:f,onClick:e=>{N().then((t=>{t.some(k.isErrorResponse)?e.preventDefault():y(!0)}))},children:[_&&(0,m.jsx)(h.Spinner,{}),S]}),A=(0,r.useMemo)((()=>getComputedStyle(document.body).backgroundColor),[]),F="below"===v,T=(0,a.A)("wc-block-cart__submit-container",{"wc-block-cart__submit-container--sticky":F});return(0,m.jsxs)("div",{className:(0,a.A)("wc-block-cart__submit",t),children:[w,(0,m.jsx)("div",{className:T,style:F?{backgroundColor:A}:{},children:j})]})}))}}]);