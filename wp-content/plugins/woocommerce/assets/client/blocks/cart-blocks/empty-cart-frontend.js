(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4857],{5107:(e,c,t)=>{"use strict";t.r(c),t.d(c,{default:()=>r});var o=t(5460),n=t(6087),s=t(3757),l=(t(9685),t(790));const r=({children:e,className:c})=>{const{cartItems:t,cartIsLoading:r}=(0,o.V)();return(0,n.useEffect)((()=>{0!==t.length||r||(0,s.Pt)("wc-blocks_render_blocks_frontend",{element:document.body.querySelector(".wp-block-woocommerce-cart")})}),[r,t]),r||0!==t.length?null:(0,l.jsx)("div",{className:c,children:e})}},9685:()=>{}}]);