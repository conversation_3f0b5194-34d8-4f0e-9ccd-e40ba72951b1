import*as e from"@wordpress/interactivity";var t={908:e=>{e.exports=import("@woocommerce/stores/store-notices")}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var s=r[e]={exports:{}};return t[e](s,s.exports,o),s.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const n=(s={store:()=>e.store},a={},o.d(a,s),a);var s,a;function i(e,t){return!e.ok}function c(e){return Object.assign(new Error(e.message||"Unknown error."),{code:e.code||"unknown_error"})}let d=!1,l=3e3;const{state:y,actions:m}=(0,n.store)("woocommerce",{actions:{*addCartItem({id:e,quantity:t,variation:r}){let o=y.cart.items.find((({id:t})=>e===t));const n=o?"update-item":"add-item",s=JSON.stringify(y.cart),a={};o?(o.quantity=t,o.key&&(a.cartItemsPendingQuantity=[o.key])):(o={id:e,quantity:t,variation:r},y.cart.items.push(o),a.productsPendingAdd=[e]);try{const e=yield fetch(`${y.restUrl}wc/store/v1/cart/${n}`,{method:"POST",headers:{Nonce:y.nonce,"Content-Type":"application/json"},body:JSON.stringify(o)}),t=yield e.json();if(i(e))throw c(t);t.errors?.forEach((e=>{m.showNoticeError(e)})),y.cart=t,(({preserveCartData:e=!1})=>{((e,{bubbles:t=!1,cancelable:r=!1,element:o,detail:n={}})=>{if(!CustomEvent)return;o||(o=document.body);const s=new CustomEvent(e,{bubbles:t,cancelable:r,detail:n});o.dispatchEvent(s)})("wc-blocks_added_to_cart",{bubbles:!0,cancelable:!0,detail:{preserveCartData:e}})})({preserveCartData:!0}),function({quantityChanges:e}){window.dispatchEvent(new CustomEvent("wc-blocks_store_sync_required",{detail:{type:"from_iAPI",quantityChanges:e}}))}({quantityChanges:a})}catch(e){y.cart=JSON.parse(s),m.showNoticeError(e)}},*refreshCartItems(){if(!d){d=!0;try{const e=yield fetch(`${y.restUrl}wc/store/v1/cart`,{headers:{"Content-Type":"application/json"}}),t=yield e.json();if(i(e))throw c(t);y.cart=t,l=3e3}catch(e){setTimeout(m.refreshCartItems,l),l*=2}finally{d=!1}}},*showNoticeError(e){yield Promise.resolve().then(o.bind(o,908));const{actions:t}=(0,n.store)("woocommerce/store-notices",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."});t.addNotice({notice:e.message,type:"error",dismissible:!0}),console.error(e)}}},{lock:!0});window.addEventListener("wc-blocks_store_sync_required",(async e=>{"from_@wordpress/data"===e.detail.type&&m.refreshCartItems()}));