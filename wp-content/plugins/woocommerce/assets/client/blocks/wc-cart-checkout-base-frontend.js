(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3817],{9295:(e,t,s)=>{"use strict";if(s.d(t,{S:()=>m}),/^(251|2895|7949)$/.test(s.j))var r=s(2515);var o=s(5703),n=s(6087),a=s(8314),c=s(1e3),i=s(4328),l=s(790);const d=(e,t)=>e&&t[e]?t[e]:null,u=(e,t,s,r)=>{if(!(0,c.hasInnerBlocks)(e))return null;const a=s?Array.from(s).map((e=>e instanceof HTMLElement&&e?.dataset.blockName||null)).filter(Boolean):[],u=(0,c.getRegisteredBlocks)(e).filter((({blockName:e,force:t})=>!0===t&&!a.includes(e))),p=r||n.Fragment;return(0,l.jsx)(l.Fragment,{children:u.map((({blockName:e,component:s},r)=>{const n=s||d(e,t);return n?(0,l.jsx)(i.A,{text:`Unexpected error in: ${e}`,showErrorBlock:o.CURRENT_USER_IS_ADMIN,children:(0,l.jsx)(p,{children:(0,l.jsx)(n,{},`${e}_forced_${r}`)})},`${e}_blockerror`):null}))})},p=({block:e,blockMap:t,blockWrapper:s,children:r,depth:c=1})=>r&&0!==r.length?Array.from(r).map(((r,m)=>{const{blockName:h="",...g}={...r instanceof HTMLElement?r.dataset:{},className:r instanceof Element?r?.className:""},_=`${e}_${c}_${m}`,v=d(h,t);if(!v){const o=(0,a.Ay)(r instanceof Element&&r?.outerHTML||r?.textContent||"");if("string"==typeof o&&o)return o;if(!(0,n.isValidElement)(o))return null;if("script"===o?.type)return o;const i=r.childNodes.length?p({block:e,blockMap:t,children:r.childNodes,depth:c+1,blockWrapper:s}):void 0;return i?(0,n.cloneElement)(o,{key:_,...o?.props||{}},i):(0,n.cloneElement)(o,{key:_,...o?.props||{}})}const b=s||n.Fragment;return(0,l.jsx)(n.Suspense,{fallback:(0,l.jsx)("div",{className:"wc-block-placeholder"}),children:(0,l.jsx)(i.A,{text:`Unexpected error in: ${h}`,showErrorBlock:o.CURRENT_USER_IS_ADMIN,children:(0,l.jsx)(b,{children:(0,l.jsxs)(v,{...g,children:[p({block:e,blockMap:t,children:r.childNodes,depth:c+1,blockWrapper:s}),u(h,t,r.childNodes,s)]},_)})})},`${e}_${c}_${m}_suspense`)})):null,m=({Block:e,selector:t,blockName:s,getProps:o=()=>({}),blockMap:n,blockWrapper:a})=>(0,r.Fq)({Block:e,selector:t,getProps:(e,t)=>{const r=p({block:s,blockMap:n,children:e.children||[],blockWrapper:a});return{...o(e,t),children:r}}})},4328:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(6087),o=s(7723),n=s(8331),a=s(790);const c=({imageUrl:e=`${n.sW}/block-error.svg`,header:t=(0,o.__)("Oops!","woocommerce"),text:s=(0,o.__)("There was an error loading the content.","woocommerce"),errorMessage:r,errorMessagePrefix:c=(0,o.__)("Error:","woocommerce"),button:i,showErrorBlock:l=!0})=>l?(0,a.jsxs)("div",{className:"wc-block-error wc-block-components-error",children:[e&&(0,a.jsx)("img",{className:"wc-block-error__image wc-block-components-error__image",src:e,alt:""}),(0,a.jsxs)("div",{className:"wc-block-error__content wc-block-components-error__content",children:[t&&(0,a.jsx)("p",{className:"wc-block-error__header wc-block-components-error__header",children:t}),s&&(0,a.jsx)("p",{className:"wc-block-error__text wc-block-components-error__text",children:s}),r&&(0,a.jsxs)("p",{className:"wc-block-error__message wc-block-components-error__message",children:[c?c+" ":"",r]}),i&&(0,a.jsx)("p",{className:"wc-block-error__button wc-block-components-error__button",children:i})]})]}):null;s(5893);class i extends r.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("strong",{children:e.status}),": ",e.statusText]}),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{header:e,imageUrl:t,showErrorMessage:s=!0,showErrorBlock:r=!0,text:o,errorMessagePrefix:n,renderError:i,button:l}=this.props,{errorMessage:d,hasError:u}=this.state;return u?"function"==typeof i?i({errorMessage:d}):(0,a.jsx)(c,{showErrorBlock:r,errorMessage:s?d:null,header:e,imageUrl:t,text:o,errorMessagePrefix:n,button:l}):this.props.children}}const l=i},9874:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(1069),o=s(6087),n=s(4921),a=(s(6882),s(790));const c=(0,o.forwardRef)(((e,t)=>{const{className:s,children:o,variant:c="contained",removeTextWrap:i=!1,...l}=e,d=(0,n.A)("wc-block-components-button","wp-element-button",s,c);if("href"in e)return(0,a.jsx)(r.$,{render:(0,a.jsx)("a",{ref:t,href:e.href,children:(0,a.jsx)("div",{className:"wc-block-components-button__text",children:o})}),className:d,...l});const u=i?e.children:(0,a.jsx)("div",{className:"wc-block-components-button__text",children:e.children});return(0,a.jsx)(r.$,{ref:t,className:d,...l,children:u})})),i=989!=s.j?c:null},5058:(e,t,s)=>{"use strict";s.d(t,{A:()=>T});var r=s(4921),o=s(7723),n=s(6087),a=s(195),c=s(8558),i=s(4347),l=(s(9959),s(790));const d=({className:e,quantity:t=1,minimum:s=1,maximum:d,onChange:u=()=>{},step:p=1,itemName:m="",disabled:h,editable:g})=>{const _=(0,r.A)("wc-block-components-quantity-selector",e),v=(0,n.useRef)(null),b=(0,n.useRef)(null),f=(0,n.useRef)(null),y=void 0!==d,k=!h&&t-p>=s,w=!h&&(!y||t+p<=d),x=(0,n.useCallback)((e=>{let t=e;y&&(t=Math.min(t,Math.floor(d/p)*p)),t=Math.max(t,Math.ceil(s/p)*p),t=Math.floor(t/p)*p,t!==e&&u(t)}),[y,d,s,u,p]),j=(0,i.YQ)(x,300);(0,n.useLayoutEffect)((()=>{x(t)}),[t,x]);const S=(0,n.useCallback)((e=>{const s=void 0!==typeof e.key?"ArrowDown"===e.key:e.keyCode===c.DOWN,r=void 0!==typeof e.key?"ArrowUp"===e.key:e.keyCode===c.UP;s&&k&&(e.preventDefault(),u(t-p)),r&&w&&(e.preventDefault(),u(t+p))}),[t,u,w,k,p]);return(0,l.jsxs)("div",{className:_,children:[(0,l.jsx)("input",{ref:v,className:"wc-block-components-quantity-selector__input",disabled:h,readOnly:!g,type:"number",step:p,min:s,max:d,value:t,onKeyDown:S,onChange:e=>{let s=parseInt(e.target.value,10);s=isNaN(s)?t:s,s!==t&&(u(s),j(s))},"aria-label":(0,o.sprintf)(/* translators: %s refers to the item name in the cart. */ /* translators: %s refers to the item name in the cart. */
(0,o.__)("Quantity of %s in your cart.","woocommerce"),m)}),g&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{ref:b,"aria-label":(0,o.sprintf)(/* translators: %s refers to the item name in the cart. */ /* translators: %s refers to the item name in the cart. */
(0,o.__)("Reduce quantity of %s","woocommerce"),m),className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--minus",disabled:!k,onClick:()=>{const e=t-p;u(e),(0,a.speak)((0,o.sprintf)(/* translators: %s refers to the item's new quantity in the cart. */ /* translators: %s refers to the item's new quantity in the cart. */
(0,o.__)("Quantity reduced to %s.","woocommerce"),e)),x(e)},children:"－"}),(0,l.jsx)("button",{ref:f,"aria-label":(0,o.sprintf)(/* translators: %s refers to the item's name in the cart. */ /* translators: %s refers to the item's name in the cart. */
(0,o.__)("Increase quantity of %s","woocommerce"),m),disabled:!w,className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--plus",onClick:()=>{const e=t+p;u(e),(0,a.speak)((0,o.sprintf)(/* translators: %s refers to the item's new quantity in the cart. */ /* translators: %s refers to the item's new quantity in the cart. */
(0,o.__)("Quantity increased to %s.","woocommerce"),e)),x(e)},children:"＋"})]})]})};var u=s(6711),p=s(4473),m=s(7143),h=s(7594),g=s(9464),_=s(3993),v=s(5460);var b=s(7052),f=s(910),y=s(1e3),k=s(6513),w=s(5703),x=s(8387),j=s(8145),S=s(387),C=s(4350),E=s(4656),A=s(4403);const N=({currency:e,saleAmount:t,format:s="<price/>"})=>{if(!t||t<=0)return null;s.includes("<price/>")||(s="<price/>",console.error("Price formats need to include the `<price/>` tag."));const r=(0,o.sprintf)(/* translators: %s will be replaced by the discount amount */ /* translators: %s will be replaced by the discount amount */
(0,o.__)("Save %s","woocommerce"),s);return(0,l.jsx)(A.A,{className:"wc-block-components-sale-badge",children:(0,n.createInterpolateElement)(r,{price:(0,l.jsx)(E.FormattedMonetaryAmount,{currency:e,value:t})})})},P=(e,t)=>e.convertPrecision(t.minorUnit).getAmount(),R=(0,n.forwardRef)((({lineItem:e,onRemove:t=()=>{},tabIndex:s},c)=>{const{name:E="",catalog_visibility:A="visible",short_description:R="",description:I="",low_stock_remaining:M=null,show_backorder_badge:T=!1,quantity_limits:$={minimum:1,maximum:99,multiple_of:1,editable:!0},sold_individually:O=!1,permalink:D="",images:L=[],variation:F=[],item_data:V=[],prices:B={currency_code:"USD",currency_minor_unit:2,currency_symbol:"$",currency_prefix:"$",currency_suffix:"",currency_decimal_separator:".",currency_thousand_separator:",",price:"0",regular_price:"0",sale_price:"0",price_range:null,raw_prices:{precision:6,price:"0",regular_price:"0",sale_price:"0"}},totals:H={currency_code:"USD",currency_minor_unit:2,currency_symbol:"$",currency_prefix:"$",currency_suffix:"",currency_decimal_separator:".",currency_thousand_separator:",",line_subtotal:"0",line_subtotal_tax:"0"},extensions:U}=e,{quantity:q,setItemQuantity:W,removeItem:z,isPendingDelete:G}=(e=>{const t={key:"",quantity:1};(e=>(0,_.isObject)(e)&&(0,_.objectHasProp)(e,"key")&&(0,_.objectHasProp)(e,"quantity")&&(0,_.isString)(e.key)&&(0,_.isNumber)(e.quantity))(e)&&(t.key=e.key,t.quantity=e.quantity);const{key:s="",quantity:r=1}=t,{cartErrors:o}=(0,v.V)(),{__internalStartCalculation:a,__internalFinishCalculation:c}=(0,m.useDispatch)(h.checkoutStore),[l,d]=(0,n.useState)(r),[u]=(0,i.d7)(l,400),p=(0,g.Z)(u),{removeItemFromCart:b,changeCartItemQuantity:f}=(0,m.useDispatch)(h.cartStore);(0,n.useEffect)((()=>d(r)),[r]);const y=(0,m.useSelect)((e=>{if(!s)return{quantity:!1,delete:!1};const t=e(h.cartStore);return{quantity:t.isItemPendingQuantity(s),delete:t.isItemPendingDelete(s)}}),[s]),k=(0,n.useCallback)((()=>s?b(s).catch((e=>{(0,h.processErrorResponse)(e)})):Promise.resolve(!1)),[s,b]);return(0,n.useEffect)((()=>{s&&(0,_.isNumber)(p)&&Number.isFinite(p)&&p!==u&&f(s,u).catch((e=>{(0,h.processErrorResponse)(e)}))}),[s,f,u,p]),(0,n.useEffect)((()=>(y.delete?a():c(),()=>{y.delete&&c()})),[c,a,y.delete]),(0,n.useEffect)((()=>(y.quantity||u!==l?a():c(),()=>{(y.quantity||u!==l)&&c()})),[a,c,y.quantity,u,l]),{isPendingDelete:y.delete,quantity:l,setItemQuantity:d,removeItem:k,cartItemQuantityErrors:o}})(e),{dispatchStoreEvent:Y}=(0,b.y)(),{receiveCart:Z,...K}=(0,v.V)(),Q=(0,n.useMemo)((()=>({context:"cart",cartItem:e,cart:K})),[e,K]),J=(0,f.getCurrencyFromPriceResponse)(B),X=(0,y.applyCheckoutFilter)({filterName:"itemName",defaultValue:E,extensions:U,arg:Q}),ee=(0,k.A)({amount:parseInt(B.raw_prices.regular_price,10),precision:B.raw_prices.precision}),te=(0,k.A)({amount:parseInt(B.raw_prices.price,10),precision:B.raw_prices.precision}),se=ee.subtract(te),re=se.multiply(q),oe=(0,f.getCurrencyFromPriceResponse)(H);let ne=parseInt(H.line_subtotal,10);(0,w.getSetting)("displayCartPricesIncludingTax",!1)&&(ne+=parseInt(H.line_subtotal_tax,10));const ae=(0,k.A)({amount:ne,precision:oe.minorUnit}),ce=L.length?L[0]:{},ie="hidden"===A||"search"===A,le=(0,y.applyCheckoutFilter)({filterName:"cartItemClass",defaultValue:"",extensions:U,arg:Q}),de=(0,y.applyCheckoutFilter)({filterName:"cartItemPrice",defaultValue:"<price/>",extensions:U,arg:Q,validation:y.productPriceValidation}),ue=(0,y.applyCheckoutFilter)({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:U,arg:Q,validation:y.productPriceValidation}),pe=(0,y.applyCheckoutFilter)({filterName:"saleBadgePriceFormat",defaultValue:"<price/>",extensions:U,arg:Q,validation:y.productPriceValidation}),me=(0,y.applyCheckoutFilter)({filterName:"showRemoveItemLink",defaultValue:!0,extensions:U,arg:Q});return(0,l.jsxs)("tr",{className:(0,r.A)("wc-block-cart-items__row",le,{"is-disabled":G}),ref:c,tabIndex:s,children:[(0,l.jsx)("td",{className:"wc-block-cart-item__image","aria-hidden":!(0,_.objectHasProp)(ce,"alt")||!ce.alt,children:ie?(0,l.jsx)(j.A,{image:ce,fallbackAlt:X}):(0,l.jsx)("a",{href:D,tabIndex:-1,children:(0,l.jsx)(j.A,{image:ce,fallbackAlt:X})})}),(0,l.jsx)("td",{className:"wc-block-cart-item__product",children:(0,l.jsxs)("div",{className:"wc-block-cart-item__wrap",children:[(0,l.jsx)(p.A,{disabled:G||ie,name:X,permalink:D}),T?(0,l.jsx)(x.A,{}):!!M&&(0,l.jsx)(S.A,{lowStockRemaining:M}),(0,l.jsx)("div",{className:"wc-block-cart-item__prices",children:(0,l.jsx)(u.A,{currency:J,regularPrice:P(ee,J),price:P(te,J),format:ue})}),(0,l.jsx)(N,{currency:J,saleAmount:P(se,J),format:pe}),(0,l.jsx)(C.A,{shortDescription:R,fullDescription:I,itemData:V,variation:F}),(0,l.jsxs)("div",{className:"wc-block-cart-item__quantity",children:[!O&&(0,l.jsx)(d,{disabled:G,editable:$.editable,quantity:q,minimum:$.minimum,maximum:$.maximum,step:$.multiple_of,onChange:t=>{W(t),Y("cart-set-item-quantity",{product:e,quantity:t})},itemName:X}),me&&(0,l.jsx)("button",{className:"wc-block-cart-item__remove-link","aria-label":(0,o.sprintf)(/* translators: %s refers to the item's name in the cart. */ /* translators: %s refers to the item's name in the cart. */
(0,o.__)("Remove %s from cart","woocommerce"),X),onClick:()=>{t(),z(),Y("cart-remove-item",{product:e,quantity:q}),(0,a.speak)((0,o.sprintf)(/* translators: %s refers to the item name in the cart. */ /* translators: %s refers to the item name in the cart. */
(0,o.__)("%s has been removed from your cart.","woocommerce"),X))},disabled:G,children:(0,o.__)("Remove item","woocommerce")})]})]})}),(0,l.jsx)("td",{className:"wc-block-cart-item__total",children:(0,l.jsxs)("div",{className:"wc-block-cart-item__total-price-and-sale-badge-wrapper",children:[(0,l.jsx)(u.A,{currency:oe,format:de,price:ae.getAmount()}),q>1&&(0,l.jsx)(N,{currency:J,saleAmount:P(re,J),format:pe})]})})]})}));s(359);const I=[...Array(3)].map(((_x,e)=>(0,l.jsx)(R,{lineItem:{}},e))),M=e=>{const t={};return e.forEach((({key:e})=>{t[e]=(0,n.createRef)()})),t},T=({lineItems:e=[],isLoading:t=!1,className:s})=>{const a=(0,n.useRef)(null),c=(0,n.useRef)(M(e));(0,n.useEffect)((()=>{c.current=M(e)}),[e]);const i=e=>()=>{c?.current&&e&&c.current[e].current instanceof HTMLElement?c.current[e].current.focus():a.current instanceof HTMLElement&&a.current.focus()},d=t?I:e.map(((t,s)=>{const r=e.length>s+1?e[s+1].key:null;return(0,l.jsx)(R,{lineItem:t,onRemove:i(r),ref:c.current[t.key],tabIndex:-1},t.key)}));return(0,l.jsxs)("table",{className:(0,r.A)("wc-block-cart-items",s),ref:a,tabIndex:-1,children:[(0,l.jsx)("caption",{className:"screen-reader-text",children:(0,l.jsx)("h2",{children:(0,o.__)("Products in cart","woocommerce")})}),(0,l.jsx)("thead",{children:(0,l.jsxs)("tr",{className:"wc-block-cart-items__header",children:[(0,l.jsx)("th",{className:"wc-block-cart-items__header-image",children:(0,l.jsx)("span",{children:(0,o.__)("Product","woocommerce")})}),(0,l.jsx)("th",{className:"wc-block-cart-items__header-product",children:(0,l.jsx)("span",{children:(0,o.__)("Details","woocommerce")})}),(0,l.jsx)("th",{className:"wc-block-cart-items__header-total",children:(0,l.jsx)("span",{children:(0,o.__)("Total","woocommerce")})})]})}),(0,l.jsx)("tbody",{children:d})]})}},5336:(e,t,s)=>{"use strict";s.d(t,{l:()=>q,b:()=>O.b});var r=s(6087),o=s(8537),n=s(4921),a=(s(4249),s(4530)),c=s(2174),i=s(7723),l=s(7143),d=s(7594),u=s(4656),p=s(1e3),m=(s(8306),s(790));const h=e=>{const{onChange:t,options:s,label:o,value:h="",className:g,size:_,errorId:v,required:b,errorMessage:f=(0,i.__)("Please select a valid option","woocommerce"),placeholder:y,...k}=e,w=(0,r.useCallback)((e=>{t(e.target.value)}),[t]),x=(0,p.getFieldLabel)(o),j=(0,r.useMemo)((()=>({value:"",label:null!=y?y:(0,i.sprintf)(
// translators: %s will be label of the field. For example "country/region".
// translators: %s will be label of the field. For example "country/region".
(0,i.__)("Select a %s","woocommerce"),x),disabled:!!b})),[y,b,x]),S=(0,r.useId)(),C=k.id||`wc-blocks-components-select-${S}`,E=v||C,A=(0,r.useMemo)((()=>b&&h?s:[j].concat(s)),[b,h,j,s]),{setValidationErrors:N,clearValidationError:P}=(0,l.useDispatch)(d.validationStore),{error:R,validationErrorId:I}=(0,l.useSelect)((e=>{const t=e(d.validationStore);return{error:t.getValidationError(E),validationErrorId:t.getValidationErrorId(E)}}),[E]);(0,r.useEffect)((()=>(!b||h?P(E):N({[E]:{message:f,hidden:!0}}),()=>{P(E)})),[P,h,E,f,b,N]);const M=(0,l.useSelect)((e=>e(d.validationStore).getValidationError(E||"")||{hidden:!0}),[E]);return(0,m.jsxs)("div",{className:(0,n.A)(g,{"has-error":!M.hidden}),children:[(0,m.jsx)("div",{className:"wc-blocks-components-select",children:(0,m.jsxs)("div",{className:"wc-blocks-components-select__container",children:[(0,m.jsx)("label",{htmlFor:C,className:"wc-blocks-components-select__label",children:o}),(0,m.jsx)("select",{className:"wc-blocks-components-select__select",id:C,size:void 0!==_?_:1,onChange:w,value:h,"aria-invalid":!(!R?.message||R?.hidden),"aria-errormessage":I,...k,children:A.map((e=>(0,m.jsx)("option",{value:e.value,"data-alternate-values":`[${e.label}]`,disabled:void 0!==e.disabled&&e.disabled,children:e.label},e.value)))}),(0,m.jsx)(a.A,{className:"wc-blocks-components-select__expand",icon:c.A})]})}),(0,m.jsx)(u.ValidationInputError,{propertyName:E})]})},g=({className:e,countries:t,id:s,errorId:a,label:c,onChange:i,value:l="",autoComplete:d="off",required:u=!1})=>{const p=(0,r.useMemo)((()=>Object.entries(t).map((([e,t])=>({value:e,label:(0,o.decodeEntities)(t)})))),[t]);return(0,m.jsx)(h,{className:(0,n.A)(e,"wc-block-components-country-input"),id:s,errorId:a,label:c||"",onChange:i,options:p,value:l,required:u,autoComplete:d})};var _=s(8331);const v=e=>{const{...t}=e;return(0,m.jsx)(g,{countries:_.FS,...t})},b=e=>(0,m.jsx)(g,{countries:_.FS,...e});s(3930);const f=(e,t)=>{const s=t.find((t=>t.label.toLocaleUpperCase()===e.toLocaleUpperCase()||t.value.toLocaleUpperCase()===e.toLocaleUpperCase()));return s?s.value:""},y=({className:e,id:t,states:s,country:a,label:c,onChange:i,autoComplete:l="off",value:d="",required:p=!1})=>{const g=s[a],_=(0,r.useMemo)((()=>g&&Object.keys(g).length>0?Object.keys(g).map((e=>({value:e,label:(0,o.decodeEntities)(g[e])}))):[]),[g]),v=(0,r.useCallback)((e=>{const t=_.length>0?f(e,_):e;t!==d&&i(t)}),[i,_,d]),b=(0,r.useRef)(d);return(0,r.useEffect)((()=>{b.current!==d&&(b.current=d)}),[d]),(0,r.useEffect)((()=>{if(_.length>0&&b.current){const e=f(b.current,_);e!==b.current&&v(e)}}),[_,v]),_.length>0?(0,m.jsx)(h,{className:(0,n.$)(e,"wc-block-components-state-input"),options:_,label:c||"",id:t,onChange:v,value:d,autoComplete:l,required:p}):(0,m.jsx)(u.ValidatedTextInput,{className:e,id:t,label:c,onChange:v,autoComplete:l,value:d,required:p})},k=e=>{const{...t}=e;return(0,m.jsx)(y,{states:_.xj,...t})},w=e=>(0,m.jsx)(y,{states:_.xj,...e});var x=s(7792),j=s(4556),S=s(9464),C=s(3993),E=s(9491),A=s(923),N=s.n(A),P=s(1824),R=s.n(P),I=s(1069);s(2770);const M=({field:e,props:t,onChange:s,value:o})=>{var n;const a=null!==(n=e?.required)&&void 0!==n&&n,c=(0,S.Z)(a),[l,d]=(0,r.useState)((()=>Boolean(o)||a)),h=(0,p.getFieldLabel)(e.label);(0,r.useEffect)((()=>{c!==a&&d(Boolean(o)||a)}),[o,c,a]);const g=(0,r.useCallback)((e=>{s(e),d(!0)}),[s]);return(0,m.jsx)(r.Fragment,{children:l?(0,m.jsx)(u.ValidatedTextInput,{...t,type:e.type,label:a?e.label:e.optionalLabel,className:"wc-block-components-address-form__address_2",value:o,onChange:e=>s(e)}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(I.$,{render:(0,m.jsx)("span",{}),className:"wc-block-components-address-form__address_2-toggle",onClick:()=>d(!0),children:(0,i.sprintf)(
// translators: %s: address 2 field label.
// translators: %s: address 2 field label.
(0,i.__)("+ Add %s","woocommerce"),h)}),(0,m.jsx)("input",{type:"text",tabIndex:-1,className:"wc-block-components-address-form__address_2-hidden-input","aria-hidden":"true","aria-label":e.label,autoComplete:e.autocomplete,id:t?.id,value:o,onChange:e=>g(e.target.value)})]})})};var T=s(2687);const $=({formId:e,address1:t,address2:s,addressType:r,onChange:o})=>{const n=(0,T.Vh)(t.field,e,r),a=(0,T.Vh)(s.field,e,r);return(0,m.jsxs)(m.Fragment,{children:[t&&(0,m.jsx)(u.ValidatedTextInput,{...n,type:t.field.type,label:t.field.label,className:"wc-block-components-address-form__address_1",value:t.value,onChange:e=>o("address_1",e)}),s.field&&!s.field.hidden&&(0,m.jsx)(M,{field:s.field,props:a,onChange:e=>o("address_2",e),value:s.value})]})};var O=s(7128),D=s(6176),L=s(3832);const F={};function V(e){let t=e;return function(e){const s=t;return t=e,s}}const B=V(),H=V(),U=({id:e="",fields:t,onChange:s,addressType:a="shipping",values:c,children:g,isEditing:f,ariaDescribedBy:y=""})=>{const A=(0,E.useInstanceId)(U),P=(0,r.useRef)(!0),{defaultFields:I}=(0,x.C)(),M=(0,j.c)(t),V=(0,j.c)("country"in c?c.country:""),q=(0,O.b)(M,I,a,V),W=(0,S.Z)(q),z=(0,S.Z)(f),G=(0,S.Z)(c),Y=(0,r.useRef)({}),{errors:Z,previousErrors:K}=((e,t,s)=>{const{parser:o,data:n}=(0,D.o)(t),a=(0,r.useRef)(F),c=(0,S.Z)(a.current);if(!n)return{errors:a.current,previousErrors:void 0};let l;if(s)l=s;else switch(t){case"billing":case"shipping":l=n.customer.address||{};break;case"contact":case"order":l=n.checkout.additional_fields||{};break;default:l={}}const d=e.reduce(((e,t)=>((0,T.Do)(t,"validation")&&!t.hidden&&(t.required||l[t.key])&&(e[t.key]=t.validation),e)),{});let u=F;if(Object.keys(d).length>0&&o){const s={type:"object",properties:{}};switch(t){case"shipping":s.properties={customer:{type:"object",properties:{shipping_address:{type:"object",properties:d}}}};break;case"billing":s.properties={customer:{type:"object",properties:{billing_address:{type:"object",properties:d}}}};break;default:s.properties={checkout:{type:"object",properties:{additional_fields:{type:"object",properties:d}}}}}const r=o.compile(s),a=r(n);u=!a&&r.errors?((e,t)=>e.reduce(((e,s)=>{var r;const o=(n=s.instancePath,n.split("/").pop()?.replace("~1","/"));var n;const a=t.find((e=>e.key===o));if(!a||!o)return e;const c=(0,p.getFieldLabel)(a.label),l=(0,i.sprintf)(
// translators: %s is the label of the field.
// translators: %s is the label of the field.
(0,i.__)("%s is invalid","woocommerce"),c);if(o)switch(s.keyword){case"errorMessage":e[o]=null!==(r=s.message)&&void 0!==r?r:l;break;case"pattern":e[o]=(0,i.sprintf)(
// translators: %1$s is the label of the field, %2$s is the pattern.
// translators: %1$s is the label of the field, %2$s is the pattern.
(0,i.__)("%1$s must match the pattern %2$s","woocommerce"),c,s.params.pattern);break;default:e[o]=l}return e}),{}))(r.errors,e):F}const m=e.map((e=>u[e.key]?[e.key,u[e.key]]:e.hidden||!e.required&&!l[e.key]?null:"postcode"===e.key&&"country"in l&&!(0,p.isPostcode)({postcode:l.postcode,country:l.country})?[e.key,(0,i.__)("Please enter a valid postcode","woocommerce")]:"email"===e.key&&"email"in l&&!(0,L.isEmail)(l.email)?[e.key,(0,i.__)("Please enter a valid email address","woocommerce")]:null)).filter(C.nonNullable);return R()(a.current,Object.fromEntries(m))||(a.current=Object.fromEntries(m)),{errors:a.current,previousErrors:c}})(q,a,"shipping"===a?c:void 0);return(0,r.useEffect)((()=>{if(Object.entries(Z).forEach((([e,t])=>{const s=Y.current[e];t&&(s?.setErrorMessage(t),(0,l.select)(d.validationStore).getValidationError(`${a}_${e}`)||(0,l.dispatch)(d.validationStore).setValidationErrors({[`${a}_${e}`]:{message:t,hidden:!!s?.isFocused()}}))})),K){const e=[];Object.entries(K).forEach((([t])=>{const s=Y.current[t];t in Z||(e.push(`${a}_${t}`),s?.setErrorMessage(""))})),e.length&&(0,l.dispatch)(d.validationStore).clearValidationErrors(e)}}),[Z,K,a,c]),(0,r.useEffect)((()=>{Y.current?.postcode?.revalidate()}),[V]),(0,r.useEffect)((()=>{let t;if(!P.current&&f&&Y.current&&z!==f){const s=q.find((e=>!1===e.hidden));if(!s)return;const{id:r}=(0,T.Vh)(s,e||`${A}`,a),o=document.getElementById(r);o&&(t=setTimeout((()=>{o.focus()}),300))}return P.current=!1,()=>{clearTimeout(t)}}),[f,q,e,A,a,z]),(0,r.useEffect)((()=>{if(R()(W,q))return;const e={...c,...Object.fromEntries(q.filter((e=>e.hidden)).map((e=>[e.key,""])))};N()(c,e)||s(e)}),[s,q,W,c]),(0,r.useEffect)((()=>{if((!R()(W,q)||!R()(G,c))&&("country"in c&&((e,t)=>{const s=`${e}_country`,r=(0,l.select)(d.validationStore).getValidationError(s),o=t.city||t.state||t.postcode;try{if(!t.country&&o)throw(0,i.__)("Please select your country","woocommerce");if("billing"===e&&t.country&&!Object.keys(_.AG).includes(t.country))throw(0,i.__)("Sorry, we do not allow orders from the selected country","woocommerce");if("shipping"===e&&t.country&&!Object.keys(_.G3).includes(t.country))throw(0,i.__)("Sorry, we do not ship orders to the selected country","woocommerce");r&&(0,l.dispatch)(d.validationStore).clearValidationError(s)}catch(e){r?(0,l.dispatch)(d.validationStore).showValidationError(s):(0,l.dispatch)(d.validationStore).setValidationErrors({[s]:{message:String(e),hidden:!1}})}})(a,c),"state"in c)){const e=q.find((e=>"state"===e.key));e&&((e,t,s)=>{const r=`${e}_state`,o=(0,l.select)(d.validationStore).getValidationError(r),n=s.required,a="shipping"===e?B(t):H(t),c=!!a&&!N()(a,t);o?!n||t.state?(0,l.dispatch)(d.validationStore).clearValidationError(r):c||(0,l.dispatch)(d.validationStore).showValidationError(r):!o&&n&&!t.state&&t.country&&(0,l.dispatch)(d.validationStore).setValidationErrors({[r]:{message:(0,i.sprintf)(/* translators: %s will be the state field label in lowercase e.g. "state" */ /* translators: %s will be the state field label in lowercase e.g. "state" */
(0,i.__)("Please select a %s","woocommerce"),s.label.toLowerCase()),hidden:!0}})})(a,c,e)}}),[c,G,a,q,W]),e=e||`${A}`,(0,m.jsxs)("div",{id:e,className:"wc-block-components-address-form",children:[q.map((t=>{var r;if(t.hidden)return null;const i=(0,T.Vh)(t,e,a),l=(0,T.mV)(i);if("email"===t.key&&(i.id="email",i.errorId="billing_email"),"checkbox"===t.type){const e=t.key in c&&c[t.key],r={checked:Boolean(e),onChange:e=>{s({...c,[t.key]:e})},...l};return t.required?(0,m.jsx)(u.ValidatedCheckboxControl,{...t.errorMessage?{errorMessage:t.errorMessage}:{},...r},t.key):(0,m.jsx)(u.CheckboxControl,{...r},t.key)}if("address_1"===t.key&&"address_1"in c){const r=(0,T.Nw)("address_1",q,c),o=(0,T.Nw)("address_2",q,c);return(0,C.isNull)(r)||(0,C.isNull)(o)?null:(0,m.jsx)($,{address1:r,address2:o,addressType:a,formId:e,onChange:(e,t)=>{s({...c,[e]:t})}},t.key)}if("address_2"===t.key)return null;if("country"===t.key&&"country"in c){const e="shipping"===a?b:v;return(0,m.jsx)(e,{...i,value:c.country,onChange:e=>{s({...c,country:e,state:"",postcode:""})}},t.key)}if("state"===t.key&&"state"in c&&"country"in c){const e="shipping"===a?w:k;return(0,m.jsx)(e,{...i,country:c.country,value:c.state,onChange:e=>s({...c,state:e})},t.key)}return"select"===t.type&&"options"in t?void 0===t.options?null:(0,m.jsx)(h,{...i,label:i.label||"",className:(0,n.A)("wc-block-components-select-input",`wc-block-components-select-input-${t.key}`.replaceAll("/","-")),value:t.key in c?c[t.key]:"",onChange:e=>{s({...c,[t.key]:e})},options:t.options,required:t.required,errorMessage:i.errorMessage||void 0},t.key):(0,m.jsx)(u.ValidatedTextInput,{ref:e=>Y.current[t.key]=e,...i,type:t.type,ariaDescribedBy:y,value:null!==(r=(0,o.decodeEntities)(c[t.key]))&&void 0!==r?r:"",onChange:e=>s({...c,[t.key]:e}),customFormatter:e=>"postcode"===t.key?e.trimStart().toUpperCase():e},t.key)})),g]})},q=U},2e3:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(8331),o=s(3993),n=s(7723);const a=Object.entries(r.iI).reduce(((e,[t,s])=>(e[t]=Object.entries(s).reduce(((e,[t,s])=>(e[t]=(e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,n.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,n.__)("%s (optional)","woocommerce"),e.label)),e.index&&((0,o.isNumber)(e.index)&&(t.index=e.index),(0,o.isString)(e.index)&&(t.index=parseInt(e.index,10))),e.hidden&&(t.required=!1),t})(s),e)),{}),e)),{}),c=/^(251|2895|7949)$/.test(s.j)?(e,t,s="")=>{const r=s&&void 0!==a[s]?a[s]:{};return e.map((e=>({key:e,...t&&e in t?t[e]:{},...r&&e in r?r[e]:{}}))).sort(((e,t)=>e.index-t.index))}:null},7128:(e,t,s)=>{"use strict";if(s.d(t,{b:()=>d}),/^(251|7949)$/.test(s.j))var r=s(6176);var o=s(5703),n=s(6087),a=s(1824),c=s.n(a),i=s(2e3),l=s(2687);const d=(e,t,s,a="")=>{const d=(0,n.useRef)([]),{parser:u,data:p}=(0,r.o)(s),m=(0,i.A)(e,t,a).map((e=>{const s=t[e.key]||{};if(u){if((0,l.Do)(s,"required")){let t={};t=Object.keys(s.required).some((e=>"cart"===e||"checkout"===e||"customer"===e))?{type:"object",properties:s.required}:s.required;try{const s=u.validate(t,p);e.required=s}catch(e){o.CURRENT_USER_IS_ADMIN&&console.error(e)}}if((0,l.Do)(s,"hidden")){const t={type:"object",properties:s.hidden};try{const s=u.validate(t,p);e.hidden=s}catch(e){o.CURRENT_USER_IS_ADMIN&&console.error(e)}}}return e}));if(!d.current||!c()(d.current,m)){const e=m.map((e=>({...e,hidden:"boolean"==typeof e.hidden&&e.hidden,required:"boolean"==typeof e.required&&e.required})));d.current=e}return d.current}},2687:(e,t,s)=>{"use strict";s.d(t,{Do:()=>c,Nw:()=>a,Vh:()=>o,mV:()=>n});var r=s(3993);const o=(e,t,s)=>({id:`${t}-${e?.key}`.replaceAll("/","-"),errorId:`${s}_${e?.key}`,label:(e?.required?e?.label:e?.optionalLabel)||"",autoCapitalize:e?.autocapitalize,autoComplete:e?.autocomplete,errorMessage:e?.errorMessage||"",required:e?.required,placeholder:e?.placeholder,className:`wc-block-components-address-form__${e?.key}`.replaceAll("/","-"),...e?.attributes}),n=e=>{const{autoCapitalize:t,autoComplete:s,placeholder:r,...o}=e;return o},a=(e,t,s)=>{const o=t.find((t=>t.key===e)),n=(0,r.objectHasProp)(s,e)?s[e]:"";return o?{field:{...o,key:e},value:n}:null},c=(e,t)=>(0,r.isObject)(e[t])&&Object.keys(e[t]).length>0},4923:(e,t,s)=>{"use strict";s.d(t,{G:()=>a});var r=s(4656);if(251==s.j)var o=s(6473);var n=s(790);const a=({title:e,setSelectedOption:t,selectedOption:s,pickupLocations:a,onSelectRate:c,renderPickupLocation:i,packageCount:l})=>{const{shippingRates:d}=(0,o.m)(),u=(d?.length||1)>1||document.querySelectorAll(".wc-block-components-local-pickup-select .wc-block-components-radio-control").length>1;return(0,n.jsxs)("div",{className:"wc-block-components-local-pickup-select",children:[!(!u||!e)&&(0,n.jsx)("div",{children:e}),(0,n.jsx)(r.RadioControl,{onChange:e=>{t(e),c(e)},highlightChecked:!0,selected:s,options:a.map((e=>i(e,l)))})]})}},6788:(e,t,s)=>{"use strict";s.d(t,{A:()=>w});var r=s(4575),o=s(4921),n=s(7723),a=s(4656),c=s(6711),i=s(4473),l=s(910),d=s(1e3),u=s(6513),p=s(5703),m=s(6087),h=s(5460),g=s(3993),_=s(8387),v=s(8145),b=s(387),f=s(4350),y=s(790);const k=({cartItem:e,disableProductDescriptions:t})=>{const{images:s,low_stock_remaining:r,show_backorder_badge:k,name:w,permalink:x,prices:j,quantity:S,short_description:C,description:E,item_data:A,variation:N,totals:P,extensions:R}=e,{receiveCart:I,...M}=(0,h.V)(),T=(0,m.useMemo)((()=>({context:"summary",cartItem:e,cart:M})),[e,M]),$=(0,l.getCurrencyFromPriceResponse)(j),O=(0,d.applyCheckoutFilter)({filterName:"itemName",defaultValue:w,extensions:R,arg:T}),D=(0,u.A)({amount:parseInt(j.raw_prices.regular_price,10),precision:(0,g.isString)(j.raw_prices.precision)?parseInt(j.raw_prices.precision,10):j.raw_prices.precision}).convertPrecision($.minorUnit).getAmount(),L=(0,u.A)({amount:parseInt(j.raw_prices.price,10),precision:(0,g.isString)(j.raw_prices.precision)?parseInt(j.raw_prices.precision,10):j.raw_prices.precision}).convertPrecision($.minorUnit).getAmount(),F=(0,l.getCurrencyFromPriceResponse)(P);let V=parseInt(P.line_subtotal,10);(0,p.getSetting)("displayCartPricesIncludingTax",!1)&&(V+=parseInt(P.line_subtotal_tax,10));const B=(0,u.A)({amount:V,precision:F.minorUnit}).getAmount(),H=(0,d.applyCheckoutFilter)({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:R,arg:T,validation:d.productPriceValidation}),U=(0,d.applyCheckoutFilter)({filterName:"cartItemPrice",defaultValue:"<price/>",extensions:R,arg:T,validation:d.productPriceValidation}),q=(0,d.applyCheckoutFilter)({filterName:"cartItemClass",defaultValue:"",extensions:R,arg:T}),W=t?{itemData:A,variation:N}:{itemData:A,variation:N,shortDescription:C,fullDescription:E};return(0,y.jsxs)("div",{className:(0,o.A)("wc-block-components-order-summary-item",q),children:[(0,y.jsxs)("div",{className:"wc-block-components-order-summary-item__image",children:[(0,y.jsx)("div",{className:"wc-block-components-order-summary-item__quantity",children:(0,y.jsx)(a.Label,{label:S.toString(),screenReaderLabel:(0,n.sprintf)(/* translators: %d number of products of the same type in the cart */ /* translators: %d number of products of the same type in the cart */
(0,n._n)("%d item","%d items",S,"woocommerce"),S)})}),(0,y.jsx)(v.A,{image:s.length?s[0]:{},fallbackAlt:O})]}),(0,y.jsxs)("div",{className:"wc-block-components-order-summary-item__description",children:[(0,y.jsx)(i.A,{disabled:!0,name:O,permalink:x,disabledTagName:"h3"}),(0,y.jsx)(c.A,{currency:$,price:L,regularPrice:D,className:"wc-block-components-order-summary-item__individual-prices",priceClassName:"wc-block-components-order-summary-item__individual-price",regularPriceClassName:"wc-block-components-order-summary-item__regular-individual-price",format:H}),k?(0,y.jsx)(_.A,{}):!!r&&(0,y.jsx)(b.A,{lowStockRemaining:r}),(0,y.jsx)(f.A,{...W})]}),(0,y.jsx)("span",{className:"screen-reader-text",children:(0,n.sprintf)(/* translators: %1$d is the number of items, %2$s is the item name and %3$s is the total price including the currency symbol. */ /* translators: %1$d is the number of items, %2$s is the item name and %3$s is the total price including the currency symbol. */
(0,n._n)("Total price for %1$d %2$s item: %3$s","Total price for %1$d %2$s items: %3$s",S,"woocommerce"),S,O,(0,l.formatPrice)(B,F))}),(0,y.jsx)("div",{className:"wc-block-components-order-summary-item__total-price","aria-hidden":"true",children:(0,y.jsx)(c.A,{currency:F,format:U,price:B})})]})};s(6161);const w=({cartItems:e=[],disableProductDescriptions:t=!1})=>{const{isLarge:s,hasContainerWidth:n}=(0,r.G)();return n?(0,y.jsx)("div",{className:(0,o.A)("wc-block-components-order-summary",{"is-large":s}),children:(0,y.jsx)("div",{className:"wc-block-components-order-summary__content",children:e.map((e=>(0,y.jsx)(k,{disableProductDescriptions:t,cartItem:e},e.key)))})}):null}},7827:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>p});var r=s(7723),o=s(9491),n=s(3558);if(251==s.j)var a=s(9464);var c=s(6087);if(251==s.j)var i=s(4921);s(6713);var l=s(790);const d=[(0,r.__)("Too weak","woocommerce"),(0,r.__)("Weak","woocommerce"),(0,r.__)("Medium","woocommerce"),(0,r.__)("Strong","woocommerce"),(0,r.__)("Very strong","woocommerce")],u=({password:e="",onChange:t})=>{var s;const p=(0,o.useInstanceId)(u,"woocommerce-password-strength-meter");let m=-1;e.length>0&&(m=(e=>void 0===window.zxcvbn?(0,n.Bi)(e,[{id:0,value:d[0],minDiversity:0,minLength:0},{id:1,value:d[1],minDiversity:1,minLength:4},{id:2,value:d[2],minDiversity:2,minLength:8},{id:3,value:d[3],minDiversity:4,minLength:12},{id:4,value:d[4],minDiversity:4,minLength:20}]).id:window.zxcvbn(e).score)(e));const h=(0,a.Z)(m);return(0,c.useEffect)((()=>{m!==h&&t&&t(m)}),[m,h,t]),(0,l.jsxs)("div",{id:p,className:(0,i.A)("wc-block-components-password-strength",{hidden:-1===m}),children:[(0,l.jsx)("label",{htmlFor:p+"-meter",className:"screen-reader-text",children:(0,r.__)("Password strength","woocommerce")}),(0,l.jsx)("meter",{id:p+"-meter",className:"wc-block-components-password-strength__meter",min:0,max:4,value:m>-1?m:0,children:null!==(s=d[m])&&void 0!==s?s:""}),!!d[m]&&(0,l.jsxs)("div",{id:p+"-result",className:"wc-block-components-password-strength__result",children:[(0,l.jsx)("span",{className:"screen-reader-text","aria-live":"polite",children:(0,r.sprintf)(/* translators: %s: Password strength */ /* translators: %s: Password strength */
(0,r.__)("Password strength: %1$s (%2$d characters long)","woocommerce"),d[m],e.length)})," ",(0,l.jsx)("span",{"aria-hidden":!0,children:d[m]})]})]})},p=251==s.j?u:null},6151:(e,t,s)=>{"use strict";s.d(t,{h:()=>d});var r=s(4921),o=s(790);const n=e=>`wc-block-components-payment-method-icon wc-block-components-payment-method-icon--${e}`,a=({id:e,src:t=null,alt:s=""})=>t?(0,o.jsx)("img",{className:n(e),src:t,alt:s}):null;var c=s(8331);const i=[{id:"alipay",alt:"Alipay",src:c.sW+"payment-methods/alipay.svg"},{id:"amex",alt:"American Express",src:c.sW+"payment-methods/amex.svg"},{id:"bancontact",alt:"Bancontact",src:c.sW+"payment-methods/bancontact.svg"},{id:"diners",alt:"Diners Club",src:c.sW+"payment-methods/diners.svg"},{id:"discover",alt:"Discover",src:c.sW+"payment-methods/discover.svg"},{id:"eps",alt:"EPS",src:c.sW+"payment-methods/eps.svg"},{id:"giropay",alt:"Giropay",src:c.sW+"payment-methods/giropay.svg"},{id:"ideal",alt:"iDeal",src:c.sW+"payment-methods/ideal.svg"},{id:"jcb",alt:"JCB",src:c.sW+"payment-methods/jcb.svg"},{id:"laser",alt:"Laser",src:c.sW+"payment-methods/laser.svg"},{id:"maestro",alt:"Maestro",src:c.sW+"payment-methods/maestro.svg"},{id:"mastercard",alt:"Mastercard",src:c.sW+"payment-methods/mastercard.svg"},{id:"multibanco",alt:"Multibanco",src:c.sW+"payment-methods/multibanco.svg"},{id:"p24",alt:"Przelewy24",src:c.sW+"payment-methods/p24.svg"},{id:"sepa",alt:"Sepa",src:c.sW+"payment-methods/sepa.svg"},{id:"sofort",alt:"Sofort",src:c.sW+"payment-methods/sofort.svg"},{id:"unionpay",alt:"Union Pay",src:c.sW+"payment-methods/unionpay.svg"},{id:"visa",alt:"Visa",src:c.sW+"payment-methods/visa.svg"},{id:"wechat",alt:"WeChat",src:c.sW+"payment-methods/wechat.svg"}];var l=s(3993);s(6983);const d=({icons:e=[],align:t="center",className:s})=>{const n=(e=>{const t={};return e.forEach((e=>{let s={};"string"==typeof e&&(s={id:e,alt:e,src:null}),"object"==typeof e&&(s={id:e.id||"",alt:e.alt||"",src:e.src||null}),s.id&&(0,l.isString)(s.id)&&!t[s.id]&&(t[s.id]=s)})),Object.values(t)})(e);if(0===n.length)return null;const c=(0,r.A)("wc-block-components-payment-method-icons",{"wc-block-components-payment-method-icons--align-left":"left"===t,"wc-block-components-payment-method-icons--align-right":"right"===t},s);return(0,o.jsx)("div",{className:c,children:n.map((e=>{const t={...e,...(s=e.id,i.find((e=>e.id===s))||{})};var s;return(0,o.jsx)(a,{...t},"payment-method-icon-"+e.id)}))})}},7355:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>p}),251==s.j)var r=s(4921);if(251==s.j)var o=s(4908);if(251==s.j)var n=s(5460);if(251==s.j)var a=s(8034);var c=s(6427),i=s(9874),l=s(910),d=s(4656),u=s(790);const p=251==s.j?({label:e,fullWidth:t=!1,showPrice:s=!1,priceSeparator:p="·"})=>{const{onSubmit:m,isCalculating:h,isDisabled:g,waitingForProcessing:_,waitingForRedirect:v}=(0,o.w)(),{cartTotals:b}=(0,n.V)(),f=(0,l.getCurrencyFromPriceResponse)(b),y=(0,u.jsxs)("div",{className:"wc-block-components-checkout-place-order-button__text",children:[e,s&&(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("style",{children:`.wp-block-woocommerce-checkout-actions-block {\n\t\t\t\t\t\t\t.wc-block-components-checkout-place-order-button__separator {\n\t\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\t\tcontent: "${p}";\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}`}),(0,u.jsx)("div",{className:"wc-block-components-checkout-place-order-button__separator"}),(0,u.jsx)("div",{className:"wc-block-components-checkout-place-order-button__price",children:(0,u.jsx)(d.FormattedMonetaryAmount,{value:b.total_price,currency:f})})]})]});return(0,u.jsxs)(i.A,{className:(0,r.A)("wc-block-components-checkout-place-order-button",{"wc-block-components-checkout-place-order-button--full-width":t},{"wc-block-components-checkout-place-order-button--loading":_||v}),onClick:m,disabled:h||g||_||v,children:[_&&(0,u.jsx)(d.Spinner,{}),v&&(0,u.jsx)(c.Icon,{className:"wc-block-components-checkout-place-order-button__icon",icon:a.A}),y]})}:null},8387:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7723),o=s(4403),n=s(790);const a=/^(251|2895|7949)$/.test(s.j)?()=>(0,n.jsx)(o.A,{className:"wc-block-components-product-backorder-badge",children:(0,r.__)("Available on backorder","woocommerce")}):null},4403:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>n}),/^(251|2895|7949)$/.test(s.j))var r=s(4921);s(7605);var o=s(790);const n=/^(251|2895|7949)$/.test(s.j)?({children:e,className:t})=>(0,o.jsx)("div",{className:(0,r.A)("wc-block-components-product-badge",t),children:e}):null},8145:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(8537),o=s(5703),n=s(790);const a=/^(251|2895|7949)$/.test(s.j)?({image:e={},fallbackAlt:t=""})=>{const s=e.thumbnail?{src:e.thumbnail,alt:(0,r.decodeEntities)(e.alt)||t||"Product Image"}:{src:o.PLACEHOLDER_IMG_SRC,alt:""};return(0,n.jsx)("img",{...s,alt:s.alt})}:null},387:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7723),o=s(4403),n=s(790);const a=/^(251|2895|7949)$/.test(s.j)?({lowStockRemaining:e})=>e?(0,n.jsx)(o.A,{className:"wc-block-components-product-low-stock-badge",children:(0,r.sprintf)(/* translators: %d stock amount (number of items in stock for product) */ /* translators: %d stock amount (number of items in stock for product) */
(0,r.__)("%d left in stock","woocommerce"),e)}):null:null},4350:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var r=s(7356),o=s(8537),n=(s(3692),s(790));const a=({details:e=[]})=>{if(!Array.isArray(e))return null;if(0===(e=e.filter((e=>!e.hidden))).length)return null;let t="ul",s="li";return 1===e.length&&(t="div",s="div"),(0,n.jsx)(t,{className:"wc-block-components-product-details",children:e.map((e=>{const t=e?.key||e.name||"",a=e?.className||(t?`wc-block-components-product-details__${(0,r.c)(t)}`:"");return(0,n.jsxs)(s,{className:a,children:[t&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("span",{className:"wc-block-components-product-details__name",children:[(0,o.decodeEntities)(t),":"]})," "]}),(0,n.jsx)("span",{className:"wc-block-components-product-details__value",children:(0,o.decodeEntities)(e.display||e.value)})]},t+(e.display||e.value))}))})};var c=s(6087),i=s(5269),l=s(6004),d=s(5784),u=s(9446);const p=["a","b","em","i","strong","p","br","ul","ol","li","h1","h2","h3","h4","h5","h6","pre","blockquote","img"],m=["target","href","rel","name","download","src","class","alt","style"],h=({source:e,maxLength:t=15,countType:s="words",className:r="",style:o={}})=>{const a=(0,c.useMemo)((()=>((e,t=15,s="words")=>{const r=(0,l.autop)(e);if((0,u.count)(r,s)<=t)return r;const o=(e=>{const t=e.indexOf("</p>");return-1===t?e:e.substr(0,t+4)})(r);return(0,u.count)(o,s)<=t?o:"words"===s?(0,d.G$)(o,t):(0,d.Bk)(o,t,"characters_including_spaces"===s)})(e,t,s)),[e,t,s]);return(0,n.jsx)(c.RawHTML,{style:o,className:r,children:(0,i.p)(a,{tags:p,attr:m})})};var g=s(8331);const _=({className:e,shortDescription:t="",fullDescription:s=""})=>{const r=t||s;return r?(0,n.jsx)(h,{className:e,source:r,maxLength:15,countType:g.r7.wordCountType||"words"}):null};s(8879);const v=({shortDescription:e="",fullDescription:t="",itemData:s=[],variation:r=[]})=>(0,n.jsxs)("div",{className:"wc-block-components-product-metadata",children:[(0,n.jsx)(_,{className:"wc-block-components-product-metadata__description",shortDescription:e,fullDescription:t}),(0,n.jsx)(a,{details:s}),(0,n.jsx)(a,{details:r.map((({attribute:e="",value:t})=>({key:e,value:t})))})]})},2024:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(8331);if(251==s.j)var o=s(4530);if(251==s.j)var n=s(5181);s(2840);var a=s(790);const c=251==s.j?({href:e,children:t,element:s="a"})=>{const c=e||r.Vo;if(!c)return null;const i=s;return(0,a.jsxs)(i,{..."a"===s?{href:c}:{},className:"wc-block-components-checkout-return-to-cart-button",children:[(0,a.jsx)(o.A,{icon:n.A}),t]})}:null},6222:(e,t,s)=>{"use strict";s.d(t,{S:()=>r});const r=(0,s(6087).createContext)({shippingCalculatorID:"",showCalculator:!1,isShippingCalculatorOpen:!1,setIsShippingCalculatorOpen:()=>{}})},4975:(e,t,s)=>{"use strict";s.d(t,{Sp:()=>f.S,fE:()=>k});var r=s(6087),o=s(9702),n=s(7143),a=s(7594),c=s(4656),i=s(1233),l=s(7723),d=s(9874),u=s(923),p=s.n(u),m=s(6441),h=s(7792),g=(s(8349),s(5336)),_=s(7128),v=s(790);const b=({address:e,onUpdate:t,onCancel:s,addressFields:o})=>{const[c,i]=(0,r.useState)(e),{showAllValidationErrors:u}=(0,n.useDispatch)(a.validationStore),b=(0,m.E)(),{hasValidationErrors:f,isCustomerDataUpdating:y}=(0,n.useSelect)((e=>({hasValidationErrors:e(a.validationStore).hasValidationErrors(),isCustomerDataUpdating:e(a.cartStore).isCustomerDataUpdating()})),[]),{defaultFields:k}=(0,h.C)(),w=(0,_.b)(o,k,"shipping",c.country),x=(0,r.useCallback)((()=>{for(const e of w)if(e.required&&!e.hidden){const t=c[e.key];if("string"==typeof t){if(""===t.trim())return!1;continue}return!1}return!0}),[w,c]),j=(0,r.useCallback)((r=>{if(r.preventDefault(),u(),!f&&x()){if(p()(c,e))return s();const r=Object.fromEntries(o.filter((e=>void 0!==c[e])).map((e=>[e,c[e]])));t(r)}}),[u,f,x,c,e,o,s,t]);return(0,v.jsxs)("form",{className:"wc-block-components-shipping-calculator-address",ref:b,children:[(0,v.jsx)(g.l,{fields:o,onChange:i,values:c}),(0,v.jsx)(d.A,{className:"wc-block-components-shipping-calculator-address__button",disabled:y,variant:"outlined",onClick:j,type:"submit",children:(0,l.__)("Check delivery options","woocommerce")})]})};var f=s(6222);const y=({onUpdate:e=()=>{},onCancel:t=()=>{},addressFields:s=["country","state","city","postcode"]})=>{const{shippingCalculatorID:l,showCalculator:d,setIsShippingCalculatorOpen:u}=(0,r.useContext)(f.S),{shippingAddress:p}=(0,o.q)(),m="wc/cart/shipping-calculator",h=(0,r.useCallback)((()=>{u(!1),t()}),[u,t]),g=(0,r.useCallback)((t=>{(0,n.dispatch)(a.cartStore).updateCustomerData({shipping_address:t},!1).then((()=>{(0,i.jj)(m),u(!1),e(t)})).catch((e=>{(0,a.processErrorResponse)(e,m)}))}),[e,u]);return d?(0,v.jsxs)("div",{className:"wc-block-components-shipping-calculator",id:l,children:[(0,v.jsx)(c.StoreNoticesContainer,{context:m}),(0,v.jsx)(b,{address:p,addressFields:s,onCancel:h,onUpdate:g})]}):null},k=({title:e})=>{const{isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:s}=(0,r.useContext)(f.S);return(0,v.jsx)(c.Panel,{className:"wc-block-components-totals-shipping-panel",initialOpen:!1,hasBorder:!1,title:e,state:[t,s],children:(0,v.jsx)(y,{})})}},4007:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var r=s(4921),o=s(8537),n=s(4656),a=s(6087),c=s(6473),i=s(5269),l=s(9464),d=s(910),u=s(5703),p=s(7723),m=s(790);const h=e=>{const t=(0,u.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10);let s=(0,m.jsxs)(m.Fragment,{children:[Number.isFinite(t)&&(0,m.jsx)(n.FormattedMonetaryAmount,{currency:(0,d.getCurrencyFromPriceResponse)(e),value:t}),(0,m.jsxs)("span",{className:"wc-block-components-shipping-rates-control__package__delivery_time",children:[Number.isFinite(t)&&e.delivery_time?" — ":null,(0,o.decodeEntities)(e.delivery_time)]})]});return 0===t&&(s=(0,m.jsxs)("span",{className:"wc-block-components-shipping-rates-control__package__description--free",children:[(0,p.__)("Free","woocommerce"),(0,m.jsx)("span",{className:"wc-block-components-shipping-rates-control__package__delivery_time",children:e.delivery_time&&" — "+(0,o.decodeEntities)(e.delivery_time)})]})),{label:(0,o.decodeEntities)(e.name),value:e.rate_id,description:s}},g=({className:e="",noResultsMessage:t,onSelectRate:s,rates:r,renderOption:o=h,selectedRate:c,disabled:i=!1,highlightChecked:d=!1})=>{const u=c?.rate_id||"",p=(0,l.Z)(u),[g,_]=(0,a.useState)(null!=u?u:"");return(0,a.useEffect)((()=>{u&&u!==p&&u!==g&&_(u)}),[u,g,p]),(0,a.useEffect)((()=>{!g&&r.length>0&&(_(r[0].rate_id),s(r[0].rate_id))}),[s,r,g]),0===r.length?t:(0,m.jsx)(n.RadioControl,{className:e,onChange:e=>{_(e),s(e)},highlightChecked:d,disabled:i,selected:g,options:r.map(o),descriptionStackingDirection:"column"})},_=({packageData:e})=>(0,m.jsx)("ul",{className:"wc-block-components-shipping-rates-control__package-items",children:Object.values(e.items).map((e=>{const t=(0,o.decodeEntities)(e.name),s=e.quantity;return(0,m.jsx)("li",{className:"wc-block-components-shipping-rates-control__package-item",children:(0,m.jsx)(n.Label,{label:s>1?`${t} × ${s}`:`${t}`,allowHTML:!0,screenReaderLabel:(0,p.sprintf)(/* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */ /* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */
(0,p._n)("%1$s (%2$d unit)","%1$s (%2$d units)",s,"woocommerce"),t,s)})},e.key)}))});s(2793);const v=({packageId:e,className:t="",noResultsMessage:s,renderOption:l,packageData:d,collapsible:u,showItems:p,highlightChecked:h=!1})=>{const{selectShippingRate:v,isSelectingRate:b,shippingRates:f}=(0,c.m)(),y=f?.length||1,[k,w]=(0,a.useState)(0),x=y>1||k>1;(0,a.useEffect)((()=>{const e=()=>{w(document.querySelectorAll(".wc-block-components-shipping-rates-control__package").length)};e();const t=new MutationObserver(e);return t.observe(document.body,{childList:!0,subtree:!0}),()=>{t.disconnect()}}),[]);const j=null!=p?p:x,S=null!=u?u:x,{selectedOptionNumber:C,selectedOption:E}=(0,a.useMemo)((()=>({selectedOptionNumber:d?.shipping_rates?.findIndex((e=>e?.selected)),selectedOption:d?.shipping_rates?.find((e=>e?.selected))})),[d?.shipping_rates]),A=S||j?(0,m.jsxs)("div",{className:"wc-block-components-shipping-rates-control__package-header",children:[(0,m.jsx)("div",{className:"wc-block-components-shipping-rates-control__package-title",dangerouslySetInnerHTML:{__html:(0,i.p)(d.name)}}),S&&(0,m.jsx)("div",{className:"wc-block-components-totals-shipping__via",children:(0,o.decodeEntities)(E?.name)}),j&&(0,m.jsx)(_,{packageData:d})]}):null,N=(0,a.useCallback)((t=>{v(t,e)}),[e,v]),P={className:t,noResultsMessage:s,rates:d.shipping_rates,onSelectRate:N,selectedRate:d.shipping_rates.find((e=>e.selected)),renderOption:l,disabled:b,highlightChecked:h};return S?(0,m.jsx)(n.Panel,{className:(0,r.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":b}),initialOpen:!1,title:A,children:(0,m.jsx)(g,{...P})}):(0,m.jsxs)("div",{className:(0,r.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":b,"wc-block-components-shipping-rates-control__package--first-selected":!b&&0===C,"wc-block-components-shipping-rates-control__package--last-selected":!b&&C===d?.shipping_rates?.length-1}),children:[A,(0,m.jsx)(g,{...P})]})}},8228:(e,t,s)=>{"use strict";s.d(t,{A:()=>b});var r=s(7723),o=s(6087),n=s(9464),a=s(3575),c=s(1e3),i=s(3932),l=s(5460),d=s(7370),u=s(6473),p=s(9021),m=s(3993),h=s(4007),g=s(195),_=s(790);const v=({packages:e,showItems:t,collapsible:s,noResultsMessage:r,renderOption:o,context:n=""})=>e.length?(0,_.jsx)(_.Fragment,{children:e.map((({package_id:e,...a})=>(0,_.jsx)(h.A,{highlightChecked:"woocommerce/cart"!==n,packageId:e,packageData:a,collapsible:s,showItems:t,noResultsMessage:r,renderOption:o},e)))}):null,b=({shippingRates:e,isLoadingRates:t,className:s,collapsible:b,showItems:f,noResultsMessage:y=(0,_.jsx)(_.Fragment,{}),renderOption:k,context:w})=>{const x=(0,i.Lb)(e),j=(0,i.T4)(e),S=(0,n.Z)(x),C=(0,n.Z)(j);(0,o.useEffect)((()=>{var e,s;t||S===x&&C===j||(s=x,1===(e=j)?(0,g.speak)((0,r.sprintf)(/* translators: %d number of shipping options found. */ /* translators: %d number of shipping options found. */
(0,r._n)("%d shipping option was found.","%d shipping options were found.",s,"woocommerce"),s)):(0,g.speak)((0,r.sprintf)(/* translators: %d number of shipping packages packages. */ /* translators: %d number of shipping packages packages. */
(0,r._n)("Shipping option searched for %d package.","Shipping options searched for %d packages.",e,"woocommerce"),e)+" "+(0,r.sprintf)(/* translators: %d number of shipping options available. */ /* translators: %d number of shipping options available. */
(0,r._n)("%d shipping option was found","%d shipping options were found",s,"woocommerce"),s)))}),[t,x,j,S,C]);const{extensions:E,receiveCart:A,...N}=(0,l.V)(),P={className:s,collapsible:b,showItems:f,noResultsMessage:y,renderOption:k,extensions:E,cart:N,components:{ShippingRatesControlPackage:h.A},context:w},{isEditor:R}=(0,d.m)(),{hasSelectedLocalPickup:I,selectedRates:M}=(0,u.m)(),T=(0,m.isObject)(M)?Object.values(M):[],$=T.every((e=>e===T[0]));return(0,_.jsxs)(a.A,{isLoading:t,screenReaderLabel:(0,r.__)("Loading shipping rates…","woocommerce"),showSpinner:!0,children:[I&&"woocommerce/cart"===w&&e.length>1&&!$&&!R&&(0,_.jsx)(p.A,{className:"wc-block-components-notice",isDismissible:!1,status:"warning",children:(0,r.__)("Multiple shipments must have the same pickup location","woocommerce")}),(0,_.jsx)(c.ExperimentalOrderShippingPackages.Slot,{...P}),(0,_.jsx)(c.ExperimentalOrderShippingPackages,{children:(0,_.jsx)(v,{packages:e,noResultsMessage:y,renderOption:k})})]})}},5486:(e,t,s)=>{"use strict";s.d(t,{_i:()=>p,n$:()=>_,Ay:()=>f,w7:()=>A});var r=s(4921),o=s(7723),n=s(6087),a=s(9874),c=s(3575),i=s(4656),l=s(7143),d=s(7594),u=(s(1962),s(790));const p=({instanceId:e,isLoading:t=!1,onSubmit:s,displayCouponForm:p=!1})=>{const[m,h]=(0,n.useState)(""),[g,_]=(0,n.useState)(p),v=`wc-block-components-totals-coupon__input-${e}`,{validationErrorId:b}=(0,l.useSelect)((t=>({validationErrorId:t(d.validationStore).getValidationErrorId(e)})),[e]),f=(0,n.useRef)(null);return(0,u.jsx)(i.Panel,{className:"wc-block-components-totals-coupon",initialOpen:g,hasBorder:!1,headingLevel:2,title:(0,o.__)("Add a coupon","woocommerce"),state:[g,_],children:(0,u.jsx)(c.A,{screenReaderLabel:(0,o.__)("Applying coupon…","woocommerce"),isLoading:t,showSpinner:!1,children:(0,u.jsxs)("div",{className:"wc-block-components-totals-coupon__content",children:[(0,u.jsxs)("form",{className:"wc-block-components-totals-coupon__form",id:"wc-block-components-totals-coupon__form",children:[(0,u.jsx)(i.ValidatedTextInput,{id:v,errorId:"coupon",className:"wc-block-components-totals-coupon__input",label:(0,o.__)("Enter code","woocommerce"),value:m,ariaDescribedBy:b||"",onChange:e=>{h(e)},focusOnMount:!0,validateOnMount:!1,showError:!1,ref:f}),(0,u.jsxs)(a.A,{className:(0,r.A)("wc-block-components-totals-coupon__button",{"wc-block-components-totals-coupon__button--loading":t}),disabled:t||!m,onClick:e=>{e.preventDefault(),void 0!==s?s(m)?.then((e=>{e?(h(""),_(!1)):f.current?.focus&&f.current.focus()})):(h(""),_(!0))},type:"submit",children:[t&&(0,u.jsx)(i.Spinner,{}),(0,o.__)("Apply","woocommerce")]})]}),(0,u.jsx)(i.ValidationInputError,{propertyName:"coupon",elementId:e})]})})})};var m=s(1e3),h=s(5703);s(619);const g={context:"summary"},_=({cartCoupons:e=[],currency:t,isRemovingCoupon:s,removeCoupon:r,values:n})=>{const{total_discount:a,total_discount_tax:l}=n,d=parseInt(a,10),p=(0,m.applyCheckoutFilter)({arg:g,filterName:"coupons",defaultValue:e});if(!d&&0===p.length)return null;const _=parseInt(l,10),v=(0,h.getSetting)("displayCartPricesIncludingTax",!1)?d+_:d;return(0,u.jsx)(i.TotalsItem,{className:"wc-block-components-totals-discount",currency:t,description:0!==p.length&&(0,u.jsx)(c.A,{screenReaderLabel:(0,o.__)("Removing coupon…","woocommerce"),isLoading:s,showSpinner:!1,children:(0,u.jsx)("ul",{className:"wc-block-components-totals-discount__coupon-list",children:p.map((e=>(0,u.jsx)(i.RemovableChip,{className:"wc-block-components-totals-discount__coupon-list-item",text:e.label,screenReaderText:(0,o.sprintf)(/* translators: %s Coupon code. */ /* translators: %s Coupon code. */
(0,o.__)("Coupon: %s","woocommerce"),e.label),disabled:s,onRemove:()=>{r(e.code)},radius:"large",ariaLabel:(0,o.sprintf)(/* translators: %s is a coupon code. */ /* translators: %s is a coupon code. */
(0,o.__)('Remove coupon "%s"',"woocommerce"),e.label)},"coupon-"+e.code)))})}),label:v?(0,o.__)("Discount","woocommerce"):(0,o.__)("Coupons","woocommerce"),value:v?-1*v:"-"})};var v=s(5460),b=s(910);s(8413);const f=({currency:e,values:t,className:s})=>{const a=(0,h.getSetting)("taxesEnabled",!0)&&(0,h.getSetting)("displayCartPricesIncludingTax",!1),{total_price:c,total_tax:l,tax_lines:d}=t,{receiveCart:p,...g}=(0,v.V)(),_=(0,m.applyCheckoutFilter)({filterName:"totalLabel",defaultValue:(0,o.__)("Total","woocommerce"),extensions:g.extensions,arg:{cart:g}}),f=(0,m.applyCheckoutFilter)({filterName:"totalValue",defaultValue:"<price/>",extensions:g.extensions,arg:{cart:g},validation:m.productPriceValidation}),y=(0,u.jsx)(i.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:parseInt(c,10)}),k=(0,n.createInterpolateElement)(f,{price:y}),w=parseInt(l,10),x=d&&d.length>0?(0,o.sprintf)(/* translators: %s is a list of tax rates */ /* translators: %s is a list of tax rates */
(0,o.__)("Including %s","woocommerce"),d.map((({name:t,price:s})=>`${(0,b.formatPrice)(s,e)} ${t}`)).join(", ")):(0,o.__)("Including <TaxAmount/> in taxes","woocommerce");return(0,u.jsx)(i.TotalsItem,{className:(0,r.A)("wc-block-components-totals-footer-item",s),currency:e,label:_,value:k,description:a&&0!==w&&(0,u.jsx)("p",{className:"wc-block-components-totals-footer-item-tax",children:(0,n.createInterpolateElement)(x,{TaxAmount:(0,u.jsx)(i.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:w})})})})};var y=s(3932),k=s(8537);const w=()=>{const{shippingRates:e}=(0,v.V)(),t=(0,y.qr)(e);return t?(0,u.jsx)("div",{className:"wc-block-components-totals-shipping__via",children:(0,k.decodeEntities)(t.filter(((e,s)=>t.indexOf(e)===s)).join(", "))}):null};var x=s(4982),j=s(4975),S=s(3993);const C=e=>{const t=(0,y.mH)(e);return 0===t?(0,u.jsx)("strong",{children:(0,o.__)("Free","woocommerce")}):t},E=()=>{const{shippingRates:e,shippingAddress:t}=(0,v.V)(),s=(0,l.useSelect)((e=>e(d.checkoutStore).prefersCollection())),r=(0,y.HI)(e),{showCalculator:a}=(0,n.useContext)(j.Sp),c=s?(e=>{const t=(e||[]).flatMap((e=>e.shipping_rates)).find((e=>e.selected&&(0,y.J_)(e)));if((0,S.isObject)(t)&&(0,S.objectHasProp)(t,"meta_data")){const e=t.meta_data.find((e=>"pickup_address"===e.key));if((0,S.isObject)(e)&&(0,S.objectHasProp)(e,"value")&&e.value)return e.value}return""})(e):(0,x.i0)(t),i=r?
// Translators: <address/> is the formatted shipping address.
// Translators: <address/> is the formatted shipping address.
(0,o.__)("Delivers to <address/>","woocommerce"):
// Translators: <address/> is the formatted shipping address.
// Translators: <address/> is the formatted shipping address.
(0,o.__)("No delivery options available for <address/>","woocommerce"),p=(0,x.KY)(t,["state","city","country","postcode"]),m=(0,h.getSetting)("shippingCostRequiresAddress",!1)&&!p,g=s?
// Translators: <address/> is the pickup location.
// Translators: <address/> is the pickup location.
(0,o.__)("Collection from <address/>","woocommerce"):i,_=(0,u.jsx)("p",{className:"wc-block-components-totals-shipping-address-summary",children:c&&!m?(0,n.createInterpolateElement)(g,{address:(0,u.jsx)("strong",{children:c})}):(0,u.jsx)(u.Fragment,{children:(0,o.__)("Enter address to check delivery options","woocommerce")})});return(0,u.jsx)("div",{className:"wc-block-components-shipping-address",children:a&&(0,u.jsx)(j.fE,{title:_})})};s(6562);const A=({label:e=(0,o.__)("Shipping","woocommerce"),placeholder:t=null,collaterals:s=null})=>{const{cartTotals:r,shippingRates:n}=(0,v.V)(),a=(0,y.HI)(n);return(0,u.jsx)("div",{className:"wc-block-components-totals-shipping",children:(0,u.jsx)(i.TotalsItem,{label:e,value:a?C(r):t,description:(0,u.jsxs)(u.Fragment,{children:[!!a&&(0,u.jsx)(w,{}),(0,u.jsx)(E,{}),s&&(0,u.jsx)("div",{className:"wc-block-components-totals-shipping__collaterals",children:s})]}),currency:(0,b.getCurrencyFromPriceResponse)(r)})})}},5320:(e,t,s)=>{"use strict";s.d(t,{s:()=>_,A:()=>b});var r=s(4921),o=s(4347),n=s(7723),a=s(6087),c=s(4530),i=s(5614),l=s(9491),d=s(6441),u=s(9874);const p=new Set(["alert","status","log","marquee","timer"]);let m=[],h=!1;s(8963);var g=s(790);const _=()=>(0,g.jsx)("div",{className:"wc-block-components-drawer__close-wrapper"}),v=({onClick:e,contentRef:t})=>{const s=t?.current?.querySelector(".wc-block-components-drawer__close-wrapper");return s?(0,a.createPortal)((0,g.jsx)(u.A,{className:"wc-block-components-drawer__close",onClick:e,removeTextWrap:!0,"aria-label":(0,n.__)("Close","woocommerce"),children:(0,g.jsx)(c.A,{icon:i.A})}),s):null},b=(0,a.forwardRef)((({children:e,className:t,isOpen:s,onClose:n,slideIn:c=!0,slideOut:i=!0},u)=>{const[_]=(0,o.d7)(s,300),b=!s&&_,f="drawer-open",y=()=>{document.body.classList.remove(f),h&&(m.forEach((e=>{e.removeAttribute("aria-hidden")})),m=[],h=!1),n()},k=(0,a.useRef)(),w=(0,l.useFocusOnMount)(),x=(0,l.useConstrainedTabbing)(),j=(0,d.E)(),S=(0,a.useRef)(null);(0,a.useEffect)((()=>{var e;s&&(e=k.current,h||(Array.from(document.body.children).forEach((t=>{t!==e&&function(e){const t=e.getAttribute("role");return!("SCRIPT"===e.tagName||e.hasAttribute("aria-hidden")||e.hasAttribute("aria-live")||t&&p.has(t))}(t)&&(t.setAttribute("aria-hidden","true"),m.push(t))})),h=!0),document.body.classList.add(f))}),[s,f]);const C=(0,l.useMergeRefs)([k,u]),E=(0,l.useMergeRefs)([x,j,w]);return s||b?(0,a.createPortal)((0,g.jsx)("div",{ref:C,className:(0,r.A)("wc-block-components-drawer__screen-overlay",{"wc-block-components-drawer__screen-overlay--is-hidden":!s,"wc-block-components-drawer__screen-overlay--with-slide-in":c,"wc-block-components-drawer__screen-overlay--with-slide-out":i}),onKeyDown:function(e){e.nativeEvent.isComposing||229===e.keyCode||"Escape"!==e.code||e.defaultPrevented||(e.preventDefault(),y())},onClick:e=>{e.target===k.current&&y()},children:(0,g.jsx)("div",{className:(0,r.A)(t,"wc-block-components-drawer"),ref:E,role:"dialog",tabIndex:-1,children:(0,g.jsxs)("div",{className:"wc-block-components-drawer__content",role:"document",ref:S,children:[(0,g.jsx)(v,{contentRef:S,onClick:y}),e]})})}),document.body):null}))},3575:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(7723);if(/^(251|7949)$/.test(s.j))var o=s(4921);var n=s(4656),a=(s(9961),s(790));const c=/^(251|7949)$/.test(s.j)?({children:e,className:t,screenReaderLabel:s,showSpinner:c=!1,isLoading:i=!0})=>(0,a.jsxs)("div",{className:(0,o.A)(t,{"wc-block-components-loading-mask":i}),children:[i&&c&&(0,a.jsx)(n.Spinner,{}),(0,a.jsx)("div",{className:(0,o.A)({"wc-block-components-loading-mask__children":i}),"aria-hidden":i,children:e}),i&&(0,a.jsx)("span",{className:"screen-reader-text",children:s||(0,r.__)("Loading…","woocommerce")})]}):null},3001:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(6087),o=s(8107);if(251==s.j)var n=s(4347);var a=s(790);const c=251==s.j?["BUTTON","FIELDSET","INPUT","OPTGROUP","OPTION","SELECT","TEXTAREA","A"]:null,i=251==s.j?({children:e,style:t={},...s})=>{const i=(0,r.useRef)(null),l=()=>{i.current&&o.focus.focusable.find(i.current).forEach((e=>{c.includes(e.nodeName)&&e.setAttribute("tabindex","-1"),e.hasAttribute("contenteditable")&&e.setAttribute("contenteditable","false")}))},d=(0,n.YQ)(l,0,{leading:!0});return(0,r.useLayoutEffect)((()=>{let e;return l(),i.current&&(e=new window.MutationObserver(d),e.observe(i.current,{childList:!0,attributes:!0,subtree:!0})),()=>{e&&e.disconnect(),d.cancel()}}),[d]),(0,a.jsx)("div",{ref:i,"aria-disabled":"true",style:{userSelect:"none",pointerEvents:"none",cursor:"normal",...t},...s,children:e})}:null},9021:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(4921),o=s(7723),n=s(4530),a=s(5614),c=(s(7575),s(8034)),i=s(2624),l=s(4144);const d=e=>{switch(e){case"success":case"warning":case"info":case"default":return"polite";default:return"assertive"}},u=e=>{switch(e){case"success":return c.A;case"warning":case"info":case"error":return i.A;default:return l.A}};var p=s(9874),m=s(4721),h=s(790);const g=({className:e,status:t="default",children:s,spokenMessage:c=s,onRemove:i=()=>{},isDismissible:l=!0,politeness:g=d(t),summary:_})=>((0,m.$)(c,g),(0,h.jsxs)("div",{className:(0,r.A)(e,"wc-block-components-notice-banner","is-"+t,{"is-dismissible":l}),children:[(0,h.jsx)(n.A,{icon:u(t)}),(0,h.jsxs)("div",{className:"wc-block-components-notice-banner__content",children:[_&&(0,h.jsx)("p",{className:"wc-block-components-notice-banner__summary",children:_}),s]}),!!l&&(0,h.jsx)(p.A,{className:"wc-block-components-notice-banner__dismiss","aria-label":(0,o.__)("Dismiss this notice","woocommerce"),onClick:e=>{"function"==typeof e?.preventDefault&&e.preventDefault&&e.preventDefault(),i()},removeTextWrap:!0,children:(0,h.jsx)(n.A,{icon:a.A})})]}))},4473:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>n}),/^(251|2895|7949)$/.test(s.j))var r=s(4921);s(959);var o=s(790);const n=/^(251|2895|7949)$/.test(s.j)?({className:e="",disabled:t=!1,name:s,permalink:n="",target:a,rel:c,style:i,onClick:l,disabledTagName:d="span",...u})=>{const p=(0,r.A)("wc-block-components-product-name",e),m=d;if(t){const e=u;return(0,o.jsx)(m,{className:p,...e,dangerouslySetInnerHTML:{__html:s}})}return(0,o.jsx)("a",{className:p,href:n,target:a,...u,dangerouslySetInnerHTML:{__html:s},style:i})}:null},6711:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(7723),o=s(4656);if(/^(251|2895|7949)$/.test(s.j))var n=s(4921);var a=s(910),c=s(6087),i=(s(8501),s(790));const l=({currency:e,maxPrice:t,minPrice:s,priceClassName:c,priceStyle:l={}})=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:"screen-reader-text",children:(0,r.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,r.__)("Price between %1$s and %2$s","woocommerce"),(0,a.formatPrice)(s),(0,a.formatPrice)(t))}),(0,i.jsxs)("span",{"aria-hidden":!0,children:[(0,i.jsx)(o.FormattedMonetaryAmount,{className:(0,n.A)("wc-block-components-product-price__value",c),currency:e,value:s,style:l})," — ",(0,i.jsx)(o.FormattedMonetaryAmount,{className:(0,n.A)("wc-block-components-product-price__value",c),currency:e,value:t,style:l})]})]}),d=({currency:e,regularPriceClassName:t,regularPriceStyle:s,regularPrice:a,priceClassName:c,priceStyle:l,price:d})=>(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{className:"screen-reader-text",children:(0,r.__)("Previous price:","woocommerce")}),(0,i.jsx)(o.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,i.jsx)("del",{className:(0,n.A)("wc-block-components-product-price__regular",t),style:s,children:e}),value:a}),(0,i.jsx)("span",{className:"screen-reader-text",children:(0,r.__)("Discounted price:","woocommerce")}),(0,i.jsx)(o.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,i.jsx)("ins",{className:(0,n.A)("wc-block-components-product-price__value","is-discounted",c),style:l,children:e}),value:d})]}),u=/^(251|2895|7949)$/.test(s.j)?({align:e,className:t,currency:s,format:r="<price/>",maxPrice:a,minPrice:u,price:p,priceClassName:m,priceStyle:h,regularPrice:g,regularPriceClassName:_,regularPriceStyle:v,style:b})=>{const f=(0,n.A)(t,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});r.includes("<price/>")||(r="<price/>",console.error("Price formats need to include the `<price/>` tag."));const y=g&&p&&p<g;let k=(0,i.jsx)("span",{className:(0,n.A)("wc-block-components-product-price__value",m)});return y?k=(0,i.jsx)(d,{currency:s,price:p,priceClassName:m,priceStyle:h,regularPrice:g,regularPriceClassName:_,regularPriceStyle:v}):void 0!==u&&void 0!==a?k=(0,i.jsx)(l,{currency:s,maxPrice:a,minPrice:u,priceClassName:m,priceStyle:h}):p&&(k=(0,i.jsx)(o.FormattedMonetaryAmount,{className:(0,n.A)("wc-block-components-product-price__value",m),currency:s,value:p,style:h})),(0,i.jsx)("span",{className:f,style:b,children:(0,c.createInterpolateElement)(r,{price:k})})}:null},9194:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(6087),o=s(7723),n=s(2136);const a=(e,t,s="...")=>(0,n.Q)(e,{suffix:s,limit:t}).html,c=(e,t,s)=>(t<=s?e.start=e.middle+1:e.end=e.middle-1,e),i=(e,t,s,r)=>{const o=((e,t,s)=>{let r={start:0,middle:0,end:e.length};for(;r.start<=r.end;)r.middle=Math.floor((r.start+r.end)/2),t.innerHTML=a(e,r.middle),r=c(r,t.clientHeight,s);return r.middle})(e,t,s);return a(e,o-r.length,r)};var l=s(790);const d={className:"read-more-content",ellipsis:"&hellip;",lessText:(0,o.__)("Read less","woocommerce"),maxLines:3,moreText:(0,o.__)("Read more","woocommerce")};class u extends r.Component{static defaultProps=d;constructor(e){super(e),this.state={isExpanded:!1,clampEnabled:null,content:e.children,summary:"."},this.reviewContent=(0,r.createRef)(),this.reviewSummary=(0,r.createRef)(),this.getButton=this.getButton.bind(this),this.onClick=this.onClick.bind(this)}componentDidMount(){this.setSummary()}componentDidUpdate(e){e.maxLines===this.props.maxLines&&e.children===this.props.children||this.setState({clampEnabled:null,summary:"."},this.setSummary)}setSummary(){if(this.props.children){const{maxLines:e,ellipsis:t}=this.props;if(!this.reviewSummary.current||!this.reviewContent.current)return;const s=(this.reviewSummary.current.clientHeight+1)*e+1,r=this.reviewContent.current.clientHeight+1>s;this.setState({clampEnabled:r}),r&&this.setState({summary:i(this.reviewContent.current.innerHTML,this.reviewSummary.current,s,t)})}}getButton(){const{isExpanded:e}=this.state,{className:t,lessText:s,moreText:r}=this.props,o=e?s:r;if(o)return(0,l.jsx)("a",{href:"#more",className:t+"__read_more",onClick:this.onClick,"aria-expanded":!e,role:"button",children:o})}onClick(e){e.preventDefault();const{isExpanded:t}=this.state;this.setState({isExpanded:!t})}render(){const{className:e}=this.props,{content:t,summary:s,clampEnabled:r,isExpanded:o}=this.state;return t?!1===r?(0,l.jsx)("div",{className:e,children:(0,l.jsx)("div",{ref:this.reviewContent,children:t})}):(0,l.jsxs)("div",{className:e,children:[(!o||null===r)&&(0,l.jsx)("div",{ref:this.reviewSummary,"aria-hidden":o,dangerouslySetInnerHTML:{__html:s}}),(o||null===r)&&(0,l.jsx)("div",{ref:this.reviewContent,"aria-hidden":!o,children:t}),this.getButton()]}):null}}const p=u},2136:(e,t,s)=>{"use strict";function r(e){let t,s,r,o=[];for(let n=0;n<e.length;n++)t=e.substring(n),s=t.match(/^&[a-z0-9#]+;/),s?(r=s[0],o.push(r),n+=r.length-1):o.push(e[n]);return o}function o(e,t){const s=(t=t||{}).limit||100,o=void 0===t.preserveTags||t.preserveTags,n=void 0!==t.wordBreak&&t.wordBreak,a=t.suffix||"...",c=t.moreLink||"",i=t.moreText||"»",l=t.preserveWhiteSpace||!1,d=e.replace(/</g,"\n<").replace(/>/g,">\n").replace(/\n\n/g,"\n").replace(/^\n/g,"").replace(/\n$/g,"").split("\n");let u,p,m,h,g,_,v=0,b=[],f=!1;for(let e=0;e<d.length;e++){if(u=d[e],h=l?u:u.replace(/[ ]+/g," "),!u.length)continue;const t=r(h);if("<"!==u[0])if(v>=s)u="";else if(v+t.length>=s){if(p=s-v," "===t[p-1])for(;p&&(p-=1," "===t[p-1]););else m=t.slice(p).indexOf(" "),n||(-1!==m?p+=m:p=u.length);if(u=t.slice(0,p).join("")+a,c){const e=document.createElement("a");e.href=c,e.style.display="inline",e.textContent=i,u+=e.outerHTML}v=s,f=!0}else v+=t.length;else if(o){if(v>=s)if(g=u.match(/[a-zA-Z]+/),_=g?g[0]:"",_)if("</"!==u.substring(0,2))b.push(_),u="";else{for(;b[b.length-1]!==_&&b.length;)b.pop();b.length&&(u=""),b.pop()}else u=""}else u="";d[e]=u}return{html:d.join("\n").replace(/\n/g,""),more:f}}s.d(t,{Q:()=>o})},2902:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(6087),o=s(4921),n=s(790);const a=(0,r.forwardRef)((({children:e,className:t=""},s)=>(0,n.jsx)("div",{ref:s,className:(0,o.A)("wc-block-components-main",t),children:e}))),c=/^(251|7949)$/.test(s.j)?a:null},9326:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>a}),/^(251|7949)$/.test(s.j))var r=s(4921);if(/^(251|7949)$/.test(s.j))var o=s(4575);s(9163);var n=s(790);const a=/^(251|7949)$/.test(s.j)?({children:e,className:t})=>(0,n.jsx)(o.u,{className:(0,r.A)("wc-block-components-sidebar-layout",t),children:e}):null},389:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(6087),o=s(4921),n=s(790);const a=(0,r.forwardRef)((({children:e,className:t=""},s)=>(0,n.jsx)("div",{ref:s,className:(0,o.A)("wc-block-components-sidebar",t),children:e}))),c=/^(251|7949)$/.test(s.j)?a:null},9015:(e,t,s)=>{"use strict";if(s.d(t,{Y:()=>o}),/^(251|2895|7949)$/.test(s.j))var r=s(9760);const o=(e,t)=>(s,o=10)=>{const n=r.o.addEventCallback(e,s,o);return t(n),()=>{t(r.o.removeEventCallback(e,n.id))}}},9162:(e,t,s)=>{"use strict";s.d(t,{_:()=>a,c:()=>n});var r=s(3993);if(/^(251|2895|7949)$/.test(s.j))var o=s(8696);const n=async(e,t,s)=>{const r=(0,o.fK)(e,t),n=[];for(const e of r)try{const t=await Promise.resolve(e.callback(s));"object"==typeof t&&n.push(t)}catch(e){console.error(e)}return!n.length||n},a=async(e,t,s)=>{const n=[],a=(0,o.fK)(e,t);for(const e of a)try{const t=await Promise.resolve(e.callback(s));if(!(0,r.isObserverResponse)(t))continue;if(!t.hasOwnProperty("type"))throw new Error("Returned objects from event emitter observers must return an object with a type property");if((0,r.isErrorResponse)(t)||(0,r.isFailResponse)(t))return n.push(t),n;n.push(t)}catch(e){return console.error(e),n.push({type:r.responseTypes.ERROR}),n}return n}},9760:(e,t,s)=>{"use strict";s.d(t,{o:()=>o,F:()=>a});let r=function(e){return e.ADD_EVENT_CALLBACK="add_event_callback",e.REMOVE_EVENT_CALLBACK="remove_event_callback",e}({});const o={addEventCallback:(e,t,s=10)=>({id:Math.floor(Math.random()*Date.now()).toString(),type:r.ADD_EVENT_CALLBACK,eventType:e,callback:t,priority:s}),removeEventCallback:(e,t)=>({id:t,type:r.REMOVE_EVENT_CALLBACK,eventType:e})},n={},a=(e=n,{type:t,eventType:s,id:o,callback:a,priority:c})=>{const i=e.hasOwnProperty(s)?new Map(e[s]):new Map;switch(t){case r.ADD_EVENT_CALLBACK:return i.set(o,{priority:c,callback:a}),{...e,[s]:i};case r.REMOVE_EVENT_CALLBACK:return i.delete(o),{...e,[s]:i}}}},8696:(e,t,s)=>{"use strict";s.d(t,{fK:()=>r,tG:()=>o}),s(3993);const r=(e,t)=>e[t]?Array.from(e[t].values()).sort(((e,t)=>e.priority-t.priority)):[];let o=function(e){return e.CART="wc/cart",e.CHECKOUT="wc/checkout",e.PAYMENTS="wc/checkout/payments",e.EXPRESS_PAYMENTS="wc/checkout/express-payments",e.CONTACT_INFORMATION="wc/checkout/contact-information",e.SHIPPING_ADDRESS="wc/checkout/shipping-address",e.BILLING_ADDRESS="wc/checkout/billing-address",e.SHIPPING_METHODS="wc/checkout/shipping-methods",e.CHECKOUT_ACTIONS="wc/checkout/checkout-actions",e.ORDER_INFORMATION="wc/checkout/order-information",e}({})},5954:(e,t,s)=>{"use strict";s.d(t,{k:()=>l});var r=s(7723),o=s(7143),n=s(7594),a=s(8537),c=s(1e3);if(/^(251|7949)$/.test(s.j))var i=s(5460);const l=(e="")=>{const{cartCoupons:t,cartIsLoading:s}=(0,i.V)(),{createErrorNotice:l}=(0,o.useDispatch)("core/notices"),{createNotice:d}=(0,o.useDispatch)("core/notices"),{setValidationErrors:u}=(0,o.useDispatch)(n.validationStore),{isApplyingCoupon:p,isRemovingCoupon:m}=(0,o.useSelect)((e=>{const t=e(n.cartStore);return{isApplyingCoupon:t.isApplyingCoupon(),isRemovingCoupon:t.isRemovingCoupon()}})),{applyCoupon:h,removeCoupon:g}=(0,o.useDispatch)(n.cartStore),_=(0,o.useSelect)((e=>e(n.checkoutStore).getOrderId()));return{appliedCoupons:t,isLoading:s,applyCoupon:t=>h(t).then((()=>((0,c.applyCheckoutFilter)({filterName:"showApplyCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&d("info",(0,r.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,r.__)('Coupon code "%s" has been applied to your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((e=>{const t=(e=>_&&_>0&&e?.data?.details?.checkout?e.data.details.checkout:e?.data?.details?.cart?e.data.details.cart:e.message)(e);return u({coupon:{message:(0,a.decodeEntities)(t),hidden:!1}}),Promise.resolve(!1)})),removeCoupon:t=>g(t).then((()=>((0,c.applyCheckoutFilter)({filterName:"showRemoveCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&d("info",(0,r.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,r.__)('Coupon code "%s" has been removed from your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((t=>(l(t.message,{id:"coupon-form",context:e}),Promise.resolve(!1)))),isApplyingCoupon:p,isRemovingCoupon:m}}},6037:(e,t,s)=>{"use strict";s.d(t,{U:()=>u});var r=s(6087),o=s(7594),n=s(7143);if(/^(251|2895|7949)$/.test(s.j))var a=s(1174);if(/^(251|2895|7949)$/.test(s.j))var c=s(3757);const i=e=>{const t=e?.detail;t&&t.preserveCartData||(0,n.dispatch)(o.cartStore).invalidateResolutionForStore()},l=e=>{(e?.persisted||"back_forward"===(0,a.F)())&&(0,n.dispatch)(o.cartStore).invalidateResolutionForStore()},d=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},u=()=>{(0,r.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),window.wcBlocksStoreCartListeners?.count>0)return void window.wcBlocksStoreCartListeners.count++;document.body.addEventListener("wc-blocks_added_to_cart",i),document.body.addEventListener("wc-blocks_removed_from_cart",i),window.addEventListener("pageshow",l);const e=(0,c.f2)("added_to_cart","wc-blocks_added_to_cart"),t=(0,c.f2)("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",i),document.body.removeEventListener("wc-blocks_removed_from_cart",i),window.removeEventListener("pageshow",l),e(),t()}})(),d)),[])}},5460:(e,t,s)=>{"use strict";s.d(t,{V:()=>_});var r=s(1824),o=s.n(r),n=s(6087),a=s(7594),c=s(7143),i=s(8537);if(/^(251|2895|7949)$/.test(s.j))var l=s(4982);if(/^(251|2895|7949)$/.test(s.j))var d=s(6037);const u={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},p={...u,email:""},m={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:a.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},h=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,i.decodeEntities)(t)]))),g={cartCoupons:a.EMPTY_CART_COUPONS,cartItems:a.EMPTY_CART_ITEMS,cartFees:a.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:a.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:a.EMPTY_CART_ITEM_ERRORS,cartTotals:m,cartIsLoading:!0,cartErrors:a.EMPTY_CART_ERRORS,billingData:p,billingAddress:p,shippingAddress:u,shippingRates:a.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:a.EMPTY_PAYMENT_METHODS,paymentRequirements:a.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:a.EMPTY_EXTENSIONS},_=(e={shouldSelect:!0})=>{const{shouldSelect:t}=e,s=(0,n.useRef)(),r=(0,n.useRef)(p),i=(0,n.useRef)(u);(0,d.U)();const m=(0,c.useSelect)(((e,{dispatch:s})=>{if(!t)return g;const n=e(a.cartStore),c=n.getCartData(),d=n.getCartErrors(),u=n.getCartTotals(),p=!n.hasFinishedResolution("getCartData"),m=n.isCustomerDataUpdating(),{receiveCart:_,receiveCartContents:v}=s(a.cartStore),b=c.fees.length>0?c.fees.map((e=>h(e))):a.EMPTY_CART_FEES,f=c.coupons.length>0?c.coupons.map((e=>({...e,label:e.code}))):a.EMPTY_CART_COUPONS,y=(0,l.TU)(h(c.billingAddress)),k=c.needsShipping?(0,l.TU)(h(c.shippingAddress)):y;return o()(y,r.current)||(r.current=y),o()(k,i.current)||(i.current=k),{cartCoupons:f,cartItems:c.items,crossSellsProducts:c.crossSells,cartFees:b,cartItemsCount:c.itemsCount,cartItemsWeight:c.itemsWeight,cartNeedsPayment:c.needsPayment,cartNeedsShipping:c.needsShipping,cartItemErrors:c.errors,cartTotals:u,cartIsLoading:p,cartErrors:d,billingData:r.current,billingAddress:r.current,shippingAddress:i.current,extensions:c.extensions,shippingRates:c.shippingRates,isLoadingRates:m,cartHasCalculatedShipping:c.hasCalculatedShipping,paymentRequirements:c.paymentRequirements,receiveCart:_,receiveCartContents:v}}),[t]);return s.current&&o()(s.current,m)||(s.current=m),s.current}},6314:(e,t,s)=>{"use strict";s.d(t,{Y:()=>T});var r=s(7723),o=s(910),n=s(6087),a=s(4921),c=s(5573),i=s(790);const l=(0,i.jsx)(c.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsxs)("g",{fill:"none",fillRule:"evenodd",children:[(0,i.jsx)("path",{d:"M0 0h24v24H0z"}),(0,i.jsx)("path",{fill:"#000",fillRule:"nonzero",d:"M17.3 8v1c1 .2 1.4.9 1.4 1.7h-1c0-.6-.3-1-1-1-.8 0-1.3.4-1.3.9 0 .4.3.6 1.4 1 1 .2 2 .6 2 1.9 0 .9-.6 1.4-1.5 1.5v1H16v-1c-.9-.1-1.6-.7-1.7-1.7h1c0 .6.4 1 1.3 1 1 0 1.2-.5 1.2-.8 0-.4-.2-.8-1.3-1.1-1.3-.3-2.1-.8-2.1-1.8 0-.9.7-1.5 1.6-1.6V8h1.3zM12 10v1H6v-1h6zm2-2v1H6V8h8zM2 4v16h20V4H2zm2 14V6h16v12H4z"}),(0,i.jsx)("path",{stroke:"#000",strokeLinecap:"round",d:"M6 16c2.6 0 3.9-3 1.7-3-2 0-1 3 1.5 3 1 0 1-.8 2.8-.8"})]})});var d=s(6600),u=s(8486),p=s(6208),m=s(4530),h=s(3993);s(9287);const g={bank:d.A,bill:u.A,card:p.A,checkPayment:l},_=({icon:e="",text:t=""})=>{const s=!!e,r=(0,n.useCallback)((e=>s&&(0,h.isString)(e)&&(0,h.objectHasProp)(g,e)),[s]),o=(0,a.A)("wc-block-components-payment-method-label",{"wc-block-components-payment-method-label--with-icon":s});return(0,i.jsxs)("span",{className:o,children:[r(e)?(0,i.jsx)(m.A,{icon:g[e]}):e,t]})};var v=s(6151),b=s(5703),f=s(4040),y=s.n(f),k=s(3575),w=s(7143),x=s(7594),j=s(2679),S=s(4656),C=s(5460),E=s(5954),A=s(8696),N=s(3586),P=s(7288),R=s(7394),I=s(7428),M=s(6473);const T=()=>{const{onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutAfterProcessingWithSuccess:s,onCheckoutAfterProcessingWithError:a,onSubmit:c}=(0,N.E)(),{onCheckoutValidation:i,onCheckoutSuccess:l,onCheckoutFail:d}=j.checkoutEvents,{isCalculating:u,isComplete:p,isIdle:m,isProcessing:g,customerId:f}=(0,w.useSelect)((e=>{const t=e(x.checkoutStore);return{isComplete:t.isComplete(),isIdle:t.isIdle(),isProcessing:t.isProcessing(),customerId:t.getCustomerId(),isCalculating:t.isCalculating()}})),{paymentStatus:T,activePaymentMethod:$,shouldSavePayment:O}=(0,w.useSelect)((e=>{const t=e(x.paymentStore);return{paymentStatus:{get isPristine(){return y()("isPristine",{since:"9.6.0",alternative:"isIdle",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentIdle()},isIdle:t.isPaymentIdle(),isStarted:t.isExpressPaymentStarted(),isProcessing:t.isPaymentProcessing(),get isFinished(){return y()("isFinished",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()||t.isPaymentReady()},hasError:t.hasPaymentError(),get hasFailed(){return y()("hasFailed",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()},get isSuccessful(){return y()("isSuccessful",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentReady()},isReady:t.isPaymentReady(),isDoingExpressPayment:t.isExpressPaymentMethodActive()},activePaymentMethod:t.getActivePaymentMethod(),shouldSavePayment:t.getShouldSavePaymentMethod()}})),{__internalSetExpressPaymentError:D}=(0,w.useDispatch)(x.paymentStore),{onPaymentProcessing:L,onPaymentSetup:F}=(0,P.e)(),{shippingErrorStatus:V,shippingErrorTypes:B,onShippingRateSuccess:H,onShippingRateFail:U,onShippingRateSelectSuccess:q,onShippingRateSelectFail:W}=(0,R.H)(),{shippingRates:z,isLoadingRates:G,selectedRates:Y,isSelectingRate:Z,selectShippingRate:K,needsShipping:Q}=(0,M.m)(),{billingAddress:J,shippingAddress:X}=(0,w.useSelect)((e=>e(x.cartStore).getCustomerData())),{setShippingAddress:ee}=(0,w.useDispatch)(x.cartStore),{cartItems:te,cartFees:se,cartTotals:re,extensions:oe}=(0,C.V)(),{appliedCoupons:ne}=(0,E.k)(),ae=(0,n.useRef)((0,I.G)(re,Q)),ce=(0,n.useRef)({label:(0,r.__)("Total","woocommerce"),value:parseInt(re.total_price,10)});(0,n.useEffect)((()=>{ae.current=(0,I.G)(re,Q),ce.current={label:(0,r.__)("Total","woocommerce"),value:parseInt(re.total_price,10)}}),[re,Q]);const ie=(0,n.useCallback)(((e="")=>{y()("setExpressPaymentError should only be used by Express Payment Methods (using the provided onError handler).",{alternative:"",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),D(e)}),[D]);return{activePaymentMethod:$,billing:{appliedCoupons:ne,billingAddress:J,billingData:J,cartTotal:ce.current,cartTotalItems:ae.current,currency:(0,o.getCurrencyFromPriceResponse)(re),customerId:f,displayPricesIncludingTax:(0,b.getSetting)("displayCartPricesIncludingTax",!1)},cartData:{cartItems:te,cartFees:se,extensions:oe},checkoutStatus:{isCalculating:u,isComplete:p,isIdle:m,isProcessing:g},components:{LoadingMask:k.A,PaymentMethodIcons:v.h,PaymentMethodLabel:_,ValidationInputError:S.ValidationInputError},emitResponse:{noticeContexts:A.tG,responseTypes:h.responseTypes},eventRegistration:{onCheckoutAfterProcessingWithError:a,onCheckoutAfterProcessingWithSuccess:s,onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutSuccess:l,onCheckoutFail:d,onCheckoutValidation:i,onPaymentProcessing:L,onPaymentSetup:F,onShippingRateFail:U,onShippingRateSelectFail:W,onShippingRateSelectSuccess:q,onShippingRateSuccess:H},onSubmit:c,paymentStatus:T,setExpressPaymentError:ie,shippingData:{isSelectingRate:Z,needsShipping:Q,selectedRates:Y,setSelectedRates:K,setShippingAddress:ee,shippingAddress:X,shippingRates:z,shippingRatesLoading:G},shippingStatus:{shippingErrorStatus:V,shippingErrorTypes:B},shouldSavePayment:O}}},8814:(e,t,s)=>{"use strict";if(s.d(t,{m:()=>i,u:()=>l}),/^(251|2895|7949)$/.test(s.j))var r=s(4556);var o=s(4083),n=s(7143),a=s(7594);const c=(e=!1)=>{const{paymentMethodsInitialized:t,expressPaymentMethodsInitialized:s,availablePaymentMethods:c,availableExpressPaymentMethods:i}=(0,n.useSelect)((e=>{const t=e(a.paymentStore);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),l=Object.values(c).map((({name:e})=>e)),d=Object.values(i).map((({name:e})=>e)),u=(0,o.getPaymentMethods)(),p=(0,o.getExpressPaymentMethods)(),m=Object.keys(u).reduce(((e,t)=>(l.includes(t)&&(e[t]=u[t]),e)),{}),h=Object.keys(p).reduce(((e,t)=>(d.includes(t)&&(e[t]=p[t]),e)),{}),g=(0,r.c)(m),_=(0,r.c)(h);return{paymentMethods:e?_:g,isInitialized:e?s:t}},i=()=>c(!1),l=()=>c(!0)},7428:(e,t,s)=>{"use strict";s.d(t,{G:()=>n});var r=s(7723),o=s(3993);const n=(e,t)=>{const s=[],n=(t,s)=>{const r=s+"_tax",n=(0,o.objectHasProp)(e,s)&&(0,o.isString)(e[s])?parseInt(e[s],10):0;return{key:s,label:t,value:n,valueWithTax:n+((0,o.objectHasProp)(e,r)&&(0,o.isString)(e[r])?parseInt(e[r],10):0)}};return s.push(n((0,r.__)("Subtotal:","woocommerce"),"total_items")),s.push(n((0,r.__)("Fees:","woocommerce"),"total_fees")),s.push(n((0,r.__)("Discount:","woocommerce"),"total_discount")),s.push({key:"total_tax",label:(0,r.__)("Taxes:","woocommerce"),value:parseInt(e.total_tax,10),valueWithTax:parseInt(e.total_tax,10)}),t&&s.push(n((0,r.__)("Shipping:","woocommerce"),"total_shipping")),s}},6473:(e,t,s)=>{"use strict";s.d(t,{m:()=>p});var r=s(7594),o=s(7143),n=s(3993),a=s(6087);if(/^(251|7949)$/.test(s.j))var c=s(3932);if(/^(251|7949)$/.test(s.j))var i=s(8522);var l=s(923),d=s.n(l);if(/^(251|7949)$/.test(s.j))var u=s(7052);const p=()=>{const{shippingRates:e,needsShipping:t,hasCalculatedShipping:s,isLoadingRates:l,isCollectable:p,isSelectingRate:m}=(0,o.useSelect)((e=>{const t=e(r.cartStore),s=t.getShippingRates();return{shippingRates:s,needsShipping:t.getNeedsShipping(),hasCalculatedShipping:t.getHasCalculatedShipping(),isLoadingRates:t.isCustomerDataUpdating(),isCollectable:s.every((({shipping_rates:e})=>e.find((({method_id:e})=>(0,c.jV)(e))))),isSelectingRate:t.isShippingRateBeingSelected()}}),[]),h=(0,a.useRef)({});(0,a.useEffect)((()=>{const t=(0,i.k)(e);(0,n.isObject)(t)&&!d()(h.current,t)&&(h.current=t)}),[e]);const{selectShippingRate:g}=(0,o.useDispatch)(r.cartStore),_=(0,c.jV)(Object.values(h.current).map((e=>e.split(":")[0]))),{dispatchCheckoutEvent:v}=(0,u.y)(),b=(0,a.useCallback)(((e,t)=>{let s;void 0!==e&&(s=(0,c.jV)(e.split(":")[0])?g(e,null):g(e,t),s.then((()=>{v("set-selected-shipping-rate",{shippingRateId:e})})).catch((e=>{(0,r.processErrorResponse)(e)})))}),[g,v]);return{isSelectingRate:m,selectedRates:h.current,selectShippingRate:b,shippingRates:e,needsShipping:t,hasCalculatedShipping:s,isLoadingRates:l,isCollectable:p,hasSelectedLocalPickup:_}}},7792:(e,t,s)=>{"use strict";s.d(t,{C:()=>d});var r=s(5703),o=s(6087),n=s(7143),a=s(7594);if(/^(251|7949)$/.test(s.j))var c=s(9702);if(/^(251|7949)$/.test(s.j))var i=s(6473);if(/^(251|7949)$/.test(s.j))var l=s(7370);const d=()=>{const{isEditor:e,getPreviewData:t}=(0,l.m)(),{needsShipping:s}=(0,i.m)(),{useShippingAsBilling:d,prefersCollection:u,editingBillingAddress:p,editingShippingAddress:m}=(0,n.useSelect)((e=>({useShippingAsBilling:e(a.checkoutStore).getUseShippingAsBilling(),prefersCollection:e(a.checkoutStore).prefersCollection(),editingBillingAddress:e(a.checkoutStore).getEditingBillingAddress(),editingShippingAddress:e(a.checkoutStore).getEditingShippingAddress()}))),{__internalSetUseShippingAsBilling:h,setEditingBillingAddress:g,setEditingShippingAddress:_}=(0,n.useDispatch)(a.checkoutStore),{billingAddress:v,setBillingAddress:b,shippingAddress:f,setShippingAddress:y}=(0,c.q)(),k=(0,o.useCallback)((e=>{b({email:e})}),[b]),w=(0,r.getSetting)("forcedBillingAddress",!1);return{shippingAddress:f,billingAddress:v,setShippingAddress:y,setBillingAddress:b,setEmail:k,defaultFields:e?t("defaultFields",r.defaultFields):r.defaultFields,useShippingAsBilling:d,setUseShippingAsBilling:h,editingBillingAddress:p,editingShippingAddress:m,setEditingBillingAddress:g,setEditingShippingAddress:_,needsShipping:s,showShippingFields:!w&&s&&!u,showShippingMethods:s&&!u,showBillingFields:!s||!d||!!u,forcedBillingAddress:w,useBillingAsShipping:w||!!u}}},2098:(e,t,s)=>{"use strict";s.d(t,{v:()=>a});var r=s(7143),o=s(6087),n=s(7594);const a=()=>{const{setExtensionData:e}=(0,r.useDispatch)(n.checkoutStore),t=(0,r.useSelect)((e=>e(n.checkoutStore).getExtensionData())),s=(0,o.useRef)(t),a=(0,o.useCallback)(((t,s,r)=>{e(t,{[s]:r})}),[e]);return{extensionData:s.current,setExtensionData:a}}},4908:(e,t,s)=>{"use strict";s.d(t,{w:()=>c});var r=s(7594),o=s(7143);if(251==s.j)var n=s(3586);if(251==s.j)var a=s(8814);const c=()=>{const{isCalculating:e,isBeforeProcessing:t,isProcessing:s,isAfterProcessing:c,isComplete:i,hasError:l}=(0,o.useSelect)((e=>{const t=e(r.checkoutStore);return{isCalculating:t.isCalculating(),isBeforeProcessing:t.isBeforeProcessing(),isProcessing:t.isProcessing(),isAfterProcessing:t.isAfterProcessing(),isComplete:t.isComplete(),hasError:t.hasError()}})),{activePaymentMethod:d,isExpressPaymentMethodActive:u}=(0,o.useSelect)((e=>{const t=e(r.paymentStore);return{activePaymentMethod:t.getActivePaymentMethod(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive()}})),{onSubmit:p}=(0,n.E)(),{paymentMethods:m={}}=(0,a.m)(),h=s||c||t,g=i&&!l;return{paymentMethodButtonLabel:(m[d]||{}).placeOrderButtonLabel,onSubmit:p,isCalculating:e,isDisabled:s||u,waitingForProcessing:h,waitingForRedirect:g}}},9702:(e,t,s)=>{"use strict";s.d(t,{q:()=>n});var r=s(7143),o=s(7594);const n=()=>{const{customerData:e,isInitialized:t}=(0,r.useSelect)((e=>{const t=e(o.cartStore);return{customerData:t.getCustomerData(),isInitialized:t.hasFinishedResolution("getCartData")}})),{setShippingAddress:s,setBillingAddress:n}=(0,r.useDispatch)(o.cartStore);return{isInitialized:t,billingAddress:e.billingAddress,shippingAddress:e.shippingAddress,setBillingAddress:n,setShippingAddress:s}}},2537:(e,t,s)=>{"use strict";s.d(t,{Z:()=>d});var r=s(7143),o=s(7594),n=s(6087),a=s(7723);if(251==s.j)var c=s(3932);var i=s(692);if(251==s.j)var l=s(6473);const d=()=>{const e="woocommerce/checkout-totals-block",t="wc-blocks-totals-shipping-warning",{shippingRates:s,hasSelectedLocalPickup:d}=(0,l.m)(),u=(0,c.HI)(s),{prefersCollection:p,isRateBeingSelected:m,shippingNotices:h}=(0,r.useSelect)((t=>({prefersCollection:t(o.checkoutStore).prefersCollection(),isRateBeingSelected:t(o.cartStore).isShippingRateBeingSelected(),shippingNotices:t(i.store).getNotices(e)})),[]),{createInfoNotice:g,removeNotice:_}=(0,r.useDispatch)(i.store);(0,n.useEffect)((()=>{const s=h.length>0&&h.some((e=>e.id===t)),r=!p&&d;u&&!m?!r||s?!r&&s&&_(t,e):g((0,a.__)("Totals will be recalculated when a valid shipping method is selected.","woocommerce"),{id:"wc-blocks-totals-shipping-warning",isDismissible:!1,context:e}):s&&_(t,e)}),[d,g,u,m,p,_,h,s])}},1057:(e,t,s)=>{"use strict";s.d(t,{R:()=>l});var r=s(6087),o=s(7143),n=s(7594),a=s(8537);if(7949==s.j)var c=s(5460);const i=(e,t)=>{const s=e.find((({id:e})=>e===t));return s?s.quantity:0},l=e=>{const{addItemToCart:t}=(0,o.useDispatch)(n.cartStore),{cartItems:s,cartIsLoading:l}=(0,c.V)(),{createErrorNotice:d,removeNotice:u}=(0,o.useDispatch)("core/notices"),[p,m]=(0,r.useState)(!1),h=(0,r.useRef)(i(s,e));return(0,r.useEffect)((()=>{const t=i(s,e);t!==h.current&&(h.current=t)}),[s,e]),{cartQuantity:Number.isFinite(h.current)?h.current:0,addingToCart:p,cartIsLoading:l,addToCart:(s=1)=>(m(!0),t(e,s).then((()=>{u("add-to-cart")})).catch((e=>{d((0,a.decodeEntities)(e.message),{id:"add-to-cart",context:"wc/all-products",isDismissible:!0})})).finally((()=>{m(!1)})))}}},7052:(e,t,s)=>{"use strict";s.d(t,{y:()=>a});var r=s(2619),o=s(7143),n=s(6087);const a=()=>({dispatchStoreEvent:(0,n.useCallback)(((e,t={})=>{try{(0,r.doAction)(`experimental__woocommerce_blocks-${e}`,t)}catch(e){console.error(e)}}),[]),dispatchCheckoutEvent:(0,n.useCallback)(((e,t={})=>{try{(0,r.doAction)(`experimental__woocommerce_blocks-checkout-${e}`,{...t,storeCart:(0,o.select)("wc/store/cart").getCartData()})}catch(e){console.error(e)}}),[])})},6674:(e,t,s)=>{"use strict";s.d(t,{$:()=>a});var r=s(6087),o=s(7143),n=s(7594);const a=()=>{const{clearValidationError:e,hideValidationError:t,setValidationErrors:s}=(0,o.useDispatch)(n.validationStore),a="extensions-errors",{hasValidationErrors:c,getValidationError:i}=(0,o.useSelect)((e=>{const t=e(n.validationStore);return{hasValidationErrors:t.hasValidationErrors(),getValidationError:e=>t.getValidationError(`${a}-${e}`)}}));return{hasValidationErrors:c,getValidationError:i,clearValidationError:(0,r.useCallback)((t=>e(`${a}-${t}`)),[e]),hideValidationError:(0,r.useCallback)((e=>t(`${a}-${e}`)),[t]),setValidationErrors:(0,r.useCallback)((e=>s(Object.fromEntries(Object.entries(e).map((([e,t])=>[`${a}-${e}`,t]))))),[s])}}},3390:(e,t,s)=>{"use strict";s.d(t,{nE:()=>a,qY:()=>n});var r=s(6087);if(/^(2895|7949)$/.test(s.j))var o=s(9015);const n={PROCEED_TO_CHECKOUT:"cart_proceed_to_checkout"},a=e=>(0,r.useMemo)((()=>({onProceedToCheckout:(0,o.Y)(n.PROCEED_TO_CHECKOUT,e)})),[e])},3224:(e,t,s)=>{"use strict";s.d(t,{e:()=>l,r:()=>d});var r=s(6087);if(/^(2895|7949)$/.test(s.j))var o=s(9760);if(/^(2895|7949)$/.test(s.j))var n=s(3390);if(/^(2895|7949)$/.test(s.j))var a=s(9162);var c=s(790);const i=(0,r.createContext)({onProceedToCheckout:()=>()=>{},dispatchOnProceedToCheckout:()=>new Promise((()=>{}))}),l=()=>(0,r.useContext)(i),d=({children:e})=>{const[t,s]=(0,r.useReducer)(o.F,{}),l=(0,r.useRef)(t),{onProceedToCheckout:d}=(0,n.nE)(s);(0,r.useEffect)((()=>{l.current=t}),[t]);const u={onProceedToCheckout:d,dispatchOnProceedToCheckout:async()=>await(0,a._)(l.current,n.qY.PROCEED_TO_CHECKOUT,null)};return(0,c.jsx)(i.Provider,{value:u,children:e})}},3056:(e,t,s)=>{"use strict";if(s.d(t,{e:()=>n}),7949==s.j)var r=s(6287);var o=s(790);const n=({children:e,redirectUrl:t})=>(0,o.jsx)(r.s,{redirectUrl:t,children:e})},3586:(e,t,s)=>{"use strict";s.d(t,{E:()=>b,H:()=>f});var r=s(6087);if(/^(251|7949)$/.test(s.j))var o=s(9464);var n=s(4040),a=s.n(n),c=s(7143),i=s(7594),l=s(692),d=s(2679);if(/^(251|7949)$/.test(s.j))var u=s(9760);if(/^(251|7949)$/.test(s.j))var p=s(8696);if(/^(251|7949)$/.test(s.j))var m=s(7052);if(/^(251|7949)$/.test(s.j))var h=s(9996);if(/^(251|7949)$/.test(s.j))var g=s(7370);var _=s(790);const v=(0,r.createContext)({onSubmit:()=>{},onCheckoutAfterProcessingWithSuccess:()=>()=>{},onCheckoutAfterProcessingWithError:()=>()=>{},onCheckoutBeforeProcessing:()=>()=>{},onCheckoutValidationBeforeProcessing:()=>()=>{},onCheckoutSuccess:()=>()=>{},onCheckoutFail:()=>()=>{},onCheckoutValidation:()=>()=>{}}),b=()=>(0,r.useContext)(v),f=({children:e,redirectUrl:t})=>{const s=(0,h.J5)(),n=(0,h.fD)(),{isEditor:b}=(0,g.m)(),{__internalUpdateAvailablePaymentMethods:f}=(0,c.useDispatch)(i.paymentStore);(0,r.useEffect)((()=>{(b||0!==Object.keys(s).length||0!==Object.keys(n).length)&&f()}),[b,s,n,f]);const{__internalSetRedirectUrl:y,__internalEmitValidateEvent:k,__internalEmitAfterProcessingEvents:w,__internalSetBeforeProcessing:x}=(0,c.useDispatch)(i.checkoutStore),{checkoutRedirectUrl:j,checkoutStatus:S,isCheckoutBeforeProcessing:C,isCheckoutAfterProcessing:E,checkoutHasError:A,checkoutOrderId:N,checkoutOrderNotes:P,checkoutCustomerId:R}=(0,c.useSelect)((e=>{const t=e(i.checkoutStore);return{checkoutRedirectUrl:t.getRedirectUrl(),checkoutStatus:t.getCheckoutStatus(),isCheckoutBeforeProcessing:t.isBeforeProcessing(),isCheckoutAfterProcessing:t.isAfterProcessing(),checkoutHasError:t.hasError(),checkoutOrderId:t.getOrderId(),checkoutOrderNotes:t.getOrderNotes(),checkoutCustomerId:t.getCustomerId()}}));t&&t!==j&&y(t);const{setValidationErrors:I}=(0,c.useDispatch)(i.validationStore),{dispatchCheckoutEvent:M}=(0,m.y)(),T=Object.values(p.tG).filter((e=>e!==p.tG.PAYMENTS&&e!==p.tG.EXPRESS_PAYMENTS)),$=(0,c.useSelect)((e=>{const{getNotices:t}=e(l.store);return T.reduce(((e,s)=>[...e,...t(s)]),[])}),[T]),{paymentNotices:O,expressPaymentNotices:D}=(0,c.useSelect)((e=>{const{getNotices:t}=e(l.store);return{paymentNotices:t(p.tG.PAYMENTS),expressPaymentNotices:t(p.tG.EXPRESS_PAYMENTS)}}),[]),[L]=(0,r.useReducer)(u.F,{}),F=(0,r.useRef)(L),{onCheckoutValidation:V,onCheckoutSuccess:B,onCheckoutFail:H}=d.checkoutEvents;(0,r.useEffect)((()=>{F.current=L}),[L]);const U=(0,r.useMemo)((()=>function(...e){return a()("onCheckoutBeforeProcessing",{alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks"}),V(...e)}),[V]),q=(0,r.useMemo)((()=>function(...e){return a()("onCheckoutValidationBeforeProcessing",{since:"9.7.0",alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),V(...e)}),[V]),W=(0,r.useMemo)((()=>function(...e){return a()("onCheckoutAfterProcessingWithSuccess",{since:"9.7.0",alternative:"onCheckoutSuccess",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),B(...e)}),[B]),z=(0,r.useMemo)((()=>function(...e){return a()("onCheckoutAfterProcessingWithError",{since:"9.7.0",alternative:"onCheckoutFail",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),H(...e)}),[H]);(0,r.useEffect)((()=>{C&&k({setValidationErrors:I})}),[C,I,k]);const G=(0,o.Z)(S),Y=(0,o.Z)(A);(0,r.useEffect)((()=>{S===G&&A===Y||E&&w({notices:{checkoutNotices:$,paymentNotices:O,expressPaymentNotices:D}})}),[S,A,j,N,R,P,E,C,G,Y,$,D,O,k,w]);const Z={onSubmit:(0,r.useCallback)((()=>{M("submit"),x()}),[M,x]),onCheckoutBeforeProcessing:U,onCheckoutValidationBeforeProcessing:q,onCheckoutAfterProcessingWithSuccess:W,onCheckoutAfterProcessingWithError:z,onCheckoutSuccess:B,onCheckoutFail:H,onCheckoutValidation:V};return(0,_.jsx)(v.Provider,{value:Z,children:e})}},3822:(e,t,s)=>{"use strict";s.d(t,{A:()=>b});var r=s(7723),o=s(1455),n=s.n(o),a=s(6087);if(/^(251|7949)$/.test(s.j))var c=s(1233);if(/^(251|7949)$/.test(s.j))var i=s(4982);var l=s(7143),d=s(7594),u=s(4083),p=s(3993),m=s(2679);if(/^(251|7949)$/.test(s.j))var h=s(8954);if(/^(251|7949)$/.test(s.j))var g=s(7394);if(/^(251|7949)$/.test(s.j))var _=s(5460);if(/^(251|7949)$/.test(s.j))var v=s(7792);const b=/^(251|7949)$/.test(s.j)?()=>{const{onCheckoutValidation:e}=m.checkoutEvents,{additionalFields:t,customerId:s,customerPassword:o,extensionData:b,hasError:f,isBeforeProcessing:y,isComplete:k,isProcessing:w,orderNotes:x,redirectUrl:j,shouldCreateAccount:S}=(0,l.useSelect)((e=>{const t=e(d.checkoutStore);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId(),customerPassword:t.getCustomerPassword(),extensionData:t.getExtensionData(),hasError:t.hasError(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),isProcessing:t.isProcessing(),orderNotes:t.getOrderNotes(),redirectUrl:t.getRedirectUrl(),shouldCreateAccount:t.getShouldCreateAccount()}}),[]),{__internalSetHasError:C,__internalProcessCheckoutResponse:E}=(0,l.useDispatch)(d.checkoutStore),A=(0,l.useSelect)((e=>e(d.validationStore).hasValidationErrors),[]),{shippingErrorStatus:N}=(0,g.H)(),{shippingAddress:P,billingAddress:R,useBillingAsShipping:I}=(0,v.C)(),{cartNeedsPayment:M,cartNeedsShipping:T,receiveCartContents:$}=(0,_.V)(),{activePaymentMethod:O,paymentMethodData:D,isExpressPaymentMethodActive:L,hasPaymentError:F,isPaymentReady:V,shouldSavePayment:B}=(0,l.useSelect)((e=>{const t=e(d.paymentStore);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),hasPaymentError:t.hasPaymentError(),isPaymentReady:t.isPaymentReady(),shouldSavePayment:t.getShouldSavePaymentMethod()}}),[]),H=(0,u.getPaymentMethods)(),U=(0,u.getExpressPaymentMethods)(),q=(0,a.useRef)(R),W=(0,a.useRef)(P),z=(0,a.useRef)(j),[G,Y]=(0,a.useState)(!1),Z=(0,a.useMemo)((()=>{const e={...U,...H};return e?.[O]?.paymentMethodId}),[O,U,H]),K=A()&&!L||F||N.hasError,Q=!f&&!K&&(V||!M)&&w;(0,a.useEffect)((()=>{K===f||!w&&!y||L||C(K)}),[K,f,w,y,L,C]),(0,a.useEffect)((()=>{q.current=R,W.current=P,z.current=j}),[R,P,j]);const J=(0,a.useCallback)((()=>A()?void 0!==(0,l.select)(d.validationStore).getValidationError("shipping-rates-error")&&{type:p.responseTypes.ERROR,errorMessage:(0,r.__)("Sorry, this order requires a shipping option.","woocommerce")}:F?{type:p.responseTypes.ERROR,errorMessage:(0,r.__)("There was a problem with your payment option.","woocommerce"),context:"wc/checkout/payments"}:!N.hasError||{type:p.responseTypes.ERROR,errorMessage:(0,r.__)("There was a problem with your shipping option.","woocommerce"),context:"wc/checkout/shipping-methods"}),[A,F,N.hasError]);(0,a.useEffect)((()=>{let t;return L||(t=e(J,0)),()=>{L||"function"!=typeof t||t()}}),[e,J,L]),(0,a.useEffect)((()=>{window.localStorage.removeItem("WOOCOMMERCE_CHECKOUT_IS_CUSTOMER_DATA_DIRTY"),z.current&&(window.location.href=z.current)}),[k]);const X=(0,a.useCallback)((async()=>{if(G)return;Y(!0),(0,c.Jq)();const e=M?{payment_method:Z,payment_data:(0,h.s)(D,B,O)}:{},a=(0,i.TU)(q.current),l=I?a:(0,i.TU)(W.current),u={additional_fields:t,billing_address:a,create_account:S,customer_note:x,customer_password:o,extensions:{...b},shipping_address:T?l:void 0,...e};(0,d.clearCheckoutPutRequests)(),n()({path:"/wc/store/v1/checkout",method:"POST",data:u,cache:"no-store",parse:!1}).then((e=>{if((0,p.assertResponseIsValid)(e),(0,h.x)(e.headers),!e.ok)throw e;return e.json()})).then((e=>{E(e),Y(!1)})).catch((e=>{(0,h.x)(e?.headers);try{e.json().then((e=>e)).then((e=>{e.data?.cart&&$(e.data.cart),(0,d.processErrorResponse)(e),E(e)}))}catch{let e=(0,r.__)("Something went wrong when placing the order. Check your email for order updates before retrying.","woocommerce");0!==s&&(e=(0,r.__)("Something went wrong when placing the order. Check your account's order history or your email for order updates before retrying.","woocommerce")),(0,d.processErrorResponse)({code:"unknown_error",message:e,data:null})}C(!0),Y(!1)}))}),[G,M,Z,D,B,O,x,S,s,o,b,t,T,$,C,E,I]);return(0,a.useEffect)((()=>{Q&&!G&&X()}),[X,Q,G]),null}:null},6287:(e,t,s)=>{"use strict";s.d(t,{s:()=>u});var r=s(2279),o=s(5703),n=s(4328);if(/^(251|7949)$/.test(s.j))var a=s(7288);if(/^(251|7949)$/.test(s.j))var c=s(7394);if(/^(251|7949)$/.test(s.j))var i=s(3586);if(/^(251|7949)$/.test(s.j))var l=s(3822);var d=s(790);const u=({children:e,redirectUrl:t})=>(0,d.jsx)(i.H,{redirectUrl:t,children:(0,d.jsx)(c.o,{children:(0,d.jsxs)(a.n,{children:[e,(0,d.jsx)(n.A,{renderError:o.CURRENT_USER_IS_ADMIN?null:()=>null,children:(0,d.jsx)(r.PluginArea,{scope:"woocommerce-checkout"})}),(0,d.jsx)(l.A,{})]})})})},7134:(e,t,s)=>{"use strict";s.d(t,{nE:()=>n});var r=s(6087);if(/^(251|2895|7949)$/.test(s.j))var o=s(9015);const n=e=>(0,r.useMemo)((()=>({onPaymentSetup:(0,o.Y)("payment_setup",e)})),[e])},7288:(e,t,s)=>{"use strict";s.d(t,{e:()=>p,n:()=>m});var r=s(6087),o=s(7143),n=s(7594),a=s(4040),c=s.n(a);if(/^(251|2895|7949)$/.test(s.j))var i=s(9760);if(/^(251|2895|7949)$/.test(s.j))var l=s(7134);var d=s(790);const u=(0,r.createContext)({onPaymentProcessing:()=>()=>()=>{},onPaymentSetup:()=>()=>()=>{}}),p=()=>(0,r.useContext)(u),m=({children:e})=>{const{isProcessing:t,isIdle:s,isCalculating:a,hasError:p}=(0,o.useSelect)((e=>{const t=e(n.checkoutStore);return{isProcessing:t.isProcessing(),isIdle:t.isIdle(),hasError:t.hasError(),isCalculating:t.isCalculating()}})),{isPaymentReady:m}=(0,o.useSelect)((e=>{const t=e(n.paymentStore);return{isPaymentProcessing:t.isPaymentProcessing(),isPaymentReady:t.isPaymentReady()}})),{setValidationErrors:h}=(0,o.useDispatch)(n.validationStore),[g,_]=(0,r.useReducer)(i.F,{}),{onPaymentSetup:v}=(0,l.nE)(_),b=(0,r.useRef)(g);(0,r.useEffect)((()=>{b.current=g}),[g]);const{__internalSetPaymentProcessing:f,__internalSetPaymentIdle:y,__internalEmitPaymentProcessingEvent:k}=(0,o.useDispatch)(n.paymentStore);(0,r.useEffect)((()=>{!t||p||a||(f(),k(b.current,h))}),[t,p,a,f,k,h]),(0,r.useEffect)((()=>{s&&!m&&y()}),[s,m,y]),(0,r.useEffect)((()=>{p&&m&&y()}),[p,m,y]);const w={onPaymentProcessing:(0,r.useMemo)((()=>function(...e){return c()("onPaymentProcessing",{alternative:"onPaymentSetup",plugin:"WooCommerce Blocks"}),v(...e)}),[v]),onPaymentSetup:v};return(0,d.jsx)(u.Provider,{value:w,children:e})}},3271:(e,t,s)=>{"use strict";s.d(t,{Kh:()=>n,LY:()=>r,dr:()=>o});const r={NONE:"none",INVALID_ADDRESS:"invalid_address",UNKNOWN:"unknown_error"},o={INVALID_COUNTRY:"woocommerce_rest_cart_shipping_rates_invalid_country",MISSING_COUNTRY:"woocommerce_rest_cart_shipping_rates_missing_country",INVALID_STATE:"woocommerce_rest_cart_shipping_rates_invalid_state"},n={shippingErrorStatus:{isPristine:!0,isValid:!1,hasInvalidAddress:!1,hasError:!1},dispatchErrorStatus:e=>e,shippingErrorTypes:r,onShippingRateSuccess:()=>()=>{},onShippingRateFail:()=>()=>{},onShippingRateSelectSuccess:()=>()=>{},onShippingRateSelectFail:()=>()=>{}}},306:(e,t,s)=>{"use strict";if(s.d(t,{U9:()=>n,Ww:()=>o}),/^(251|7949)$/.test(s.j))var r=s(9015);const o={SHIPPING_RATES_SUCCESS:"shipping_rates_success",SHIPPING_RATES_FAIL:"shipping_rates_fail",SHIPPING_RATE_SELECT_SUCCESS:"shipping_rate_select_success",SHIPPING_RATE_SELECT_FAIL:"shipping_rate_select_fail"},n=e=>({onSuccess:(0,r.Y)(o.SHIPPING_RATES_SUCCESS,e),onFail:(0,r.Y)(o.SHIPPING_RATES_FAIL,e),onSelectSuccess:(0,r.Y)(o.SHIPPING_RATE_SELECT_SUCCESS,e),onSelectFail:(0,r.Y)(o.SHIPPING_RATE_SELECT_FAIL,e)})},7394:(e,t,s)=>{"use strict";s.d(t,{H:()=>f,o:()=>y});var r=s(6087),o=s(7143),n=s(7594),a=s(3271);if(/^(251|7949)$/.test(s.j))var c=s(9497);if(/^(251|7949)$/.test(s.j))var i=s(5547);if(/^(251|7949)$/.test(s.j))var l=s(9760);if(/^(251|7949)$/.test(s.j))var d=s(306);if(/^(251|7949)$/.test(s.j))var u=s(9162);if(/^(251|7949)$/.test(s.j))var p=s(5460);if(/^(251|7949)$/.test(s.j))var m=s(6473);var h=s(790);const{NONE:g,INVALID_ADDRESS:_,UNKNOWN:v}=a.LY,b=(0,r.createContext)(a.Kh),f=()=>(0,r.useContext)(b),y=({children:e})=>{const{__internalStartCalculation:t,__internalFinishCalculation:s}=(0,o.useDispatch)(n.checkoutStore),{shippingRates:f,isLoadingRates:y,cartErrors:k}=(0,p.V)(),{selectedRates:w,isSelectingRate:x}=(0,m.m)(),[j,S]=(0,r.useReducer)(i.b,g),[C,E]=(0,r.useReducer)(l.F,{}),A=(0,r.useRef)(C),N=(0,r.useMemo)((()=>({onShippingRateSuccess:(0,d.U9)(E).onSuccess,onShippingRateFail:(0,d.U9)(E).onFail,onShippingRateSelectSuccess:(0,d.U9)(E).onSelectSuccess,onShippingRateSelectFail:(0,d.U9)(E).onSelectFail})),[E]);(0,r.useEffect)((()=>{A.current=C}),[C]),(0,r.useEffect)((()=>{y?t():s()}),[y,t,s]),(0,r.useEffect)((()=>{x?t():s()}),[t,s,x]),(0,r.useEffect)((()=>{k.length>0&&(0,c.S)(k)?S({type:_}):S({type:g})}),[k]);const P=(0,r.useMemo)((()=>({isPristine:j===g,isValid:j===g,hasInvalidAddress:j===_,hasError:j===v||j===_})),[j]);(0,r.useEffect)((()=>{y||0!==f.length&&!P.hasError||(0,u.c)(A.current,d.Ww.SHIPPING_RATES_FAIL,{hasInvalidAddress:P.hasInvalidAddress,hasError:P.hasError})}),[f,y,P.hasError,P.hasInvalidAddress]),(0,r.useEffect)((()=>{!y&&f.length>0&&!P.hasError&&(0,u.c)(A.current,d.Ww.SHIPPING_RATES_SUCCESS,f)}),[f,y,P.hasError]),(0,r.useEffect)((()=>{x||(P.hasError?(0,u.c)(A.current,d.Ww.SHIPPING_RATE_SELECT_FAIL,{hasError:P.hasError,hasInvalidAddress:P.hasInvalidAddress}):(0,u.c)(A.current,d.Ww.SHIPPING_RATE_SELECT_SUCCESS,w.current))}),[w,x,P.hasError,P.hasInvalidAddress]);const R={shippingErrorStatus:P,dispatchErrorStatus:S,shippingErrorTypes:a.LY,...N};return(0,h.jsx)(h.Fragment,{children:(0,h.jsx)(b.Provider,{value:R,children:e})})}},5547:(e,t,s)=>{"use strict";if(s.d(t,{b:()=>o}),/^(251|7949)$/.test(s.j))var r=s(3271);const o=(e,{type:t})=>Object.values(r.LY).includes(t)?t:e},9497:(e,t,s)=>{"use strict";if(s.d(t,{S:()=>o}),/^(251|7949)$/.test(s.j))var r=s(3271);const o=e=>e.some((e=>!(!e.code||!Object.values(r.dr).includes(e.code))))},8954:(e,t,s)=>{"use strict";s.d(t,{s:()=>c,x:()=>i});var r=s(1455),o=s.n(r),n=s(7143),a=s(7594);const c=(e,t,s)=>{const r=Object.keys(e).map((t=>({key:t,value:e[t]})),[]),o=`wc-${s}-new-payment-method`;return r.push({key:o,value:t}),r},i=e=>{if(!e)return;const{__internalSetCustomerId:t}=(0,n.dispatch)(a.checkoutStore);o().setNonce&&"function"==typeof o().setNonce&&o().setNonce(e),o().setCartHash&&"function"==typeof o().setCartHash&&o().setCartHash(e),e?.get("User-ID")&&t(parseInt(e.get("User-ID")||"0",10))}},4575:(e,t,s)=>{"use strict";s.d(t,{G:()=>i,u:()=>l});var r=s(6087);if(/^(251|7949)$/.test(s.j))var o=s(7543);if(/^(251|7949)$/.test(s.j))var n=s(4921);var a=s(790);const c=(0,r.createContext)({hasContainerWidth:!1,containerClassName:"",isMobile:!1,isSmall:!1,isMedium:!1,isLarge:!1}),i=()=>(0,r.useContext)(c),l=({children:e,className:t=""})=>{const[s,r]=(0,o.B)(),i={hasContainerWidth:""!==r,containerClassName:r,isMobile:"is-mobile"===r,isSmall:"is-small"===r,isMedium:"is-medium"===r,isLarge:"is-large"===r};return(0,a.jsx)(c.Provider,{value:i,children:(0,a.jsxs)("div",{className:(0,n.A)(t,r),children:[s,e]})})}},7370:(e,t,s)=>{"use strict";s.d(t,{m:()=>n});var r=s(6087);s(7143),s(790);const o=(0,r.createContext)({isEditor:!1,currentPostId:0,currentView:"",previewData:{},getPreviewData:()=>({})}),n=()=>(0,r.useContext)(o)},3023:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(6087),o=(s(7525),s(790));const n=e=>{if(!e)return;const t=e.getBoundingClientRect().bottom;t>=0&&t<=window.innerHeight||e.scrollIntoView()},a=/^(251|7949)$/.test(s.j)?e=>t=>{const s=(0,r.useRef)(null);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"with-scroll-to-top__scroll-point",ref:s,"aria-hidden":!0}),(0,o.jsx)(e,{...t,scrollToTop:e=>{null!==s.current&&((e,t)=>{const{focusableSelector:s}=t||{};window&&Number.isFinite(window.innerHeight)&&(s?((e,t)=>{const s=e.parentElement?.querySelectorAll(t)||[];if(s.length){const e=s[0];n(e),e?.focus()}else n(e)})(e,s):n(e))})(s.current,e)}})]})}:null},7543:(e,t,s)=>{"use strict";s.d(t,{B:()=>o});var r=s(9491);const o=()=>{const[e,{width:t}]=(0,r.useResizeObserver)();let s="";return t>700?s="is-large":t>520?s="is-medium":t>400?s="is-small":t&&(s="is-mobile"),[e,s]}},843:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(6087);function o(){const[e,t]=(0,r.useState)({height:0,width:0}),[s,o]=(0,r.useState)({height:0,width:0}),n=(0,r.useRef)(null);return(0,r.useEffect)((()=>{if(!n.current)return;const e=n.current,s=new ResizeObserver((s=>{s.forEach((s=>{if(s.target===e){let r="0";r=e.computedStyleMap?e.computedStyleMap().get("top")?.toString()||r:getComputedStyle(e).top||r;const{height:o,width:n}=s.contentRect;t({height:o+parseInt(r,10),width:n})}}))})),r=new IntersectionObserver((e=>{e.forEach((e=>{const{height:s,width:r}=e.boundingClientRect;t({height:s,width:r}),e.target.ownerDocument.defaultView&&o({height:e.target.ownerDocument.defaultView?.innerHeight,width:e.target.ownerDocument.defaultView?.innerWidth})}))}),{root:null,rootMargin:"0px",threshold:1});return s.observe(e),r.observe(e),()=>{e&&(s.unobserve(e),r.unobserve(e))}}),[]),[n,e,s]}},2754:(e,t,s)=>{"use strict";s.d(t,{E:()=>a});var r=s(6087),o=s(790);const n={bottom:0,left:0,opacity:0,pointerEvents:"none",position:"absolute",right:0,top:0,zIndex:-1},a=()=>{const[e,t]=(0,r.useState)(""),s=(0,r.useRef)(null),a=(0,r.useRef)(new IntersectionObserver((e=>{e[0].isIntersecting?t("visible"):t(e[0].boundingClientRect.top>0?"below":"above")}),{threshold:[0,.5,1]}));return(0,r.useLayoutEffect)((()=>{const e=s.current,t=a.current;return e&&t.observe(e),()=>{t.unobserve(e)}}),[]),[(0,o.jsx)("div",{"aria-hidden":!0,ref:s,style:n}),e]}},9464:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var r=s(6087);function o(e,t){const s=(0,r.useRef)();return(0,r.useEffect)((()=>{s.current===e||t&&!t(e,s.current)||(s.current=e)}),[e,t]),s.current}},6176:(e,t,s)=>{"use strict";s.d(t,{o:()=>l});var r=s(6087),o=s(7143);if(/^(251|7949)$/.test(s.j))var n=s(468);var a=s(1824),c=s.n(a),i=s(7594);const l=e=>{const t=(e=>{const t=(0,r.useRef)({cart:{},checkout:{},customer:{}}),s=(0,o.useSelect)((t=>{const s=t(i.cartStore),r=t(i.checkoutStore),o=t(i.paymentStore),a=s.getCartData(),{coupons:c,shippingRates:l,shippingAddress:d,billingAddress:u,items:p,itemsCount:m,itemsWeight:h,needsShipping:g,totals:_}=a,v={cart:{coupons:c.map((e=>e.code)),shippingRates:[...new Set(l.map((e=>e.shipping_rates.find((e=>e.selected))?.rate_id)).filter(Boolean))],items:p.map((e=>Array(e.quantity).fill(e.id))).flat(),itemsType:[...new Set(p.map((e=>e.type)))],itemsCount:m,itemsWeight:h,needsShipping:g,prefersCollection:"boolean"==typeof r.prefersCollection()&&r.prefersCollection(),totals:{totalPrice:Number(_.total_price),totalTax:Number(_.total_tax)},extensions:a.extensions},checkout:{createAccount:r.getShouldCreateAccount(),customerNote:r.getOrderNotes(),additionalFields:r.getAdditionalFields(),paymentMethod:o.getActivePaymentMethod()},customer:{id:r.getCustomerId(),billingAddress:u,shippingAddress:d,..."billing"===e||"shipping"===e?{address:"billing"===e?u:d}:{}}};return{cart:(0,n.f)(v.cart),checkout:(0,n.f)(v.checkout),customer:(0,n.f)(v.customer)}}),[e]);return t.current&&c()(t.current,s)||(t.current=s),t.current})(e);return window.schemaParser?{parser:window.schemaParser,data:t}:{parser:null,data:t}}},4556:(e,t,s)=>{"use strict";s.d(t,{c:()=>a});var r=s(6087),o=s(923),n=s.n(o);function a(e){const t=(0,r.useRef)(e);return n()(e,t.current)||(t.current=e),t.current}},4721:(e,t,s)=>{"use strict";s.d(t,{$:()=>n});var r=s(6087),o=s(195);const n=(e,t)=>{const s="string"==typeof e?e:(0,r.renderToString)(e);(0,r.useEffect)((()=>{s&&(0,o.speak)(s,t)}),[s,t])}},371:(e,t,s)=>{"use strict";if(s.d(t,{p:()=>c}),/^(2895|7949)$/.test(s.j))var r=s(4921);var o=s(3993);if(/^(2895|7949)$/.test(s.j))var n=s(219);if(/^(2895|7949)$/.test(s.j))var a=s(17);const c=e=>{const t=(e=>{const t=(0,o.isObject)(e)?e:{style:{}};let s=t.style;return(0,o.isString)(s)&&(s=JSON.parse(s)||{}),(0,o.isObject)(s)||(s={}),{...t,style:s}})(e),s=(0,a.BK)(t),c=(0,a.aR)(t),i=(0,a.fo)(t),l=(0,n.x)(t);return{className:(0,r.A)(l.className,s.className,c.className,i.className),style:{...l.style,...s.style,...c.style,...i.style}}}},219:(e,t,s)=>{"use strict";s.d(t,{x:()=>o});var r=s(3993);const o=e=>{const t=(0,r.isObject)(e.style.typography)?e.style.typography:{},s=(0,r.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:s,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}}},4982:(e,t,s)=>{"use strict";s.d(t,{KY:()=>p,TU:()=>l,i0:()=>u,ln:()=>d});var r=s(2e3),o=s(8331),n=s(5703),a=s(3993),c=s(8537);s(3832);const i=(e,t)=>e in t,l=e=>{const t=(0,r.A)(o.Hw,n.defaultFields,e.country),s=Object.assign({},e);return t.forEach((({key:t,hidden:r})=>{!0===r&&i(t,e)&&(s[t]="")})),s},d=e=>{const t=(0,r.A)(o.Hw,n.defaultFields,e.country),s=Object.assign({},e);return t.forEach((({key:t})=>{"country"!==t&&"state"!==t&&i(t,e)&&(s[t]="")})),s},u=e=>{if(0===Object.values(e).length)return null;const t=(0,a.isString)(o.FS[e.country])?(0,c.decodeEntities)(o.FS[e.country]):"",s=(0,a.isObject)(o.xj[e.country])&&(0,a.isString)(o.xj[e.country][e.state])?(0,c.decodeEntities)(o.xj[e.country][e.state]):e.state,r=[];r.push(e.postcode.toUpperCase()),r.push(e.city),r.push(s),r.push(t);return r.filter(Boolean).join(", ")||null},p=(e,t=[])=>{if(!e.country)return!1;const s=(0,r.A)(o.Hw,n.defaultFields,e.country);return(t.length>0?s.filter((({key:e})=>t.includes(e))):s).every((({key:t,hidden:s,required:r})=>!0===s||!1===r||i(t,e)&&""!==e[t]))}},1233:(e,t,s)=>{"use strict";s.d(t,{Jq:()=>i,h5:()=>c,jj:()=>l});var r=s(7723),o=s(692),n=s(7143);if(/^(6981|8157)$/.test(s.j))var a=s(8696);(0,r.__)("Something went wrong. Please contact us to get assistance.","woocommerce");const c=()=>Object.values(a.tG),i=()=>{const e=(0,n.select)("wc/store/store-notices").getRegisteredContainers(),{removeNotice:t}=(0,n.dispatch)(o.store),{getNotices:s}=(0,n.select)(o.store);e.forEach((e=>{s(e).forEach((s=>{t(s.id,e)}))}))},l=e=>{const{removeNotice:t}=(0,n.dispatch)(o.store),{getNotices:s}=(0,n.select)(o.store);s(e).forEach((s=>{t(s.id,e)}))}},8522:(e,t,s)=>{"use strict";s.d(t,{k:()=>r});const r=e=>Object.fromEntries(e.map((({package_id:e,shipping_rates:t})=>[e,t.find((e=>e.selected))?.rate_id||""])))},3826:(e,t,s)=>{"use strict";s.d(t,{R:()=>r});const r=e=>Object.values(e).reduce(((e,t)=>(null!==t.icons&&(e=e.concat(t.icons)),e)),[])},17:(e,t,s)=>{"use strict";if(s.d(t,{BK:()=>l,aR:()=>d,fo:()=>u}),/^(2895|7949)$/.test(s.j))var r=s(4921);if(/^(2895|7949)$/.test(s.j))var o=s(7356);var n=s(9786),a=s(3993);function c(e={}){const t={};return(0,n.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function i(e,t){return e&&t?`has-${(0,o.c)(t)}-${e}`:""}function l(e){const{backgroundColor:t,textColor:s,gradient:o,style:n}=e,l=i("background-color",t),d=i("color",s),u=function(e){if(e)return`has-${e}-gradient-background`}(o),p=u||n?.color?.gradient;return{className:(0,r.A)(d,u,{[l]:!p&&!!l,"has-text-color":s||n?.color?.text,"has-background":t||n?.color?.background||o||n?.color?.gradient,"has-link-color":(0,a.isObject)(n?.elements?.link)?n?.elements?.link?.color:void 0}),style:c({color:n?.color||{}})}}function d(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:s}=e,o=t?i("border-color",t):"";return(0,r.A)({"has-border-color":!!t||!!s?.border?.color,[o]:!!o})}(e),style:c({border:t})}}function u(e){return{className:void 0,style:c({spacing:e.style?.spacing||{}})}}},1174:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,F:()=>r});const r=()=>window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:"",o=989==s.j?r:null},9525:(e,t,s)=>{"use strict";s.d(t,{N:()=>r});const r=(e,t)=>{const s=[];return Object.keys(e).forEach((r=>{if(void 0!==t[r])switch(e[r].type){case"boolean":s[r]="false"!==t[r]&&!1!==t[r];break;case"number":s[r]=parseInt(t[r],10);break;case"array":case"object":s[r]=JSON.parse(t[r]);break;default:s[r]=t[r]}else s[r]=e[r].default})),s}},8587:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(3993);const o=(e,t="")=>{if("wc-blocks-registry-js"===e&&"object"==typeof window?.wc?.wcBlocksRegistry)return!0;const s=t.split("?");s?.length>1&&(t=s[0]);const r=t?`script#${e}, script[src*="${t}"]`:`script#${e}`;return document.querySelectorAll(r).length>0},n=e=>{if(!(0,r.isString)(e.id)||o(e.id,e?.src))return;const t=document.createElement("script");for(const s in e){if(!e.hasOwnProperty(s))continue;const o=s;if("onload"===o||"onerror"===o)continue;const n=e[o];(0,r.isString)(n)&&(t[o]=n)}"function"==typeof e.onload&&(t.onload=e.onload),"function"==typeof e.onerror&&(t.onerror=e.onerror),document.body.appendChild(t)},a=989==s.j?({handle:e,src:t,version:s,after:r,before:a,translations:c})=>new Promise(((i,l)=>{o(`${e}-js`,t)&&i(),c&&n({id:`${e}-js-translations`,innerHTML:c}),a&&n({id:`${e}-js-before`,innerHTML:a}),n({id:`${e}-js`,onerror:l,onload:()=>{r&&n({id:`${e}-js-after`,innerHTML:r}),i()},src:s?`${t}?ver=${s}`:t})})):null},3757:(e,t,s)=>{"use strict";s.d(t,{Pt:()=>o,f2:()=>n});const r=window.CustomEvent||null,o=(e,{bubbles:t=!1,cancelable:s=!1,element:o,detail:n={}})=>{if(!r)return;o||(o=document.body);const a=new r(e,{bubbles:t,cancelable:s,detail:n});o.dispatchEvent(a)},n=(e,t,s=!1,r=!1)=>{if("function"!=typeof jQuery)return()=>{};const n=()=>{o(t,{bubbles:s,cancelable:r})};return jQuery(document).on(e,n),()=>jQuery(document).off(e,n)}},3989:(e,t,s)=>{"use strict";s.d(t,{d:()=>r});const r=(e,t)=>Object.entries(e).reduce(((e,[s,r])=>({...e,[t(r,s)]:r})),{})},179:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});const r=989==s.j?({handle:e,src:t,version:s})=>{const r=t.split("?");r?.length>1&&(t=r[0]);const o=`#${e}-js, #${e}-js-prefetch, script[src*="${t}"]`;if(0===document.querySelectorAll(o).length){const r=document.createElement("link");r.href=s?`${t}?ver=${s}`:t,r.rel="preload",r.as="script",r.id=`${e}-js-prefetch`,document.head.appendChild(r)}}:null},2515:(e,t,s)=>{"use strict";s.d(t,{Fq:()=>l});var r=s(6087),o=s(4328),n=s(790);const a=/^(251|2895|7949)$/.test(s.j)?[".wp-block-woocommerce-cart"]:null,c=({Block:e,container:t,attributes:s={},props:a={},errorBoundaryProps:c={}})=>{const i=()=>((0,r.useEffect)((()=>{t.classList&&t.classList.remove("is-loading")}),[]),(0,n.jsx)(o.A,{...c,children:(0,n.jsx)(r.Suspense,{fallback:(0,n.jsx)("div",{className:"wc-block-placeholder",children:"Loading..."}),children:e&&(0,n.jsx)(e,{...a,attributes:s})})})),l=(0,r.createRoot)(t);return l.render((0,n.jsx)(i,{})),l},i=({Block:e,containers:t,getProps:s=()=>({}),getErrorBoundaryProps:r=()=>({})})=>{if(0===t.length)return[];const o=[];return Array.prototype.forEach.call(t,((t,n)=>{const a=s(t,n),i=r(t,n),l={...t.dataset,...a.attributes||{}};o.push({container:t,root:c({Block:e,container:t,props:a,attributes:l,errorBoundaryProps:i})})})),o},l=e=>{const t=document.body.querySelectorAll(a.join(",")),{Block:s,getProps:r,getErrorBoundaryProps:o,selector:n}=e,c=(({Block:e,getProps:t,getErrorBoundaryProps:s,selector:r,wrappers:o})=>{const n=document.body.querySelectorAll(r);return o&&o.length>0&&Array.prototype.filter.call(n,(e=>!((e,t)=>Array.prototype.some.call(t,(t=>t.contains(e)&&!t.isSameNode(e))))(e,o))),i({Block:e,containers:n,getProps:t,getErrorBoundaryProps:s})})({Block:s,getProps:r,getErrorBoundaryProps:o,selector:n,wrappers:t});return Array.prototype.forEach.call(t,(t=>{t.addEventListener("wc-blocks_render_blocks_frontend",(()=>{(({Block:e,getProps:t,getErrorBoundaryProps:s,selector:r,wrapper:o})=>{const n=o.querySelectorAll(r);i({Block:e,containers:n,getProps:t,getErrorBoundaryProps:s})})({...e,wrapper:t})}))})),c}},3932:(e,t,s)=>{"use strict";s.d(t,{$u:()=>g,HI:()=>d,J_:()=>c,Lb:()=>l,PU:()=>h,T4:()=>n,jV:()=>i,mH:()=>p,qr:()=>m,uo:()=>u});var r=s(5703),o=s(8331);const n=e=>e.length,a=(0,r.getSetting)("collectableMethodIds",[]),c=e=>a.includes(e.method_id),i=e=>!!o.F7&&(Array.isArray(e)?!!e.find((e=>a.includes(e))):a.includes(e)),l=e=>e.reduce((function(e,t){return e+t.shipping_rates.length}),0),d=e=>e.some((e=>!!e.shipping_rates.length)),u=(e,t)=>e.map((e=>({...e,shipping_rates:e.shipping_rates.filter((e=>{const s=i(e.method_id);return t?s:!s}))}))),p=e=>(0,r.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.total_shipping,10)+parseInt(e.total_shipping_tax,10):parseInt(e.total_shipping,10),m=e=>e.flatMap((e=>e.shipping_rates.filter((e=>e.selected)).flatMap((e=>e.name)))),h=e=>!!d(e)&&e.every((e=>e.shipping_rates.every((e=>!e.selected||c(e))))),g=e=>!!d(e)&&e.every((e=>e.shipping_rates.every((e=>c(e)))))},468:(e,t,s)=>{"use strict";if(s.d(t,{f:()=>n}),/^(251|7949)$/.test(s.j))var r=s(7740);if(/^(251|7949)$/.test(s.j))var o=s(3989);const n=e=>(0,o.d)(e,((e,t)=>(0,r.L)(t)))},6441:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(6087);let o=null;function n(e){const t=(0,r.useRef)(null),s=(0,r.useRef)(null),n=(0,r.useRef)(e);return(0,r.useEffect)((()=>{n.current=e}),[e]),(0,r.useCallback)((e=>{if(e)t.current=e,s.current=e.ownerDocument.activeElement;else if(s.current){const e=t.current?.contains(t.current?.ownerDocument.activeElement);var r;if(t.current?.isConnected&&!e&&(null!==(r=o)&&void 0!==r||(o=s.current)),n.current)n.current();else{const e=s.current;(e?.isConnected?e:o)?.focus()}o=null}}),[])}},4509:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(7723);if(/^(251|7949)$/.test(s.j))var o=s(8814);if(/^(251|7949)$/.test(s.j))var n=s(6314);var a=s(6087);if(/^(251|7949)$/.test(s.j))var c=s(7370);var i=s(4040),l=s.n(i),d=s(7143);if(/^(251|7949)$/.test(s.j))var u=s(3797);if(/^(251|7949)$/.test(s.j))var p=s(7897);if(/^(251|7949)$/.test(s.j))var m=s(9030);var h=s(790);const g=/^(251|7949)$/.test(s.j)?()=>{const{isEditor:e}=(0,c.m)(),{showButtonStyles:t,buttonHeight:s,buttonBorderRadius:i}=(0,m.V)(),g=t?{height:s,borderRadius:i}:void 0,{activePaymentMethod:_,paymentMethodData:v}=(0,d.useSelect)((e=>{const t=e(p.U);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData()}})),{__internalSetActivePaymentMethod:b,__internalSetExpressPaymentStarted:f,__internalSetPaymentIdle:y,__internalSetPaymentError:k,__internalSetPaymentMethodData:w,__internalSetExpressPaymentError:x}=(0,d.useDispatch)(p.U),{paymentMethods:j}=(0,o.u)(),S=(0,n.Y)(),C=(0,a.useRef)(_),E=(0,a.useRef)(v),A=(0,a.useCallback)((e=>()=>{C.current=_,E.current=v,f(),b(e)}),[_,v,b,f]),N=(0,a.useCallback)((()=>{y(),b(C.current,E.current)}),[b,y]),P=(0,a.useCallback)((e=>{k(),w(e),x(e),b(C.current,E.current)}),[b,k,w,x]),R=(0,a.useCallback)(((e="")=>{l()("Express Payment Methods should use the provided onError handler instead.",{alternative:"onError",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),e?P(e):x("")}),[x,P]),I=Object.entries(j),M=I.length>0?I.map((([t,s])=>{const r=e?s.edit:s.content;return(0,a.isValidElement)(r)?(0,h.jsx)("li",{id:`express-payment-method-${t}`,children:(0,a.cloneElement)(r,{...S,onClick:A(t),onClose:N,onError:P,setExpressPaymentError:R,buttonAttributes:g})},t):null})):(0,h.jsx)("li",{children:(0,r.__)("No registered Payment Methods","woocommerce")},"noneRegistered");return(0,h.jsx)(u.A,{isEditor:e,children:(0,h.jsx)("ul",{className:"wc-block-components-express-payment__event-buttons",children:M})})}:null},2213:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(7723);if(7949==s.j)var o=s(8814);if(7949==s.j)var n=s(8696);var a=s(4656),c=s(3575),i=s(7143),l=s(7594);if(7949==s.j)var d=s(4509);s(2831);var u=s(790);const p=7949==s.j?()=>{const{paymentMethods:e,isInitialized:t}=(0,o.u)(),{isCalculating:s,isProcessing:p,isAfterProcessing:m,isBeforeProcessing:h,isComplete:g,hasError:_}=(0,i.useSelect)((e=>{const t=e(l.checkoutStore);return{isCalculating:t.isCalculating(),isProcessing:t.isProcessing(),isAfterProcessing:t.isAfterProcessing(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),hasError:t.hasError()}})),v=(0,i.useSelect)((e=>e(l.paymentStore).isExpressPaymentMethodActive()));if(!t||t&&0===Object.keys(e).length)return null;const b=p||m||h||g&&!_;return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(c.A,{isLoading:s||b||v,children:(0,u.jsx)("div",{className:"wc-block-components-express-payment wc-block-components-express-payment--cart",children:(0,u.jsxs)("div",{className:"wc-block-components-express-payment__content",children:[(0,u.jsx)(a.StoreNoticesContainer,{context:n.tG.EXPRESS_PAYMENTS}),(0,u.jsx)(d.A,{})]})})}),(0,u.jsx)("div",{className:"wc-block-components-express-payment-continue-rule wc-block-components-express-payment-continue-rule--cart",children:(0,r.__)("Or","woocommerce")})]})}:null},3135:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(7723);if(251==s.j)var o=s(7370);if(251==s.j)var n=s(8696);var a=s(4656),c=s(3575),i=s(5703),l=s(7594),d=s(7143);if(251==s.j)var u=s(4509);s(2831);var p=s(790);const m=251==s.j?()=>{const{isCalculating:e,isProcessing:t,isAfterProcessing:s,isBeforeProcessing:m,isComplete:h,hasError:g}=(0,d.useSelect)((e=>{const t=e(l.checkoutStore);return{isCalculating:t.isCalculating(),isProcessing:t.isProcessing(),isAfterProcessing:t.isAfterProcessing(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),hasError:t.hasError()}})),{availableExpressPaymentMethods:_,expressPaymentMethodsInitialized:v,isExpressPaymentMethodActive:b}=(0,d.useSelect)((e=>{const t=e(l.paymentStore);return{availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive()}})),{isEditor:f}=(0,o.m)();if(!v||v&&0===Object.keys(_).length)return f||i.CURRENT_USER_IS_ADMIN?(0,p.jsx)(a.StoreNoticesContainer,{context:n.tG.EXPRESS_PAYMENTS}):null;const y=t||s||m||h&&!g;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(c.A,{isLoading:e||y||b,children:(0,p.jsxs)("div",{className:"wc-block-components-express-payment wc-block-components-express-payment--checkout",children:[(0,p.jsx)("div",{className:"wc-block-components-express-payment__title-container",children:(0,p.jsx)(a.Title,{className:"wc-block-components-express-payment__title",headingLevel:"2",children:(0,r.__)("Express Checkout","woocommerce")})}),(0,p.jsxs)("div",{className:"wc-block-components-express-payment__content",children:[(0,p.jsx)(a.StoreNoticesContainer,{context:n.tG.EXPRESS_PAYMENTS}),(0,p.jsx)(u.A,{})]})]})}),(0,p.jsx)("div",{className:"wc-block-components-express-payment-continue-rule wc-block-components-express-payment-continue-rule--checkout",children:(0,r.__)("Or continue below","woocommerce")})]})}:null},9030:(e,t,s)=>{"use strict";s.d(t,{V:()=>n,W:()=>o});var r=s(6087);const o=(0,r.createContext)({showButtonStyles:!1,buttonHeight:"48",buttonBorderRadius:"4"}),n=()=>(0,r.useContext)(o)},2821:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(7723),o=s(9021),n=(s(4147),s(790));const a=251==s.j?()=>(0,n.jsx)(o.A,{isDismissible:!1,className:"wc-block-checkout__no-payment-methods-notice",status:"error",children:(0,r.__)("There are no payment methods available. This may be an error on our side. Please contact us if you need any help placing your order.","woocommerce")}):null},8366:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(7723);if(251==s.j)var o=s(7370);var n=s(4656),a=s(7143),c=s(6087),i=s(7594),l=s(5703);if(251==s.j)var d=s(3797);var u=s(790);const p=251==s.j?({children:e,showSaveOption:t})=>{const{isEditor:s}=(0,o.m)(),{shouldSavePaymentMethod:p,customerId:m,shouldCreateAccount:h}=(0,a.useSelect)((e=>{const t=e(i.paymentStore),s=e(i.checkoutStore);return{shouldSavePaymentMethod:t.getShouldSavePaymentMethod(),customerId:s.getCustomerId(),shouldCreateAccount:s.getShouldCreateAccount()}}),[]),{__internalSetShouldSavePaymentMethod:g}=(0,a.useDispatch)(i.paymentStore),_=(0,l.getSetting)("checkoutAllowsGuest",!1),v=m>0||h||!_;return(0,c.useEffect)((()=>{!v&&p&&g(!1)}),[v,p,g]),(0,u.jsxs)(d.A,{isEditor:s,children:[e,v&&t&&(0,u.jsx)(n.CheckboxControl,{className:"wc-block-components-payment-methods__save-card-info",label:(0,r.__)("Save payment information to my account for future purchases.","woocommerce"),checked:p,onChange:()=>g(!p)})]})}:null},3797:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(7723),o=s(6087),n=s(5703),a=s(4656);if(/^(251|7949)$/.test(s.j))var c=s(8696);var i=s(790);class l extends(/^(251|7949)$/.test(s.j)?o.Component:null){state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return{errorMessage:e.message,hasError:!0}}render(){const{hasError:e,errorMessage:t}=this.state,{isEditor:s}=this.props;if(e){let e=(0,r.__)("We are experiencing difficulties with this payment method. Please contact us for assistance.","woocommerce");(s||n.CURRENT_USER_IS_ADMIN)&&(e=t||(0,r.__)("There was an error with this payment method. Please verify it's configured correctly.","woocommerce"));const o=[{id:"0",content:e,isDismissible:!1,status:"error"}];return(0,i.jsx)(a.StoreNoticesContainer,{additionalNotices:o,context:c.tG.PAYMENTS})}return this.props.children}}const d=/^(251|7949)$/.test(s.j)?l:null},8198:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>g}),251==s.j)var r=s(6314);if(251==s.j)var o=s(7052);var n=s(6087);if(251==s.j)var a=s(7370);if(251==s.j)var c=s(4921);var i=s(4656),l=s(7143),d=s(4083),u=s(7594);if(251==s.j)var p=s(8366);if(251==s.j)var m=s(8696);var h=s(790);const g=251==s.j?()=>{const{activeSavedToken:e,activePaymentMethod:t,isExpressPaymentMethodActive:s,savedPaymentMethods:g,availablePaymentMethods:_}=(0,l.useSelect)((e=>{const t=e(u.paymentStore);return{activeSavedToken:t.getActiveSavedToken(),activePaymentMethod:t.getActivePaymentMethod(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),savedPaymentMethods:t.getSavedPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),{__internalSetActivePaymentMethod:v}=(0,l.useDispatch)(u.paymentStore),b=(0,d.getPaymentMethods)(),{...f}=(0,r.Y)(),{removeNotice:y}=(0,l.useDispatch)("core/notices"),{dispatchCheckoutEvent:k}=(0,o.y)(),{isEditor:w}=(0,a.m)(),x=Object.keys(_).map((e=>{const{edit:t,content:s,label:r,supports:o}=b[e],a=w?t:s;return{value:e,label:"string"==typeof r?r:(0,n.cloneElement)(r,{components:f.components}),name:`wc-saved-payment-method-token-${e}`,content:(0,h.jsx)(p.A,{showSaveOption:o.showSaveOption,children:(0,n.cloneElement)(a,{__internalSetActivePaymentMethod:v,...f})})}})),j=(0,n.useCallback)((e=>{v(e),y("wc-payment-error",m.tG.PAYMENTS),k("set-active-payment-method",{paymentMethodSlug:e})}),[k,y,v]),S=0===Object.keys(g).length&&1===Object.keys(b).length,C=(0,c.A)({"disable-radio-control":S});return s?null:(0,h.jsx)(i.RadioControlAccordion,{highlightChecked:!0,id:"wc-payment-method-options",className:C,selected:e?null:t,onChange:j,options:x})}:null},4914:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(7723),o=s(4656),n=s(7143),a=s(7594);if(251==s.j)var c=s(2821);if(251==s.j)var i=s(8198);if(251==s.j)var l=s(6846);s(7215);var d=s(790);const u=251==s.j?({noPaymentMethods:e=(0,d.jsx)(c.A,{})})=>{const{paymentMethodsInitialized:t,availablePaymentMethods:s,savedPaymentMethods:u}=(0,n.useSelect)((e=>{const t=e(a.paymentStore);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),availablePaymentMethods:t.getAvailablePaymentMethods(),savedPaymentMethods:t.getSavedPaymentMethods()}}));return t&&0===Object.keys(s).length?e:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(l.A,{}),Object.keys(u).length>0&&(0,d.jsx)(o.Label,{label:(0,r.__)("Use another payment method.","woocommerce"),screenReaderLabel:(0,r.__)("Other available payment methods","woocommerce"),wrapperElement:"p",wrapperProps:{className:["wc-block-components-checkout-step__description wc-block-components-checkout-step__description-payments-aligned"]}}),(0,d.jsx)(i.A,{})]})}:null},6846:(e,t,s)=>{"use strict";s.d(t,{A:()=>v});var r=s(6087),o=s(7723);if(251==s.j)var n=s(8696);var a=s(4656);if(251==s.j)var c=s(6314);if(251==s.j)var i=s(7052);var l=s(7594),d=s(7143),u=s(4083),p=s(3993);if(251==s.j)var m=s(1418);var h=s(790);const g=({method:e,expires:t})=>{var s,r;return(0,o.sprintf)(/* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card, %3$s is referring to the expiry date.  */ /* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card, %3$s is referring to the expiry date.  */
(0,o.__)("%1$s ending in %2$s (expires %3$s)","woocommerce"),null!==(s=null!==(r=e?.display_brand)&&void 0!==r?r:e?.networks?.preferred)&&void 0!==s?s:e.brand,e.last4,t)},_=({method:e})=>e.brand&&e.last4?(0,o.sprintf)(/* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card. */ /* translators: %1$s is referring to the payment method brand, %2$s is referring to the last 4 digits of the payment card. */
(0,o.__)("%1$s ending in %2$s","woocommerce"),e.brand,e.last4):(0,o.sprintf)(/* translators: %s is the name of the payment method gateway. */ /* translators: %s is the name of the payment method gateway. */
(0,o.__)("Saved token for %s","woocommerce"),e.gateway),v=251==s.j?()=>{const{activeSavedToken:e,activePaymentMethod:t,savedPaymentMethods:s}=(0,d.useSelect)((e=>{const t=e(l.paymentStore);return{activeSavedToken:t.getActiveSavedToken(),activePaymentMethod:t.getActivePaymentMethod(),savedPaymentMethods:t.getSavedPaymentMethods()}})),{__internalSetActivePaymentMethod:o}=(0,d.useDispatch)(l.paymentStore),v=(0,m.z)(),b=(0,u.getPaymentMethods)(),f=(0,c.Y)(),{removeNotice:y}=(0,d.useDispatch)("core/notices"),{dispatchCheckoutEvent:k}=(0,i.y)(),w=(0,r.useMemo)((()=>{const e=Object.keys(s),t=new Set(e.flatMap((e=>s[e].map((e=>e.method.gateway))))),r=Array.from(t).filter((e=>b[e]?.canMakePayment(v)));return e.flatMap((e=>s[e].map((t=>{if(!r.includes(t.method.gateway))return;const s="cc"===e||"echeck"===e,a=t.method.gateway;return{name:`wc-saved-payment-method-token-${a}`,label:s?g(t):_(t),value:t.tokenId.toString(),onChange:e=>{const t=`wc-${a}-payment-token`;o(a,{token:e,payment_method:a,[t]:e.toString(),isSavedToken:!0}),y("wc-payment-error",n.tG.PAYMENTS),k("set-active-payment-method",{paymentMethodSlug:a})}}})))).filter((e=>void 0!==e))}),[s,b,o,y,k,v]),x=e&&b[t]&&void 0!==b[t]?.savedTokenComponent&&!(0,p.isNull)(b[t].savedTokenComponent)?(0,r.cloneElement)(b[t].savedTokenComponent,{token:e,...f}):null;return w.length>0?(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(a.RadioControl,{highlightChecked:!0,id:"wc-payment-method-saved-tokens",selected:e,options:w,onChange:()=>{}}),x]}):null}:null},1641:(e,t,s)=>{"use strict";s.d(t,{U:()=>o});var r=s(7723);const o="wc/store/cart";(0,r.__)("Unable to get cart data from the API.","woocommerce")},8897:(e,t,s)=>{"use strict";s.d(t,{r:()=>c});var r=s(8331),o=s(7520);const n={};r.Hw.forEach((e=>{n[e]=""}));const a={};r.Hw.forEach((e=>{a[e]=""})),a.email="";const c={cartItemsPendingQuantity:[],cartItemsPendingDelete:[],productsPendingAdd:[],cartData:{coupons:o.fH,shippingRates:o.BE,shippingAddress:n,billingAddress:a,items:o.Kx,itemsCount:0,itemsWeight:0,crossSells:o.kB,needsShipping:!0,needsPayment:!1,hasCalculatedShipping:!0,fees:o.xH,totals:{currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:".",currency_thousand_separator:",",currency_prefix:"",currency_suffix:"",total_items:"0",total_items_tax:"0",total_fees:"0",total_fees_tax:"0",total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_price:"0",total_tax:"0",tax_lines:o.gp},errors:o.vP,paymentMethods:o.I0,paymentRequirements:o.uk,extensions:o.Vi},metaData:{updatingCustomerData:!1,updatingSelectedRate:!1,applyingCoupon:"",removingCoupon:"",isCartDataStale:!1},errors:o.FU}},4784:(e,t,s)=>{"use strict";s.d(t,{ND:()=>n});var r=s(7143);s(3832);var o=s(2308);const n=()=>{const e=(0,r.select)(o.U),t=e.getValidationError("shipping_state"),s=e.getValidationError("shipping_address_1"),n=e.getValidationError("shipping_country"),a=e.getValidationError("shipping_postcode");return[e.getValidationError("shipping_city"),t,s,n,a].some((e=>void 0!==e))};(e=>{let t,s=null;const r=(...r)=>{s=r,t&&clearTimeout(t),t=setTimeout((()=>{t=null,s&&e(...s)}),300)};r.flush=()=>{t&&s&&(e(...s),clearTimeout(t),t=null)},r.clear=()=>{t&&clearTimeout(t),t=null}})((e=>{window.localStorage.setItem("WOOCOMMERCE_CHECKOUT_IS_CUSTOMER_DATA_DIRTY",e?"true":"false")}))},7520:(e,t,s)=>{"use strict";s.d(t,{BE:()=>l,FU:()=>i,I0:()=>d,Kx:()=>o,Vi:()=>p,fH:()=>r,gp:()=>m,kB:()=>n,uk:()=>u,vP:()=>c,xH:()=>a});const r=251==s.j?[]:null,o=251==s.j?[]:null,n=251==s.j?[]:null,a=251==s.j?[]:null,c=251==s.j?[]:null,i=251==s.j?[]:null,l=251==s.j?[]:null,d=251==s.j?[]:null,u=251==s.j?[]:null,p={},m=251==s.j?[]:null},7897:(e,t,s)=>{"use strict";s.d(t,{U:()=>r});const r="wc/store/payment"},1418:(e,t,s)=>{"use strict";s.d(t,{z:()=>d}),s(5703);var r=s(7143);if(251==s.j)var o=s(8522);if(251==s.j)var n=s(4982);if(s(7723),s(4083),251==s.j)var a=s(2614);if(251==s.j)var c=s(1641);if(251==s.j)var i=s(7520);if(251==s.j)var l=s(8897);const d=()=>{let e;if((0,r.select)("core/editor")){const t={cartCoupons:a.B.coupons,cartItems:a.B.items,crossSellsProducts:a.B.cross_sells,cartFees:a.B.fees,cartItemsCount:a.B.items_count,cartItemsWeight:a.B.items_weight,cartNeedsPayment:a.B.needs_payment,cartNeedsShipping:a.B.needs_shipping,cartItemErrors:i.vP,cartTotals:a.B.totals,cartIsLoading:!1,cartErrors:i.FU,billingData:l.r.cartData.billingAddress,billingAddress:l.r.cartData.billingAddress,shippingAddress:l.r.cartData.shippingAddress,extensions:i.Vi,shippingRates:a.B.shipping_rates,isLoadingRates:!1,cartHasCalculatedShipping:a.B.has_calculated_shipping,paymentRequirements:a.B.payment_requirements,receiveCart:()=>{}};e={cart:t,cartTotals:t.cartTotals,cartNeedsShipping:t.cartNeedsShipping,billingData:t.billingAddress,billingAddress:t.billingAddress,shippingAddress:t.shippingAddress,selectedShippingMethods:(0,o.k)(t.shippingRates),paymentMethods:a.B.payment_methods,paymentRequirements:t.paymentRequirements}}else{const t=(0,r.select)(c.U),s=t.getCartData(),a=t.getCartErrors(),i=t.getCartTotals(),l=!t.hasFinishedResolution("getCartData"),d=t.isCustomerDataUpdating(),u=(0,o.k)(s.shippingRates);e={cart:{cartCoupons:s.coupons,cartItems:s.items,crossSellsProducts:s.crossSells,cartFees:s.fees,cartItemsCount:s.itemsCount,cartItemsWeight:s.itemsWeight,cartNeedsPayment:s.needsPayment,cartNeedsShipping:s.needsShipping,cartItemErrors:s.errors,cartTotals:i,cartIsLoading:l,cartErrors:a,billingData:(0,n.TU)(s.billingAddress),billingAddress:(0,n.TU)(s.billingAddress),shippingAddress:(0,n.TU)(s.shippingAddress),extensions:s.extensions,shippingRates:s.shippingRates,isLoadingRates:d,cartHasCalculatedShipping:s.hasCalculatedShipping,paymentRequirements:s.paymentRequirements,receiveCart:(0,r.dispatch)(c.U).receiveCart},cartTotals:s.totals,cartNeedsShipping:s.needsShipping,billingData:s.billingAddress,billingAddress:s.billingAddress,shippingAddress:s.shippingAddress,selectedShippingMethods:u,paymentMethods:s.paymentMethods,paymentRequirements:s.paymentRequirements}}return e}},2308:(e,t,s)=>{"use strict";s.d(t,{U:()=>r});const r="wc/store/validation"},1043:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),o=s(790);const n=(0,o.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"none",children:[(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.5556 12.3333C19.0646 12.3333 18.6667 11.9354 18.6667 11.4444C18.6667 10.7372 18.3857 8.05893 17.8856 7.55883C17.3855 7.05873 16.7073 6.77778 16 6.77778C15.2928 6.77778 14.6145 7.05873 14.1144 7.55883C13.6143 8.05893 13.3333 10.7372 13.3333 11.4444C13.3333 11.9354 12.9354 12.3333 12.4445 12.3333C11.9535 12.3333 11.5556 11.9354 11.5556 11.4444C11.5556 10.2657 12.0238 7.13524 12.8573 6.30175C13.6908 5.46825 14.8213 5 16 5C17.1788 5 18.3092 5.46825 19.1427 6.30175C19.9762 7.13524 20.4445 10.2657 20.4445 11.4444C20.4445 11.9354 20.0465 12.3333 19.5556 12.3333Z",fill:"currentColor"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.5 12C7.5 11.4477 7.94772 11 8.5 11H23.5C24.0523 11 24.5 11.4477 24.5 12V25.3333C24.5 25.8856 24.0523 26.3333 23.5 26.3333H8.5C7.94772 26.3333 7.5 25.8856 7.5 25.3333V12ZM9.5 13V24.3333H22.5V13H9.5Z",fill:"currentColor"})]}),a=2895==s.j?n:null},2925:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),o=s(790);const n=(0,o.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"none",children:[(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.4444 14.2222C12.9354 14.2222 13.3333 14.6202 13.3333 15.1111C13.3333 15.8183 13.6143 16.4966 14.1144 16.9967C14.6145 17.4968 15.2927 17.7778 16 17.7778C16.7072 17.7778 17.3855 17.4968 17.8856 16.9967C18.3857 16.4966 18.6667 15.8183 18.6667 15.1111C18.6667 14.6202 19.0646 14.2222 19.5555 14.2222C20.0465 14.2222 20.4444 14.6202 20.4444 15.1111C20.4444 16.2898 19.9762 17.4203 19.1427 18.2538C18.3092 19.0873 17.1787 19.5555 16 19.5555C14.8212 19.5555 13.6908 19.0873 12.8573 18.2538C12.0238 17.4203 11.5555 16.2898 11.5555 15.1111C11.5555 14.6202 11.9535 14.2222 12.4444 14.2222Z",fill:"currentColor"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M11.2408 6.68254C11.4307 6.46089 11.7081 6.33333 12 6.33333H20C20.2919 6.33333 20.5693 6.46089 20.7593 6.68254L24.7593 11.3492C25.0134 11.6457 25.0717 12.0631 24.9085 12.4179C24.7453 12.7727 24.3905 13 24 13H8.00001C7.60948 13 7.25469 12.7727 7.0915 12.4179C6.92832 12.0631 6.9866 11.6457 7.24076 11.3492L11.2408 6.68254ZM12.4599 8.33333L10.1742 11H21.8258L19.5401 8.33333H12.4599Z",fill:"currentColor"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7 12C7 11.4477 7.44772 11 8 11H24C24.5523 11 25 11.4477 25 12V25.3333C25 25.8856 24.5523 26.3333 24 26.3333H8C7.44772 26.3333 7 25.8856 7 25.3333V12ZM9 13V24.3333H23V13H9Z",fill:"currentColor"})]}),a=2895==s.j?n:null},1648:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),o=s(790);const n=(0,o.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"none",children:[(0,o.jsx)("circle",{cx:"12.6667",cy:"24.6667",r:"2",fill:"currentColor"}),(0,o.jsx)("circle",{cx:"23.3333",cy:"24.6667",r:"2",fill:"currentColor"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.28491 10.0356C9.47481 9.80216 9.75971 9.66667 10.0606 9.66667H25.3333C25.6232 9.66667 25.8989 9.79247 26.0888 10.0115C26.2787 10.2305 26.3643 10.5211 26.3233 10.8081L24.99 20.1414C24.9196 20.6341 24.4977 21 24 21H12C11.5261 21 11.1173 20.6674 11.0209 20.2034L9.08153 10.8701C9.02031 10.5755 9.09501 10.269 9.28491 10.0356ZM11.2898 11.6667L12.8136 19H23.1327L24.1803 11.6667H11.2898Z",fill:"currentColor"}),(0,o.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M5.66669 6.66667C5.66669 6.11438 6.1144 5.66667 6.66669 5.66667H9.33335C9.81664 5.66667 10.2308 6.01229 10.3172 6.48778L11.0445 10.4878C11.1433 11.0312 10.7829 11.5517 10.2395 11.6505C9.69614 11.7493 9.17555 11.3889 9.07676 10.8456L8.49878 7.66667H6.66669C6.1144 7.66667 5.66669 7.21895 5.66669 6.66667Z",fill:"currentColor"})]}),a=2895==s.j?n:null},3013:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),o=s(790);const n=(0,o.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,o.jsx)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,o.jsx)("path",{d:"M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45zM6.16 6h12.15l-2.76 5H8.53L6.16 6zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"})]}),a=251==s.j?n:null},9680:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),o=s(790);const n=(0,o.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,o.jsxs)("g",{fill:"none",fillRule:"evenodd",children:[(0,o.jsx)("path",{d:"M0 0h24v24H0z"}),(0,o.jsx)("path",{fill:"currentColor",fillRule:"nonzero",d:"M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49A.996.996 0 0 0 20.01 4H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45ZM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2Zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2Z"})]})," "]}),a=/^(2895|7949)$/.test(s.j)?n:null},9280:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5573),o=s(790);const n=(0,o.jsxs)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,o.jsx)("path",{d:"M22.7 22.7l-20-20L2 2l-.7-.7L0 2.5 4.4 7l2.2 4.7L5.2 14A2 2 0 007 17h7.5l1.3 1.4a2 2 0 102.8 2.8l2.9 2.8 1.2-1.3zM7.4 15a.2.2 0 01-.2-.3l.9-1.7h2.4l2 2h-5zm8.2-2a2 2 0 001.7-1l3.6-6.5.1-.5c0-.6-.4-1-1-1H6.5l9 9zM7 18a2 2 0 100 4 2 2 0 000-4z"}),(0,o.jsx)("path",{fill:"none",d:"M0 0h24v24H0z"})]}),a=/^(251|2895|7949)$/.test(s.j)?n:null},2614:(e,t,s)=>{"use strict";s.d(t,{B:()=>g});var r=s(7723),o=s(8331),n=s(5703);const a={currency_code:n.SITE_CURRENCY.code,currency_symbol:n.SITE_CURRENCY.symbol,currency_minor_unit:n.SITE_CURRENCY.minorUnit,currency_decimal_separator:n.SITE_CURRENCY.decimalSeparator,currency_thousand_separator:n.SITE_CURRENCY.thousandSeparator,currency_prefix:n.SITE_CURRENCY.prefix,currency_suffix:n.SITE_CURRENCY.suffix},c=(e,t=2)=>{const s=n.SITE_CURRENCY.minorUnit;if(s===t||!e)return e;const r=Math.pow(10,s);return(Math.round(parseInt(e,10)/Math.pow(10,t))*r).toString()},i=(0,n.getSetting)("localPickupEnabled",!1),l=(0,n.getSetting)("localPickupText",(0,r.__)("Local pickup","woocommerce")),d=(0,n.getSetting)("localPickupCost",""),u=i?(0,n.getSetting)("localPickupLocations",[]):[],p=u?Object.values(u).map(((e,t)=>({...a,name:`${l} (${e.name})`,description:"",delivery_time:"",price:c(d,0)||"0",taxes:"0",rate_id:`pickup_location:${t+1}`,instance_id:t+1,meta_data:[{key:"pickup_location",value:e.name},{key:"pickup_address",value:e.formatted_address},{key:"pickup_details",value:e.details}],method_id:"pickup_location",selected:!1}))):[],m=[{destination:{address_1:"",address_2:"",city:"",state:"",postcode:"",country:""},package_id:0,name:(0,r.__)("Shipping","woocommerce"),items:[{key:"33e75ff09dd601bbe69f351039152189",name:(0,r._x)("Beanie with Logo","example product in Cart Block","woocommerce"),quantity:2},{key:"6512bd43d9caa6e02c990b0a82652dca",name:(0,r._x)("Beanie","example product in Cart Block","woocommerce"),quantity:1}],shipping_rates:[{...a,name:(0,r.__)("Flat rate shipping","woocommerce"),description:"",delivery_time:"",price:c("500"),taxes:"0",rate_id:"flat_rate:0",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!1},{...a,name:(0,r.__)("Free shipping","woocommerce"),description:"",delivery_time:"",price:"0",taxes:"0",rate_id:"free_shipping:1",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!0},...p]}],h=(0,n.getSetting)("displayCartPricesIncludingTax",!1),g={coupons:[],shipping_rates:(0,n.getSetting)("shippingMethodsExist",!1)||(0,n.getSetting)("localPickupEnabled",!1)?m:[],items:[{key:"1",id:1,type:"simple",quantity:2,catalog_visibility:"visible",name:(0,r.__)("Beanie","woocommerce"),summary:(0,r.__)("Beanie","woocommerce"),short_description:(0,r.__)("Warm hat for winter","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-beanie",permalink:"https://example.org",low_stock_remaining:2,backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:10,src:o.sW+"previews/beanie.jpg",thumbnail:o.sW+"previews/beanie.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,r.__)("Color","woocommerce"),value:(0,r.__)("Yellow","woocommerce")},{attribute:(0,r.__)("Size","woocommerce"),value:(0,r.__)("Small","woocommerce")}],prices:{...a,price:c(h?"12000":"10000"),regular_price:c(h?"120":"100"),sale_price:c(h?"12000":"10000"),price_range:null,raw_prices:{precision:6,price:h?"12000000":"10000000",regular_price:h?"12000000":"10000000",sale_price:h?"12000000":"10000000"}},totals:{...a,line_subtotal:c("2000"),line_subtotal_tax:c("400"),line_total:c("2000"),line_total_tax:c("400")},extensions:{},item_data:[]},{key:"2",id:2,type:"simple",quantity:1,catalog_visibility:"visible",name:(0,r.__)("Cap","woocommerce"),summary:(0,r.__)("Cap","woocommerce"),short_description:(0,r.__)("Lightweight baseball cap","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-cap",low_stock_remaining:null,permalink:"https://example.org",backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:11,src:o.sW+"previews/cap.jpg",thumbnail:o.sW+"previews/cap.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,r.__)("Color","woocommerce"),value:(0,r.__)("Orange","woocommerce")}],prices:{...a,price:c(h?"2400":"2000"),regular_price:c(h?"2400":"2000"),sale_price:c(h?"2400":"2000"),price_range:null,raw_prices:{precision:6,price:h?"24000000":"20000000",regular_price:h?"24000000":"20000000",sale_price:h?"24000000":"20000000"}},totals:{...a,line_subtotal:c("2000"),line_subtotal_tax:c("400"),line_total:c("2000"),line_total_tax:c("400")},extensions:{},item_data:[]}],cross_sells:[{id:1,name:(0,r.__)("Polo","woocommerce"),slug:"polo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-polo",short_description:(0,r.__)("Polo","woocommerce"),description:(0,r.__)("Polo","woocommerce"),on_sale:!1,prices:{...a,price:c(h?"24000":"20000"),regular_price:c(h?"24000":"20000"),sale_price:c(h?"12000":"10000"),price_range:null},price_html:"",average_rating:"4.5",review_count:2,images:[{id:17,src:o.sW+"previews/polo.jpg",thumbnail:o.sW+"previews/polo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:2,name:(0,r.__)("Long Sleeve Tee","woocommerce"),slug:"long-sleeve-tee",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-long-sleeve-tee",short_description:(0,r.__)("Long Sleeve Tee","woocommerce"),description:(0,r.__)("Long Sleeve Tee","woocommerce"),on_sale:!1,prices:{...a,price:c(h?"30000":"25000"),regular_price:c(h?"30000":"25000"),sale_price:c(h?"30000":"25000"),price_range:null},price_html:"",average_rating:"4",review_count:2,images:[{id:17,src:o.sW+"previews/long-sleeve-tee.jpg",thumbnail:o.sW+"previews/long-sleeve-tee.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:3,name:(0,r.__)("Hoodie with Zipper","woocommerce"),slug:"hoodie-with-zipper",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-zipper",short_description:(0,r.__)("Hoodie with Zipper","woocommerce"),description:(0,r.__)("Hoodie with Zipper","woocommerce"),on_sale:!0,prices:{...a,price:c(h?"15000":"12500"),regular_price:c(h?"30000":"25000"),sale_price:c(h?"15000":"12500"),price_range:null},price_html:"",average_rating:"1",review_count:2,images:[{id:17,src:o.sW+"previews/hoodie-with-zipper.jpg",thumbnail:o.sW+"previews/hoodie-with-zipper.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:4,name:(0,r.__)("Hoodie with Logo","woocommerce"),slug:"hoodie-with-logo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-logo",short_description:(0,r.__)("Polo","woocommerce"),description:(0,r.__)("Polo","woocommerce"),on_sale:!1,prices:{...a,price:c(h?"4500":"4250"),regular_price:c(h?"4500":"4250"),sale_price:c(h?"4500":"4250"),price_range:null},price_html:"",average_rating:"5",review_count:2,images:[{id:17,src:o.sW+"previews/hoodie-with-logo.jpg",thumbnail:o.sW+"previews/hoodie-with-logo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:5,name:(0,r.__)("Hoodie with Pocket","woocommerce"),slug:"hoodie-with-pocket",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-pocket",short_description:(0,r.__)("Hoodie with Pocket","woocommerce"),description:(0,r.__)("Hoodie with Pocket","woocommerce"),on_sale:!0,prices:{...a,price:c(h?"3500":"3250"),regular_price:c(h?"4500":"4250"),sale_price:c(h?"3500":"3250"),price_range:null},price_html:"",average_rating:"3.75",review_count:4,images:[{id:17,src:o.sW+"previews/hoodie-with-pocket.jpg",thumbnail:o.sW+"previews/hoodie-with-pocket.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:6,name:(0,r.__)("T-Shirt","woocommerce"),slug:"t-shirt",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-t-shirt",short_description:(0,r.__)("T-Shirt","woocommerce"),description:(0,r.__)("T-Shirt","woocommerce"),on_sale:!1,prices:{...a,price:c(h?"1800":"1500"),regular_price:c(h?"1800":"1500"),sale_price:c(h?"1800":"1500"),price_range:null},price_html:"",average_rating:"3",review_count:2,images:[{id:17,src:o.sW+"previews/tshirt.jpg",thumbnail:o.sW+"previews/tshirt.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}}],fees:[{id:"fee",name:(0,r.__)("Fee","woocommerce"),totals:{...a,total:c("100"),total_tax:c("20")}}],items_count:3,items_weight:0,needs_payment:!0,needs_shipping:o.h0,has_calculated_shipping:!0,shipping_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},billing_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",email:"",phone:""},totals:{...a,total_items:c("4000"),total_items_tax:c("800"),total_fees:c("100"),total_fees_tax:c("20"),total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_tax:c("820"),total_price:c("4920"),tax_lines:[{name:(0,r.__)("Sales tax","woocommerce"),rate:"20%",price:c("820")}]},errors:[],payment_methods:["cod","bacs","cheque"],payment_requirements:["products"],extensions:{}}},6070:(e,t,s)=>{"use strict";s.d(t,{AG:()=>b,F7:()=>m,FS:()=>y,G3:()=>f,Hw:()=>j,Jn:()=>c,Vo:()=>u,XK:()=>a,aW:()=>p,fO:()=>S,gu:()=>d,h0:()=>g,iI:()=>w,mH:()=>h,pk:()=>l,pt:()=>C,r7:()=>o,sW:()=>n,tn:()=>i,xj:()=>k});var r=s(5703);const o=(0,r.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),n=o.pluginUrl+"assets/images/",a=o.pluginUrl+"assets/client/blocks/",c=r.STORE_PAGES.shop?.permalink,i=(r.STORE_PAGES.checkout,r.STORE_PAGES.checkout?.permalink),l=r.STORE_PAGES.privacy?.permalink,d=(r.STORE_PAGES.privacy,r.STORE_PAGES.terms?.permalink),u=(r.STORE_PAGES.terms,r.STORE_PAGES.cart,r.STORE_PAGES.cart?.permalink),p=r.STORE_PAGES.myaccount?.permalink?r.STORE_PAGES.myaccount.permalink:(0,r.getSetting)("wpLoginUrl","/wp-login.php"),m=(0,r.getSetting)("localPickupEnabled",!1),h=(0,r.getSetting)("shippingMethodsExist",!1),g=(0,r.getSetting)("shippingEnabled",!0),_=(0,r.getSetting)("countries",{}),v=(0,r.getSetting)("countryData",{}),b=Object.fromEntries(Object.keys(v).filter((e=>!0===v[e].allowBilling)).map((e=>[e,_[e]||""]))),f=Object.fromEntries(Object.keys(v).filter((e=>!0===v[e].allowShipping)).map((e=>[e,_[e]||""]))),y={...b,...f},k=Object.fromEntries(Object.keys(y).map((e=>[e,v[e].states||{}]))),w=Object.fromEntries(Object.keys(y).map((e=>[e,v[e].locale||{}]))),x={address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]},j=(0,r.getSetting)("addressFieldsLocations",x).address,S=(0,r.getSetting)("addressFieldsLocations",x).contact,C=(0,r.getSetting)("addressFieldsLocations",x).order;(0,r.getSetting)("additionalOrderFields",{}),(0,r.getSetting)("additionalContactFields",{}),(0,r.getSetting)("additionalAddressFields",{})},8331:(e,t,s)=>{"use strict";if(s.d(t,{AG:()=>r.AG,F7:()=>r.F7,FS:()=>r.FS,G3:()=>r.G3,Hw:()=>r.Hw,Jn:()=>r.Jn,Vo:()=>r.Vo,XK:()=>r.XK,aW:()=>r.aW,fO:()=>r.fO,gu:()=>r.gu,h0:()=>r.h0,iI:()=>r.iI,mH:()=>r.mH,pk:()=>r.pk,pt:()=>r.pt,r7:()=>r.r7,sW:()=>r.sW,tn:()=>r.tn,xj:()=>r.xj}),/^(251|2895|7949)$/.test(s.j))var r=s(6070)},7254:(e,t,s)=>{"use strict";s.d(t,{c:()=>o});const r=(0,s(5703).getSetting)("productTypes",{});function o(){return Object.keys(r).map((e=>({slug:e,label:r[e]})))}},5269:(e,t,s)=>{"use strict";s.d(t,{p:()=>c});var r=s(3240),o=s.n(r);const n=989!=s.j?["a","b","em","i","strong","p","br"]:null,a=989!=s.j?["target","href","rel","name","download"]:null,c=(e,t)=>{const s=t?.tags||n,r=t?.attr||a;return o().sanitize(e,{ALLOWED_TAGS:s,ALLOWED_ATTR:r})}},5784:(e,t,s)=>{"use strict";s.d(t,{Bk:()=>c,G$:()=>a});var r=s(6004);const o=e=>e.replace(/<\/?[a-z][^>]*?>/gi,""),n=(e,t)=>e.replace(/[\s|\.\,]+$/i,"")+t,a=(e,t,s="&hellip;",a=!0)=>{const c=o(e),i=c.split(" ").splice(0,t).join(" ");return i===c?a?(0,r.autop)(c):c:a?(0,r.autop)(n(i,s)):n(i,s)},c=(e,t,s=!0,a="&hellip;",c=!0)=>{const i=o(e),l=i.slice(0,t);if(l===i)return c?(0,r.autop)(i):i;if(s)return(0,r.autop)(n(l,a));const d=l.match(/([\s]+)/g),u=d?d.length:0,p=i.slice(0,t+u);return c?(0,r.autop)(n(p,a)):n(p,a)}},4234:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Button:()=>R.A,CheckboxControl:()=>T.CheckboxControl,ExperimentalDiscountsMeta:()=>x,ExperimentalOrderLocalPickupPackages:()=>N,ExperimentalOrderMeta:()=>f,ExperimentalOrderShippingPackages:()=>C,Label:()=>I,Panel:()=>P.A,SlotFillProvider:()=>i.Kq,StoreNotice:()=>L.A,StoreNoticesContainer:()=>M.A,Subtotal:()=>r.Ve,TextInput:()=>O.A,TotalsFees:()=>r.ht,TotalsItem:()=>r.Zi,TotalsTaxes:()=>r.Zv,TotalsWrapper:()=>o.A,ValidatedTextInput:()=>$.A,ValidatedTextInputHandle:()=>$.ValidatedTextInputHandle,ValidationInputError:()=>D.A,__experimentalApplyCheckoutFilter:()=>te,__experimentalRegisterCheckoutFilters:()=>K,applyCheckoutFilter:()=>ee,createSlotFill:()=>g,extensionCartUpdate:()=>F.jx,getFieldLabel:()=>F.lu,getRegisteredBlocks:()=>ne,getValidityMessageForInput:()=>F.N2,hasInnerBlocks:()=>oe,hasValidFills:()=>h,innerBlockAreas:()=>se,isPostcode:()=>F.ow,mustContain:()=>F.sH,productPriceValidation:()=>F.mT,registerCheckoutBlock:()=>ce,registerCheckoutFilters:()=>Z,useSlot:()=>l.A,useSlotFills:()=>d.A});var r=s(2669),o=s(9463),n=s(4921),a=s(5703),c=s(6087),i=s(1203),l=s(2622),d=s(9521),u=s(790);class p extends c.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("strong",{children:e.status}),": "+e.statusText]}),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{renderError:e}=this.props,{errorMessage:t,hasError:s}=this.state;return s?"function"==typeof e?e(t):(0,u.jsx)("p",{children:t}):this.props.children}}const m=p,h=e=>Array.isArray(e)&&e.filter(Boolean).length>0,g=(e,t=null)=>{const{Fill:s,Slot:r}=(0,i.QJ)(e);return{Fill:({children:e})=>(0,u.jsx)(s,{children:s=>c.Children.map(e,(e=>(0,u.jsx)(m,{renderError:a.CURRENT_USER_IS_ADMIN?t:()=>null,children:(0,c.cloneElement)(e,s)})))}),Slot:e=>(0,u.jsx)(r,{...e,bubblesVirtually:!0})}},_="__experimentalOrderMeta",{Fill:v,Slot:b}=g(_);v.Slot=({className:e,extensions:t,cart:s,context:r})=>{const a=(0,d.A)(_);return h(a)&&(0,u.jsx)(o.A,{slotWrapper:!0,children:(0,u.jsx)(b,{className:(0,n.A)(e,"wc-block-components-order-meta"),fillProps:{extensions:t,cart:s,context:r}})})};const f=v,y="__experimentalDiscountsMeta",{Fill:k,Slot:w}=g(y);k.Slot=({className:e,extensions:t,cart:s,context:r})=>{const a=(0,d.A)(y);return h(a)&&(0,u.jsx)(o.A,{slotWrapper:!0,children:(0,u.jsx)(w,{className:(0,n.A)(e,"wc-block-components-discounts-meta"),fillProps:{extensions:t,cart:s,context:r}})})};const x=k,{Fill:j,Slot:S}=g("__experimentalOrderShippingPackages");j.Slot=({className:e,noResultsMessage:t,renderOption:s,extensions:r,cart:o,components:a,context:c,collapsible:i,showItems:l})=>(0,u.jsx)(S,{className:(0,n.A)("wc-block-components-shipping-rates-control",e),fillProps:{collapse:i,collapsible:i,showItems:l,noResultsMessage:t,renderOption:s,extensions:r,cart:o,components:a,context:c}});const C=j,{Fill:E,Slot:A}=g("__experimentalOrderLocalPickupPackages");E.Slot=({extensions:e,cart:t,components:s,renderPickupLocation:r})=>(0,u.jsx)(A,{className:(0,n.A)("wc-block-components-local-pickup-rates-control"),fillProps:{extensions:e,cart:t,components:s,renderPickupLocation:r}});const N=E;var P=s(6514),R=s(9048);const I=s(2362).A;var M=s(2285),T=s(4656),$=s(6293),O=s(9006),D=s(4563),L=s(6568),F=s(1075),V=s(7723),B=s(4040),H=s.n(B),U=s(923),q=s.n(U),W=s(3993);const z=()=>!0;let G={},Y={};const Z=(e,t)=>{Object.keys(t).includes("couponName")&&H()("couponName",{alternative:"coupons",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/blob/bb921d21f42e21f38df2b1c87b48e07aa4cb0538/docs/extensibility/available-filters.md#coupons"}),Y={},G={...G,[e]:t}},K=(e,t)=>{H()("__experimentalRegisterCheckoutFilters",{alternative:"registerCheckoutFilters",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8346",since:"9.6.0",hint:"__experimentalRegisterCheckoutFilters has graduated to stable and this experimental function will be removed."}),Z(e,t)},Q={},J=(e,t,s,r)=>{Q[e]={arg:t,extensions:s,defaultValue:r}},X=(e,t)=>!(!(0,W.isNull)(e)||!(0,W.isNull)(t))||(0,W.isObject)(e)&&(0,W.isObject)(t)&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every((s=>(0,W.objectHasProp)(t,s)&&q()(e[s],t[s]))),ee=({filterName:e,defaultValue:t,extensions:s=null,arg:r=null,validation:o=z})=>{if(!((e,t,s,r)=>{const o=Q[e];if(!o)return J(e,t,s,r),!0;const{arg:n={},extensions:a={},defaultValue:c=null}=o;return X(t,n)?!(r===c&&X(s,a)||(J(e,t,s,r),0)):(J(e,t,s,r),!0)})(e,r,s,t)&&void 0!==Y[e])return Y[e];const n=(e=>Object.keys(G).map((t=>G[t][e])).filter(Boolean))(e);let c=t;return n.forEach((e=>{try{const t=e(c,s||{},r);if(typeof t!=typeof c)throw new Error((0,V.sprintf)(/* translators: %1$s is the type of the variable passed to the filter function, %2$s is the type of the value returned by the filter function. */ /* translators: %1$s is the type of the variable passed to the filter function, %2$s is the type of the value returned by the filter function. */
(0,V.__)("The type returned by checkout filters must be the same as the type they receive. The function received %1$s but returned %2$s.","woocommerce"),typeof c,typeof t));c=o(t)?t:c}catch(e){if(a.CURRENT_USER_IS_ADMIN)throw e;console.error(e)}})),Y[e]=c,c},te=({filterName:e,defaultValue:t,extensions:s=null,arg:r=null,validation:o=z})=>(H()("__experimentalApplyCheckoutFilter",{alternative:"applyCheckoutFilter",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8346",since:"9.6.0",hint:"__experimentalApplyCheckoutFilter has graduated to stable and this experimental function will be removed."}),ee({filterName:e,defaultValue:t,extensions:s,arg:r,validation:o}));let se=function(e){return e.CHECKOUT="woocommerce/checkout",e.CHECKOUT_FIELDS="woocommerce/checkout-fields-block",e.CHECKOUT_TOTALS="woocommerce/checkout-totals-block",e.CONTACT_INFORMATION="woocommerce/checkout-contact-information-block",e.SHIPPING_ADDRESS="woocommerce/checkout-shipping-address-block",e.BILLING_ADDRESS="woocommerce/checkout-billing-address-block",e.SHIPPING_METHOD="woocommerce/checkout-shipping-method-block",e.SHIPPING_METHODS="woocommerce/checkout-shipping-methods-block",e.PICKUP_LOCATION="woocommerce/checkout-pickup-options-block",e.PAYMENT_METHODS="woocommerce/checkout-payment-block",e.CART="woocommerce/cart",e.EMPTY_CART="woocommerce/empty-cart-block",e.FILLED_CART="woocommerce/filled-cart-block",e.CART_ITEMS="woocommerce/cart-items-block",e.CART_CROSS_SELLS="woocommerce/cart-cross-sells-block",e.CART_TOTALS="woocommerce/cart-totals-block",e.MINI_CART="woocommerce/mini-cart-contents",e.EMPTY_MINI_CART="woocommerce/empty-mini-cart-contents-block",e.FILLED_MINI_CART="woocommerce/filled-mini-cart-contents-block",e.MINI_CART_TITLE="woocommerce/mini-cart-title-block",e.MINI_CART_ITEMS="woocommerce/mini-cart-items-block",e.MINI_CART_FOOTER="woocommerce/mini-cart-footer-block",e.CART_ORDER_SUMMARY="woocommerce/cart-order-summary-block",e.CART_ORDER_SUMMARY_TOTALS="woocommerce/cart-order-summary-totals-block",e.CHECKOUT_ORDER_SUMMARY="woocommerce/checkout-order-summary-block",e.CHECKOUT_ORDER_SUMMARY_TOTALS="woocommerce/checkout-order-summary-totals-block",e}({});const re={},oe=e=>Object.values(se).includes(e),ne=e=>oe(e)?Object.values(re).filter((({metadata:t})=>(t?.parent||[]).includes(e))):[];var ae=s(4083);const ce=e=>{((e,t,s)=>{if(!(0,W.isObject)(e))return;const r=typeof e[t];if(r!==s)throw new Error(`Incorrect value for the ${t} argument when registering a block component. It was a ${r}, but must be a ${s}.`)})(e,"metadata","object"),(e=>{if(((e,t,s)=>{const r=typeof t;if(r!==s)throw new Error(`Incorrect value for the blockName argument when registering a checkout block. It was a ${r}, but must be a ${s}.`)})(0,e,"string"),!e)throw new Error("Value for the blockName argument must not be empty.")})(e.metadata.name),(e=>{if("string"!=typeof e&&!Array.isArray(e))throw new Error(`Incorrect value for the parent argument when registering a checkout block. It was a ${typeof e}, but must be a string or array of strings.`);if("string"==typeof e&&!oe(e))throw new Error("When registering a checkout block, the parent must be a valid inner block area.");if(Array.isArray(e)&&!e.some((e=>oe(e))))throw new Error("When registering a checkout block, the parent must be a valid inner block area.")})(e.metadata.parent),((e,t)=>{const s=e[t];if(s){if("function"==typeof s)return;if((0,W.isObject)(s)&&s.$$typeof&&s.$$typeof===Symbol.for("react.lazy"))return}throw new Error(`Incorrect value for the ${t} argument when registering a block component. Component must be a valid React Element or Lazy callback.`)})(e,"component"),(0,ae.registerBlockComponent)({blockName:e.metadata.name,component:e.component});const t="boolean"==typeof e.force?e.force:Boolean(e.metadata?.attributes?.lock?.default?.remove);re[e.metadata.name]={blockName:e.metadata.name,metadata:e.metadata,component:e.component,force:t}}},1075:(e,t,s)=>{"use strict";s.d(t,{jx:()=>g,lu:()=>n,N2:()=>a,ow:()=>d,sH:()=>o,mT:()=>u});var r=s(7723);const o=8157==s.j?(e,t)=>{if(!e.includes(t))throw Error((0,r.sprintf)(/* translators: %1$s value passed to filter, %2$s : value that must be included. */ /* translators: %1$s value passed to filter, %2$s : value that must be included. */
(0,r.__)('Returned value must include %1$s, you passed "%2$s"',"woocommerce"),t,e));return!0}:null,n=e=>{var t;const s=(0,r.getLocaleData)();return["de","de_AT","de_CH"].includes(null!==(t=s?.[""]?.lang)&&void 0!==t?t:"en")?e:e?.toLocaleLowerCase()||(0,r.__)("field","woocommerce")},a=(e,t,s)=>{if(t.validity.valid||t.validity.customError)return t.validationMessage;const o=s||((e,t)=>s=>{const o=n(e);let a=(0,r.sprintf)(/* translators: %s field label */ /* translators: %s field label */
(0,r.__)("Please enter a valid %s","woocommerce"),o);if("checkbox"===t.type&&(a=(0,r.__)("Please check this box if you want to proceed.","woocommerce")),s.valueMissing||s.badInput||s.typeMismatch)return a})(e,t);return o(t.validity)||t.validationMessage};var c=s(9712);const i=new Map([["BA",/^([7-8]{1})([0-9]{4})$/],["GB",/^([A-Z]){1}([0-9]{1,2}|[A-Z][0-9][A-Z]|[A-Z][0-9]{2}|[A-Z][0-9]|[0-9][A-Z]){1}([ ])?([0-9][A-Z]{2}){1}|BFPO(?:\s)?([0-9]{1,4})$|BFPO(c\/o[0-9]{1,3})$/i],["IN",/^[1-9]{1}[0-9]{2}\s{0,1}[0-9]{3}$/],["JP",/^([0-9]{3})([-]?)([0-9]{4})$/],["KH",/^[0-9]{6}$/],["LI",/^(94[8-9][0-9])$/],["MN",/^[0-9]{5}(-[0-9]{4})?$/],["NI",/^[1-9]{1}[0-9]{4}$/],["NL",/^([1-9][0-9]{3})(\s?)(?!SA|SD|SS)[A-Z]{2}$/i],["SI",/^([1-9][0-9]{3})$/]]),l=new Map([...c.O,...i]),d=8157==s.j?({postcode:e,country:t})=>{const s=l.get(t)?.test(e);return void 0===s||s}:null,u=e=>o(e,"<price/>");var p=s(7143),m=s(7594);if(8157==s.j)var h=s(1641);const g=e=>{const{applyExtensionCartUpdate:t}=(0,p.dispatch)(h.U);return t(e).catch((e=>("woocommerce_rest_cart_extensions_error"===e?.code&&(0,m.processErrorResponse)(e),Promise.reject(e))))}},9048:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(9874);const o=/^(6981|8157)$/.test(s.j)?r.A:null},953:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(2497);if(/^(251|6981|8157)$/.test(s.j))var o=s(4921);var n=s(5703),a=(s(6303),s(790));const c=e=>{const{prefix:t,suffix:s,thousandSeparator:r,decimalSeparator:o}=e,n=r===o;return n&&console.warn("Thousand separator and decimal separator are the same. This may cause formatting issues."),{thousandSeparator:n?"":r,decimalSeparator:o,fixedDecimalScale:!0,prefix:t,suffix:s,isNumericString:!0}},i=/^(251|6981|8157)$/.test(s.j)?({className:e,value:t,currency:s=n.SITE_CURRENCY,onValueChange:i,displayType:l="text",...d})=>{var u;const p={...n.SITE_CURRENCY,...s},m="string"==typeof t?parseInt(t,10):t;if(!Number.isFinite(m))return null;const h=m/10**p.minorUnit;if(!Number.isFinite(h))return null;const g=(0,o.A)("wc-block-formatted-money-amount","wc-block-components-formatted-money-amount",e),_=null!==(u=d.decimalScale)&&void 0!==u?u:p?.minorUnit,v={...d,...c(p),decimalScale:_,value:void 0,currency:void 0,onValueChange:void 0},b=i?e=>{const t=+e.value*10**p.minorUnit;i(t)}:()=>{};return(0,a.jsx)(r.A,{className:g,displayType:l,...v,value:h,onValueChange:b})}:null},2919:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Button:()=>r.A,CheckboxControl:()=>l,CheckboxList:()=>f,Chip:()=>y,FormStep:()=>C,FormattedMonetaryAmount:()=>E.A,Label:()=>A.A,Panel:()=>N.A,RadioControl:()=>M,RadioControlAccordion:()=>T,RadioControlOption:()=>R,RadioControlOptionLayout:()=>P,RemovableChip:()=>x,SortSelect:()=>$,Spinner:()=>O,StoreNotice:()=>D.A,StoreNoticesContainer:()=>L.A,Subtotal:()=>H.Ve,TextInput:()=>B.A,Textarea:()=>F,Title:()=>j,TotalsFees:()=>H.ht,TotalsItem:()=>H.Zi,TotalsTaxes:()=>H.Zv,TotalsWrapper:()=>U.A,ValidatedCheckboxControl:()=>_,ValidatedTextInput:()=>V.A,ValidationInputError:()=>m.A});var r=s(9048),o=s(4921),n=s(9491),a=s(6087),c=(s(4601),s(790));const i=(0,a.forwardRef)((({className:e,label:t,id:s,onChange:r,children:a,hasError:l=!1,checked:d=!1,disabled:u=!1,errorId:p,errorMessage:m,value:h,...g},_)=>{const v=(0,n.useInstanceId)(i),b=s||`checkbox-control-${v}`;return(0,c.jsx)("div",{className:(0,o.A)("wc-block-components-checkbox",{"has-error":l},e),children:(0,c.jsxs)("label",{htmlFor:b,children:[(0,c.jsx)("input",{ref:_,id:b,className:"wc-block-components-checkbox__input",type:"checkbox",onChange:e=>r(e.target.checked),"aria-invalid":!0===l,checked:d,disabled:u,value:h,...g}),(0,c.jsx)("svg",{className:"wc-block-components-checkbox__mark","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 20",children:(0,c.jsx)("path",{d:"M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"})}),t&&(0,c.jsx)("span",{className:"wc-block-components-checkbox__label",children:t}),a]})})})),l=i;var d=s(3993),u=s(7143),p=s(7594),m=s(4563),h=s(1075);const g=(0,a.forwardRef)((({className:e,id:t,"aria-describedby":s,errorId:r,onChange:n,showError:i=!0,errorMessage:g="",checked:_=!1,customValidation:v=()=>!0,customValidityMessage:b,label:f,validateOnMount:y=!0,instanceId:k="",disabled:w=!1,...x},j)=>{const S=(0,a.useRef)(null),C=(0,a.useId)(),E=t||`textinput-${k||C}`,A=r||E,{setValidationErrors:N,clearValidationError:P}=(0,u.useDispatch)(p.validationStore),R=(0,a.useRef)(v);(0,a.useEffect)((()=>{R.current=v}),[v]);const{validationError:I,validationErrorId:M}=(0,u.useSelect)((e=>{const t=e(p.validationStore);return{validationError:t.getValidationError(A),validationErrorId:t.getValidationErrorId(A)}}),[A]),T=(0,a.useCallback)(((e=!0)=>{const t=S.current||null;null!==t&&(t.checkValidity()&&R.current(t)?P(A):N({[A]:{message:(0,h.N2)(f,t,b),hidden:e}}))}),[P,A,N,f,b]);(0,a.useImperativeHandle)(j,(function(){return{focus(){S.current?.focus()},revalidate(){T(!1)}}}),[T]),(0,a.useEffect)((()=>{y&&T(!0)}),[y,T]),(0,a.useEffect)((()=>()=>{P(A)}),[P,A]),""!==g&&(0,d.isObject)(I)&&(I.message=g);const $=I?.message&&!I?.hidden;return(0,c.jsx)(l,{className:(0,o.A)("wc-block-components-validated-checkbox-control",e,{"has-error":$}),"aria-invalid":!0===$,id:E,"aria-errormessage":i&&$&&M?M:void 0,ref:S,onChange:(0,a.useCallback)((e=>{T(!1),n(e)}),[n,T]),"aria-describedby":s,checked:_,title:"",label:f,disabled:w,...x,children:(0,c.jsx)(m.a,{propertyName:A})})})),_=6981==s.j?g:null;var v=s(7723);function b({option:e,shouldTruncateOptions:t,showExpanded:s,index:r,limit:o,checked:a,disabled:l,renderedShowMore:d,onChange:u}){const p=(0,n.useInstanceId)(b,"wc-block-checkbox-list-option");return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("li",{...t&&!s&&r>=o&&{hidden:!0},children:(0,c.jsx)(i,{id:p,className:"wc-block-checkbox-list__checkbox",label:e.label,checked:a,value:e.value,onChange:()=>{u(e.value)},disabled:l})}),t&&r===o-1&&d]})}s(3108);const f=6981==s.j?({className:e,onChange:t,options:s=[],checked:r=[],isLoading:n=!1,isDisabled:i=!1,limit:l=10})=>{const[d,u]=(0,a.useState)(!1),p=(0,a.useMemo)((()=>[...Array(5)].map(((e,t)=>(0,c.jsx)("li",{style:{width:Math.floor(75*Math.random())+25+"%"},children:" "},t)))),[]),m=(0,a.useMemo)((()=>{const e=s.length-l;return!d&&(0,c.jsx)("li",{className:"show-more",children:(0,c.jsx)("button",{onClick:()=>{u(!0)},"aria-expanded":!1,"aria-label":(0,v.sprintf)(/* translators: %s is referring the remaining count of options */ /* translators: %s is referring the remaining count of options */
(0,v._n)("Show %s more option","Show %s more options",e,"woocommerce"),e),children:(0,v.sprintf)(/* translators: %s number of options to reveal. */ /* translators: %s number of options to reveal. */
(0,v._n)("Show %s more","Show %s more",e,"woocommerce"),e)})},"show-more")}),[s,l,d]),h=(0,a.useMemo)((()=>d&&(0,c.jsx)("li",{className:"show-less",children:(0,c.jsx)("button",{onClick:()=>{u(!1)},"aria-expanded":!0,"aria-label":(0,v.__)("Show less options","woocommerce"),children:(0,v.__)("Show less","woocommerce")})},"show-less")),[d]),g=(0,a.useMemo)((()=>{const e=s.length>l+5;return(0,c.jsxs)(c.Fragment,{children:[s.map(((s,o)=>(0,c.jsx)(b,{option:s,shouldTruncateOptions:e,showExpanded:d,index:o,limit:l,checked:r.includes(s.value),disabled:i,renderedShowMore:m,onChange:t},s.value))),e&&h]})}),[s,t,r,d,l,h,m,i]),_=(0,o.A)("wc-block-checkbox-list","wc-block-components-checkbox-list",{"is-loading":n},e);return(0,c.jsx)("ul",{className:_,children:n?p:g})}:null;s(2334);const y=6981==s.j?({text:e,screenReaderText:t="",element:s="li",className:r="",radius:n="small",children:a=null,...i})=>{const l=s,d=(0,o.A)(r,"wc-block-components-chip","wc-block-components-chip--radius-"+n),u=Boolean(t&&t!==e);return(0,c.jsxs)(l,{className:d,...i,children:[(0,c.jsx)("span",{"aria-hidden":u,className:"wc-block-components-chip__text",children:e}),u&&(0,c.jsx)("span",{className:"screen-reader-text",children:t}),a]})}:null;if(6981==s.j)var k=s(4530);if(6981==s.j)var w=s(1924);const x=6981==s.j?({ariaLabel:e="",className:t="",disabled:s=!1,onRemove:r=()=>{},removeOnAnyClick:n=!1,text:a,screenReaderText:i="",...l})=>{const d=n?"span":"button";if(!e){const t=i&&"string"==typeof i?i:a;e="string"!=typeof t?/* translators: Remove chip. */ /* translators: Remove chip. */
(0,v.__)("Remove","woocommerce"):(0,v.sprintf)(/* translators: %s text of the chip to remove. */ /* translators: %s text of the chip to remove. */
(0,v.__)('Remove "%s"',"woocommerce"),t)}const u={"aria-label":e,disabled:s,onClick:r,onKeyDown:e=>{"Backspace"!==e.key&&"Delete"!==e.key||r()}},p=n?u:{},m=n?{"aria-hidden":!0}:u;return(0,c.jsx)(y,{...l,...p,className:(0,o.A)(t,"is-removable"),element:n?"button":l.element||"li",screenReaderText:i,text:a,children:(0,c.jsx)(d,{className:"wc-block-components-chip__remove",...m,children:(0,c.jsx)(k.A,{className:"wc-block-components-chip__remove-icon",icon:w.A,size:16,role:"img"})})})}:null;s(7255),s(1226);const j=6981==s.j?({children:e,className:t="",headingLevel:s,...r})=>{const n=(0,o.A)("wc-block-components-title",t),a=`h${s}`;return(0,c.jsx)(a,{className:n,...r,children:e})}:null,S=({title:e,stepHeadingContent:t})=>(0,c.jsxs)("div",{className:"wc-block-components-checkout-step__heading",children:[(0,c.jsx)(j,{className:"wc-block-components-checkout-step__title",headingLevel:"2",children:e}),!!t&&(0,c.jsx)("span",{className:"wc-block-components-checkout-step__heading-content",children:t})]}),C=6981==s.j?({id:e,className:t,title:s,legend:r,description:n,children:a,disabled:i=!1,showStepNumber:l=!0,stepHeadingContent:d=()=>{}})=>{const u=r||s?"fieldset":"div";return(0,c.jsxs)(u,{className:(0,o.A)(t,"wc-block-components-checkout-step",{"wc-block-components-checkout-step--with-step-number":l,"wc-block-components-checkout-step--disabled":i}),id:e,disabled:i,children:[!(!r&&!s)&&(0,c.jsx)("legend",{className:"screen-reader-text",children:r||s}),!!s&&(0,c.jsx)(S,{title:s,stepHeadingContent:d()}),(0,c.jsxs)("div",{className:"wc-block-components-checkout-step__container",children:[!!n&&(0,c.jsx)("p",{className:"wc-block-components-checkout-step__description",children:n}),(0,c.jsx)("div",{className:"wc-block-components-checkout-step__content",children:a})]})]})}:null;var E=s(953),A=s(2362),N=s(6514);const P=({label:e,secondaryLabel:t,description:s,secondaryDescription:r,id:n,descriptionStackingDirection:a="row"})=>(0,c.jsxs)("div",{className:"wc-block-components-radio-control__option-layout",children:[(0,c.jsxs)("div",{className:"wc-block-components-radio-control__label-group",children:[e&&(0,c.jsx)("span",{id:n&&`${n}__label`,className:"wc-block-components-radio-control__label",children:e}),t&&(0,c.jsx)("span",{id:n&&`${n}__secondary-label`,className:"wc-block-components-radio-control__secondary-label",children:t})]}),(s||r)&&(0,c.jsxs)("div",{className:(0,o.A)("wc-block-components-radio-control__description-group",{"wc-block-components-radio-control__description-group--column":"column"===a}),children:[s&&(0,c.jsx)("span",{id:n&&`${n}__description`,className:"wc-block-components-radio-control__description",children:s}),r&&(0,c.jsx)("span",{id:n&&`${n}__secondary-description`,className:"wc-block-components-radio-control__secondary-description",children:r})]})]}),R=({checked:e,name:t,onChange:s,option:r,disabled:n=!1,highlightChecked:a=!1,descriptionStackingDirection:i})=>{const{value:l,label:d,description:u,secondaryLabel:p,secondaryDescription:m,content:h}=r;return(0,c.jsxs)("label",{className:(0,o.A)("wc-block-components-radio-control__option",{"wc-block-components-radio-control__option-checked":e,"wc-block-components-radio-control__option--checked-option-highlighted":e&&a}),htmlFor:`${t}-${l}`,children:[(0,c.jsx)("input",{id:`${t}-${l}`,className:"wc-block-components-radio-control__input",type:"radio",name:t,value:l,onChange:e=>s(e.target.value),checked:e,"aria-describedby":(0,o.A)({[`${t}-${l}__secondary-label`]:p,[`${t}-${l}__description`]:u,[`${t}-${l}__secondary-description`]:m,[`${t}-${l}__content`]:h}),"aria-disabled":n,onKeyDown:e=>{n&&["ArrowUp","ArrowDown","AllowLeft","ArrowRight"].includes(e.key)&&e.preventDefault()}}),(0,c.jsx)(P,{id:`${t}-${l}`,label:d,secondaryLabel:p,description:u,secondaryDescription:m,descriptionStackingDirection:i})]})};s(4851);const I=({className:e="",id:t,selected:s="",onChange:r,options:i=[],disabled:l=!1,highlightChecked:d=!1,descriptionStackingDirection:u})=>{const p=(0,n.useInstanceId)(I),m=t||p,h=(0,a.useMemo)((()=>i.findIndex((e=>e.value===s))),[i,s]);return i.length?(0,c.jsx)("div",{className:(0,o.A)("wc-block-components-radio-control",{"wc-block-components-radio-control--highlight-checked--first-selected":d&&0===h,"wc-block-components-radio-control--highlight-checked--last-selected":d&&h===i.length-1,"wc-block-components-radio-control--highlight-checked":d},e),children:i.map((e=>(0,c.jsx)(R,{highlightChecked:d,name:`radio-control-${m}`,checked:e.value===s,option:e,onChange:t=>{r(t),"function"==typeof e.onChange&&e.onChange(t)},disabled:l,descriptionStackingDirection:u},`${m}-${e.value}`)))}):null},M=6981==s.j?I:null,T=(0,n.withInstanceId)((({className:e,instanceId:t,id:s,selected:r,onChange:n,options:i=[],highlightChecked:l=!1})=>{const d=s||t,u=(0,a.useMemo)((()=>i.findIndex((e=>e.value===r))),[i,r]);return i.length?(0,c.jsx)("div",{className:(0,o.A)("wc-block-components-radio-control",{"wc-block-components-radio-control--highlight-checked":l,"wc-block-components-radio-control--highlight-checked--first-selected":l&&0===u,"wc-block-components-radio-control--highlight-checked--last-selected":l&&u===i.length-1},e),children:i.map((e=>{const t="object"==typeof e&&"content"in e,s=e.value===r,a=`radio-control-${d}`;return(0,c.jsxs)("div",{className:(0,o.A)("wc-block-components-radio-control-accordion-option",{"wc-block-components-radio-control-accordion-option--checked-option-highlighted":s&&l}),children:[(0,c.jsx)(R,{name:a,checked:s,option:e,onChange:t=>{n(t),"function"==typeof e.onChange&&e.onChange(t)}}),t&&s&&(0,c.jsx)("div",{id:`${a}-${e.value}__content`,className:(0,o.A)("wc-block-components-radio-control-accordion-content",{"wc-block-components-radio-control-accordion-content-hide":!s}),children:e.content})]},e.value)}))}):null}));s(3847);const $=(0,n.withInstanceId)((({className:e,instanceId:t,label:s="",onChange:r,options:n,screenReaderLabel:a,value:i="",readOnly:l=!1})=>{const d=`wc-block-components-sort-select__select-${t}`;return(0,c.jsxs)("div",{className:(0,o.A)("wc-block-sort-select","wc-block-components-sort-select",e),children:[(0,c.jsx)(A.A,{label:s,screenReaderLabel:a,wrapperElement:"label",wrapperProps:{className:"wc-block-sort-select__label wc-block-components-sort-select__label",htmlFor:d}}),(0,c.jsx)("select",{disabled:!!l,id:d,className:"wc-block-sort-select__select wc-block-components-sort-select__select",onChange:r,value:i,children:n&&n.map((e=>(0,c.jsx)("option",{value:e.key,children:e.label},e.key)))})]})}));s(8141);const O=6981==s.j?()=>(0,c.jsx)("span",{className:"wc-block-components-spinner","aria-hidden":"true"}):null;var D=s(6568),L=s(2285);s(5790);const F=6981==s.j?({className:e="",disabled:t=!1,onTextChange:s,placeholder:r,value:n=""})=>(0,c.jsx)("textarea",{className:(0,o.A)("wc-block-components-textarea",e),disabled:t,onChange:e=>{s(e.target.value)},placeholder:r,rows:2,value:n}):null;var V=s(6293),B=s(9006),H=s(2669),U=s(9463)},2362:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(6087);if(/^(251|6981|8157)$/.test(s.j))var o=s(5269);if(/^(251|6981|8157)$/.test(s.j))var n=s(4921);var a=s(790);const c=/^(251|6981|8157)$/.test(s.j)?({label:e,screenReaderLabel:t,wrapperElement:s,wrapperProps:c={},allowHTML:i=!1})=>{let l;const d=null!=e,u=null!=t;return!d&&u?(l=s||"span",c={...c,className:(0,n.A)(c.className,"screen-reader-text")},(0,a.jsx)(l,{...c,children:t})):(l=s||r.Fragment,d&&u&&e!==t?(0,a.jsxs)(l,{...c,children:[i?(0,a.jsx)(r.RawHTML,{children:(0,o.p)(e,{tags:["b","em","i","strong","p","br","span"],attr:["style"]})}):(0,a.jsx)("span",{"aria-hidden":"true",children:e}),(0,a.jsx)("span",{className:"screen-reader-text",children:t})]}):(0,a.jsx)(l,{...c,children:e}))}:null},6514:(e,t,s)=>{"use strict";s.d(t,{A:()=>p});var r=s(6087);if(/^(6981|8157)$/.test(s.j))var o=s(4921);if(/^(6981|8157)$/.test(s.j))var n=s(4530);if(/^(6981|8157)$/.test(s.j))var a=s(559);if(/^(6981|8157)$/.test(s.j))var c=s(2174);if(/^(6981|8157)$/.test(s.j))var i=s(1069);var l=s(4040),d=s.n(l),u=(s(6698),s(790));const p=/^(6981|8157)$/.test(s.j)?({children:e,className:t,initialOpen:s=!1,hasBorder:l=!1,headingLevel:p,title:m,titleTag:h,state:g})=>{let[_,v]=(0,r.useState)(s);return Array.isArray(g)&&2===g.length&&([_,v]=g),h&&d()("Panel component's titleTag prop",{since:"9.4.0"}),(0,u.jsxs)("div",{role:p?"heading":void 0,"aria-level":p||void 0,className:(0,o.A)(t,"wc-block-components-panel",{"has-border":l}),children:[(0,u.jsxs)(i.$,{render:(0,u.jsx)("div",{}),"aria-expanded":_,className:"wc-block-components-panel__button",onClick:()=>v(!_),children:[(0,u.jsx)(n.A,{"aria-hidden":"true",className:"wc-block-components-panel__button-icon",icon:_?a.A:c.A}),m]}),_&&(0,u.jsx)("div",{className:"wc-block-components-panel__content",children:e})]})}:null},6568:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>a}),/^(6981|8157)$/.test(s.j))var r=s(4921);var o=s(9021),n=s(790);const a=/^(6981|8157)$/.test(s.j)?({className:e,children:t,status:s,...a})=>(0,n.jsx)(o.A,{className:(0,r.A)("wc-block-store-notice",e),status:s,...a,children:t}):null},2285:(e,t,s)=>{"use strict";s.d(t,{A:()=>S});var r=s(7143),o=s(7594);if(251!=s.j)var n=s(1233);var a=s(6087),c=s(692),i=(s(6751),s(7723));if(251!=s.j)var l=s(4921);if(251!=s.j)var d=s(5269);if(251!=s.j)var u=s(9464);var p=s(8537),m=s(6568),h=s(790);const g=251!=s.j?({className:e,notices:t})=>{const s=(0,a.useRef)(null),{removeNotice:o}=(0,r.useDispatch)("core/notices"),n=t.map((e=>"error"===e.status||"warning"===e.status?e.id:null)).filter(Boolean),c=(0,u.Z)(n);(0,a.useEffect)((()=>{const e=s.current;if(!e)return;const t=e.ownerDocument.activeElement;t&&-1!==["input","select","button","textarea"].indexOf(t.tagName.toLowerCase())&&"radio"!==t.getAttribute("type")||n.filter((e=>!c||!c.includes(e))).length&&e?.scrollIntoView&&e.scrollIntoView({behavior:"smooth"})}),[n,c,s]);const g=t.filter((({isDismissible:e})=>!!e)),_=t.filter((({isDismissible:e})=>!e)),v={error:g.filter((({status:e})=>"error"===e)),success:g.filter((({status:e})=>"success"===e)),warning:g.filter((({status:e})=>"warning"===e)),info:g.filter((({status:e})=>"info"===e)),default:g.filter((({status:e})=>"default"===e))};return(0,h.jsxs)("div",{ref:s,className:(0,l.A)(e,"wc-block-components-notices"),children:[_.map((e=>(0,h.jsx)(m.A,{...e,children:(0,h.jsx)(a.RawHTML,{children:(0,d.p)((0,p.decodeEntities)(e.content))})},e.id+"-"+e.context))),Object.entries(v).map((([e,t])=>{if(!t.length)return null;const s=t.filter(((e,t,s)=>s.findIndex((t=>t.content===e.content))===t)).map((e=>({...e,content:(0,d.p)((0,p.decodeEntities)(e.content))}))),r={status:e,onRemove:()=>{t.forEach((e=>{o(e.id,e.context)}))}};return 1===s.length?(0,h.jsx)(m.A,{...r,children:(0,h.jsx)(a.RawHTML,{children:t[0].content})},"store-notice-"+e):(0,h.jsx)(m.A,{...r,summary:"error"===e?(0,i.__)("Please fix the following errors before continuing","woocommerce"):"",children:(0,h.jsx)("ul",{children:s.map((e=>(0,h.jsx)("li",{children:(0,h.jsx)(a.RawHTML,{children:e.content})},e.id+"-"+e.context)))})},"store-notice-"+e)}))]})}:null;var _=s(9491);if(251!=s.j)var v=s(2517);if(251!=s.j)var b=s(6438);s(1356);var f=s(9021);const y=251!=s.j?({onRemove:e=()=>{},children:t,listRef:s,className:r,...o})=>((0,a.useEffect)((()=>{const t=setTimeout((()=>{e()}),1e4);return()=>clearTimeout(t)}),[e]),(0,h.jsx)(f.A,{className:(0,l.A)(r,"wc-block-components-notice-snackbar"),...o,onRemove:()=>{s&&s.current&&s.current.focus(),e()},children:t})):null;var k=s(1609);const w=251!=s.j?({notices:e,className:t,onRemove:s=()=>{}})=>{const r=(0,a.useRef)(null),o=(0,_.useReducedMotion)(),n=e=>()=>s(e?.id||"");return(0,h.jsx)("div",{className:(0,l.A)(t,"wc-block-components-notice-snackbar-list"),tabIndex:-1,ref:r,children:o?e.map((e=>{const{content:t,...s}=e;return(0,k.createElement)(y,{...s,onRemove:n(e),listRef:r,key:e.id},e.content)})):(0,h.jsx)(v.A,{children:e.map((e=>{const{content:t,...s}=e;return(0,h.jsx)(b.A,{timeout:500,classNames:"notice-transition",children:(0,h.jsx)(y,{...s,onRemove:n(e),listRef:r,children:t})},"snackbar-"+e.id)}))})})}:null,x=251!=s.j?({className:e,notices:t})=>{const{removeNotice:s}=(0,r.useDispatch)("core/notices");return(0,h.jsx)(w,{className:(0,l.A)(e,"wc-block-components-notices__snackbar"),notices:t,onRemove:e=>{t.forEach((t=>{t.explicitDismiss&&t.id===e?s(t.id,t.context):t.explicitDismiss||s(t.id,t.context)}))}})}:null,j=(e,t)=>e.map((e=>({...e,context:t}))),S=251!=s.j?({className:e="",context:t="",additionalNotices:s=[]})=>{const{registerContainer:i,unregisterContainer:l}=(0,r.useDispatch)(o.storeNoticesStore),{suppressNotices:d,registeredContainers:u}=(0,r.useSelect)((e=>({suppressNotices:e(o.paymentStore).isExpressPaymentMethodActive(),registeredContainers:e(o.storeNoticesStore).getRegisteredContainers()})),[]),p=(0,a.useMemo)((()=>Array.isArray(t)?t:[t]),[t]),m=(0,n.h5)().filter((e=>p.some((t=>e.includes(t+"/")))&&!u.includes(e))),_=(0,r.useSelect)((e=>{const t=e(c.store).getNotices;return[...m.flatMap((e=>j(t(e),e))),...p.flatMap((e=>j(t(e).concat(s),e)))].filter(Boolean)}),[p,m,s])||[];return(0,a.useEffect)((()=>(p.forEach((e=>i(e))),()=>{p.forEach((e=>l(e)))})),[p,i,l]),d?null:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(g,{className:e,notices:_.filter((e=>"default"===e.type))}),(0,h.jsx)(x,{className:e,notices:_.filter((e=>"snackbar"===e.type))})]})}:null},9006:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(4921),o=s(6087),n=s(8537),a=s(2362),c=(s(6318),s(790));const i=(0,o.forwardRef)((({className:e,id:t,type:s="text",ariaLabel:i,ariaDescribedBy:l,label:d,screenReaderLabel:u,disabled:p,help:m,autoCapitalize:h="off",autoComplete:g="off",value:_="",onChange:v,required:b=!1,onBlur:f=()=>{},feedback:y,...k},w)=>{const[x,j]=(0,o.useState)(!1);return(0,c.jsxs)("div",{className:(0,r.A)("wc-block-components-text-input",e,{"is-active":x||_}),children:[(0,c.jsx)("input",{type:s,id:t,value:(0,n.decodeEntities)(_),ref:w,autoCapitalize:h,autoComplete:g,onChange:e=>{v(e.target.value)},onFocus:()=>j(!0),onBlur:e=>{f(e.target.value),j(!1)},"aria-label":i||d,disabled:p,"aria-describedby":m&&!l?t+"__help":l,required:b,...k}),(0,c.jsx)(a.A,{label:d,screenReaderLabel:u||d,wrapperElement:"label",wrapperProps:{htmlFor:t},htmlFor:t}),!!m&&(0,c.jsx)("p",{id:t+"__help",className:"wc-block-components-text-input__help",children:m}),y]})})),l=/^(251|6981|8157)$/.test(s.j)?i:null},6293:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var r=s(6087),o=s(4921),n=s(3993),a=s(7143),c=s(7594),i=s(9464),l=s(9491),d=s(9006),u=(s(6318),s(4563)),p=s(1075),m=s(790);const h=(0,r.forwardRef)((({className:e,id:t,type:s="text",ariaDescribedBy:g="",errorId:_,focusOnMount:v=!1,onChange:b,showError:f=!0,errorMessage:y="",value:k="",customValidation:w=()=>!0,customValidityMessage:x,feedback:j=null,customFormatter:S=e=>e,label:C,validateOnMount:E=!0,instanceId:A="",...N},P)=>{const[R,I]=(0,r.useState)(!0),M=(0,i.Z)(k),T=(0,r.useRef)(null),$=(0,l.useInstanceId)(h,"",A),O=void 0!==t?t:"textinput-"+$,D=void 0!==_?_:O,{setValidationErrors:L,hideValidationError:F,clearValidationError:V,showValidationError:B}=(0,a.useDispatch)(c.validationStore),H=(0,r.useRef)(w);(0,r.useEffect)((()=>{H.current=w}),[w]);const{validationError:U,validationErrorId:q}=(0,a.useSelect)((e=>{const t=e(c.validationStore);return{validationError:t.getValidationError(D),validationErrorId:t.getValidationErrorId(D)}}),[D]),W=(0,r.useCallback)(((e=!0)=>{const t=T.current||null;if(null===t)return;if(t.value=t.value.trim(),t.setCustomValidity(""),t.checkValidity()&&H.current(t)&&e)return void V(D);e||B(D);const s=(0,p.N2)(C,t,x);s&&L({[D]:{message:s,hidden:e}})}),[V,D,L,C,x,B]);(0,r.useImperativeHandle)(P,(function(){return{focus(){T.current?.focus()},revalidate(){W(!k)},isFocused:()=>T.current?.ownerDocument?.activeElement===T.current,setErrorMessage(e){T.current?.setCustomValidity(e)}}}),[W,k]),(0,r.useEffect)((()=>{if(k!==M&&(k||M)&&T&&null!==T.current&&T.current?.ownerDocument?.activeElement!==T.current){const e=S(T.current.value);e!==k?b(e):W(!0)}}),[W,S,k,M,b]),(0,r.useEffect)((()=>{R&&(I(!1),v&&T.current?.focus(),!E&&v||W(!0))}),[E,v,R,I,W]),(0,r.useEffect)((()=>()=>{V(D)}),[V,D]),""!==y&&(0,n.isObject)(U)&&(U.message=y);const z=U?.message&&!U?.hidden;return(0,m.jsx)(d.A,{className:(0,o.A)(e,{"has-error":z}),"aria-invalid":!0===z,id:O,"aria-errormessage":f&&z&&q?q:void 0,type:s,feedback:f&&z?(0,m.jsx)(u.a,{errorMessage:y,propertyName:D,elementId:D}):j,ref:T,onChange:e=>{F(D),W(!0);const t=S(e);t!==k&&b(t)},onBlur:()=>W(!1),"aria-describedby":g,value:k,title:"",label:C,...N})})),g=/^(6981|8157)$/.test(s.j)?h:null},9463:(e,t,s)=>{"use strict";if(s.d(t,{A:()=>a}),/^(6981|8157)$/.test(s.j))var r=s(4921);var o=s(6087),n=(s(5205),s(790));const a=/^(6981|8157)$/.test(s.j)?({children:e,slotWrapper:t=!1,className:s})=>o.Children.count(e)?(0,n.jsx)("div",{className:(0,r.A)(s,"wc-block-components-totals-wrapper",{"slot-wrapper":t}),children:e}):null:null},2669:(e,t,s)=>{"use strict";if(s.d(t,{Ve:()=>u,ht:()=>m,Zi:()=>i,Zv:()=>p}),251!=s.j)var r=s(4921);var o=s(6087),n=(s(8143),s(953)),a=s(790);const c=({value:e,currency:t})=>(0,o.isValidElement)(e)?(0,a.jsx)("div",{className:"wc-block-components-totals-item__value",children:e}):Number.isFinite(e)?(0,a.jsx)(n.A,{className:"wc-block-components-totals-item__value",currency:t||void 0,value:e}):null,i=251!=s.j?({className:e,currency:t,label:s,value:o,description:n})=>(0,a.jsxs)("div",{className:(0,r.A)("wc-block-components-totals-item",e),children:[(0,a.jsx)("span",{className:"wc-block-components-totals-item__label",children:s}),(0,a.jsx)(c,{value:o,currency:t}),(0,a.jsx)("div",{className:"wc-block-components-totals-item__description",children:n})]}):null;var l=s(7723),d=s(5703);const u=251!=s.j?({currency:e,values:t,className:s})=>{const{total_items:r,total_items_tax:o}=t,n=parseInt(r,10),c=parseInt(o,10);return(0,a.jsx)(i,{className:s,currency:e,label:(0,l.__)("Subtotal","woocommerce"),value:(0,d.getSetting)("displayCartPricesIncludingTax",!1)?n+c:n})}:null,p=251!=s.j?({currency:e,values:t,className:s,showRateAfterTaxName:o})=>{const{total_tax:n,tax_lines:c}=t;if(!(0,d.getSetting)("taxesEnabled",!0)&&parseInt(n,10)<=0)return null;const u=(0,d.getSetting)("displayItemizedTaxes",!1),p=u&&c.length>0?(0,a.jsxs)(a.Fragment,{children:[c.map((({name:t,rate:n,price:c},l)=>{const d=`${t}${o?` ${n}`:""}`;return(0,a.jsx)(i,{className:(0,r.A)("wc-block-components-totals-taxes",s),currency:e,label:d,value:parseInt(c,10)},`tax-line-${l}`)}))," "]}):null;return u?p:(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(i,{className:(0,r.A)("wc-block-components-totals-taxes",s),currency:e,label:(0,l.__)("Taxes","woocommerce"),value:parseInt(n,10),description:null})})}:null,m=251!=s.j?({currency:e,cartFees:t,className:s})=>(0,a.jsx)(a.Fragment,{children:t.map((({id:t,key:o,name:n,totals:c},u)=>{const p=parseInt(c.total,10);if(!p)return null;const m=parseInt(c.total_tax,10);return(0,a.jsx)(i,{className:(0,r.A)("wc-block-components-totals-fees","wc-block-components-totals-fees__"+o,s),currency:e,label:n||(0,l.__)("Fee","woocommerce"),value:(0,d.getSetting)("displayCartPricesIncludingTax",!1)?p+m:p},t||`${u}-${n}`)}))}):null},4563:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,a:()=>i});var r=s(7143),o=s(7594);if(/^(251|6981|8157)$/.test(s.j))var n=s(4530);if(/^(251|6981|8157)$/.test(s.j))var a=s(9717);s(9373);var c=s(790);const i=({errorMessage:e="",propertyName:t="",elementId:s=""})=>{const{validationError:i,validationErrorId:l}=(0,r.useSelect)((e=>{const r=e(o.validationStore);return{validationError:r.getValidationError(t),validationErrorId:r.getValidationErrorId(s)}}),[t,s]);if(!e||"string"!=typeof e){if(!i?.message||i?.hidden)return null;e=i.message}return(0,c.jsx)("div",{className:"wc-block-components-validation-error",role:"alert",children:(0,c.jsxs)("p",{id:l,children:[(0,c.jsx)(n.A,{icon:a.A}),(0,c.jsx)("span",{children:e})]})})},l=/^(6981|8157)$/.test(s.j)?i:null},5893:()=>{},6882:()=>{},359:()=>{},2770:()=>{},6161:()=>{},6713:()=>{},6983:()=>{},9287:()=>{},7605:()=>{},3692:()=>{},8879:()=>{},2840:()=>{},8349:()=>{},2793:()=>{},1962:()=>{},619:()=>{},8413:()=>{},6562:()=>{},4249:()=>{},8963:()=>{},9961:()=>{},7575:()=>{},959:()=>{},8501:()=>{},9959:()=>{},8306:()=>{},9163:()=>{},1356:()=>{},3930:()=>{},7525:()=>{},2831:()=>{},4147:()=>{},7215:()=>{},4601:()=>{},3108:()=>{},2334:()=>{},7255:()=>{},6303:()=>{},6698:()=>{},4851:()=>{},3847:()=>{},8141:()=>{},6751:()=>{},6318:()=>{},5790:()=>{},1226:()=>{},5205:()=>{},8143:()=>{},9373:()=>{}}]);