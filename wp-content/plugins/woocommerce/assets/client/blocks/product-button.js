(globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[]).push([[7409],{595:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Block:()=>f,default:()=>w});var s=r(6087),o=r(4921),n=r(7723),c=r(7052),a=r(1057),i=r(371),d=r(8537),l=r(8331),u=r(5703),p=r(415),m=r(1616),_=(r(7316),r(2281)),y=r(790);const b=({product:e,isDescendantOfAddToCartWithOptions:t,className:r,style:s})=>{const{id:i,permalink:p,add_to_cart:m,has_options:_,is_purchasable:b,is_in_stock:g}=e,{dispatchStoreEvent:h}=(0,c.y)(),{cartQuantity:f,addingToCart:w,addToCart:T}=(0,a.R)(i),C=Number.isFinite(f)&&f>0,S=!_&&b&&g,E=(0,d.decodeEntities)(m?.description||""),k=(({cartQuantity:e,productCartDetails:t,isDescendantOfAddToCartWithOptions:r})=>Number.isFinite(e)&&e>0?(0,n.sprintf)(/* translators: %s number of products in cart. */ /* translators: %s number of products in cart. */
(0,n._n)("%d in cart","%d in cart",e,"woocommerce"),e):r&&t?.single_text?t?.single_text:t?.text||(0,n.__)("Add to cart","woocommerce"))({cartQuantity:f,productCartDetails:m,isDescendantOfAddToCartWithOptions:t}),v=S?"button":"a",R={};return S?R.onClick=async()=>{await T(),h("cart-add-item",{product:e});const{cartRedirectAfterAdd:t}=(0,u.getSetting)("productsSettings");t&&(window.location.href=l.Vo)}:(R.href=p,R.rel="nofollow",R.onClick=()=>{h("product-view-link",{product:e})}),(0,y.jsx)(v,{...R,"aria-label":E,disabled:w,className:(0,o.A)(r,"wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",{loading:w,added:C}),style:s,children:k})},g=({className:e,style:t})=>(0,y.jsx)("button",{className:(0,o.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button","wc-block-components-product-button__button--placeholder",e),style:t,disabled:!0,children:(0,n.__)("Add to cart","woocommerce")}),h=({className:e,style:t,blockClientId:r})=>{const{current:c,registerListener:a,unregisterListener:i}=(0,_.A)();(0,s.useEffect)((()=>{if(r)return a(r),()=>{i(r)}}),[r,a,i]);const d="external"===c?.slug?(0,n.__)("Buy product","woocommerce"):(0,n.__)("Add to cart","woocommerce");return(0,y.jsx)("button",{className:(0,o.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",e),style:t,disabled:!0,children:d})},f=e=>{const{className:t,textAlign:r,blockClientId:s}=e,n=(0,i.p)(e),{parentClassName:c}=(0,p.useInnerBlockLayoutContext)(),{isLoading:a,product:d}=(0,p.useProductDataContext)();return(0,y.jsx)("div",{className:(0,o.A)(t,"wp-block-button","wc-block-components-product-button",{[`${c}__product-add-to-cart`]:c,[`align-${r}`]:r}),children:a?(0,y.jsx)(g,{className:n.className,style:n.style}):(0,y.jsx)(y.Fragment,{children:d.id?(0,y.jsx)(b,{product:d,style:n.style,className:n.className,isDescendantOfAddToCartWithOptions:e["woocommerce/isDescendantOfAddToCartWithOptions"]}):(0,y.jsx)(h,{style:n.style,className:n.className,isLoading:a,blockClientId:s})})})},w=(0,m.withProductDataContext)(f)},2e3:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(8331),o=r(3993),n=r(7723);const c=Object.entries(s.iI).reduce(((e,[t,r])=>(e[t]=Object.entries(r).reduce(((e,[t,r])=>(e[t]=(e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,n.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,n.__)("%s (optional)","woocommerce"),e.label)),e.index&&((0,o.isNumber)(e.index)&&(t.index=e.index),(0,o.isString)(e.index)&&(t.index=parseInt(e.index,10))),e.hidden&&(t.required=!1),t})(r),e)),{}),e)),{}),a=(e,t,r="")=>{const s=r&&void 0!==c[r]?c[r]:{};return e.map((e=>({key:e,...t&&e in t?t[e]:{},...s&&e in s?s[e]:{}}))).sort(((e,t)=>e.index-t.index))}},6037:(e,t,r)=>{"use strict";r.d(t,{U:()=>u});var s=r(6087),o=r(7594),n=r(7143),c=r(1174),a=r(3757);const i=e=>{const t=e?.detail;t&&t.preserveCartData||(0,n.dispatch)(o.cartStore).invalidateResolutionForStore()},d=e=>{(e?.persisted||"back_forward"===(0,c.F)())&&(0,n.dispatch)(o.cartStore).invalidateResolutionForStore()},l=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},u=()=>{(0,s.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),window.wcBlocksStoreCartListeners?.count>0)return void window.wcBlocksStoreCartListeners.count++;document.body.addEventListener("wc-blocks_added_to_cart",i),document.body.addEventListener("wc-blocks_removed_from_cart",i),window.addEventListener("pageshow",d);const e=(0,a.f2)("added_to_cart","wc-blocks_added_to_cart"),t=(0,a.f2)("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",i),document.body.removeEventListener("wc-blocks_removed_from_cart",i),window.removeEventListener("pageshow",d),e(),t()}})(),l)),[])}},5460:(e,t,r)=>{"use strict";r.d(t,{V:()=>b});var s=r(1824),o=r.n(s),n=r(6087),c=r(7594),a=r(7143),i=r(8537),d=r(4982),l=r(6037);const u={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},p={...u,email:""},m={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:c.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},_=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,i.decodeEntities)(t)]))),y={cartCoupons:c.EMPTY_CART_COUPONS,cartItems:c.EMPTY_CART_ITEMS,cartFees:c.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:c.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:c.EMPTY_CART_ITEM_ERRORS,cartTotals:m,cartIsLoading:!0,cartErrors:c.EMPTY_CART_ERRORS,billingData:p,billingAddress:p,shippingAddress:u,shippingRates:c.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:c.EMPTY_PAYMENT_METHODS,paymentRequirements:c.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:c.EMPTY_EXTENSIONS},b=(e={shouldSelect:!0})=>{const{shouldSelect:t}=e,r=(0,n.useRef)(),s=(0,n.useRef)(p),i=(0,n.useRef)(u);(0,l.U)();const m=(0,a.useSelect)(((e,{dispatch:r})=>{if(!t)return y;const n=e(c.cartStore),a=n.getCartData(),l=n.getCartErrors(),u=n.getCartTotals(),p=!n.hasFinishedResolution("getCartData"),m=n.isCustomerDataUpdating(),{receiveCart:b,receiveCartContents:g}=r(c.cartStore),h=a.fees.length>0?a.fees.map((e=>_(e))):c.EMPTY_CART_FEES,f=a.coupons.length>0?a.coupons.map((e=>({...e,label:e.code}))):c.EMPTY_CART_COUPONS,w=(0,d.TU)(_(a.billingAddress)),T=a.needsShipping?(0,d.TU)(_(a.shippingAddress)):w;return o()(w,s.current)||(s.current=w),o()(T,i.current)||(i.current=T),{cartCoupons:f,cartItems:a.items,crossSellsProducts:a.crossSells,cartFees:h,cartItemsCount:a.itemsCount,cartItemsWeight:a.itemsWeight,cartNeedsPayment:a.needsPayment,cartNeedsShipping:a.needsShipping,cartItemErrors:a.errors,cartTotals:u,cartIsLoading:p,cartErrors:l,billingData:s.current,billingAddress:s.current,shippingAddress:i.current,extensions:a.extensions,shippingRates:a.shippingRates,isLoadingRates:m,cartHasCalculatedShipping:a.hasCalculatedShipping,paymentRequirements:a.paymentRequirements,receiveCart:b,receiveCartContents:g}}),[t]);return r.current&&o()(r.current,m)||(r.current=m),r.current}},1057:(e,t,r)=>{"use strict";r.d(t,{R:()=>d});var s=r(6087),o=r(7143),n=r(7594),c=r(8537),a=r(5460);const i=(e,t)=>{const r=e.find((({id:e})=>e===t));return r?r.quantity:0},d=e=>{const{addItemToCart:t}=(0,o.useDispatch)(n.cartStore),{cartItems:r,cartIsLoading:d}=(0,a.V)(),{createErrorNotice:l,removeNotice:u}=(0,o.useDispatch)("core/notices"),[p,m]=(0,s.useState)(!1),_=(0,s.useRef)(i(r,e));return(0,s.useEffect)((()=>{const t=i(r,e);t!==_.current&&(_.current=t)}),[r,e]),{cartQuantity:Number.isFinite(_.current)?_.current:0,addingToCart:p,cartIsLoading:d,addToCart:(r=1)=>(m(!0),t(e,r).then((()=>{u("add-to-cart")})).catch((e=>{l((0,c.decodeEntities)(e.message),{id:"add-to-cart",context:"wc/all-products",isDismissible:!0})})).finally((()=>{m(!1)})))}}},7052:(e,t,r)=>{"use strict";r.d(t,{y:()=>c});var s=r(2619),o=r(7143),n=r(6087);const c=()=>({dispatchStoreEvent:(0,n.useCallback)(((e,t={})=>{try{(0,s.doAction)(`experimental__woocommerce_blocks-${e}`,t)}catch(e){console.error(e)}}),[]),dispatchCheckoutEvent:(0,n.useCallback)(((e,t={})=>{try{(0,s.doAction)(`experimental__woocommerce_blocks-checkout-${e}`,{...t,storeCart:(0,o.select)("wc/store/cart").getCartData()})}catch(e){console.error(e)}}),[])})},371:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(4921),o=r(3993),n=r(219),c=r(17);const a=e=>{const t=(e=>{const t=(0,o.isObject)(e)?e:{style:{}};let r=t.style;return(0,o.isString)(r)&&(r=JSON.parse(r)||{}),(0,o.isObject)(r)||(r={}),{...t,style:r}})(e),r=(0,c.BK)(t),a=(0,c.aR)(t),i=(0,c.fo)(t),d=(0,n.x)(t);return{className:(0,s.A)(d.className,r.className,a.className,i.className),style:{...d.style,...r.style,...a.style,...i.style}}}},219:(e,t,r)=>{"use strict";r.d(t,{x:()=>o});var s=r(3993);const o=e=>{const t=(0,s.isObject)(e.style.typography)?e.style.typography:{},r=(0,s.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:r,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}}},4982:(e,t,r)=>{"use strict";r.d(t,{TU:()=>c});var s=r(2e3),o=r(8331),n=r(5703);r(3993),r(8537),r(3832);const c=e=>{const t=(0,s.A)(o.Hw,n.defaultFields,e.country),r=Object.assign({},e);return t.forEach((({key:t,hidden:s})=>{!0===s&&((e,t)=>e in t)(t,e)&&(r[t]="")})),r}},17:(e,t,r)=>{"use strict";r.d(t,{BK:()=>d,aR:()=>l,fo:()=>u});var s=r(4921),o=r(7356),n=r(9786),c=r(3993);function a(e={}){const t={};return(0,n.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function i(e,t){return e&&t?`has-${(0,o.c)(t)}-${e}`:""}function d(e){const{backgroundColor:t,textColor:r,gradient:o,style:n}=e,d=i("background-color",t),l=i("color",r),u=function(e){if(e)return`has-${e}-gradient-background`}(o),p=u||n?.color?.gradient;return{className:(0,s.A)(l,u,{[d]:!p&&!!d,"has-text-color":r||n?.color?.text,"has-background":t||n?.color?.background||o||n?.color?.gradient,"has-link-color":(0,c.isObject)(n?.elements?.link)?n?.elements?.link?.color:void 0}),style:a({color:n?.color||{}})}}function l(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:r}=e,o=t?i("border-color",t):"";return(0,s.A)({"has-border-color":!!t||!!r?.border?.color,[o]:!!o})}(e),style:a({border:t})}}function u(e){return{className:void 0,style:a({spacing:e.style?.spacing||{}})}}},1174:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});const s=()=>window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:""},3757:(e,t,r)=>{"use strict";r.d(t,{f2:()=>o});const s=window.CustomEvent||null,o=(e,t,r=!1,o=!1)=>{if("function"!=typeof jQuery)return()=>{};const n=()=>{((e,{bubbles:t=!1,cancelable:r=!1,element:o,detail:n={}})=>{if(!s)return;o||(o=document.body);const c=new s(e,{bubbles:t,cancelable:r,detail:n});o.dispatchEvent(c)})(t,{bubbles:r,cancelable:o})};return jQuery(document).on(e,n),()=>jQuery(document).off(e,n)}},2266:(e,t,r)=>{"use strict";r.d(t,{EF:()=>s,Ie:()=>c,UI:()=>n,ht:()=>a,j9:()=>o});const s="woocommerce/product-type-template-state",o="SWITCH_PRODUCT_TYPE",n="SET_PRODUCT_TYPES",c="REGISTER_LISTENER",a="UNREGISTER_LISTENER"},8207:(e,t,r)=>{"use strict";r.d(t,{M:()=>i});var s=r(7143),o=r(2266);const n=(0,r(7254).c)(),c={productTypes:{list:n,current:n[0]?.slug},listeners:[]},a={switchProductType:e=>({type:o.j9,current:e}),setProductTypes:e=>({type:o.UI,productTypes:e}),registerListener:e=>({type:o.Ie,listener:e}),unregisterListener:e=>({type:o.ht,listener:e})},i=(0,s.createReduxStore)(o.EF,{reducer:(e=c,t)=>{switch(t.type){case o.UI:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case o.j9:return{...e,productTypes:{...e.productTypes,current:t.current}};case o.Ie:return{...e,listeners:[...e.listeners,t.listener||""]};case o.ht:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:a,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});(0,s.select)(o.EF)||(0,s.register)(i)},2281:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(7143),o=r(8207);function n(){const{productTypes:e,current:t,registeredListeners:r}=(0,s.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:r,getRegisteredListeners:s}=e(o.M);return{productTypes:t(),current:r(),registeredListeners:s()}}),[]),{switchProductType:n,registerListener:c,unregisterListener:a}=(0,s.useDispatch)(o.M);return{productTypes:e,current:t,set:n,registeredListeners:r,registerListener:c,unregisterListener:a}}},7254:(e,t,r)=>{"use strict";r.d(t,{c:()=>o});const s=(0,r(5703).getSetting)("productTypes",{});function o(){return Object.keys(s).map((e=>({slug:e,label:s[e]})))}},7316:()=>{}}]);