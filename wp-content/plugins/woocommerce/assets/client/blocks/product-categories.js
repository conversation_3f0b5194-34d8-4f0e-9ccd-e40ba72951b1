(()=>{var e,o,t,r={422:(e,o,t)=>{"use strict";const r=window.wp.blocks;var a=t(4530),c=t(3196);const s=JSON.parse('{"name":"woocommerce/product-categories","title":"Product Categories List","category":"woocommerce","description":"Show all product categories as a list or dropdown.","keywords":["WooCommerce"],"supports":{"interactivity":{"clientNavigation":true},"align":["wide","full"],"html":false,"color":{"background":false,"link":true},"typography":{"fontSize":true,"lineHeight":true}},"attributes":{"align":{"type":"string"},"hasCount":{"type":"boolean","default":true},"hasImage":{"type":"boolean","default":false},"hasEmpty":{"type":"boolean","default":false},"isDropdown":{"type":"boolean","default":false},"isHierarchical":{"type":"boolean","default":true},"showChildrenOnly":{"type":"boolean","default":false}},"example":{"attributes":{"hasCount":true,"hasImage":false}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),i=window.wp.blockEditor;var l=t(7723);const n=window.wp.serverSideRender;var d=t.n(n);const p=e=>!(e=>null===e)(e)&&e instanceof Object&&e.constructor===Object,u=window.wp.data,h=window.wp.components;var w=t(790);const m=()=>(0,w.jsx)(h.Placeholder,{icon:(0,w.jsx)(a.A,{icon:c.A}),label:(0,l.__)("Product Categories List","woocommerce"),className:"wc-block-product-categories",children:(0,l.__)("This block displays the product categories for your store. To use it you first need to create a product and assign it to a category.","woocommerce")}),g=({attributes:e,setAttributes:o,name:t})=>{const r=(0,u.useSelect)((e=>e("core/edit-site"))),a=(0,u.useSelect)((e=>e("core/edit-widgets"))),c=(e=>{if(p(e)){const o=e.getEditedPostType();return"wp_template"===o||"wp_template_part"===o}return!1})(r),s=(e=>{if(p(e)){const o=e.getWidgetAreas();return Array.isArray(o)&&o.length>0}return!1})(a),n=(0,i.useBlockProps)({className:"wc-block-product-categories"});return(0,w.jsxs)("div",{...n,children:[(()=>{const{hasCount:t,hasImage:r,hasEmpty:a,isDropdown:n,isHierarchical:d,showChildrenOnly:p}=e;return(0,w.jsxs)(i.InspectorControls,{children:[(0,w.jsx)(h.PanelBody,{title:(0,l.__)("List Settings","woocommerce"),initialOpen:!0,children:(0,w.jsxs)(h.__experimentalToggleGroupControl,{label:(0,l.__)("Display style","woocommerce"),isBlock:!0,value:n?"dropdown":"list",onChange:e=>o({isDropdown:"dropdown"===e}),children:[(0,w.jsx)(h.__experimentalToggleGroupControlOption,{value:"list",label:(0,l.__)("List","woocommerce")}),(0,w.jsx)(h.__experimentalToggleGroupControlOption,{value:"dropdown",label:(0,l.__)("Dropdown","woocommerce")})]})}),(0,w.jsxs)(h.PanelBody,{title:(0,l.__)("Content","woocommerce"),initialOpen:!0,children:[(0,w.jsx)(h.ToggleControl,{label:(0,l.__)("Show product count","woocommerce"),checked:t,onChange:()=>o({hasCount:!t})}),!n&&(0,w.jsx)(h.ToggleControl,{label:(0,l.__)("Show category images","woocommerce"),help:r?(0,l.__)("Category images are visible.","woocommerce"):(0,l.__)("Category images are hidden.","woocommerce"),checked:r,onChange:()=>o({hasImage:!r})}),(0,w.jsx)(h.ToggleControl,{label:(0,l.__)("Show hierarchy","woocommerce"),checked:d,onChange:()=>o({isHierarchical:!d})}),(0,w.jsx)(h.ToggleControl,{label:(0,l.__)("Show empty categories","woocommerce"),checked:a,onChange:()=>o({hasEmpty:!a})}),(c||s)&&(0,w.jsx)(h.ToggleControl,{label:(0,l.__)("Only show children of current category","woocommerce"),help:(0,l.__)("This will affect product category pages","woocommerce"),checked:p,onChange:()=>o({showChildrenOnly:!p})})]})]},"inspector")})(),(0,w.jsx)(h.Disabled,{children:(0,w.jsx)(d(),{block:t,attributes:e,EmptyResponsePlaceholder:m})})]})};t(1008),t(2357),(0,r.registerBlockType)(s,{icon:{src:(0,w.jsx)(a.A,{icon:c.A,className:"wc-block-editor-components-block-icon"})},transforms:{from:[{type:"block",blocks:["core/legacy-widget"],isMatch:({idBase:e,instance:o})=>"woocommerce_product_categories"===e&&!!o?.raw,transform:({instance:e})=>(0,r.createBlock)("woocommerce/product-categories",{hasCount:!!e.raw.count,hasEmpty:!e.raw.hide_empty,isDropdown:!!e.raw.dropdown,isHierarchical:!!e.raw.hierarchical})}]},deprecated:[{attributes:{hasCount:{type:"boolean",default:!0,source:"attribute",selector:"div",attribute:"data-has-count"},hasEmpty:{type:"boolean",default:!1,source:"attribute",selector:"div",attribute:"data-has-empty"},isDropdown:{type:"boolean",default:!1,source:"attribute",selector:"div",attribute:"data-is-dropdown"},isHierarchical:{type:"boolean",default:!0,source:"attribute",selector:"div",attribute:"data-is-hierarchical"}},migrate:e=>e,save(e){const{hasCount:o,hasEmpty:t,isDropdown:r,isHierarchical:a}=e,c={};return o&&(c["data-has-count"]=!0),t&&(c["data-has-empty"]=!0),r&&(c["data-is-dropdown"]=!0),a&&(c["data-is-hierarchical"]=!0),(0,w.jsx)("div",{className:"is-loading",...c,children:r?(0,w.jsx)("span",{"aria-hidden":!0,className:"wc-block-product-categories__placeholder"}):(0,w.jsxs)("ul",{"aria-hidden":!0,children:[(0,w.jsx)("li",{children:(0,w.jsx)("span",{className:"wc-block-product-categories__placeholder"})}),(0,w.jsx)("li",{children:(0,w.jsx)("span",{className:"wc-block-product-categories__placeholder"})}),(0,w.jsx)("li",{children:(0,w.jsx)("span",{className:"wc-block-product-categories__placeholder"})})]})})}}],edit:function(e){const o=(0,i.useBlockProps)();return(0,w.jsx)("div",{...o,children:(0,w.jsx)(g,{...e})})},save:()=>null})},1008:()=>{},2357:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},a={};function c(e){var o=a[e];if(void 0!==o)return o.exports;var t=a[e]={exports:{}};return r[e].call(t.exports,t,t.exports,c),t.exports}c.m=r,e=[],c.O=(o,t,r,a)=>{if(!t){var s=1/0;for(d=0;d<e.length;d++){for(var[t,r,a]=e[d],i=!0,l=0;l<t.length;l++)(!1&a||s>=a)&&Object.keys(c.O).every((e=>c.O[e](t[l])))?t.splice(l--,1):(i=!1,a<s&&(s=a));if(i){e.splice(d--,1);var n=r();void 0!==n&&(o=n)}}return o}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[t,r,a]},c.n=e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return c.d(o,{a:o}),o},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var a=Object.create(null);c.r(a);var s={};o=o||[null,t({}),t([]),t(t)];for(var i=2&r&&e;"object"==typeof i&&!~o.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((o=>s[o]=()=>e[o]));return s.default=()=>e,c.d(a,s),a},c.d=(e,o)=>{for(var t in o)c.o(o,t)&&!c.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},c.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=2845,(()=>{var e={2845:0};c.O.j=o=>0===e[o];var o=(o,t)=>{var r,a,[s,i,l]=t,n=0;if(s.some((o=>0!==e[o]))){for(r in i)c.o(i,r)&&(c.m[r]=i[r]);if(l)var d=l(c)}for(o&&o(t);n<s.length;n++)a=s[n],c.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return c.O(d)},t=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];t.forEach(o.bind(null,0)),t.push=o.bind(null,t.push.bind(t))})();var s=c.O(void 0,[94],(()=>c(422)));s=c.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-categories"]=s})();