(globalThis.webpackChunkwebpackWcBlocksFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksFrontendJsonp||[]).push([[409],{3867:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Block:()=>M,default:()=>F});var s=r(6087),n=r(4921),o=r(7723),c=r(1509),a=r(7143),i=r(7594),d=r(8537),l=r(1824),u=r.n(l),p=r(8331),m=r(3993);const _=Object.entries(p.iI).reduce(((e,[t,r])=>(e[t]=Object.entries(r).reduce(((e,[t,r])=>(e[t]=(e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,o.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,o.__)("%s (optional)","woocommerce"),e.label)),e.index&&((0,m.isNumber)(e.index)&&(t.index=e.index),(0,m.isString)(e.index)&&(t.index=parseInt(e.index,10))),e.hidden&&(t.required=!1),t})(r),e)),{}),e)),{});var y=r(5703);r(3832);const b=e=>{const t=((e,t,r="")=>{const s=r&&void 0!==_[r]?_[r]:{};return e.map((e=>({key:e,...t&&e in t?t[e]:{},...s&&e in s?s[e]:{}}))).sort(((e,t)=>e.index-t.index))})(p.Hw,y.defaultFields,e.country),r=Object.assign({},e);return t.forEach((({key:t,hidden:s})=>{!0===s&&((e,t)=>e in t)(t,e)&&(r[t]="")})),r},g=window.CustomEvent||null,h=(e,t,r=!1,s=!1)=>{if("function"!=typeof jQuery)return()=>{};const n=()=>{((e,{bubbles:t=!1,cancelable:r=!1,element:s,detail:n={}})=>{if(!g)return;s||(s=document.body);const o=new g(e,{bubbles:t,cancelable:r,detail:n});s.dispatchEvent(o)})(t,{bubbles:r,cancelable:s})};return jQuery(document).on(e,n),()=>jQuery(document).off(e,n)},w=e=>{const t=e?.detail;t&&t.preserveCartData||(0,a.dispatch)(i.cartStore).invalidateResolutionForStore()},f=e=>{(e?.persisted||"back_forward"===(window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:""))&&(0,a.dispatch)(i.cartStore).invalidateResolutionForStore()},T=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},C={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},S={...C,email:""},E={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:i.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},k=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,d.decodeEntities)(t)]))),v={cartCoupons:i.EMPTY_CART_COUPONS,cartItems:i.EMPTY_CART_ITEMS,cartFees:i.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:i.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:i.EMPTY_CART_ITEM_ERRORS,cartTotals:E,cartIsLoading:!0,cartErrors:i.EMPTY_CART_ERRORS,billingData:S,billingAddress:S,shippingAddress:C,shippingRates:i.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:i.EMPTY_PAYMENT_METHODS,paymentRequirements:i.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:i.EMPTY_EXTENSIONS},N=(e={shouldSelect:!0})=>{const{shouldSelect:t}=e,r=(0,s.useRef)(),n=(0,s.useRef)(S),o=(0,s.useRef)(C);(0,s.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),window.wcBlocksStoreCartListeners?.count>0)return void window.wcBlocksStoreCartListeners.count++;document.body.addEventListener("wc-blocks_added_to_cart",w),document.body.addEventListener("wc-blocks_removed_from_cart",w),window.addEventListener("pageshow",f);const e=h("added_to_cart","wc-blocks_added_to_cart"),t=h("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",w),document.body.removeEventListener("wc-blocks_removed_from_cart",w),window.removeEventListener("pageshow",f),e(),t()}})(),T)),[]);const c=(0,a.useSelect)(((e,{dispatch:r})=>{if(!t)return v;const s=e(i.cartStore),c=s.getCartData(),a=s.getCartErrors(),d=s.getCartTotals(),l=!s.hasFinishedResolution("getCartData"),p=s.isCustomerDataUpdating(),{receiveCart:m,receiveCartContents:_}=r(i.cartStore),y=c.fees.length>0?c.fees.map((e=>k(e))):i.EMPTY_CART_FEES,g=c.coupons.length>0?c.coupons.map((e=>({...e,label:e.code}))):i.EMPTY_CART_COUPONS,h=b(k(c.billingAddress)),w=c.needsShipping?b(k(c.shippingAddress)):h;return u()(h,n.current)||(n.current=h),u()(w,o.current)||(o.current=w),{cartCoupons:g,cartItems:c.items,crossSellsProducts:c.crossSells,cartFees:y,cartItemsCount:c.itemsCount,cartItemsWeight:c.itemsWeight,cartNeedsPayment:c.needsPayment,cartNeedsShipping:c.needsShipping,cartItemErrors:c.errors,cartTotals:d,cartIsLoading:l,cartErrors:a,billingData:n.current,billingAddress:n.current,shippingAddress:o.current,extensions:c.extensions,shippingRates:c.shippingRates,isLoadingRates:p,cartHasCalculatedShipping:c.hasCalculatedShipping,paymentRequirements:c.paymentRequirements,receiveCart:m,receiveCartContents:_}}),[t]);return r.current&&u()(r.current,c)||(r.current=c),r.current},R=(e,t)=>{const r=e.find((({id:e})=>e===t));return r?r.quantity:0};var x=r(41),L=r(2796),A=r(1616),P=(r(7316),r(4179)),I=r(790);const O=({product:e,isDescendantOfAddToCartWithOptions:t,className:r,style:l})=>{const{id:u,permalink:m,add_to_cart:_,has_options:b,is_purchasable:g,is_in_stock:h}=e,{dispatchStoreEvent:w}=(0,c.y)(),{cartQuantity:f,addingToCart:T,addToCart:C}=(e=>{const{addItemToCart:t}=(0,a.useDispatch)(i.cartStore),{cartItems:r,cartIsLoading:n}=N(),{createErrorNotice:o,removeNotice:c}=(0,a.useDispatch)("core/notices"),[l,u]=(0,s.useState)(!1),p=(0,s.useRef)(R(r,e));return(0,s.useEffect)((()=>{const t=R(r,e);t!==p.current&&(p.current=t)}),[r,e]),{cartQuantity:Number.isFinite(p.current)?p.current:0,addingToCart:l,cartIsLoading:n,addToCart:(r=1)=>(u(!0),t(e,r).then((()=>{c("add-to-cart")})).catch((e=>{o((0,d.decodeEntities)(e.message),{id:"add-to-cart",context:"wc/all-products",isDismissible:!0})})).finally((()=>{u(!1)})))}})(u),S=Number.isFinite(f)&&f>0,E=!b&&g&&h,k=(0,d.decodeEntities)(_?.description||""),v=(({cartQuantity:e,productCartDetails:t,isDescendantOfAddToCartWithOptions:r})=>Number.isFinite(e)&&e>0?(0,o.sprintf)(/* translators: %s number of products in cart. */ /* translators: %s number of products in cart. */
(0,o._n)("%d in cart","%d in cart",e,"woocommerce"),e):r&&t?.single_text?t?.single_text:t?.text||(0,o.__)("Add to cart","woocommerce"))({cartQuantity:f,productCartDetails:_,isDescendantOfAddToCartWithOptions:t}),x=E?"button":"a",L={};return E?L.onClick=async()=>{await C(),w("cart-add-item",{product:e});const{cartRedirectAfterAdd:t}=(0,y.getSetting)("productsSettings");t&&(window.location.href=p.Vo)}:(L.href=m,L.rel="nofollow",L.onClick=()=>{w("product-view-link",{product:e})}),(0,I.jsx)(x,{...L,"aria-label":k,disabled:T,className:(0,n.A)(r,"wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",{loading:T,added:S}),style:l,children:v})},D=({className:e,style:t})=>(0,I.jsx)("button",{className:(0,n.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button","wc-block-components-product-button__button--placeholder",e),style:t,disabled:!0,children:(0,o.__)("Add to cart","woocommerce")}),j=({className:e,style:t,blockClientId:r})=>{const{current:c,registerListener:a,unregisterListener:i}=(0,P.A)();(0,s.useEffect)((()=>{if(r)return a(r),()=>{i(r)}}),[r,a,i]);const d="external"===c?.slug?(0,o.__)("Buy product","woocommerce"):(0,o.__)("Add to cart","woocommerce");return(0,I.jsx)("button",{className:(0,n.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",e),style:t,disabled:!0,children:d})},M=e=>{const{className:t,textAlign:r,blockClientId:s}=e,o=(0,x.p)(e),{parentClassName:c}=(0,L.useInnerBlockLayoutContext)(),{isLoading:a,product:i}=(0,L.useProductDataContext)();return(0,I.jsx)("div",{className:(0,n.A)(t,"wp-block-button","wc-block-components-product-button",{[`${c}__product-add-to-cart`]:c,[`align-${r}`]:r}),children:a?(0,I.jsx)(D,{className:o.className,style:o.style}):(0,I.jsx)(I.Fragment,{children:i.id?(0,I.jsx)(O,{product:i,style:o.style,className:o.className,isDescendantOfAddToCartWithOptions:e["woocommerce/isDescendantOfAddToCartWithOptions"]}):(0,I.jsx)(j,{style:o.style,className:o.className,isLoading:a,blockClientId:s})})})},F=(0,A.withProductDataContext)(M)},41:(e,t,r)=>{"use strict";r.d(t,{p:()=>d});var s=r(4921),n=r(3993),o=r(7356),c=r(9786);function a(e={}){const t={};return(0,c.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function i(e,t){return e&&t?`has-${(0,o.c)(t)}-${e}`:""}const d=e=>{const t=(e=>{const t=(0,n.isObject)(e)?e:{style:{}};let r=t.style;return(0,n.isString)(r)&&(r=JSON.parse(r)||{}),(0,n.isObject)(r)||(r={}),{...t,style:r}})(e),r=function(e){const{backgroundColor:t,textColor:r,gradient:o,style:c}=e,d=i("background-color",t),l=i("color",r),u=function(e){if(e)return`has-${e}-gradient-background`}(o),p=u||c?.color?.gradient;return{className:(0,s.A)(l,u,{[d]:!p&&!!d,"has-text-color":r||c?.color?.text,"has-background":t||c?.color?.background||o||c?.color?.gradient,"has-link-color":(0,n.isObject)(c?.elements?.link)?c?.elements?.link?.color:void 0}),style:a({color:c?.color||{}})}}(t),o=function(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:r}=e,n=t?i("border-color",t):"";return(0,s.A)({"has-border-color":!!t||!!r?.border?.color,[n]:!!n})}(e),style:a({border:t})}}(t),c=function(e){return{className:void 0,style:a({spacing:e.style?.spacing||{}})}}(t),d=(e=>{const t=(0,n.isObject)(e.style.typography)?e.style.typography:{},r=(0,n.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:r,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,s.A)(d.className,r.className,o.className,c.className),style:{...d.style,...r.style,...o.style,...c.style}}}},4179:(e,t,r)=>{"use strict";r.d(t,{A:()=>_});var s=r(7143);const n="woocommerce/product-type-template-state",o="SWITCH_PRODUCT_TYPE",c="SET_PRODUCT_TYPES",a="REGISTER_LISTENER",i="UNREGISTER_LISTENER",d=(0,r(5703).getSetting)("productTypes",{}),l=Object.keys(d).map((e=>({slug:e,label:d[e]}))),u={productTypes:{list:l,current:l[0]?.slug},listeners:[]},p={switchProductType:e=>({type:o,current:e}),setProductTypes:e=>({type:c,productTypes:e}),registerListener:e=>({type:a,listener:e}),unregisterListener:e=>({type:i,listener:e})},m=(0,s.createReduxStore)(n,{reducer:(e=u,t)=>{switch(t.type){case c:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case o:return{...e,productTypes:{...e.productTypes,current:t.current}};case a:return{...e,listeners:[...e.listeners,t.listener||""]};case i:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:p,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});function _(){const{productTypes:e,current:t,registeredListeners:r}=(0,s.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:r,getRegisteredListeners:s}=e(m);return{productTypes:t(),current:r(),registeredListeners:s()}}),[]),{switchProductType:n,registerListener:o,unregisterListener:c}=(0,s.useDispatch)(m);return{productTypes:e,current:t,set:n,registeredListeners:r,registerListener:o,unregisterListener:c}}(0,s.select)(n)||(0,s.register)(m)},7316:()=>{}}]);