(()=>{var e,t,o,r,s,c={4514:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>l,default:()=>d});var r=o(4921),s=o(415),c=o(371),n=o(7723),i=o(1616),a=o(790);const l=e=>{const{textAlign:t}=e,o=(0,c.p)(e),{product:i}=(0,s.useProductDataContext)(),l=(0,r.A)(o.className,"wc-block-components-product-average-rating",{[`has-text-align-${t}`]:t});return(0,a.jsx)("div",{className:l,style:o.style,children:Number(i.average_rating)>0?i.average_rating:(0,n.__)("No ratings","woocommerce")})},d=(0,i.withProductDataContext)(l)},595:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>b,default:()=>y});var r=o(6087),s=o(4921),c=o(7723),n=o(7052),i=o(1057),a=o(371),l=o(8537),d=o(8331),u=o(5703),m=o(415),p=o(1616),h=(o(7316),o(2281)),g=o(790);const w=({product:e,isDescendantOfAddToCartWithOptions:t,className:o,style:r})=>{const{id:a,permalink:m,add_to_cart:p,has_options:h,is_purchasable:w,is_in_stock:x}=e,{dispatchStoreEvent:_}=(0,n.y)(),{cartQuantity:b,addingToCart:y,addToCart:f}=(0,i.R)(a),k=Number.isFinite(b)&&b>0,v=!h&&w&&x,j=(0,l.decodeEntities)(p?.description||""),C=(({cartQuantity:e,productCartDetails:t,isDescendantOfAddToCartWithOptions:o})=>Number.isFinite(e)&&e>0?(0,c.sprintf)(/* translators: %s number of products in cart. */ /* translators: %s number of products in cart. */
(0,c._n)("%d in cart","%d in cart",e,"woocommerce"),e):o&&t?.single_text?t?.single_text:t?.text||(0,c.__)("Add to cart","woocommerce"))({cartQuantity:b,productCartDetails:p,isDescendantOfAddToCartWithOptions:t}),S=v?"button":"a",N={};return v?N.onClick=async()=>{await f(),_("cart-add-item",{product:e});const{cartRedirectAfterAdd:t}=(0,u.getSetting)("productsSettings");t&&(window.location.href=d.Vo)}:(N.href=m,N.rel="nofollow",N.onClick=()=>{_("product-view-link",{product:e})}),(0,g.jsx)(S,{...N,"aria-label":j,disabled:y,className:(0,s.A)(o,"wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",{loading:y,added:k}),style:r,children:C})},x=({className:e,style:t})=>(0,g.jsx)("button",{className:(0,s.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button","wc-block-components-product-button__button--placeholder",e),style:t,disabled:!0,children:(0,c.__)("Add to cart","woocommerce")}),_=({className:e,style:t,blockClientId:o})=>{const{current:n,registerListener:i,unregisterListener:a}=(0,h.A)();(0,r.useEffect)((()=>{if(o)return i(o),()=>{a(o)}}),[o,i,a]);const l="external"===n?.slug?(0,c.__)("Buy product","woocommerce"):(0,c.__)("Add to cart","woocommerce");return(0,g.jsx)("button",{className:(0,s.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",e),style:t,disabled:!0,children:l})},b=e=>{const{className:t,textAlign:o,blockClientId:r}=e,c=(0,a.p)(e),{parentClassName:n}=(0,m.useInnerBlockLayoutContext)(),{isLoading:i,product:l}=(0,m.useProductDataContext)();return(0,g.jsx)("div",{className:(0,s.A)(t,"wp-block-button","wc-block-components-product-button",{[`${n}__product-add-to-cart`]:n,[`align-${o}`]:o}),children:i?(0,g.jsx)(x,{className:c.className,style:c.style}):(0,g.jsx)(g.Fragment,{children:l.id?(0,g.jsx)(w,{product:l,style:c.style,className:c.className,isDescendantOfAddToCartWithOptions:e["woocommerce/isDescendantOfAddToCartWithOptions"]}):(0,g.jsx)(_,{style:c.style,className:c.className,isLoading:i,blockClientId:r})})})},y=(0,p.withProductDataContext)(b)},3104:(e,t,o)=>{"use strict";o.d(t,{A:()=>_});var r=o(6087),s=o(7723),c=o(4921),n=o(5703),i=o(415),a=o(371),l=o(1616),d=o(7052),u=o(8537),m=o(3993),p=o(3848),h=(o(1189),o(4264)),g=o(790);const w=e=>(0,g.jsx)("img",{...e,src:n.PLACEHOLDER_IMG_SRC,alt:e.alt,width:void 0,height:void 0}),x=({image:e,loaded:t,showFullSize:o,fallbackAlt:r,width:s,scale:c,height:n,aspectRatio:i})=>{const{thumbnail:a,src:l,srcset:d,sizes:u,alt:m}=e||{},p={alt:m||r,hidden:!t,src:a,...o&&{src:l,srcSet:d,sizes:u}},h={height:n,width:s,objectFit:c,aspectRatio:i};return(0,g.jsxs)(g.Fragment,{children:[p.src&&(0,g.jsx)("img",{style:h,"data-testid":"product-image",...p}),!e&&(0,g.jsx)(w,{style:h,alt:p.alt})]})},_=(0,l.withProductDataContext)((e=>{const{className:t,imageSizing:o=h.e.SINGLE,showProductLink:n=!0,showSaleBadge:l,saleBadgeAlign:_="right",height:b,width:y,scale:f,aspectRatio:k,style:v,...j}=e,C=(0,a.p)(e),{parentClassName:S}=(0,i.useInnerBlockLayoutContext)(),{product:N,isLoading:E}=(0,i.useProductDataContext)(),{dispatchStoreEvent:P}=(0,d.y)();if(!N.id)return(0,g.jsx)("div",{className:(0,c.A)(t,"wc-block-components-product-image",{[`${S}__product-image`]:S},C.className),style:C.style,children:(0,g.jsx)(w,{})});const A=!!N.images.length,T=A?N.images[0]:null,I=n?"a":r.Fragment,B=(0,s.sprintf)(/* translators: %s is referring to the product name */ /* translators: %s is referring to the product name */
(0,s.__)("Link to %s","woocommerce"),N.name),L={href:N.permalink,...!A&&{"aria-label":B},onClick:()=>{P("product-view-link",{product:N})}};return(0,g.jsx)("div",{className:(0,c.A)(t,"wc-block-components-product-image",{[`${S}__product-image`]:S},C.className),style:C.style,children:(0,g.jsxs)(I,{...n&&L,children:[!!l&&(0,g.jsx)(p.default,{align:_,...j}),(0,g.jsx)(x,{fallbackAlt:(0,u.decodeEntities)(N.name),image:T,loaded:!E,showFullSize:o!==h.e.THUMBNAIL,width:y,height:b,scale:f,aspectRatio:(0,m.objectHasProp)(v,"dimensions")&&(0,m.objectHasProp)(v.dimensions,"aspectRatio")&&(0,m.isString)(v.dimensions.aspectRatio)?v.dimensions.aspectRatio:k})]})})}))},4264:(e,t,o)=>{"use strict";o.d(t,{e:()=>r});let r=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({})},1308:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>d,default:()=>u});var r=o(4921),s=o(6711),c=o(910),n=o(415),i=o(371),a=o(1616),l=o(790);const d=e=>{const{className:t,textAlign:o,isDescendentOfSingleProductTemplate:a}=e,d=(0,i.p)(e),{parentName:u,parentClassName:m}=(0,n.useInnerBlockLayoutContext)(),{product:p}=(0,n.useProductDataContext)(),h="woocommerce/all-products"===u,g=a&&!("woocommerce/add-to-cart-with-options-grouped-product-selector-item"===u),w=(0,r.A)("wc-block-components-product-price",t,d.className,{[`${m}__product-price`]:m});if(!p.id&&!a){const e=(0,l.jsx)(s.A,{align:o,className:w});return h?(0,l.jsx)("div",{className:"wp-block-woocommerce-product-price",children:e}):e}const x=p.prices,_=g?(0,c.getCurrencyFromPriceResponse)():(0,c.getCurrencyFromPriceResponse)(x),b="5000",y=x.price!==x.regular_price,f=(0,r.A)({[`${m}__product-price__value`]:m,[`${m}__product-price__value--on-sale`]:y}),k=(0,l.jsx)(s.A,{align:o,className:w,style:d.style,regularPriceStyle:d.style,priceStyle:d.style,priceClassName:f,currency:_,price:g?b:x.price,minPrice:x?.price_range?.min_amount,maxPrice:x?.price_range?.max_amount,regularPrice:g?b:x.regular_price,regularPriceClassName:(0,r.A)({[`${m}__product-price__regular`]:m})});return h?(0,l.jsx)("div",{className:"wp-block-woocommerce-product-price",children:k}):k},u=e=>e.isDescendentOfSingleProductTemplate?(0,l.jsx)(d,{...e}):(0,a.withProductDataContext)(d)(e)},9147:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>m,default:()=>p});var r=o(7723),s=o(4921),c=o(415),n=o(371),i=o(1616),a=o(3993),l=o(6427),d=o(790);const u=e=>{const{reviews:t}=e,o=t?(0,r.sprintf)(/* translators: %s is referring to the total of reviews for a product */ /* translators: %s is referring to the total of reviews for a product */
(0,r._n)("(%s customer review)","(%s customer reviews)",t,"woocommerce"),t):(0,r.__)("(X customer reviews)","woocommerce");return(0,d.jsx)("span",{className:"wc-block-components-product-rating-counter__reviews_count",children:(0,d.jsx)(l.Disabled,{children:(0,d.jsx)("a",{href:"/",children:o})})})},m=e=>{const{textAlign:t,shouldDisplayMockedReviewsWhenProductHasNoReviews:o}=e,r=(0,n.p)(e),{parentClassName:i}=(0,c.useInnerBlockLayoutContext)(),{product:l}=(0,c.useProductDataContext)(),m=(e=>{const t=(0,a.isNumber)(e.review_count)?e.review_count:parseInt(e.review_count,10);return Number.isFinite(t)&&t>0?t:0})(l),p=(0,s.A)(r.className,"wc-block-components-product-rating-counter",{[`${i}__product-rating`]:i,[`has-text-align-${t}`]:t});if(m||o)return(0,d.jsx)("div",{className:p,style:r.style,children:(0,d.jsx)("div",{className:"wc-block-components-product-rating-counter__container",children:(0,d.jsx)(u,{reviews:m})})})},p=(0,i.withProductDataContext)(m)},7220:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>a,default:()=>l});var r=o(415),s=o(371),c=o(1616),n=o(5841),i=(o(7545),o(790));const a=e=>{const{textAlign:t="",shouldDisplayMockedReviewsWhenProductHasNoReviews:o}=e,c=(0,s.p)(e),{parentClassName:a}=(0,r.useInnerBlockLayoutContext)(),{product:l}=(0,r.useProductDataContext)(),d=(0,n.p3)(l),u=(0,n.Nm)(l);return(0,i.jsx)(n.fb,{className:"wc-block-components-product-rating-stars",showMockedReviews:o,styleProps:c,parentClassName:a,reviews:u,rating:d,textAlign:t})},l=(0,c.withProductDataContext)(a)},9812:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>a,default:()=>l});var r=o(415),s=o(371),c=o(1616),n=o(5841),i=(o(4053),o(790));const a=e=>{const{textAlign:t="",isDescendentOfSingleProductBlock:o,shouldDisplayMockedReviewsWhenProductHasNoReviews:c}=e,a=(0,s.p)(e),{parentClassName:l}=(0,r.useInnerBlockLayoutContext)(),{product:d}=(0,r.useProductDataContext)(),u=(0,n.p3)(d),m=(0,n.Nm)(d);if(m||c)return(0,i.jsx)(n.fb,{className:"wc-block-components-product-rating",showReviewCount:o,showMockedReviews:c,styleProps:a,parentClassName:l,reviews:m,rating:u,textAlign:t})},l=(0,c.withProductDataContext)(a)},3848:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>d,default:()=>u});var r=o(7723),s=o(4921),c=o(4656),n=o(415),i=o(371),a=o(1616),l=(o(4313),o(790));const d=e=>{const{className:t,align:o}=e,a=(0,i.p)(e),{parentClassName:d}=(0,n.useInnerBlockLayoutContext)(),{product:u}=(0,n.useProductDataContext)();if(!(u.id&&u.on_sale||e.isDescendentOfSingleProductTemplate))return null;const m="string"==typeof o?`wc-block-components-product-sale-badge--align-${o}`:"";return(0,l.jsx)("div",{className:(0,s.A)("wc-block-components-product-sale-badge",t,m,{[`${d}__product-onsale`]:d},a.className),style:a.style,children:(0,l.jsx)(c.Label,{label:(0,r.__)("Sale","woocommerce"),screenReaderLabel:(0,r.__)("Product on sale","woocommerce")})})},u=(0,a.withProductDataContext)(d)},1648:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>u});var r=o(4921),s=o(415),c=o(1616),n=o(371),i=o(4715),a=o(7723),l=(o(1129),o(790));const d=({setAttributes:e,parentClassName:t,sku:o,className:s,style:c,prefix:n,suffix:d})=>(0,l.jsxs)("div",{className:(0,r.A)(s,"wp-block-post-terms",{[`${t}__product-sku`]:t}),style:c,children:[(0,l.jsx)(i.RichText,{className:"wc-block-components-product-sku__prefix",tagName:"span",placeholder:(0,a.__)("Prefix","woocommerce"),value:n,onChange:t=>e({prefix:t})}),(0,l.jsxs)("span",{children:[" ",o]}),(0,l.jsx)(i.RichText,{className:"wc-block-components-product-sku__suffix",tagName:"span",placeholder:" "+(0,a.__)("Suffix","woocommerce"),value:d,onChange:t=>e({suffix:t})})]}),u=(0,c.withProductDataContext)((e=>{const{className:t}=e,o=(0,n.p)(e),{parentClassName:c}=(0,s.useInnerBlockLayoutContext)(),{product:i}=(0,s.useProductDataContext)(),u=i.sku;return e.isDescendentOfSingleProductTemplate?(0,l.jsx)(d,{setAttributes:e.setAttributes,parentClassName:c,className:t,sku:(0,a.__)("Product SKU","woocommerce"),prefix:e.prefix,suffix:e.suffix}):u?(0,l.jsx)(d,{setAttributes:e.setAttributes,className:t,parentClassName:c,sku:u,prefix:e.prefix,suffix:e.suffix,...e.isDescendantOfAllProducts&&{className:(0,r.A)(t,"wc-block-components-product-sku wp-block-woocommerce-product-sku",o.className),style:{...o.style}}}):null}))},6374:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Block:()=>u,default:()=>m});var r=o(7723),s=o(4921),c=o(415),n=o(371),i=o(1616),a=o(5703),l=o(2281),d=(o(3790),o(790));const u=e=>{const{className:t}=e,o=(0,n.p)(e),{parentClassName:i}=(0,c.useInnerBlockLayoutContext)(),{product:u}=(0,c.useProductDataContext)(),{text:m,class:p}=u.stock_availability,{current:h}=(0,l.A)();if(!((e,t,o)=>{if(0!==e.id)return""!==t;const r=(0,a.getSetting)("productTypesWithoutStockIndicator",["external","grouped","variable"]),s=o||e?.type;return!r.includes(s)})(u,m,h?.slug))return null;const g=0===u.id,w=u.low_stock_remaining;return(0,d.jsx)("div",{className:(0,s.A)(t,{[`${i}__stock-indicator`]:i,[`wc-block-components-product-stock-indicator--${p}`]:p,"wc-block-components-product-stock-indicator--in-stock":g,"wc-block-components-product-stock-indicator--low-stock":!!w,...e.isDescendantOfAllProducts&&{[o.className]:o.className,"wc-block-components-product-stock-indicator wp-block-woocommerce-product-stock-indicator":!0}}),...e.isDescendantOfAllProducts&&{style:o.style},children:g?(0,r.__)("In stock","woocommerce"):m})},m=e=>{const{product:t}=(0,c.useProductDataContext)();return 0===t.id?(0,d.jsx)(u,{...e}):(0,i.withProductDataContext)(u)(e)}},4001:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>C});var r=o(7723),s=o(4921),c=o(6087),n=o(3240),i=o.n(n);const a=["a","b","em","i","strong","p","br"],l=["target","href","rel","name","download"],d=(e,t)=>{const o=t?.tags||a,r=t?.attr||l;return i().sanitize(e,{ALLOWED_TAGS:o,ALLOWED_ATTR:r})};var u=o(6004);const m=e=>e.replace(/<\/?[a-z][^>]*?>/gi,""),p=(e,t)=>e.replace(/[\s|\.\,]+$/i,"")+t;var h=o(9446);var g=o(790);const w=["a","b","em","i","strong","p","br","ul","ol","li","h1","h2","h3","h4","h5","h6","pre","blockquote","img"],x=["target","href","rel","name","download","src","class","alt","style"],_=({source:e,maxLength:t=15,countType:o="words",className:r="",style:s={}})=>{const n=(0,c.useMemo)((()=>((e,t=15,o="words")=>{const r=(0,u.autop)(e);if((0,h.count)(r,o)<=t)return r;const s=(e=>{const t=e.indexOf("</p>");return-1===t?e:e.substr(0,t+4)})(r);return(0,h.count)(s,o)<=t?s:"words"===o?((e,t,o="&hellip;",r=!0)=>{const s=m(e),c=s.split(" ").splice(0,t).join(" ");return c===s?r?(0,u.autop)(s):s:r?(0,u.autop)(p(c,o)):p(c,o)})(s,t):((e,t,o=!0,r="&hellip;",s=!0)=>{const c=m(e),n=c.slice(0,t);if(n===c)return s?(0,u.autop)(c):c;if(o)return(0,u.autop)(p(n,r));const i=n.match(/([\s]+)/g),a=i?i.length:0,l=c.slice(0,t+a);return s?(0,u.autop)(p(l,r)):p(l,r)})(s,t,"characters_including_spaces"===o)})(e,t,o)),[e,t,o]);return(0,g.jsx)(c.RawHTML,{style:s,className:r,children:d(n,{tags:w,attr:x})})};var b=o(8331),y=o(3993),f=o(415),k=o(371),v=o(1616);o(3608);const j=e=>{const{className:t,showDescriptionIfEmpty:o,summaryLength:c,showLink:n,linkText:i,isDescendantOfAllProducts:a,isDescendentOfSingleProductTemplate:l}=e,{parentClassName:d}=(0,f.useInnerBlockLayoutContext)(),{product:u}=(0,f.useProductDataContext)(),m=(0,k.p)(e),p=(e=>{const{isDescendantOfAllProducts:t,summaryLength:o,showDescriptionIfEmpty:r,showLink:s}=e;return t&&(0,y.isEmpty)(o)&&(0,y.isEmpty)(r)&&(0,y.isEmpty)(s)})(e),h=p?150:c,w=!p&&n,x=((e,t)=>{const{short_description:o,description:r}=e;return o||(t&&r?r:"")})(u,!!p||o),v=h||1/0;return u?l?(0,g.jsx)("p",{children:(0,r.__)("This block displays the product summary and all its customizations.","woocommerce")}):x?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(_,{className:(0,s.A)(t,m.className,"wc-block-components-product-summary",{[`${d}__product-summary`]:d}),source:x,maxLength:v,countType:b.r7.wordCountType||"words",style:m.style}),a&&w&&i?(0,g.jsx)("a",{href:`${u.permalink}#tab-description`,children:i}):null]}):a?null:(0,g.jsx)("p",{children:(0,r.__)("No product summary to show.","woocommerce")}):(0,g.jsx)("div",{className:(0,s.A)(t,"wc-block-components-product-summary",{[`${d}__product-summary`]:d})})},C=e=>e.isDescendentOfSingleProductTemplate?(0,g.jsx)(j,{...e}):(0,v.withProductDataContext)(j)(e)},7581:(e,t,o)=>{"use strict";o.d(t,{A:()=>u});var r=o(4921),s=o(415),c=o(1616),n=o(4473),i=o(7052),a=o(371),l=(o(7578),o(790));const d=({children:e,headingLevel:t,elementType:o=`h${t}`,...r})=>(0,l.jsx)(o,{...r,children:e}),u=(0,c.withProductDataContext)((e=>{const{className:t,headingLevel:o=2,showProductLink:c=!0,linkTarget:u,align:m}=e,p=(0,a.p)(e),{parentClassName:h}=(0,s.useInnerBlockLayoutContext)(),{product:g}=(0,s.useProductDataContext)(),{dispatchStoreEvent:w}=(0,i.y)();return g.id?(0,l.jsx)(d,{headingLevel:o,className:(0,r.A)(t,p.className,"wc-block-components-product-title",{[`${h}__product-title`]:h,[`wc-block-components-product-title--align-${m}`]:m}),style:p.style,children:(0,l.jsx)(n.A,{disabled:!c,name:g.name,permalink:g.permalink,target:u,onClick:()=>{w("product-view-link",{product:g})}})}):(0,l.jsx)(d,{headingLevel:o,className:(0,r.A)(t,p.className,"wc-block-components-product-title",{[`${h}__product-title`]:h,[`wc-block-components-product-title--align-${m}`]:m}),style:p.style})}))},2e3:(e,t,o)=>{"use strict";o.d(t,{A:()=>i});var r=o(8331),s=o(3993),c=o(7723);const n=Object.entries(r.iI).reduce(((e,[t,o])=>(e[t]=Object.entries(o).reduce(((e,[t,o])=>(e[t]=(e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,c.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,c.__)("%s (optional)","woocommerce"),e.label)),e.index&&((0,s.isNumber)(e.index)&&(t.index=e.index),(0,s.isString)(e.index)&&(t.index=parseInt(e.index,10))),e.hidden&&(t.required=!1),t})(o),e)),{}),e)),{}),i=(e,t,o="")=>{const r=o&&void 0!==n[o]?n[o]:{};return e.map((e=>({key:e,...t&&e in t?t[e]:{},...r&&e in r?r[e]:{}}))).sort(((e,t)=>e.index-t.index))}},4473:(e,t,o)=>{"use strict";o.d(t,{A:()=>c});var r=o(4921),s=(o(959),o(790));const c=({className:e="",disabled:t=!1,name:o,permalink:c="",target:n,rel:i,style:a,onClick:l,disabledTagName:d="span",...u})=>{const m=(0,r.A)("wc-block-components-product-name",e),p=d;if(t){const e=u;return(0,s.jsx)(p,{className:m,...e,dangerouslySetInnerHTML:{__html:o}})}return(0,s.jsx)("a",{className:m,href:c,target:n,...u,dangerouslySetInnerHTML:{__html:o},style:a})}},6711:(e,t,o)=>{"use strict";o.d(t,{A:()=>u});var r=o(7723),s=o(4656),c=o(4921),n=o(910),i=o(6087),a=(o(8501),o(790));const l=({currency:e,maxPrice:t,minPrice:o,priceClassName:i,priceStyle:l={}})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"screen-reader-text",children:(0,r.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,r.__)("Price between %1$s and %2$s","woocommerce"),(0,n.formatPrice)(o),(0,n.formatPrice)(t))}),(0,a.jsxs)("span",{"aria-hidden":!0,children:[(0,a.jsx)(s.FormattedMonetaryAmount,{className:(0,c.A)("wc-block-components-product-price__value",i),currency:e,value:o,style:l})," — ",(0,a.jsx)(s.FormattedMonetaryAmount,{className:(0,c.A)("wc-block-components-product-price__value",i),currency:e,value:t,style:l})]})]}),d=({currency:e,regularPriceClassName:t,regularPriceStyle:o,regularPrice:n,priceClassName:i,priceStyle:l,price:d})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"screen-reader-text",children:(0,r.__)("Previous price:","woocommerce")}),(0,a.jsx)(s.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,a.jsx)("del",{className:(0,c.A)("wc-block-components-product-price__regular",t),style:o,children:e}),value:n}),(0,a.jsx)("span",{className:"screen-reader-text",children:(0,r.__)("Discounted price:","woocommerce")}),(0,a.jsx)(s.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,a.jsx)("ins",{className:(0,c.A)("wc-block-components-product-price__value","is-discounted",i),style:l,children:e}),value:d})]}),u=({align:e,className:t,currency:o,format:r="<price/>",maxPrice:n,minPrice:u,price:m,priceClassName:p,priceStyle:h,regularPrice:g,regularPriceClassName:w,regularPriceStyle:x,style:_})=>{const b=(0,c.A)(t,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});r.includes("<price/>")||(r="<price/>",console.error("Price formats need to include the `<price/>` tag."));const y=g&&m&&m<g;let f=(0,a.jsx)("span",{className:(0,c.A)("wc-block-components-product-price__value",p)});return y?f=(0,a.jsx)(d,{currency:o,price:m,priceClassName:p,priceStyle:h,regularPrice:g,regularPriceClassName:w,regularPriceStyle:x}):void 0!==u&&void 0!==n?f=(0,a.jsx)(l,{currency:o,maxPrice:n,minPrice:u,priceClassName:p,priceStyle:h}):m&&(f=(0,a.jsx)(s.FormattedMonetaryAmount,{className:(0,c.A)("wc-block-components-product-price__value",p),currency:o,value:m,style:h})),(0,a.jsx)("span",{className:b,style:_,children:(0,i.createInterpolateElement)(r,{price:f})})}},6037:(e,t,o)=>{"use strict";o.d(t,{U:()=>u});var r=o(6087),s=o(7594),c=o(7143),n=o(1174),i=o(3757);const a=e=>{const t=e?.detail;t&&t.preserveCartData||(0,c.dispatch)(s.cartStore).invalidateResolutionForStore()},l=e=>{(e?.persisted||"back_forward"===(0,n.F)())&&(0,c.dispatch)(s.cartStore).invalidateResolutionForStore()},d=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},u=()=>{(0,r.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),window.wcBlocksStoreCartListeners?.count>0)return void window.wcBlocksStoreCartListeners.count++;document.body.addEventListener("wc-blocks_added_to_cart",a),document.body.addEventListener("wc-blocks_removed_from_cart",a),window.addEventListener("pageshow",l);const e=(0,i.f2)("added_to_cart","wc-blocks_added_to_cart"),t=(0,i.f2)("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",a),document.body.removeEventListener("wc-blocks_removed_from_cart",a),window.removeEventListener("pageshow",l),e(),t()}})(),d)),[])}},5460:(e,t,o)=>{"use strict";o.d(t,{V:()=>w});var r=o(1824),s=o.n(r),c=o(6087),n=o(7594),i=o(7143),a=o(8537),l=o(4982),d=o(6037);const u={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},m={...u,email:""},p={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:n.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},h=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,a.decodeEntities)(t)]))),g={cartCoupons:n.EMPTY_CART_COUPONS,cartItems:n.EMPTY_CART_ITEMS,cartFees:n.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:n.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:n.EMPTY_CART_ITEM_ERRORS,cartTotals:p,cartIsLoading:!0,cartErrors:n.EMPTY_CART_ERRORS,billingData:m,billingAddress:m,shippingAddress:u,shippingRates:n.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:n.EMPTY_PAYMENT_METHODS,paymentRequirements:n.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:n.EMPTY_EXTENSIONS},w=(e={shouldSelect:!0})=>{const{shouldSelect:t}=e,o=(0,c.useRef)(),r=(0,c.useRef)(m),a=(0,c.useRef)(u);(0,d.U)();const p=(0,i.useSelect)(((e,{dispatch:o})=>{if(!t)return g;const c=e(n.cartStore),i=c.getCartData(),d=c.getCartErrors(),u=c.getCartTotals(),m=!c.hasFinishedResolution("getCartData"),p=c.isCustomerDataUpdating(),{receiveCart:w,receiveCartContents:x}=o(n.cartStore),_=i.fees.length>0?i.fees.map((e=>h(e))):n.EMPTY_CART_FEES,b=i.coupons.length>0?i.coupons.map((e=>({...e,label:e.code}))):n.EMPTY_CART_COUPONS,y=(0,l.TU)(h(i.billingAddress)),f=i.needsShipping?(0,l.TU)(h(i.shippingAddress)):y;return s()(y,r.current)||(r.current=y),s()(f,a.current)||(a.current=f),{cartCoupons:b,cartItems:i.items,crossSellsProducts:i.crossSells,cartFees:_,cartItemsCount:i.itemsCount,cartItemsWeight:i.itemsWeight,cartNeedsPayment:i.needsPayment,cartNeedsShipping:i.needsShipping,cartItemErrors:i.errors,cartTotals:u,cartIsLoading:m,cartErrors:d,billingData:r.current,billingAddress:r.current,shippingAddress:a.current,extensions:i.extensions,shippingRates:i.shippingRates,isLoadingRates:p,cartHasCalculatedShipping:i.hasCalculatedShipping,paymentRequirements:i.paymentRequirements,receiveCart:w,receiveCartContents:x}}),[t]);return o.current&&s()(o.current,p)||(o.current=p),o.current}},1057:(e,t,o)=>{"use strict";o.d(t,{R:()=>l});var r=o(6087),s=o(7143),c=o(7594),n=o(8537),i=o(5460);const a=(e,t)=>{const o=e.find((({id:e})=>e===t));return o?o.quantity:0},l=e=>{const{addItemToCart:t}=(0,s.useDispatch)(c.cartStore),{cartItems:o,cartIsLoading:l}=(0,i.V)(),{createErrorNotice:d,removeNotice:u}=(0,s.useDispatch)("core/notices"),[m,p]=(0,r.useState)(!1),h=(0,r.useRef)(a(o,e));return(0,r.useEffect)((()=>{const t=a(o,e);t!==h.current&&(h.current=t)}),[o,e]),{cartQuantity:Number.isFinite(h.current)?h.current:0,addingToCart:m,cartIsLoading:l,addToCart:(o=1)=>(p(!0),t(e,o).then((()=>{u("add-to-cart")})).catch((e=>{d((0,n.decodeEntities)(e.message),{id:"add-to-cart",context:"wc/all-products",isDismissible:!0})})).finally((()=>{p(!1)})))}}},7052:(e,t,o)=>{"use strict";o.d(t,{y:()=>n});var r=o(2619),s=o(7143),c=o(6087);const n=()=>({dispatchStoreEvent:(0,c.useCallback)(((e,t={})=>{try{(0,r.doAction)(`experimental__woocommerce_blocks-${e}`,t)}catch(e){console.error(e)}}),[]),dispatchCheckoutEvent:(0,c.useCallback)(((e,t={})=>{try{(0,r.doAction)(`experimental__woocommerce_blocks-checkout-${e}`,{...t,storeCart:(0,s.select)("wc/store/cart").getCartData()})}catch(e){console.error(e)}}),[])})},371:(e,t,o)=>{"use strict";o.d(t,{p:()=>i});var r=o(4921),s=o(3993),c=o(219),n=o(17);const i=e=>{const t=(e=>{const t=(0,s.isObject)(e)?e:{style:{}};let o=t.style;return(0,s.isString)(o)&&(o=JSON.parse(o)||{}),(0,s.isObject)(o)||(o={}),{...t,style:o}})(e),o=(0,n.BK)(t),i=(0,n.aR)(t),a=(0,n.fo)(t),l=(0,c.x)(t);return{className:(0,r.A)(l.className,o.className,i.className,a.className),style:{...l.style,...o.style,...i.style,...a.style}}}},219:(e,t,o)=>{"use strict";o.d(t,{x:()=>s});var r=o(3993);const s=e=>{const t=(0,r.isObject)(e.style.typography)?e.style.typography:{},o=(0,r.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:o,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}}},4982:(e,t,o)=>{"use strict";o.d(t,{TU:()=>n});var r=o(2e3),s=o(8331),c=o(5703);o(3993),o(8537),o(3832);const n=e=>{const t=(0,r.A)(s.Hw,c.defaultFields,e.country),o=Object.assign({},e);return t.forEach((({key:t,hidden:r})=>{!0===r&&((e,t)=>e in t)(t,e)&&(o[t]="")})),o}},17:(e,t,o)=>{"use strict";o.d(t,{BK:()=>l,aR:()=>d,fo:()=>u});var r=o(4921),s=o(7356),c=o(9786),n=o(3993);function i(e={}){const t={};return(0,c.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function a(e,t){return e&&t?`has-${(0,s.c)(t)}-${e}`:""}function l(e){const{backgroundColor:t,textColor:o,gradient:s,style:c}=e,l=a("background-color",t),d=a("color",o),u=function(e){if(e)return`has-${e}-gradient-background`}(s),m=u||c?.color?.gradient;return{className:(0,r.A)(d,u,{[l]:!m&&!!l,"has-text-color":o||c?.color?.text,"has-background":t||c?.color?.background||s||c?.color?.gradient,"has-link-color":(0,n.isObject)(c?.elements?.link)?c?.elements?.link?.color:void 0}),style:i({color:c?.color||{}})}}function d(e){const t=e.style?.border||{};return{className:function(e){const{borderColor:t,style:o}=e,s=t?a("border-color",t):"";return(0,r.A)({"has-border-color":!!t||!!o?.border?.color,[s]:!!s})}(e),style:i({border:t})}}function u(e){return{className:void 0,style:i({spacing:e.style?.spacing||{}})}}},1174:(e,t,o)=>{"use strict";o.d(t,{F:()=>r});const r=()=>window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:""},3757:(e,t,o)=>{"use strict";o.d(t,{f2:()=>s});const r=window.CustomEvent||null,s=(e,t,o=!1,s=!1)=>{if("function"!=typeof jQuery)return()=>{};const c=()=>{((e,{bubbles:t=!1,cancelable:o=!1,element:s,detail:c={}})=>{if(!r)return;s||(s=document.body);const n=new r(e,{bubbles:t,cancelable:o,detail:c});s.dispatchEvent(n)})(t,{bubbles:o,cancelable:s})};return jQuery(document).on(e,c),()=>jQuery(document).off(e,c)}},4040:(e,t,o)=>{"use strict";o.r(t),o.d(t,{metadata:()=>$t,name:()=>Io});const r=window.wp.blocks;var s=o(4530),c=o(9264),n=o(7723),i=o(6427),a=o(9491),l=o(4715),d=o(6087),u=o(5573),m=o(790);function p({level:e}){const t={1:"M9 5h2v10H9v-4H5v4H3V5h2v4h4V5zm6.6 0c-.6.9-1.5 1.7-2.6 2v1h2v7h2V5h-1.4z",2:"M7 5h2v10H7v-4H3v4H1V5h2v4h4V5zm8 8c.5-.4.6-.6 1.1-1.1.4-.4.8-.8 1.2-1.3.3-.4.6-.8.9-1.3.2-.4.3-.8.3-1.3 0-.4-.1-.9-.3-1.3-.2-.4-.4-.7-.8-1-.3-.3-.7-.5-1.2-.6-.5-.2-1-.2-1.5-.2-.4 0-.7 0-1.1.1-.3.1-.7.2-1 .3-.3.1-.6.3-.9.5-.3.2-.6.4-.8.7l1.2 1.2c.3-.3.6-.5 1-.7.4-.2.7-.3 1.2-.3s.9.1 1.3.4c.3.3.5.7.5 1.1 0 .4-.1.8-.4 1.1-.3.5-.6.9-1 1.2-.4.4-1 .9-1.6 1.4-.6.5-1.4 1.1-2.2 1.6V15h8v-2H15z",3:"M12.1 12.2c.4.3.8.5 1.2.7.4.2.9.3 1.4.3.5 0 1-.1 1.4-.3.3-.1.5-.5.5-.8 0-.2 0-.4-.1-.6-.1-.2-.3-.3-.5-.4-.3-.1-.7-.2-1-.3-.5-.1-1-.1-1.5-.1V9.1c.7.1 1.5-.1 2.2-.4.4-.2.6-.5.6-.9 0-.3-.1-.6-.4-.8-.3-.2-.7-.3-1.1-.3-.4 0-.8.1-1.1.3-.4.2-.7.4-1.1.6l-1.2-1.4c.5-.4 1.1-.7 1.6-.9.5-.2 1.2-.3 1.8-.3.5 0 1 .1 1.6.2.4.1.8.3 1.2.5.3.2.6.5.8.8.2.3.3.7.3 1.1 0 .5-.2.9-.5 1.3-.4.4-.9.7-1.5.9v.1c.6.1 1.2.4 1.6.8.4.4.7.9.7 1.5 0 .4-.1.8-.3 1.2-.2.4-.5.7-.9.9-.4.3-.9.4-1.3.5-.5.1-1 .2-1.6.2-.8 0-1.6-.1-2.3-.4-.6-.2-1.1-.6-1.6-1l1.1-1.4zM7 9H3V5H1v10h2v-4h4v4h2V5H7v4z",4:"M9 15H7v-4H3v4H1V5h2v4h4V5h2v10zm10-2h-1v2h-2v-2h-5v-2l4-6h3v6h1v2zm-3-2V7l-2.8 4H16z",5:"M12.1 12.2c.4.3.7.5 1.1.7.4.2.9.3 1.3.3.5 0 1-.1 1.4-.4.4-.3.6-.7.6-1.1 0-.4-.2-.9-.6-1.1-.4-.3-.9-.4-1.4-.4H14c-.1 0-.3 0-.4.1l-.4.1-.5.2-1-.6.3-5h6.4v1.9h-4.3L14 8.8c.2-.1.5-.1.7-.2.2 0 .5-.1.7-.1.5 0 .9.1 1.4.2.4.1.8.3 1.1.6.3.2.6.6.8.9.2.4.3.9.3 1.4 0 .5-.1 1-.3 1.4-.2.4-.5.8-.9 1.1-.4.3-.8.5-1.3.7-.5.2-1 .3-1.5.3-.8 0-1.6-.1-2.3-.4-.6-.2-1.1-.6-1.6-1-.1-.1 1-1.5 1-1.5zM9 15H7v-4H3v4H1V5h2v4h4V5h2v10z",6:"M9 15H7v-4H3v4H1V5h2v4h4V5h2v10zm8.6-7.5c-.2-.2-.5-.4-.8-.5-.6-.2-1.3-.2-1.9 0-.3.1-.6.3-.8.5l-.6.9c-.2.5-.2.9-.2 1.4.4-.3.8-.6 1.2-.8.4-.2.8-.3 1.3-.3.4 0 .8 0 1.2.2.4.1.7.3 1 .6.3.3.5.6.7.9.2.4.3.8.3 1.3s-.1.9-.3 1.4c-.2.4-.5.7-.8 1-.4.3-.8.5-1.2.6-1 .3-2 .3-3 0-.5-.2-1-.5-1.4-.9-.4-.4-.8-.9-1-1.5-.2-.6-.3-1.3-.3-2.1s.1-1.6.4-2.3c.2-.6.6-1.2 1-1.6.4-.4.9-.7 1.4-.9.6-.3 1.1-.4 1.7-.4.7 0 1.4.1 2 .3.5.2 1 .5 1.4.8 0 .1-1.3 1.4-1.3 1.4zm-2.4 5.8c.2 0 .4 0 .6-.1.2 0 .4-.1.5-.2.1-.1.3-.3.4-.5.1-.2.1-.5.1-.7 0-.4-.1-.8-.4-1.1-.3-.2-.7-.3-1.1-.3-.3 0-.7.1-1 .2-.4.2-.7.4-1 .7 0 .3.1.7.3 1 .1.2.3.4.4.6.2.1.3.3.5.3.2.1.5.2.7.1z"};return t.hasOwnProperty(e)?(0,m.jsx)(u.SVG,{width:"20",height:"20",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:(0,m.jsx)(u.Path,{d:t[e]})}):null}class h extends d.Component{createLevelControl(e,t,o){const r=e===t;return{icon:(0,m.jsx)(p,{level:e}),title:(0,n.sprintf)(/* translators: %s: heading level e.g: "2", "3", "4" */ /* translators: %s: heading level e.g: "2", "3", "4" */
(0,n.__)("Heading %d","woocommerce"),e),isActive:r,onClick:()=>o(e)}}render(){const{isCollapsed:e=!0,minLevel:t,maxLevel:o,selectedLevel:r,onChange:s}=this.props,c=Array.from({length:o-t+1},((e,o)=>o+t));return(0,m.jsx)(i.ToolbarGroup,{isCollapsed:e,icon:(0,m.jsx)(p,{level:r}),controls:c.map((e=>this.createLevelControl(e,r,s)))})}}const g=h;var w=o(7581),x=o(3993),_=o(4921);function b(e,t,o){const r=new Set(t.map((e=>e[o])));return e.filter((e=>!r.has(e[o])))}var y=o(8537);const f={clear:(0,n.__)("Clear all selected items","woocommerce"),noItems:(0,n.__)("No items found.","woocommerce"),
/* Translators: %s search term */
noResults:(0,n.__)("No results for %s","woocommerce"),search:(0,n.__)("Search for items","woocommerce"),selected:e=>(0,n.sprintf)(/* translators: Number of items selected from list. */ /* translators: Number of items selected from list. */
(0,n._n)("%d item selected","%d items selected",e,"woocommerce"),e),updated:(0,n.__)("Search results updated.","woocommerce")},k=(e,t=e)=>{const o=e.reduce(((e,t)=>{const o=t.parent||0;return e[o]||(e[o]=[]),e[o].push(t),e}),{}),r=t.reduce(((e,t)=>(e[String(t.id)]=t,e)),{});const s=["0"],c=(e={})=>e.parent?[...c(r[e.parent]),e.name]:e.name?[e.name]:[],n=e=>e.map((e=>{const t=o[e.id];return s.push(""+e.id),{...e,breadcrumbs:c(r[e.parent]),children:t&&t.length?n(t):[]}})),i=n(o[0]||[]);return Object.entries(o).forEach((([e,t])=>{s.includes(e)||i.push(...n(t||[]))})),i},v=(e,t)=>{if(!t)return e;const o=new RegExp(`(${t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")})`,"ig");return e.split(o).map(((e,t)=>o.test(e)?(0,m.jsx)("strong",{children:e},t):(0,m.jsx)(d.Fragment,{children:e},t)))},j=({label:e})=>(0,m.jsx)("span",{className:"woocommerce-search-list__item-count",children:e}),C=e=>{const{item:t,search:o}=e,r=t.breadcrumbs&&t.breadcrumbs.length;return(0,m.jsxs)("span",{className:"woocommerce-search-list__item-label",children:[r?(0,m.jsx)("span",{className:"woocommerce-search-list__item-prefix",children:(s=t.breadcrumbs,1===s.length?s.slice(0,1).toString():2===s.length?s.slice(0,1).toString()+" › "+s.slice(-1).toString():s.slice(0,1).toString()+" … "+s.slice(-1).toString())}):null,(0,m.jsx)("span",{className:"woocommerce-search-list__item-name",children:v((0,y.decodeEntities)(t.name),o)})]});var s},S=({countLabel:e,className:t,depth:o=0,controlId:r="",item:s,isSelected:c,isSingle:n,onSelect:a,search:l="",selected:u,useExpandedPanelId:p,...h})=>{const[g,w]=p,x=null!=e&&void 0!==s.count&&null!==s.count,f=!!s.breadcrumbs?.length,k=!!s.children?.length,S=g===s.id,N=(0,_.A)(["woocommerce-search-list__item",`depth-${o}`,t],{"has-breadcrumbs":f,"has-children":k,"has-count":x,"is-expanded":S,"is-radio-button":n});(0,d.useEffect)((()=>{k&&c&&w(s.id)}),[s,k,c,w]);const E=h.name||`search-list-item-${r}`,P=`${E}-${s.id}`,A=(0,d.useCallback)((()=>{w(S?-1:Number(s.id))}),[S,s.id,w]);return k?(0,m.jsx)("div",{className:N,onClick:A,onKeyDown:e=>"Enter"===e.key||" "===e.key?A():null,role:"treeitem",tabIndex:0,children:n?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("input",{type:"radio",id:P,name:E,value:s.value,onChange:a(s),onClick:e=>e.stopPropagation(),checked:c,className:"woocommerce-search-list__item-input",...h}),(0,m.jsx)(C,{item:s,search:l}),x?(0,m.jsx)(j,{label:e||s.count}):null]}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(i.CheckboxControl,{className:"woocommerce-search-list__item-input",checked:c,...!c&&s.children.some((e=>u.find((t=>t.id===e.id))))?{indeterminate:!0}:{},label:v((0,y.decodeEntities)(s.name),l),onChange:()=>{c?a(b(u,s.children,"id"))():a(function(e,t){const o=b(t,e,"id");return[...e,...o]}(u,s.children))()},onClick:e=>e.stopPropagation()}),x?(0,m.jsx)(j,{label:e||s.count}):null]})}):(0,m.jsxs)("label",{htmlFor:P,className:N,children:[n?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("input",{...h,type:"radio",id:P,name:E,value:s.value,onChange:a(s),checked:c,className:"woocommerce-search-list__item-input"}),(0,m.jsx)(C,{item:s,search:l})]}):(0,m.jsx)(i.CheckboxControl,{...h,id:P,name:E,className:"woocommerce-search-list__item-input",value:(0,y.decodeEntities)(s.value),label:v((0,y.decodeEntities)(s.name),l),onChange:a(s),checked:c}),x?(0,m.jsx)(j,{label:e||s.count}):null]})},N=S;var E=o(2624),P=o(3028);o(5022);const A=({id:e,label:t,popoverContents:o,remove:r,screenReaderLabel:c,className:l=""})=>{const[u,p]=(0,d.useState)(!1),h=(0,a.useInstanceId)(A);if(c=c||t,!t)return null;t=(0,y.decodeEntities)(t);const g=(0,_.A)("woocommerce-tag",l,{"has-remove":!!r}),w=`woocommerce-tag__label-${h}`,x=(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("span",{className:"screen-reader-text",children:c}),(0,m.jsx)("span",{"aria-hidden":"true",children:t})]});return(0,m.jsxs)("span",{className:g,children:[o?(0,m.jsx)(i.Button,{className:"woocommerce-tag__text",id:w,onClick:()=>p(!0),children:x}):(0,m.jsx)("span",{className:"woocommerce-tag__text",id:w,children:x}),o&&u&&(0,m.jsx)(i.Popover,{onClose:()=>p(!1),children:o}),r&&(0,m.jsx)(i.Button,{className:"woocommerce-tag__remove",onClick:r(e),label:(0,n.sprintf)(
// Translators: %s label.
// Translators: %s label.
(0,n.__)("Remove %s","woocommerce"),t),"aria-describedby":w,children:(0,m.jsx)(s.A,{icon:P.A,size:20,className:"clear-icon",role:"img"})})]})},T=A;o(1939);const I=e=>(0,m.jsx)(N,{...e}),B=e=>{const{list:t,selected:o,renderItem:r,depth:s=0,onSelect:c,instanceId:n,isSingle:i,search:a,useExpandedPanelId:l}=e,[u]=l;return t?(0,m.jsx)(m.Fragment,{children:t.map((t=>{const p=t.children?.length&&!i?t.children.every((({id:e})=>o.find((t=>t.id===e)))):!!o.find((({id:e})=>e===t.id)),h=t.children?.length&&u===t.id;return(0,m.jsxs)(d.Fragment,{children:[(0,m.jsx)("li",{children:r({item:t,isSelected:p,onSelect:c,isSingle:i,selected:o,search:a,depth:s,useExpandedPanelId:l,controlId:n})}),h?(0,m.jsx)(B,{...e,list:t.children,depth:s+1}):null]},t.id)}))}):null},L=({isLoading:e,isSingle:t,selected:o,messages:r,onChange:s,onRemove:c})=>{if(e||t||!o)return null;const a=o.length;return(0,m.jsxs)("div",{className:"woocommerce-search-list__selected",children:[(0,m.jsxs)("div",{className:"woocommerce-search-list__selected-header",children:[(0,m.jsx)("strong",{children:r.selected(a)}),a>0?(0,m.jsx)(i.Button,{variant:"link",isDestructive:!0,onClick:()=>s([]),"aria-label":r.clear,children:(0,n.__)("Clear all","woocommerce")}):null]}),a>0?(0,m.jsx)("ul",{children:o.map(((e,t)=>(0,m.jsx)("li",{children:(0,m.jsx)(T,{label:e.name,id:e.id,remove:c})},t)))}):null]})},O=({filteredList:e,search:t,onSelect:o,instanceId:r,useExpandedPanelId:c,...i})=>{const{messages:a,renderItem:l,selected:d,isSingle:u}=i,p=l||I;return 0===e.length?(0,m.jsxs)("div",{className:"woocommerce-search-list__list is-not-found",children:[(0,m.jsx)("span",{className:"woocommerce-search-list__not-found-icon",children:(0,m.jsx)(s.A,{icon:E.A,role:"img"})}),(0,m.jsx)("span",{className:"woocommerce-search-list__not-found-text",children:t?(0,n.sprintf)(a.noResults,t):a.noItems})]}):(0,m.jsx)("ul",{className:"woocommerce-search-list__list",children:(0,m.jsx)(B,{useExpandedPanelId:c,list:e,selected:d,renderItem:p,onSelect:o,instanceId:r,isSingle:u,search:t})})},D=e=>{const{className:t="",isCompact:o,isHierarchical:r,isLoading:s,isSingle:c,list:l,messages:u=f,onChange:p,onSearch:h,selected:g,type:w="text",debouncedSpeak:x}=e,[b,y]=(0,d.useState)(""),v=(0,d.useState)(-1),j=(0,a.useInstanceId)(D),C=(0,d.useMemo)((()=>({...f,...u})),[u]),S=(0,d.useMemo)((()=>((e,t,o)=>{if(!t)return o?k(e):e;const r=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"i"),s=e.map((e=>!!r.test(e.name)&&e)).filter(Boolean);return o?k(s,e):s})(l,b,r)),[l,b,r]);(0,d.useEffect)((()=>{x&&x(C.updated)}),[x,C]),(0,d.useEffect)((()=>{"function"==typeof h&&h(b)}),[b,h]);const N=(0,d.useCallback)((e=>()=>{c&&p([]);const t=g.findIndex((({id:t})=>t===e));p([...g.slice(0,t),...g.slice(t+1)])}),[c,g,p]),E=(0,d.useCallback)((e=>()=>{Array.isArray(e)?p(e):-1===g.findIndex((({id:t})=>t===e.id))?p(c?[e]:[...g,e]):N(e.id)()}),[c,N,p,g]),P=(0,d.useCallback)((e=>{const[t]=g.filter((t=>!e.find((e=>t.id===e.id))));N(t.id)()}),[N,g]);return(0,m.jsxs)("div",{className:(0,_.A)("woocommerce-search-list",t,{"is-compact":o,"is-loading":s,"is-token":"token"===w}),children:["text"===w&&(0,m.jsx)(L,{...e,onRemove:N,messages:C}),(0,m.jsx)("div",{className:"woocommerce-search-list__search",children:"text"===w?(0,m.jsx)(i.TextControl,{label:C.search,type:"search",value:b,onChange:e=>y(e)}):(0,m.jsx)(i.FormTokenField,{disabled:s,label:C.search,onChange:P,onInputChange:e=>y(e),suggestions:[],__experimentalValidateInput:()=>!1,value:s?[(0,n.__)("Loading…","woocommerce")]:g.map((e=>({...e,value:e.name}))),__experimentalShowHowTo:!1})}),s?(0,m.jsx)("div",{className:"woocommerce-search-list__list",children:(0,m.jsx)(i.Spinner,{})}):(0,m.jsx)(O,{...e,search:b,filteredList:S,messages:C,onSelect:E,instanceId:j,useExpandedPanelId:v})]})},R=((0,i.withSpokenMessages)(D),e=>t=>{let{selected:o}=t;o=void 0===o?null:o;const r=null===o;return Array.isArray(o)?(0,m.jsx)(e,{...t}):(0,m.jsx)(e,{...t,selected:r?[]:[o]})});var F=o(8331),V=o(3832);const $=window.wp.apiFetch;var M=o.n($),z=o(5703);const H=({selected:e=[],search:t="",queryArgs:o={}})=>{const r=(({selected:e=[],search:t="",queryArgs:o={}})=>{const r=F.r7.productCount>100,s={per_page:r?100:0,catalog_visibility:"any",search:t,orderby:"title",order:"asc"},c=[(0,V.addQueryArgs)("/wc/store/v1/products",{...s,...o})];return r&&e.length&&c.push((0,V.addQueryArgs)("/wc/store/v1/products",{catalog_visibility:"any",include:e,per_page:0})),c})({selected:e,search:t,queryArgs:o});return Promise.all(r.map((e=>M()({path:e})))).then((e=>{const t=((e,t)=>{const o=new Map;return e.filter((e=>{const r=t(e);return!o.has(r)&&(o.set(r,e),!0)}))})(e.flat(),(e=>e.id));return t.map((e=>({...e,parent:0})))})).catch((e=>{throw e}))};var W=o(4347);const q=async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}};var Q=o(923),G=o.n(Q);const U=(0,a.createHigherOrderComponent)((e=>{class t extends d.Component{state={error:null,loading:!1,variations:{}};componentDidMount(){const{selected:e,showVariations:t}=this.props;e&&t&&this.loadVariations()}componentDidUpdate(e){const{isLoading:t,selected:o,showVariations:r}=this.props;r&&(!G()(e.selected,o)||e.isLoading&&!t)&&this.loadVariations()}loadVariations=()=>{const{products:e}=this.props,{loading:t,variations:o}=this.state;if(t)return;const r=this.getExpandedProduct();if(!r||o[r])return;const s=e.find((e=>e.id===r));var c;s?.variations&&0!==s.variations.length?(this.setState({loading:!0}),(c=r,M()({path:(0,V.addQueryArgs)("wc/store/v1/products",{per_page:0,type:"variation",parent:c})})).then((e=>{const t=e.map((e=>({...e,parent:r})));this.setState({variations:{...this.state.variations,[r]:t},loading:!1,error:null})})).catch((async e=>{const t=await q(e);this.setState({variations:{...this.state.variations,[r]:null},loading:!1,error:t})}))):this.setState({variations:{...this.state.variations,[r]:null},loading:!1,error:null})};isProductId(e){const{products:t}=this.props;return t.some((t=>t.id===e))}findParentProduct(e){const{products:t}=this.props,o=t.filter((t=>t.variations&&t.variations.find((({id:t})=>t===e))));return o[0]?.id}getExpandedProduct(){const{isLoading:e,selected:t,showVariations:o}=this.props;if(!o)return null;let r=t&&t.length?t[0]:null;return r?this.prevSelectedItem=r:!this.prevSelectedItem||e||this.isProductId(this.prevSelectedItem)||(r=this.prevSelectedItem),!e&&r?this.isProductId(r)?r:this.findParentProduct(r):null}render(){const{error:t,isLoading:o}=this.props,{error:r,loading:s,variations:c}=this.state;return(0,m.jsx)(e,{...this.props,error:r||t,expandedProduct:this.getExpandedProduct(),isLoading:o,variations:c,variationsLoading:s})}}return t}),"withProductVariations"),Y=U,J=e=>{const{id:t,name:o,parent:r}=e;return{id:t,name:o,parent:r,breadcrumbs:[],children:[],details:e,value:e.slug}},K=window.wp.escapeHtml,Z=({message:e,type:t})=>e?"general"===t?(0,m.jsxs)("span",{children:[(0,n.__)("The following error was returned","woocommerce"),(0,m.jsx)("br",{}),(0,m.jsx)("code",{children:(0,K.escapeHTML)(e)})]}):"api"===t?(0,m.jsxs)("span",{children:[(0,n.__)("The following error was returned from the API","woocommerce"),(0,m.jsx)("br",{}),(0,m.jsx)("code",{children:(0,K.escapeHTML)(e)})]}):e:(0,n.__)("An error has prevented the block from being updated.","woocommerce"),X=({error:e})=>(0,m.jsx)("div",{className:"wc-block-error-message",children:Z(e)});var ee=o(1609);const te=({className:e,item:t,isSelected:o,isLoading:r,onSelect:s,disabled:c,...n})=>(0,m.jsxs)(m.Fragment,{children:[(0,ee.createElement)(S,{...n,key:t.id,className:e,isSelected:o,item:t,onSelect:s,disabled:c}),o&&r&&(0,m.jsx)("div",{className:(0,_.A)("woocommerce-search-list__item","woocommerce-product-attributes__item","depth-1","is-loading","is-not-active"),children:(0,m.jsx)(i.Spinner,{})},"loading")]});o(5653);const oe={list:(0,n.__)("Products","woocommerce"),noItems:(0,n.__)("Your store doesn't have any products.","woocommerce"),search:(0,n.__)("Search for a product to display","woocommerce"),updated:(0,n.__)("Product search results updated.","woocommerce")},re=R((se=Y((0,a.withInstanceId)((e=>{const{expandedProduct:t=null,error:o,instanceId:r,isCompact:s=!1,isLoading:c,onChange:i,onSearch:a,products:l,renderItem:d,selected:u=[],showVariations:p=!1,variations:h,variationsLoading:g}=e;if(o)return(0,m.jsx)(X,{error:o});const w=[...l,...h&&t&&h[t]?h[t]:[]].map(J);return(0,m.jsx)(D,{className:"woocommerce-products",list:w,isCompact:s,isLoading:c,isSingle:!0,selected:w.filter((({id:e})=>u.includes(Number(e)))),onChange:i,renderItem:d||(p?e=>{const{item:t,search:o,depth:s=0,isSelected:i,onSelect:a}=e,l=t.details?.variations&&Array.isArray(t.details.variations)?t.details.variations.length:0,d=(0,_.A)("woocommerce-search-product__item","woocommerce-search-list__item",`depth-${s}`,"has-count",{"is-searching":o.length>0,"is-skip-level":0===s&&0!==t.parent,"is-variable":l>0});if(!t.breadcrumbs.length){const o=t.details?.variations&&t.details.variations.length>0;return(0,m.jsx)(te,{...e,className:(0,_.A)(d,{"is-selected":i}),isSelected:i,item:t,onSelect:()=>()=>{a(t)()},isLoading:c||g,countLabel:o?(0,n.sprintf)(/* translators: %1$d is the number of variations of a product product. */ /* translators: %1$d is the number of variations of a product product. */
(0,n.__)("%1$d variations","woocommerce"),t.details?.variations.length):null,name:`products-${r}`,"aria-label":o?(0,n.sprintf)(/* translators: %1$s is the product name, %2$d is the number of variations of that product. */ /* translators: %1$s is the product name, %2$d is the number of variations of that product. */
(0,n._n)("%1$s, has %2$d variation","%1$s, has %2$d variations",t.details?.variations?.length,"woocommerce"),t.name,t.details?.variations.length):void 0})}const u=(0,x.isEmpty)(t.details?.variation)?e:{...e,item:{...e.item,name:t.details?.variation},"aria-label":`${t.breadcrumbs[0]}: ${t.details?.variation}`};return(0,m.jsx)(S,{...u,className:d,name:`variations-${r}`})}:void 0),onSearch:a,messages:{...oe,...e.messages},isHierarchical:!0})}))),({selected:e,...t})=>{const[o,r]=(0,d.useState)(!0),[s,c]=(0,d.useState)(null),[n,i]=(0,d.useState)([]),a=F.r7.productCount>100,l=async e=>{const t=await q(e);c(t),r(!1)},u=(0,d.useRef)(e);(0,d.useEffect)((()=>{H({selected:u.current}).then((e=>{i(e),r(!1)})).catch(l)}),[u]);const p=(0,W.YQ)((t=>{H({selected:e,search:t}).then((e=>{i(e),r(!1)})).catch(l)}),400),h=(0,d.useCallback)((e=>{r(!0),p(e)}),[r,p]);return(0,m.jsx)(se,{...t,selected:e,error:s,products:n,isLoading:o,onSearch:a?h:null})}));var se;o(3324);const ce=function({className:e="",...t}){const o=(0,_.A)("wc-block-text-toolbar-button",e);return(0,m.jsx)(i.Button,{className:o,...t})};var ne=o(415);o(3433);const ie=e=>t=>o=>{const r=(0,ne.useProductDataContext)(),{attributes:s,setAttributes:c}=o,{productId:a}=s,[u,p]=(0,d.useState)(!a);return r.hasContext||Number.isFinite(o.context?.queryId)?(0,m.jsx)(t,{...o}):(0,m.jsx)(m.Fragment,{children:u?(0,m.jsxs)(i.Placeholder,{icon:e.icon||"",label:e.label||"",className:"wc-atomic-blocks-product",children:[!!e.description&&(0,m.jsx)("div",{children:e.description}),(0,m.jsxs)("div",{className:"wc-atomic-blocks-product__selection",children:[(0,m.jsx)(re,{selected:a||0,showVariations:!0,onChange:(e=[])=>{c({productId:e[0]?e[0].id:0})}}),(0,m.jsx)(i.Button,{variant:"secondary",disabled:!a,onClick:()=>{p(!1)},children:(0,n.__)("Done","woocommerce")})]})]}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(i.ToolbarGroup,{children:(0,m.jsx)(ce,{onClick:()=>p(!0),children:(0,n.__)("Switch product…","woocommerce")})})}),(0,m.jsx)(t,{...o})]})})};var ae=o(8992);const le=(0,m.jsx)(s.A,{icon:ae.A,className:"wc-block-editor-components-block-icon"});var de=o(3925);o(1784);const ue=(0,a.compose)([ie({icon:le,label:de.title,description:(0,n.__)("Choose a product to display its title.","woocommerce")})])((({attributes:e,setAttributes:t})=>{const o=(0,l.useBlockProps)(),{headingLevel:r,showProductLink:s,align:c,linkTarget:a}=e;return(0,m.jsxs)("div",{...o,children:[(0,m.jsxs)(l.BlockControls,{children:[(0,m.jsx)(g,{isCollapsed:!0,minLevel:1,maxLevel:7,selectedLevel:r,onChange:e=>t({headingLevel:e})}),(0,m.jsx)(l.AlignmentToolbar,{value:c,onChange:e=>{t({align:e})}})]}),(0,m.jsx)(l.InspectorControls,{children:(0,m.jsxs)(i.PanelBody,{title:(0,n.__)("Link settings","woocommerce"),children:[(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Make title a link","woocommerce"),checked:s,onChange:()=>t({showProductLink:!s})}),s&&(0,m.jsx)(m.Fragment,{children:(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Open in new tab","woocommerce"),onChange:e=>t({linkTarget:e?"_blank":"_self"}),checked:"_blank"===a})})]})}),(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(w.A,{...e})})]})}));(0,r.registerBlockType)(de,{icon:{src:le},edit:ue,save:({attributes:e})=>(0,m.jsx)("div",{...l.useBlockProps.save({className:(0,_.A)("is-loading",e.className)})})});var me=o(7143);class pe{blocks=new Map;initialized=!1;attemptedRegisteredBlocks=new Set;constructor(){this.initializeSubscriptions()}static getInstance(){return pe.instance||(pe.instance=new pe),pe.instance}parseTemplateId(e){const t=(0,x.isNumber)(e)?void 0:e;return t?.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const e=(0,me.subscribe)((()=>{const t=(0,me.select)("core/edit-site"),o=(0,me.select)("core/edit-post");if(t||o)if(t){const o=t.getEditedPostId();e(),this.currentTemplateId="string"==typeof o?this.parseTemplateId(o):void 0,(0,me.subscribe)((()=>{const e=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(t.getEditedPostId()),e!==this.currentTemplateId&&this.handleTemplateChange(e)}),"core/edit-site"),this.initialized=!0}else o&&(e(),this.blocks.forEach((e=>{if(e.isAvailableOnPostEditor){const t=e.variationName||e.blockName;this.hasAttemptedRegistration(t)||this.registerBlock(e)}})),this.initialized=!0)}))}handleTemplateChange(e){(this.currentTemplateId?.includes("single-product")||e?.includes("single-product"))&&this.blocks.forEach((e=>{this.unregisterBlock(e),this.registerBlock(e)}))}hasAttemptedRegistration(e){return this.attemptedRegisteredBlocks.has(e)}unregisterBlock(e){const{blockName:t,isVariationBlock:o,variationName:s}=e;try{o&&s?((0,r.unregisterBlockVariation)(t,s),this.attemptedRegisteredBlocks.delete(s)):((0,r.unregisterBlockType)(t),this.attemptedRegisteredBlocks.delete(t))}catch(e){console.debug(`Failed to unregister block ${t}:`,e)}}registerBlock(e){const{blockName:t,settings:o,isVariationBlock:s,variationName:c,isAvailableOnPostEditor:n}=e;try{const e=c||t;if(this.hasAttemptedRegistration(e))return;const i=(0,me.select)("core/edit-site");if(!i&&!n)return;if(s)(0,r.registerBlockVariation)(t,o);else{const e=(0,x.isEmpty)(o?.ancestor)?["woocommerce/single-product"]:o?.ancestor,s=i&&this.currentTemplateId?.includes("single-product");(0,r.registerBlockType)(t,{...o,ancestor:s?void 0:e})}this.attemptedRegisteredBlocks.add(e)}catch(e){console.error(`Failed to register block ${t}:`,e)}}registerBlockConfig(e){const t=e.variationName||e.blockName;this.blocks.set(t,e),this.registerBlock(e)}}const he=(e,t)=>{const o="string"==typeof e?e:e.name;if(!o)return void console.error("registerProductBlockType: Block name is required for registration");const r="string"==typeof e?{}:(({name:e,...t})=>t)(e),{isVariationBlock:s,variationName:c,isAvailableOnPostEditor:n,...i}={...r,...t||{}},a={blockName:o,settings:{...i},isVariationBlock:null!=s&&s,variationName:null!=c?c:void 0,isAvailableOnPostEditor:null!=n&&n};pe.getInstance().registerBlockConfig(a)};var ge=o(8486);const we=({attributes:e})=>e.isDescendentOfQueryLoop||e.isDescendentOfSingleProductBlock||e.isDescendentOfSingleProductTemplate?null:(0,m.jsx)("div",{className:(0,_.A)("is-loading",e.className)});var xe=o(1308);const _e=()=>({isDescendentOfSingleProductTemplate:(0,me.useSelect)((e=>{const t=e("core/edit-site"),o=t?.getEditedPostId();return Boolean(o?.includes("//single-product"))}),[])});he({...JSON.parse('{"name":"woocommerce/product-price","title":"Product Price","description":"Display the price of a product.","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"textAlign":{"type":"string","default":""},"isDescendentOfSingleProductTemplate":{"type":"boolean","default":false},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false}},"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"supports":{"html":false,"interactivity":{"clientNavigation":true},"color":{"text":true,"background":true,"link":false,"__experimentalSkipSerialization":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalSkipSerialization":true,"__experimentalLetterSpacing":true},"__experimentalSelector":".wp-block-woocommerce-product-price .wc-block-components-product-price","spacing":{"margin":true,"padding":true}},"ancestor":["woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template"],"$schema":"https://schemas.wp.org/trunk/block.json"}'),icon:(0,m.jsx)(s.A,{icon:ge.A,className:"wc-block-editor-components-block-icon"}),edit:({attributes:e,setAttributes:t,context:o})=>{const r=(0,l.useBlockProps)(),s={...e,...o},c=Number.isFinite(o.queryId);let{isDescendentOfSingleProductTemplate:n}=_e();return c&&(n=!1),(0,d.useEffect)((()=>t({isDescendentOfQueryLoop:c,isDescendentOfSingleProductTemplate:n})),[c,n,t]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(l.AlignmentToolbar,{value:e.textAlign,onChange:e=>{t({textAlign:e})}})}),(0,m.jsx)("div",{...r,children:(0,m.jsx)(xe.default,{...s})})]})},save:we},{isAvailableOnPostEditor:!0});var be=o(3104),ye=o(4003);const fe=(0,m.jsx)(s.A,{icon:ye.A,className:"wc-block-editor-components-block-icon"});var ke=o(7746),ve=o(4264);const je={cover:(0,n.__)("Image is scaled and cropped to fill the entire space without being distorted.","woocommerce"),contain:(0,n.__)("Image is scaled to fill the space without clipping nor distorting.","woocommerce"),fill:(0,n.__)("Image will be stretched and distorted to completely fill the space.","woocommerce")},Ce=[{value:"px",label:"px"},{value:"em",label:"em"},{value:"rem",label:"rem"},{value:"%",label:"%"},{value:"vw",label:"vw"},{value:"vh",label:"vh"}],Se=({scale:e,width:t,height:o,setAttributes:r})=>(0,m.jsxs)(i.__experimentalToolsPanel,{className:"wc-block-product-image__tools-panel",label:(0,n.__)("Image size","woocommerce"),children:[(0,m.jsx)(i.__experimentalUnitControl,{label:(0,n.__)("Height","woocommerce"),onChange:e=>{r({height:e})},value:o,units:Ce}),(0,m.jsx)(i.__experimentalUnitControl,{label:(0,n.__)("Width","woocommerce"),onChange:e=>{r({width:e})},value:t,units:Ce}),o&&(0,m.jsx)(i.__experimentalToolsPanelItem,{hasValue:()=>!0,label:(0,n.__)("Scale","woocommerce"),children:(0,m.jsx)(i.__experimentalToggleGroupControl,{label:(0,n.__)("Scale","woocommerce"),value:e,help:je[e],onChange:e=>r({scale:e}),isBlock:!0,children:(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:"cover",label:(0,n.__)("Cover","woocommerce")}),(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:"contain",label:(0,n.__)("Contain","woocommerce")}),(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:"fill",label:(0,n.__)("Fill","woocommerce")})]})})})]}),Ne=ie({icon:fe,title:ke.title,description:ke.description})((({attributes:e,setAttributes:t,context:o})=>{const{showProductLink:r,imageSizing:s,showSaleBadge:c,saleBadgeAlign:a,width:u,height:p,scale:h}=e,g=(0,l.useBlockProps)({style:{width:u,height:p}}),w=Number.isFinite(o.queryId),_=(0,z.getSettingWithCoercion)("isBlockTheme",!1,x.isBoolean);return(0,d.useEffect)((()=>t({isDescendentOfQueryLoop:w})),[t,w]),(0,m.jsxs)("div",{...g,children:[(0,m.jsxs)(l.InspectorControls,{children:[(0,m.jsx)(Se,{scale:h,width:u,height:p,setAttributes:t}),(0,m.jsxs)(i.PanelBody,{title:(0,n.__)("Content","woocommerce"),children:[(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Link to Product Page","woocommerce"),help:(0,n.__)("Links the image to the single product listing.","woocommerce"),checked:r,onChange:()=>t({showProductLink:!r})}),(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Show On-Sale Badge","woocommerce"),help:(0,n.__)("Display a “sale” badge if the product is on-sale.","woocommerce"),checked:c,onChange:()=>t({showSaleBadge:!c})}),c&&(0,m.jsxs)(i.__experimentalToggleGroupControl,{label:(0,n.__)("Sale Badge Alignment","woocommerce"),isBlock:!0,value:a,onChange:e=>t({saleBadgeAlign:e}),children:[(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:"left",label:(0,n.__)("Left","woocommerce")}),(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:"center",label:(0,n.__)("Center","woocommerce")}),(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:"right",label:(0,n.__)("Right","woocommerce")})]}),(0,m.jsxs)(i.__experimentalToggleGroupControl,{label:(0,n.__)("Image Sizing","woocommerce"),isBlock:!0,help:_?null:(0,d.createInterpolateElement)((0,n.__)("Product image cropping can be modified in the <a>Customizer</a>.","woocommerce"),{a:(0,m.jsx)("a",{href:`${(0,z.getAdminLink)("customize.php")}?autofocus[panel]=woocommerce&autofocus[section]=woocommerce_product_images`,target:"_blank",rel:"noopener noreferrer"})}),value:s,onChange:e=>t({imageSizing:e}),children:[(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:ve.e.SINGLE,label:(0,n.__)("Full Size","woocommerce")}),(0,m.jsx)(i.__experimentalToggleGroupControlOption,{value:ve.e.THUMBNAIL,label:(0,n.__)("Cropped","woocommerce")})]})]})]}),(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(be.A,{...e,...o})})]})}));(0,r.registerBlockType)(ke,{save:we,icon:fe,edit:Ne});var Ee=o(9812);o(3081);const Pe=({blockClientId:e})=>{const{isDescendentOfSingleProductBlock:t}=(0,me.useSelect)((t=>{const{getBlockParentsByBlockName:o}=t("core/block-editor");return{isDescendentOfSingleProductBlock:o(e?.replace("block-",""),["woocommerce/single-product"]).length>0}}),[e]);return{isDescendentOfSingleProductBlock:t}};var Ae=o(2108);(0,n.__)("Product Rating","woocommerce");const Te=(0,m.jsx)(s.A,{icon:Ae.A,className:"wc-block-editor-components-block-icon"});(0,n.__)("Display the average rating of a product.","woocommerce"),he({...JSON.parse('{"name":"woocommerce/product-rating","icon":"info","title":"Product Rating","description":"Display the average rating of a product.","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"textAlign":{"type":"string","default":""},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false},"isDescendentOfSingleProductTemplate":{"type":"boolean","default":false}},"supports":{"interactivity":{"clientNavigation":true},"color":{"text":true,"background":false,"link":false,"__experimentalSkipSerialization":true},"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wc-block-components-product-rating"},"ancestor":["woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template"],"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),icon:{src:Te},edit:e=>{const{attributes:t,setAttributes:o,context:r}=e,s=(0,l.useBlockProps)({className:"wp-block-woocommerce-product-rating"}),c={...t,...r,shouldDisplayMockedReviewsWhenProductHasNoReviews:!0},n=Number.isFinite(r.queryId),{isDescendentOfSingleProductBlock:i}=Pe({blockClientId:s?.id});let{isDescendentOfSingleProductTemplate:a}=_e();return(n||i)&&(a=!1),(0,d.useEffect)((()=>{o({isDescendentOfQueryLoop:n,isDescendentOfSingleProductBlock:i,isDescendentOfSingleProductTemplate:a})}),[o,n,i,a]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(l.AlignmentToolbar,{value:t.textAlign,onChange:e=>{o({textAlign:e||""})}})}),(0,m.jsx)("div",{...s,children:(0,m.jsx)(Ee.default,{...c})})]})},save:we},{isAvailableOnPostEditor:!0});var Ie=o(3129);const Be=JSON.parse('{"name":"woocommerce/product-rating-stars","title":"Product Rating Stars","description":"Display the average rating of a product with stars","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"textAlign":{"type":"string","default":""},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false},"isDescendentOfSingleProductTemplate":{"type":"boolean","default":false}},"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"supports":{"interactivity":{"clientNavigation":true},"inserter":false,"color":{"text":true,"background":false,"link":false,"__experimentalSkipSerialization":true},"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wc-block-components-product-rating"},"ancestor":["woocommerce/single-product"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var Le=o(7220);(0,r.registerBlockType)(Be,{apiVersion:3,icon:{src:(0,m.jsx)(s.A,{icon:Ie.A,className:"wc-block-editor-components-block-icon"})},edit:e=>{const{attributes:t,setAttributes:o,context:r}=e,s=(0,l.useBlockProps)({className:"wp-block-woocommerce-product-rating"}),c={...t,...r,shouldDisplayMockedReviewsWhenProductHasNoReviews:!0},n=Number.isFinite(r.queryId),{isDescendentOfSingleProductBlock:i}=Pe({blockClientId:s?.id});let{isDescendentOfSingleProductTemplate:a}=_e();return(n||i)&&(a=!1),(0,d.useEffect)((()=>{o({isDescendentOfQueryLoop:n,isDescendentOfSingleProductBlock:i,isDescendentOfSingleProductTemplate:a})}),[o,n,i,a]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(l.AlignmentToolbar,{value:t.textAlign,onChange:e=>{o({textAlign:e||""})}})}),(0,m.jsx)("div",{...s,children:(0,m.jsx)(Le.default,{...c})})]})}});const Oe=JSON.parse('{"name":"woocommerce/product-rating-counter","title":"Product Rating Counter","description":"Display the review count of a product","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"textAlign":{"type":"string","default":""},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false},"isDescendentOfSingleProductTemplate":{"type":"boolean","default":false}},"supports":{"interactivity":{"clientNavigation":true},"inserter":false,"color":{"text":false,"background":false,"link":true},"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wc-block-components-product-rating-counter"},"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"ancestor":["woocommerce/single-product"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var De=o(9147);(0,r.registerBlockType)(Oe,{apiVersion:3,icon:{src:(0,m.jsx)(s.A,{icon:Ie.A,className:"wc-block-editor-components-block-icon"})},edit:e=>{const{attributes:t,setAttributes:o,context:r}=e,s=(0,l.useBlockProps)({className:"wp-block-woocommerce-product-rating-counter"}),c={...t,...r,shouldDisplayMockedReviewsWhenProductHasNoReviews:!0},n=Number.isFinite(r.queryId),{isDescendentOfSingleProductBlock:i}=Pe({blockClientId:s?.id});let{isDescendentOfSingleProductTemplate:a}=_e();return(n||i)&&(a=!1),(0,d.useEffect)((()=>{o({isDescendentOfQueryLoop:n,isDescendentOfSingleProductBlock:i,isDescendentOfSingleProductTemplate:a})}),[o,n,i,a]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(l.AlignmentToolbar,{value:t.textAlign,onChange:e=>{o({textAlign:e||""})}})}),(0,m.jsx)("div",{...s,children:(0,m.jsx)(De.default,{...c})})]})}});var Re=o(6052);const Fe=JSON.parse('{"name":"woocommerce/product-average-rating","title":"Product Average Rating (Beta)","description":"Display the average rating of a product","apiVersion":3,"category":"woocommerce-product-elements","attributes":{"textAlign":{"type":"string"}},"keywords":["WooCommerce"],"ancestor":["woocommerce/single-product"],"supports":{"interactivity":{"clientNavigation":true},"color":{"text":true,"background":true,"__experimentalSkipSerialization":true},"spacing":{"margin":true,"padding":true,"__experimentalSkipSerialization":true},"typography":{"fontSize":true,"__experimentalFontWeight":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wc-block-components-product-average-rating"},"textdomain":"woocommerce","$schema":"https://schemas.wp.org/trunk/block.json"}');var Ve=o(4514);(0,r.registerBlockType)(Fe,{apiVersion:3,icon:{src:(0,m.jsx)(s.A,{icon:Re.A,className:"wc-block-editor-components-block-icon"})},edit:e=>{const{attributes:t,setAttributes:o}=e,r=(0,l.useBlockProps)({className:"wp-block-woocommerce-product-average-rating"});return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(l.AlignmentToolbar,{value:t.textAlign,onChange:e=>{o({textAlign:e||""})}})}),(0,m.jsx)("div",{...r,children:(0,m.jsx)(Ve.default,{...t})})]})}});var $e=o(6012),Me=o(595);function ze({selectedWidth:e,setAttributes:t}){return(0,m.jsx)(i.PanelBody,{title:(0,n.__)("Width settings","woocommerce"),children:(0,m.jsx)(i.ButtonGroup,{"aria-label":(0,n.__)("Button width","woocommerce"),children:[25,50,75,100].map((o=>(0,m.jsxs)(i.Button,{isSmall:!0,variant:o===e?"primary":void 0,onClick:()=>{var r;t({width:e===(r=o)?void 0:r})},children:[o,"%"]},o)))})})}const He=JSON.parse('{"name":"woocommerce/product-button","title":"Add to Cart Button","description":"Display a call to action button which either adds the product to the cart, or links to the product page.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"usesContext":["query","queryId","postId","woocommerce/isDescendantOfAddToCartWithOptions"],"textdomain":"woocommerce","attributes":{"productId":{"type":"number","default":0},"textAlign":{"type":"string","default":""},"width":{"type":"number"},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false},"isDescendentOfQueryLoop":{"type":"boolean","default":false}},"supports":{"align":["wide","full"],"color":{"text":true,"background":true,"link":false,"__experimentalSkipSerialization":true},"interactivity":true,"html":false,"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontWeight":true,"__experimentalFontFamily":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true}},"__experimentalBorder":{"radius":true,"__experimentalSkipSerialization":true},"spacing":{"margin":true,"padding":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wp-block-button.wc-block-components-product-button .wc-block-components-product-button__button"},"ancestor":["woocommerce/all-products","woocommerce/single-product","core/post-template","woocommerce/product-template"],"styles":[{"name":"fill","label":"Fill","isDefault":true},{"name":"outline","label":"Outline"}],"apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json","viewScriptModule":"woocommerce/product-button","style":"file:../woocommerce/product-button-style.css"}');(0,r.registerBlockType)(He,{apiVersion:3,icon:{src:(0,m.jsx)(s.A,{icon:$e.A,className:"wc-block-editor-components-block-icon"})},attributes:{...He.attributes},edit:({attributes:e,setAttributes:t,context:o})=>{const r=(0,l.useBlockProps)(),s=Number.isFinite(o?.queryId),{width:c}=e;return(0,d.useEffect)((()=>t({isDescendentOfQueryLoop:s})),[t,s]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(l.AlignmentToolbar,{value:e.textAlign,onChange:e=>{t({textAlign:e||""})}})}),(0,m.jsx)(l.InspectorControls,{children:(0,m.jsx)(ze,{selectedWidth:c,setAttributes:t})}),(0,m.jsx)("div",{...r,children:(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(Me.default,{...e,...o,blockClientId:r?.id,className:(0,_.A)(e.className,{[`has-custom-width wp-block-button__width-${c}`]:c})})})})]})},save:({attributes:e,innerBlocks:t})=>e.isDescendentOfQueryLoop||e.isDescendentOfSingleProductBlock||!t||0===t?.length?null:(0,m.jsx)("div",{...l.useBlockProps.save({className:(0,_.A)("is-loading",e.className,{[`has-custom-width wp-block-button__width-${e.width}`]:e.width})})})});var We=o(4001);o(3030);const qe=({showDescriptionIfEmpty:e,setAttributes:t})=>{const o=(0,n.__)("Show description if empty","woocommerce"),r=(0,n.__)("Display the product description if it doesn't have a summary","woocommerce");return(0,m.jsx)(i.__experimentalToolsPanelItem,{label:o,hasValue:()=>!0===e,onDeselect:()=>t({showDescriptionIfEmpty:!1}),isShownByDefault:!0,children:(0,m.jsx)(i.ToggleControl,{label:o,help:r,checked:e,onChange:e=>{t({showDescriptionIfEmpty:e})}})})},Qe=({summaryLength:e,setAttributes:t})=>{const o=(0,n.__)("Max word count","woocommerce"),r=(0,n.__)("When there is a word limit, only the first paragraph will be considered and displayed. Set to 0 to remove the word limit.","woocommerce");return(0,m.jsx)(i.__experimentalToolsPanelItem,{label:o,hasValue:()=>0!==e,onDeselect:()=>t({summaryLength:0}),isShownByDefault:!0,children:(0,m.jsx)(i.RangeControl,{label:o,help:r,value:e,onChange:e=>{t({summaryLength:e||0})},min:0,max:200,step:10})})},Ge=({showLink:e,setAttributes:t})=>{const o=(0,n.__)("Link to description","woocommerce"),r=(0,n.__)("Display a button to let shoppers jump to the product's description","woocommerce");return(0,m.jsx)(i.__experimentalToolsPanelItem,{label:o,hasValue:()=>!1===e,onDeselect:()=>t({showLink:!1}),isShownByDefault:!0,children:(0,m.jsx)(i.ToggleControl,{label:o,help:r,checked:e,onChange:e=>{t({showLink:e})}})})},Ue=({linkText:e,setAttributes:t})=>(0,m.jsx)("p",{children:(0,m.jsx)(l.RichText,{identifier:"linkToDescrption",className:"wc-block-components-product-summary__more-link",tagName:"a","aria-label":(0,n.__)("“Read more” link text","woocommerce"),placeholder:(0,n.__)('Add "read more" link text',"woocommerce"),value:e,onChange:e=>t({linkText:e}),withoutInteractiveFormatting:!0})});var Ye=o(7715);const Je=(0,n.__)("Product Summary","woocommerce"),Ke=(0,m.jsx)(s.A,{icon:Ye.A,className:"wc-block-editor-components-block-icon"}),Ze=(0,n.__)("Display a short description about a product.","woocommerce"),Xe=JSON.parse('{"name":"woocommerce/product-summary","icon":"page","title":"Product Summary","description":"Display a short description about a product.","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"isDescendentOfSingleProductTemplate":{"type":"boolean","default":false},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false},"isDescendantOfAllProducts":{"type":"boolean","default":false},"showDescriptionIfEmpty":{"type":"boolean","default":false},"showLink":{"type":"boolean","default":false},"summaryLength":{"type":"number","default":0},"linkText":{"type":"string","default":""}},"supports":{"interactivity":{"clientNavigation":true},"color":{"text":true,"background":true,"link":true},"spacing":{"margin":true,"padding":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true}},"__experimentalSelector":".wc-block-components-product-summary"},"ancestor":["woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template"],"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var et=o(2619);function tt(e,{blockDescription:t,blockIcon:o,blockTitle:s,variationName:c,scope:n}){(0,r.registerBlockVariation)(e,{description:t,name:c,title:s,isActive:e=>e.__woocommerceNamespace===c,icon:{src:o},attributes:{__woocommerceNamespace:c},scope:n})}const ot="woocommerce/product-query/product-summary";tt("core/post-excerpt",{blockDescription:Ze,blockIcon:(0,m.jsx)(i.Icon,{icon:Ye.A}),blockTitle:Je,variationName:ot,scope:[]});const rt=`${JSON.parse('{"name":"woocommerce/product-collection"}').name}/product-summary`;function st({children:e,className:t,actionLabel:o,onActionClick:r,...s}){return(0,m.jsx)(i.Notice,{...s,className:(0,_.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:o,onClick:r,noDefaultClasses:!0,variant:"link"}],children:(0,m.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:e})})}o(9969);const ct=({clientId:e})=>{const t=(0,d.createInterpolateElement)((0,n.__)("There's <strongText /> with important fixes and brand new features.","woocommerce"),{strongText:(0,m.jsx)("strong",{children:(0,n.__)("new version of Product Summary","woocommerce")})}),o=(0,n.__)("Upgrade now (just this block)","woocommerce");return(0,m.jsx)(st,{isDismissible:!1,actionLabel:o,onActionClick:()=>{const t=(0,me.select)(l.store).getBlocksByClientId(e);if(t.length){const o=t[0],{excerptLength:s,showMoreOnNewLine:c,moreText:n,...i}=o.attributes,a=(0,r.createBlock)("woocommerce/product-summary",i);(0,me.dispatch)(l.store).replaceBlock(e,a)}},children:t})};(0,et.addFilter)("editor.BlockEdit","woocommerce-blocks/product-summary-upgrade-notice",(e=>t=>(e=>{const t=e.attributes.__woocommerceNamespace===ot,o=e.attributes.__woocommerceNamespace===rt;return"core/post-excerpt"===e.name&&(t||o)})(t)?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(l.InspectorControls,{children:(0,m.jsx)(ct,{clientId:t.clientId})}),(0,m.jsx)(e,{...t})]}):(0,m.jsx)(e,{...t}))),he({...Xe,icon:{src:Ke},deprecated:[{save:we,migrate:e=>({...e,showDescriptionIfEmpty:!0,summaryLength:150}),isEligible:e=>(0,x.isEmptyObject)(e)}],edit:({attributes:e,context:t,setAttributes:o})=>{const r=(0,l.useBlockProps)(),{showDescriptionIfEmpty:s,showLink:c,summaryLength:a,linkText:u,isDescendantOfAllProducts:p}=e,h=Number.isFinite(t.queryId),{isDescendentOfSingleProductBlock:g}=Pe({blockClientId:r.id});let{isDescendentOfSingleProductTemplate:w}=_e();return h&&(w=!1),(0,d.useEffect)((()=>o({isDescendentOfQueryLoop:h,isDescendentOfSingleProductTemplate:w,isDescendentOfSingleProductBlock:g})),[o,h,w,g]),(0,m.jsxs)("div",{...r,children:[(0,m.jsx)(We.default,{...e}),(0,m.jsx)(l.InspectorControls,{children:(0,m.jsxs)(i.__experimentalToolsPanel,{label:(0,n.__)("Settings","woocommerce"),resetAll:()=>{o({})},children:[(0,m.jsx)(Qe,{summaryLength:a,setAttributes:o}),(0,m.jsx)(qe,{showDescriptionIfEmpty:s,setAttributes:o}),!p&&(0,m.jsx)(Ge,{showLink:c,setAttributes:o})]})}),!p&&c&&(0,m.jsx)(Ue,{linkText:u,setAttributes:o})]})},save:we},{isAvailableOnPostEditor:!0});var nt=o(9771),it=o(3848);const at=JSON.parse('{"name":"woocommerce/product-sale-badge","title":"On-Sale Badge","description":"Displays an on-sale badge if the product is on-sale.","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"isDescendentOfSingleProductTemplate":{"type":"boolean","default":false}},"supports":{"interactivity":{"clientNavigation":true},"html":false,"align":true,"color":{"gradients":true,"background":true,"link":false,"__experimentalSkipSerialization":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalSkipSerialization":true,"__experimentalLetterSpacing":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true},"__experimentalBorder":{"color":true,"radius":true,"width":true,"__experimentalSkipSerialization":true},"spacing":{"margin":true},"__experimentalSelector":".wc-block-components-product-sale-badge"},"ancestor":["woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template","woocommerce/product-gallery"],"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"example":{},"$schema":"https://schemas.wp.org/trunk/block.json"}');(0,r.registerBlockType)(at,{icon:(0,m.jsx)(s.A,{icon:nt.A,className:"wc-block-editor-components-block-icon"}),edit:({attributes:e,setAttributes:t,context:o})=>{const r=(0,l.useBlockProps)(),s={...e,...o},c=Number.isFinite(o.queryId),{isDescendentOfSingleProductTemplate:n}=_e();return(0,d.useEffect)((()=>t({isDescendentOfQueryLoop:c,isDescendentOfSingleProductTemplate:n})),[t,c,n]),(0,m.jsx)("div",{...r,children:(0,m.jsx)(it.default,{...s})})},save:we});const lt=(0,m.jsx)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)("path",{d:"M2 6h2v12H2V6m3 0h1v12H5V6m2 0h3v12H7V6m4 0h1v12h-1V6m3 0h2v12h-2V6m3 0h3v12h-3V6m4 0h1v12h-1V6z"})}),dt=JSON.parse('{"name":"woocommerce/product-sku","title":"Product SKU","description":"Displays the SKU of a product.","category":"woocommerce-product-elements","attributes":{"productId":{"type":"number","default":0},"isDescendantOfAllProducts":{"type":"boolean","default":false},"showProductSelector":{"type":"boolean","default":false},"prefix":{"type":"string","default":"SKU:"},"suffix":{"type":"string","default":""}},"usesContext":["query","queryId","postId"],"ancestor":["woocommerce/product-meta","woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template"],"supports":{"html":false,"interactivity":{"clientNavigation":true},"color":{"text":true,"background":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontWeight":true,"__experimentalFontFamily":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true},"spacing":{"margin":true,"padding":true}},"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');var ut=o(7035);const mt=e=>{const t=((0,ne.useProductDataContext)().product||{}).id||e.productId||0;return t&&1!==t?(0,m.jsx)(l.InspectorControls,{children:(0,m.jsxs)("div",{className:"wc-block-single-product__edit-card",children:[(0,m.jsx)("div",{className:"wc-block-single-product__edit-card-title",children:(0,m.jsxs)("a",{href:`${z.ADMIN_URL}post.php?post=${t}&action=edit`,target:"_blank",rel:"noopener noreferrer",children:[(0,n.__)("Edit this product's details","woocommerce"),(0,m.jsx)(s.A,{icon:ut.A,size:16})]})}),(0,m.jsx)("div",{className:"wc-block-single-product__edit-card-description",children:(0,n.__)("Edit details such as title, price, description and more.","woocommerce")})]})}):null};o(4093);var pt=o(1648);(0,r.registerBlockType)(dt,{icon:(0,m.jsx)(s.A,{icon:lt,className:"wc-block-editor-components-block-icon"}),edit:({attributes:e,setAttributes:t,context:o})=>{const{style:r,...s}=(0,l.useBlockProps)({className:"wc-block-components-product-sku wp-block-woocommerce-product-sku"}),c={...e,...o},n=Number.isFinite(o.queryId);let{isDescendentOfSingleProductTemplate:i}=_e();return n&&(i=!1),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(mt,{}),(0,m.jsx)("div",{...s,style:e.isDescendantOfAllProducts?void 0:r,children:(0,m.jsx)(pt.default,{...c,setAttributes:t,isDescendentOfSingleProductTemplate:i,isDescendantOfAllProducts:e.isDescendantOfAllProducts})})]})},save:()=>null});var ht=o(6374),gt=o(8471);const wt=(0,m.jsx)(s.A,{icon:gt.A,className:"wc-block-editor-components-block-icon"}),xt=JSON.parse('{"name":"woocommerce/product-stock-indicator","icon":"info","title":"Product Stock Indicator","description":"Let shoppers know when products are out of stock or on backorder. This block is hidden when products are in stock.","category":"woocommerce-product-elements","attributes":{"isDescendentOfQueryLoop":{"type":"boolean","default":false},"isDescendantOfAllProducts":{"type":"boolean","default":false}},"supports":{"html":false,"interactivity":{"clientNavigation":true},"color":{"text":true,"background":true},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontWeight":true,"__experimentalFontFamily":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true},"spacing":{"margin":true,"padding":true}},"ancestor":["woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template"],"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),_t=({attributes:e,setAttributes:t,context:o})=>{const{style:r,...s}=(0,l.useBlockProps)({className:"wc-block-components-product-stock-indicator"}),c={...e,...o},n=Number.isFinite(o.queryId);return(0,d.useEffect)((()=>t({isDescendentOfQueryLoop:n})),[t,n]),(0,m.jsxs)("div",{...s,style:e.isDescendantOfAllProducts?void 0:r,children:[(0,m.jsx)(mt,{}),(0,m.jsx)(ht.default,{...c})]})};he({...xt,icon:{src:wt},edit:e=>{const{product:t}=(0,ne.useProductDataContext)();return 0===t.id?(0,m.jsx)(_t,{...e}):ie({icon:wt,label:xt.title,description:xt.description})(_t)(e)},save:()=>null},{isAvailableOnPostEditor:!0});var bt=o(9784);o(1986);const yt=()=>(0,m.jsxs)("div",{className:"wc-block-editor-product-gallery",children:[(0,m.jsx)("img",{src:`${F.sW}block-placeholders/product-image-gallery.svg`,alt:"Placeholder"}),(0,m.jsx)("div",{className:"wc-block-editor-product-gallery__other-images",children:[...Array(4).keys()].map((e=>(0,m.jsx)("img",{src:`${F.sW}block-placeholders/product-image-gallery.svg`,alt:"Placeholder"},e)))})]}),ft=JSON.parse('{"name":"woocommerce/product-image-gallery","title":"Product Image Gallery","icon":"gallery","description":"Display a product\'s images.","category":"woocommerce-product-elements","supports":{"interactivity":{"clientNavigation":true},"align":true,"multiple":false},"keywords":["WooCommerce"],"usesContext":["postId","postType","queryId"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}');o(9556);const kt="woocommerce/product-gallery";he({...ft,icon:bt.A,edit:()=>{const e=(0,l.useBlockProps)();return(0,m.jsx)("div",{...e,children:(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(yt,{})})})},transforms:{to:[{type:"block",blocks:[kt],transform:()=>(0,r.createBlock)(kt)}]}},{isAvailableOnPostEditor:!1});const vt=(0,m.jsxs)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,m.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",fill:"currentColor",d:"M5 5.5H19C19.1326 5.5 19.2598 5.55268 19.3536 5.64645C19.4473 5.74021 19.5 5.86739 19.5 6V7.5C19.5 7.63261 19.4473 7.75979 19.3536 7.85355C19.2598 7.94732 19.1326 8 19 8H5C4.86739 8 4.74021 7.94732 4.64645 7.85355C4.55268 7.75979 4.5 7.63261 4.5 7.5V6C4.5 5.86739 4.55268 5.74021 4.64645 5.64645C4.74021 5.55268 4.86739 5.5 5 5.5V5.5ZM4 9.232C3.69597 9.05647 3.4435 8.804 3.26796 8.49997C3.09243 8.19594 3.00001 7.85106 3 7.5V6C3 5.46957 3.21071 4.96086 3.58579 4.58579C3.96086 4.21071 4.46957 4 5 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V7.5C21 7.85106 20.9076 8.19594 20.732 8.49997C20.5565 8.804 20.304 9.05647 20 9.232V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V9.232ZM5.5 9.5V18C5.5 18.1326 5.55268 18.2598 5.64645 18.3536C5.74021 18.4473 5.86739 18.5 6 18.5H18C18.1326 18.5 18.2598 18.4473 18.3536 18.3536C18.4473 18.2598 18.5 18.1326 18.5 18V9.5H5.5Z"}),(0,m.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 13.25V11.75H16V13.25L8 13.25Z"}),(0,m.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 16.25V14.75H16V16.25H8Z"})]}),jt=JSON.parse('{"name":"woocommerce/product-details","icon":"info","title":"Product Details","description":"Display a product\'s description, attributes, and reviews.","category":"woocommerce-product-elements","attributes":{"hideTabTitle":{"type":"boolean","default":false}},"keywords":["WooCommerce"],"supports":{"interactivity":{"clientNavigation":true},"align":true,"spacing":{"margin":true}},"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),Ct=({id:e,title:t,active:o})=>(0,m.jsx)("li",{className:(0,_.A)(`${e}_tab`,{active:o}),id:`tab-title-${e}`,role:"tab","aria-controls":`tab-${e}`,children:(0,m.jsx)("a",{href:`#tab-${e}`,children:t})}),St=({id:e,content:t})=>(0,m.jsx)("div",{className:`${e}_tab`,id:`tab-title-${e}`,role:"tab","aria-controls":`tab-${e}`,children:t}),Nt=({hideTabTitle:e})=>{const t=[{id:"description",title:"Description",active:!0,content:(0,m.jsxs)(m.Fragment,{children:[!e&&(0,m.jsx)("h2",{children:(0,n.__)("Description","woocommerce")}),(0,m.jsx)("p",{children:(0,n.__)("This block lists description, attributes and reviews for a single product.","woocommerce")})]})},{id:"additional_information",title:"Additional Information",active:!1},{id:"reviews",title:"Reviews",active:!1}],o=t.map((({id:e,title:t,active:o})=>(0,m.jsx)(Ct,{id:e,title:t,active:o},e))),r=t.map((({id:e,content:t})=>(0,m.jsx)(St,{id:e,content:t},e)));return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("ul",{className:"wc-tabs tabs",role:"tablist",children:o}),r]})};o(5938);o(660),he({...jt,icon:{src:(0,m.jsx)(s.A,{icon:vt,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{className:o,hideTabTitle:r}=e,s=(0,l.useBlockProps)({className:o});return(0,m.jsx)(m.Fragment,{children:(0,m.jsxs)("div",{...s,children:[(0,m.jsx)(l.InspectorControls,{children:(0,m.jsx)(i.PanelBody,{title:(0,n.__)("Settings","woocommerce"),children:(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Show tab title in content","woocommerce"),checked:!r,onChange:()=>t({hideTabTitle:!r})})})},"inspector"),(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(Nt,{hideTabTitle:r})})]})})}},{isAvailableOnPostEditor:!1});const Et=JSON.parse('{"name":"woocommerce/product-reviews","icon":"admin-comments","title":"Product Reviews","description":"A block that shows the reviews for a product.","category":"woocommerce-product-elements","keywords":["WooCommerce"],"supports":{"interactivity":{"clientNavigation":true}},"attributes":{},"usesContext":["postId"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),Pt=()=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(i.Notice,{className:"wc-block-editor-related-products__notice",status:"info",isDismissible:!1,children:(0,m.jsx)("p",{children:(0,n.__)("The products reviews and the form to add a new review will be displayed here according to your theme. The look you see here is not representative of what is going to look like, this is just a placeholder.","woocommerce")})}),(0,m.jsx)("h2",{children:(0,n.__)("3 reviews for this product","woocommerce")}),(0,m.jsx)("img",{src:`${F.sW}block-placeholders/product-reviews.svg`,alt:"Placeholder"}),(0,m.jsx)("h3",{children:(0,n.__)("Add a review","woocommerce")}),(0,m.jsxs)("div",{className:"wp-block-woocommerce-product-reviews__editor__form-container",children:[(0,m.jsxs)("div",{className:"wp-block-woocommerce-product-reviews__editor__row",children:[(0,m.jsx)("span",{children:(0,n.__)("Your rating *","woocommerce")}),(0,m.jsx)("p",{className:"wp-block-woocommerce-product-reviews__editor__stars"})]}),(0,m.jsxs)("div",{className:"wp-block-woocommerce-product-reviews__editor__row",children:[(0,m.jsx)("span",{children:(0,n.__)("Your review *","woocommerce")}),(0,m.jsx)("textarea",{})]}),(0,m.jsx)("input",{type:"submit",className:"submit wp-block-button__link wp-element-button",value:(0,n.__)("Submit","woocommerce")})]})]});o(3744),he({...Et,edit:({attributes:e})=>{const{className:t}=e,o=(0,l.useBlockProps)({className:t});return(0,m.jsx)(m.Fragment,{children:(0,m.jsx)("div",{...o,children:(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(Pt,{})})})})}},{isAvailableOnPostEditor:!1});const At=(0,m.jsx)(u.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",children:(0,m.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.5 19.375L4.5 7.625C4.5 7.55596 4.55596 7.5 4.625 7.5L16.375 7.5C16.444 7.5 16.5 7.55596 16.5 7.625L16.5 19.375C16.5 19.444 16.444 19.5 16.375 19.5L4.625 19.5C4.55596 19.5 4.5 19.444 4.5 19.375ZM4.625 21C3.72754 21 3 20.2725 3 19.375L3 7.625C3 6.72754 3.72754 6 4.625 6L16.375 6C17.2725 6 18 6.72754 18 7.625L18 19.375C18 20.2725 17.2725 21 16.375 21L4.625 21ZM19 3.75L8 3.75L8 2.25L19 2.25C20.5183 2.25 21.75 3.4796 21.75 4.99891L21.75 18L20.25 18L20.25 4.99891C20.25 4.30909 19.6909 3.75 19 3.75Z"})}),Tt="woocommerce/product-query/product-title";tt("core/post-title",{blockDescription:de.description,blockIcon:(0,m.jsx)(i.Icon,{icon:ae.A}),blockTitle:de.title,variationName:Tt,scope:["block"]});var It=o(5534);const Bt="woocommerce/product-query/product-template";tt("core/post-template",{blockDescription:(0,n.__)("Contains the block elements used to render a product, like its name, featured image, rating, and more.","woocommerce"),blockIcon:(0,m.jsx)(i.Icon,{icon:It.A}),blockTitle:(0,n.__)("Product template","woocommerce"),variationName:Bt,scope:["block","inserter"]});const Lt=(0,z.getSetting)("stockStatusOptions",[]),Ot=((0,z.getSetting)("hideOutOfStockItems",!1)?Object.keys(function(e,t){const{[t]:o,...r}=e;return r}(Lt,"outofstock")):Object.keys(Lt),(0,z.getSettingWithCoercion)("postTemplateHasSupportForGridView",!1,x.isBoolean),ve.e.THUMBNAIL,"woocommerce/related-products"),Dt={namespace:Ot,allowedControls:[],displayLayout:{type:"flex",columns:5},query:{perPage:5,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",author:"",search:"",exclude:[],sticky:"",inherit:!1},lock:{remove:!0,move:!0}},Rt=(0,z.getSettingWithCoercion)("postTemplateHasSupportForGridView",!1,x.isBoolean),Ft=[["core/heading",{level:2,content:(0,n.__)("Related products","woocommerce"),style:{spacing:{margin:{top:"1rem",bottom:"1rem"}}}}],["core/post-template",{__woocommerceNamespace:Bt,...Rt&&{layout:{type:"grid",columnCount:5}}},[["woocommerce/product-image",{productId:0,imageSizing:"cropped"}],["core/post-title",{textAlign:"center",level:3,fontSize:"medium",isLink:!0,__woocommerceNamespace:Tt},[]],["woocommerce/product-price",{textAlign:"center",fontSize:"small",style:{spacing:{margin:{bottom:"1rem"}}}},[]],["woocommerce/product-button",{textAlign:"center",fontSize:"small",style:{spacing:{margin:{bottom:"1rem"}}}},[]]]]];he({name:"core/query",description:(0,n.__)("Display related products.","woocommerce"),title:(0,n.__)("Related Products Controls","woocommerce"),isActive:e=>e.namespace===Ot,icon:(0,m.jsx)(i.Icon,{icon:At,className:"wc-block-editor-components-block-icon wc-block-editor-components-block-icon--stacks"}),attributes:Dt,allowedControls:[],innerBlocks:Ft,scope:["block"]},{isVariationBlock:!0,variationName:Ot,isAvailableOnPostEditor:!1}),o(752);he({...JSON.parse('{"name":"woocommerce/related-products","title":"Related Products","icon":"product","description":"Display related products.","category":"woocommerce","supports":{"interactivity":{"clientNavigation":true},"align":true,"reusable":false,"inserter":false},"keywords":["WooCommerce"],"usesContext":["postId","postType","queryId"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),icon:{src:gt.A},edit:()=>{const e=[["core/query",Dt,Ft]],t=(0,l.useBlockProps)();return(0,m.jsxs)("div",{...t,children:[(0,m.jsx)(l.InspectorControls,{children:(0,m.jsx)(i.Notice,{className:"wc-block-editor-related-products__notice",status:"warning",isDismissible:!1,children:(0,m.jsx)("p",{children:(0,n.__)("These products will vary depending on the main product in the page","woocommerce")})})}),(0,m.jsx)(l.InnerBlocks,{template:e})]})},save:()=>{const e=l.useBlockProps.save();return(0,m.jsx)("div",{...e,children:(0,m.jsx)(l.InnerBlocks.Content,{})})},isAvailableOnPostEditor:!1},{isAvailableOnPostEditor:!1});const Vt=(0,m.jsxs)(u.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,m.jsx)("path",{id:"icon/action/view_list_24px",fillRule:"evenodd",clipRule:"evenodd",fill:"currentColor",d:"M5 5.5H19C19.1326 5.5 19.2598 5.55268 19.3536 5.64645C19.4473 5.74021 19.5 5.86739 19.5 6V7.5C19.5 7.63261 19.4473 7.75979 19.3536 7.85355C19.2598 7.94732 19.1326 8 19 8H5C4.86739 8 4.74021 7.94732 4.64645 7.85355C4.55268 7.75979 4.5 7.63261 4.5 7.5V6C4.5 5.86739 4.55268 5.74021 4.64645 5.64645C4.74021 5.55268 4.86739 5.5 5 5.5V5.5ZM4 9.232C3.69597 9.05647 3.4435 8.804 3.26796 8.49997C3.09243 8.19594 3.00001 7.85106 3 7.5V6C3 5.46957 3.21071 4.96086 3.58579 4.58579C3.96086 4.21071 4.46957 4 5 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V7.5C21 7.85106 20.9076 8.19594 20.732 8.49997C20.5565 8.804 20.304 9.05647 20 9.232V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V9.232ZM5.5 9.5V18C5.5 18.1326 5.55268 18.2598 5.64645 18.3536C5.74021 18.4473 5.86739 18.5 6 18.5H18C18.1326 18.5 18.2598 18.4473 18.3536 18.3536C18.4473 18.2598 18.5 18.1326 18.5 18V9.5H5.5Z"}),(0,m.jsx)("circle",{cx:"9",cy:"14",r:"1"}),(0,m.jsx)("circle",{cx:"12",cy:"14",r:"1"}),(0,m.jsx)("circle",{cx:"15",cy:"14",r:"1"})]});o(9835);he({...JSON.parse('{"name":"woocommerce/product-meta","title":"Product Meta","icon":"product","description":"Display a product’s SKU, categories, tags, and more.","category":"woocommerce-product-elements","supports":{"interactivity":{"clientNavigation":true},"align":true,"reusable":false},"keywords":["WooCommerce"],"usesContext":["postId","postType","queryId"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}'),icon:{src:(0,m.jsx)(s.A,{icon:Vt,className:"wc-block-editor-components-block-icon"})},edit:()=>{const e=[["core/group",{layout:{type:"flex",flexWrap:"nowrap"}},[["woocommerce/product-sku"],["core/post-terms",{prefix:(0,n.__)("Category: ","woocommerce"),term:"product_cat"}],["core/post-terms",{prefix:(0,n.__)("Tags: ","woocommerce"),term:"product_tag"}]]]],t=(0,l.useBlockProps)();return(0,m.jsx)("div",{...t,children:(0,m.jsx)(l.InnerBlocks,{template:e})})},save:()=>{const e=l.useBlockProps.save();return(0,m.jsx)("div",{...e,children:(0,m.jsx)(l.InnerBlocks.Content,{})})},ancestor:["woocommerce/single-product"]},{isAvailableOnPostEditor:!0});const $t=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"textdomain":"woocommerce","name":"woocommerce/all-products","title":"All Products","category":"woocommerce","keywords":["WooCommerce"],"description":"Display products from your store in a grid layout.","supports":{"interactivity":{"clientNavigation":false},"align":["wide","full"],"html":false,"multiple":false,"inserter":false},"attributes":{"columns":{"type":"number"},"rows":{"type":"number"},"alignButtons":{"type":"boolean"},"contentVisibility":{"type":"object"},"orderby":{"type":"string"},"layoutConfig":{"type":"array"},"isPreview":{"type":"boolean","default":false}}}'),Mt=(e,t)=>{const{className:o,contentVisibility:r}=t;return(0,_.A)(e,o,{"has-image":r&&r.image,"has-title":r&&r.title,"has-rating":r&&r.rating,"has-price":r&&r.price,"has-button":r&&r.button})},{attributes:zt}=$t,Ht=[{attributes:Object.assign({},zt,{rows:{type:"number",default:1}}),save({attributes:e}){const t={"data-attributes":JSON.stringify(e)};return(0,m.jsx)("div",{className:Mt("wc-block-all-products",e),...t,children:(0,m.jsx)(l.InnerBlocks.Content,{})})}}],Wt=window.wc.wcBlocksRegistry;o.p=F.XK,(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-price",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(2388)]).then(o.bind(o,1308))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-image",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(4232)]).then(o.bind(o,933))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-title",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(2105)]).then(o.bind(o,5168))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-rating",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(462)]).then(o.bind(o,9812))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-rating-stars",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(8578)]).then(o.bind(o,7220))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-rating-counter",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(8553)]).then(o.bind(o,9147))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-average-rating",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(8647)]).then(o.bind(o,4514))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-button",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(7409)]).then(o.bind(o,595))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-summary",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(3895)]).then(o.bind(o,4001))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-sale-badge",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(4442)]).then(o.bind(o,3848))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-sku",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(2724)]).then(o.bind(o,1648))))}),(0,Wt.registerBlockComponent)({blockName:"woocommerce/product-stock-indicator",component:(0,d.lazy)((()=>Promise.all([o.e(94),o.e(345)]).then(o.bind(o,6374))))});const qt=e=>(0,Wt.getRegisteredBlockComponents)(e),Qt=(e,t,o)=>o?Math.min(e,t)===e?t:Math.max(e,o)===e?o:e:Math.max(e,t)===t?e:t,Gt=({columns:e,rows:t,setAttributes:o,alignButtons:r,minColumns:s=1,maxColumns:c=6,minRows:a=1,maxRows:l=6})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(i.RangeControl,{label:(0,n.__)("Columns","woocommerce"),value:e,onChange:e=>{const t=Qt(e,s,c);o({columns:Number.isNaN(t)?"":t})},min:s,max:c}),(0,m.jsx)(i.RangeControl,{label:(0,n.__)("Rows","woocommerce"),value:t,onChange:e=>{const t=Qt(e,a,l);o({rows:Number.isNaN(t)?"":t})},min:a,max:l}),(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Align the last block to the bottom","woocommerce"),help:r?(0,n.__)("Align the last block to the bottom.","woocommerce"):(0,n.__)("The last inner block will follow other content.","woocommerce"),checked:r,onChange:()=>o({alignButtons:!r})})]}),Ut=[{id:1,name:"WordPress Pennant",variation:"",permalink:"https://example.org",sku:"wp-pennant",short_description:(0,n.__)("Fly your WordPress banner with this beauty! Deck out your office space or add it to your kids walls. This banner will spruce up any space it’s hung!","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",price:"7.99",price_html:'<span class="woocommerce-Price-amount amount"><span class="woocommerce-Price-currencySymbol">$</span>7.99</span>',images:[{id:1,src:F.sW+"previews/pennant.jpg",thumbnail:F.sW+"previews/pennant.jpg",name:"pennant-1.jpg",alt:"WordPress Pennant",srcset:"",sizes:""}],average_rating:5,categories:[{id:1,name:"Decor",slug:"decor",link:"https://example.org"}],review_count:1,prices:{currency_code:"GBP",decimal_separator:".",thousand_separator:",",decimals:2,price_prefix:"£",price_suffix:"",price:"7.99",regular_price:"9.99",sale_price:"7.99",price_range:null},add_to_cart:{text:(0,n.__)("Add to cart","woocommerce"),description:(0,n.__)("Add to cart","woocommerce")},has_options:!1,is_purchasable:!0,is_in_stock:!0,on_sale:!0}],Yt=[["woocommerce/product-image",{imageSizing:ve.e.THUMBNAIL}],["woocommerce/product-title"],["woocommerce/product-price"],["woocommerce/product-rating"],["woocommerce/product-button"]],Jt=e=>e&&0!==e.length?e.map((e=>[e.name,{...e.attributes,product:void 0,children:e.innerBlocks.length>0?Jt(e.innerBlocks):[],...e.name===He.name&&{className:(0,_.A)(e.attributes.className,{[`has-custom-width wp-block-button__width-${e.attributes?.width}`]:e.attributes?.width})},isDescendantOfAllProducts:!0}])):[],Kt=(e,t)=>{const{contentVisibility:o}=e;return(0,m.jsx)(i.ToggleControl,{label:(0,n.__)("Show Sorting Dropdown","woocommerce"),checked:o.orderBy,onChange:()=>t({contentVisibility:{...o,orderBy:!o.orderBy}})})},Zt=(e,t)=>(0,m.jsx)(i.SelectControl,{label:(0,n.__)("Order Products By","woocommerce"),value:e.orderby,options:[{label:(0,n.__)("Default sorting (menu order)","woocommerce"),value:"menu_order"},{label:(0,n.__)("Popularity","woocommerce"),value:"popularity"},{label:(0,n.__)("Average rating","woocommerce"),value:"rating"},{label:(0,n.__)("Latest","woocommerce"),value:"date"},{label:(0,n.__)("Price: low to high","woocommerce"),value:"price"},{label:(0,n.__)("Price: high to low","woocommerce"),value:"price-desc"}],onChange:e=>t({orderby:e})});var Xt=o(1824),eo=o.n(Xt),to=o(4656);o(8714);const oo=({currentPage:e,displayFirstAndLastPages:t=!0,displayNextAndPreviousArrows:o=!0,pagesToDisplay:r=3,onPageChange:s,totalPages:c})=>{let{minIndex:i,maxIndex:a}=((e,t,o)=>{if(o<=2)return{minIndex:null,maxIndex:null};const r=e-1,s=Math.max(Math.floor(t-r/2),2),c=Math.min(Math.ceil(t+(r-(t-s))),o-1);return{minIndex:Math.max(Math.floor(t-(r-(c-t))),2),maxIndex:c}})(r,e,c);const l=t&&Boolean(1!==i),d=t&&Boolean(a!==c),u=t&&Boolean(i&&i>3),p=t&&Boolean(a&&a<c-2);l&&3===i&&(i-=1),d&&a===c-2&&(a+=1);const h=[];if(i&&a)for(let e=i;e<=a;e++)h.push(e);return(0,m.jsxs)("div",{className:"wc-block-pagination wc-block-components-pagination",children:[(0,m.jsx)(to.Label,{screenReaderLabel:(0,n.__)("Navigate to another page","woocommerce")}),o&&(0,m.jsx)("button",{className:"wc-block-pagination-page wc-block-components-pagination__page wc-block-components-pagination-page--arrow",onClick:()=>s(e-1),title:(0,n.__)("Previous page","woocommerce"),disabled:e<=1,children:(0,m.jsx)(to.Label,{label:"←",screenReaderLabel:(0,n.__)("Previous page","woocommerce")})}),l&&(0,m.jsx)("button",{className:(0,_.A)("wc-block-pagination-page","wc-block-components-pagination__page",{"wc-block-pagination-page--active":1===e,"wc-block-components-pagination__page--active":1===e}),onClick:()=>s(1),disabled:1===e,children:(0,m.jsx)(to.Label,{label:"1",screenReaderLabel:(0,n.sprintf)(/* translators: %d is the page number (1, 2, 3...). */ /* translators: %d is the page number (1, 2, 3...). */
(0,n.__)("Page %d","woocommerce"),1)})}),u&&(0,m.jsx)("span",{className:"wc-block-pagination-ellipsis wc-block-components-pagination__ellipsis","aria-hidden":"true",children:(0,n.__)("…","woocommerce")}),h.map((t=>(0,m.jsx)("button",{className:(0,_.A)("wc-block-pagination-page","wc-block-components-pagination__page",{"wc-block-pagination-page--active":e===t,"wc-block-components-pagination__page--active":e===t}),onClick:e===t?void 0:()=>s(t),disabled:e===t,children:(0,m.jsx)(to.Label,{label:t.toString(),screenReaderLabel:(0,n.sprintf)(/* translators: %d is the page number (1, 2, 3...). */ /* translators: %d is the page number (1, 2, 3...). */
(0,n.__)("Page %d","woocommerce"),t)})},t))),p&&(0,m.jsx)("span",{className:"wc-block-pagination-ellipsis wc-block-components-pagination__ellipsis","aria-hidden":"true",children:(0,n.__)("…","woocommerce")}),d&&(0,m.jsx)("button",{className:(0,_.A)("wc-block-pagination-page","wc-block-components-pagination__page",{"wc-block-pagination-page--active":e===c,"wc-block-components-pagination__page--active":e===c}),onClick:()=>s(c),disabled:e===c,children:(0,m.jsx)(to.Label,{label:c.toString(),screenReaderLabel:(0,n.sprintf)(/* translators: %d is the page number (1, 2, 3...). */ /* translators: %d is the page number (1, 2, 3...). */
(0,n.__)("Page %d","woocommerce"),c)})}),o&&(0,m.jsx)("button",{className:"wc-block-pagination-page wc-block-components-pagination__page wc-block-components-pagination-page--arrow",onClick:()=>s(e+1),title:(0,n.__)("Next page","woocommerce"),disabled:e>=c,children:(0,m.jsx)(to.Label,{label:"→",screenReaderLabel:(0,n.__)("Next page","woocommerce")})})]})};function ro(e,t){const o=(0,d.useRef)();return(0,d.useEffect)((()=>{o.current===e||t&&!t(e,o.current)||(o.current=e)}),[e,t]),o.current}var so=o(7594);function co(e){const t=(0,d.useRef)(e);return G()(e,t.current)||(t.current=e),t.current}const no=(0,d.createContext)("page"),io=()=>(0,d.useContext)(no),ao=(no.Provider,(e,t,o)=>{const r=io();o=o||r;const s=(0,me.useSelect)((r=>r(so.QUERY_STATE_STORE_KEY).getValueForQueryKey(o,e,t)),[o,e]),{setQueryValue:c}=(0,me.useDispatch)(so.QUERY_STATE_STORE_KEY);return[s,(0,d.useCallback)((t=>{c(o,e,t)}),[o,e,c])]}),lo=e=>{const t={namespace:"/wc/store/v1",resourceName:"products"},{results:o,isLoading:r}=(e=>{const{namespace:t,resourceName:o,resourceValues:r=[],query:s={},shouldSelect:c=!0}=e;if(!t||!o)throw new Error("The options object must have valid values for the namespace and the resource properties.");const n=(0,d.useRef)({results:[],isLoading:!0}),i=co(s),a=co(r),l=(()=>{const[,e]=(0,d.useState)();return(0,d.useCallback)((t=>{e((()=>{throw t}))}),[])})(),u=(0,me.useSelect)((e=>{if(!c)return null;const r=e(so.COLLECTIONS_STORE_KEY),s=[t,o,i,a],n=r.getCollectionError(...s);if(n){if(!(0,x.isError)(n))throw new Error("TypeError: `error` object is not an instance of Error constructor");l(n)}return{results:r.getCollection(...s),isLoading:!r.hasFinishedResolution("getCollection",s)}}),[t,o,a,i,c,l]);return null!==u&&(n.current=u),n.current})({...t,query:e}),{value:s}=((e,t)=>{const{namespace:o,resourceName:r,resourceValues:s=[],query:c={}}=t;if(!o||!r)throw new Error("The options object must have valid values for the namespace and the resource name properties.");const n=co(c),i=co(s),{value:a,isLoading:l=!0}=(0,me.useSelect)((t=>{const s=t(so.COLLECTIONS_STORE_KEY),c=[e,o,r,n,i];return{value:s.getCollectionHeader(...c),isLoading:s.hasFinishedResolution("getCollectionHeader",c)}}),[e,o,r,i,n]);return{value:a,isLoading:l}})("x-wp-total",{...t,query:e});return{products:o,totalProducts:parseInt(s,10),productsLoading:r}};var uo=o(7052);o(7525);const mo=e=>{if(!e)return;const t=e.getBoundingClientRect().bottom;t>=0&&t<=window.innerHeight||e.scrollIntoView()};var po=o(195),ho=o(2098);const go=()=>{const{parentClassName:e}=(0,ne.useInnerBlockLayoutContext)();return(0,m.jsxs)("div",{className:`${e}__no-products`,children:[(0,m.jsx)(s.A,{className:`${e}__no-products-image`,icon:ho.A,size:100}),(0,m.jsx)("strong",{className:`${e}__no-products-title`,children:(0,n.__)("No products","woocommerce")}),(0,m.jsx)("p",{className:`${e}__no-products-description`,children:(0,n.__)("There are currently no products available to display.","woocommerce")})]})};var wo=o(428);const xo=({resetCallback:e=()=>{}})=>{const{parentClassName:t}=(0,ne.useInnerBlockLayoutContext)();return(0,m.jsxs)("div",{className:`${t}__no-products`,children:[(0,m.jsx)(s.A,{className:`${t}__no-products-image`,icon:wo.A,size:100}),(0,m.jsx)("strong",{className:`${t}__no-products-title`,children:(0,n.__)("No products found","woocommerce")}),(0,m.jsx)("p",{className:`${t}__no-products-description`,children:(0,n.__)("We were unable to find any results based on your search.","woocommerce")}),(0,m.jsx)("button",{onClick:e,children:(0,n.__)("Reset Search","woocommerce")})]})};o(6854);const _o=({onChange:e,value:t})=>(0,m.jsx)(to.SortSelect,{className:"wc-block-product-sort-select wc-block-components-product-sort-select",onChange:e,options:[{key:"menu_order",label:(0,n.__)("Default sorting","woocommerce")},{key:"popularity",label:(0,n.__)("Popularity","woocommerce")},{key:"rating",label:(0,n.__)("Average rating","woocommerce")},{key:"date",label:(0,n.__)("Latest","woocommerce")},{key:"price",label:(0,n.__)("Price: low to high","woocommerce")},{key:"price-desc",label:(0,n.__)("Price: high to low","woocommerce")}],screenReaderLabel:(0,n.__)("Order products by","woocommerce"),value:t}),bo=(e,t,o,r)=>{if(!o)return;const s=qt(e);return o.map((([o,c={}],n)=>{let i=[];c.children&&c.children.length>0&&(i=bo(e,t,c.children,r));const a=s[o];if(!a)return null;const l=t.id||0,u=["layout",o,n,r,l];return(0,m.jsx)(d.Suspense,{fallback:(0,m.jsx)("div",{className:"wc-block-placeholder"}),children:(0,m.jsx)(a,{...c,children:i,product:t})},u.join("_"))}))},yo=(0,a.withInstanceId)((({product:e={},attributes:t,instanceId:o})=>{const{layoutConfig:r}=t,{parentClassName:s,parentName:c}=(0,ne.useInnerBlockLayoutContext)(),n=0===Object.keys(e).length,i=(0,_.A)(`${s}__product`,"wc-block-layout",{"is-loading":n});return(0,m.jsx)("li",{className:i,"aria-hidden":n,children:bo(c,e,r,o)})}));o(3320);const fo=e=>{switch(e){case"menu_order":case"popularity":case"rating":case"price":return{orderby:e,order:"asc"};case"price-desc":return{orderby:"price",order:"desc"};case"date":return{orderby:"date",order:"desc"}}},ko=({totalQuery:e,totalProducts:t},{totalQuery:o}={})=>!eo()(e,o)&&Number.isFinite(t),vo=(e=>t=>{const o=(0,d.useRef)(null);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("div",{className:"with-scroll-to-top__scroll-point",ref:o,"aria-hidden":!0}),(0,m.jsx)(e,{...t,scrollToTop:e=>{null!==o.current&&((e,t)=>{const{focusableSelector:o}=t||{};window&&Number.isFinite(window.innerHeight)&&(o?((e,t)=>{const o=e.parentElement?.querySelectorAll(t)||[];if(o.length){const e=o[0];mo(e),e?.focus()}else mo(e)})(e,o):mo(e))})(o.current,e)}})]})})((({attributes:e,currentPage:t,onPageChange:o,onSortChange:r,sortValue:s,scrollToTop:c})=>{const[i,a]=ao("attributes",[]),[l,u]=ao("stock_status",[]),[p,h]=ao("rating",[]),[g,w]=ao("min_price"),[x,b]=ao("max_price"),[y]=((e,t)=>{const o=io();t=t||o;const[r,s]=(e=>{const t=io();e=e||t;const o=(0,me.useSelect)((t=>t(so.QUERY_STATE_STORE_KEY).getValueForQueryContext(e,void 0)),[e]),{setValueForQueryContext:r}=(0,me.useDispatch)(so.QUERY_STATE_STORE_KEY);return[o,(0,d.useCallback)((t=>{r(e,t)}),[e,r])]})(t),c=co(r),n=co(e),i=ro(n),a=(0,d.useRef)(!1);return(0,d.useEffect)((()=>{G()(i,n)||(s(Object.assign({},c,n)),a.current=!0)}),[c,n,i,s]),a.current?[r,s]:[e,s]})((({sortValue:e,currentPage:t,attributes:o})=>{const{columns:r,rows:s}=o;return{...fo(e),catalog_visibility:"catalog",per_page:r*s,page:t}})({attributes:e,sortValue:s,currentPage:t})),{products:f,totalProducts:k,productsLoading:v}=lo(y),{parentClassName:j,parentName:C}=(0,ne.useInnerBlockLayoutContext)(),S=(e=>{const{order:t,orderby:o,page:r,per_page:s,...c}=e;return c||{}})(y),{dispatchStoreEvent:N}=(0,uo.y)(),E=ro({totalQuery:S,totalProducts:k},ko);(0,d.useEffect)((()=>{N("product-list-render",{products:f,listName:C})}),[f,C,N]),(0,d.useEffect)((()=>{eo()(S,E?.totalQuery)||(o(1),E?.totalQuery&&(e=>{Number.isFinite(e)&&(0===e?(0,po.speak)((0,n.__)("No products found","woocommerce")):(0,po.speak)((0,n.sprintf)(/* translators: %s is an integer higher than 0 (1, 2, 3...) */ /* translators: %s is an integer higher than 0 (1, 2, 3...) */
(0,n._n)("%d product found","%d products found",e,"woocommerce"),e)))})(k))}),[E?.totalQuery,k,o,S]);const{contentVisibility:P}=e,A=e.columns*e.rows,T=!Number.isFinite(k)&&Number.isFinite(E?.totalProducts)&&eo()(S,E?.totalQuery)?Math.ceil((E?.totalProducts||0)/A):Math.ceil(k/A),I=f.length?f:Array.from({length:A}),B=0!==f.length||v,L=i.length>0||l.length>0||p.length>0||Number.isFinite(g)||Number.isFinite(x);return(0,m.jsxs)("div",{className:(()=>{const{columns:t,rows:o,alignButtons:r,align:s}=e,c=void 0!==s?"align"+s:"";return(0,_.A)(j,c,"has-"+t+"-columns",{"has-multiple-rows":o>1,"has-aligned-buttons":r})})(),children:[P?.orderBy&&B&&(0,m.jsx)(_o,{onChange:r,value:s}),!B&&L&&(0,m.jsx)(xo,{resetCallback:()=>{a([]),u([]),h([]),w(null),b(null)}}),!B&&!L&&(0,m.jsx)(go,{}),B&&(0,m.jsx)("ul",{className:(0,_.A)(`${j}__products`,{"is-loading-products":v}),children:I.map(((t={},o)=>(0,m.jsx)(yo,{attributes:e,product:t},t.id||o)))}),T>1&&(0,m.jsx)(oo,{currentPage:t,onPageChange:e=>{c({focusableSelector:"a, button"}),o(e)},totalPages:T})]})})),jo=({attributes:e})=>{const[t,o]=(0,d.useState)(1),[r,s]=(0,d.useState)(e.orderby);return(0,d.useEffect)((()=>{s(e.orderby)}),[e.orderby]),(0,m.jsx)(vo,{attributes:e,currentPage:t,onPageChange:e=>{o(e)},onSortChange:e=>{const t=e?.target?.value;s(t),o(1)},sortValue:r})},Co=(0,m.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 230 250",style:{width:"100%"},children:[(0,m.jsx)("title",{children:"Grid Block Preview"}),(0,m.jsx)("rect",{width:"65.374",height:"65.374",x:".162",y:".779",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"47.266",height:"5.148",x:"9.216",y:"76.153",fill:"#E1E3E6",rx:"2.574"}),(0,m.jsx)("rect",{width:"62.8",height:"15",x:"1.565",y:"101.448",fill:"#E1E3E6",rx:"5"}),(0,m.jsx)("rect",{width:"65.374",height:"65.374",x:".162",y:"136.277",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"47.266",height:"5.148",x:"9.216",y:"211.651",fill:"#E1E3E6",rx:"2.574"}),(0,m.jsx)("rect",{width:"62.8",height:"15",x:"1.565",y:"236.946",fill:"#E1E3E6",rx:"5"}),(0,m.jsx)("rect",{width:"65.374",height:"65.374",x:"82.478",y:".779",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"47.266",height:"5.148",x:"91.532",y:"76.153",fill:"#E1E3E6",rx:"2.574"}),(0,m.jsx)("rect",{width:"62.8",height:"15",x:"83.882",y:"101.448",fill:"#E1E3E6",rx:"5"}),(0,m.jsx)("rect",{width:"65.374",height:"65.374",x:"82.478",y:"136.277",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"47.266",height:"5.148",x:"91.532",y:"211.651",fill:"#E1E3E6",rx:"2.574"}),(0,m.jsx)("rect",{width:"62.8",height:"15",x:"83.882",y:"236.946",fill:"#E1E3E6",rx:"5"}),(0,m.jsx)("rect",{width:"65.374",height:"65.374",x:"164.788",y:".779",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"47.266",height:"5.148",x:"173.843",y:"76.153",fill:"#E1E3E6",rx:"2.574"}),(0,m.jsx)("rect",{width:"62.8",height:"15",x:"166.192",y:"101.448",fill:"#E1E3E6",rx:"5"}),(0,m.jsx)("rect",{width:"65.374",height:"65.374",x:"164.788",y:"136.277",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"47.266",height:"5.148",x:"173.843",y:"211.651",fill:"#E1E3E6",rx:"2.574"}),(0,m.jsx)("rect",{width:"62.8",height:"15",x:"166.192",y:"236.946",fill:"#E1E3E6",rx:"5"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"13.283",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"21.498",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"29.713",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"37.927",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"46.238",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"95.599",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"103.814",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"112.029",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"120.243",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"128.554",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"177.909",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"186.124",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"194.339",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"202.553",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"210.864",y:"86.301",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"13.283",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"21.498",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"29.713",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"37.927",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"46.238",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"95.599",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"103.814",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"112.029",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"120.243",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"128.554",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"177.909",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"186.124",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"194.339",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"202.553",y:"221.798",fill:"#E1E3E6",rx:"3"}),(0,m.jsx)("rect",{width:"6.177",height:"6.177",x:"210.864",y:"221.798",fill:"#E1E3E6",rx:"3"})]});class So extends d.Component{render(){const{attributes:e,urlParameterSuffix:t}=this.props;return e.isPreview?Co:(0,m.jsxs)(ne.InnerBlockLayoutContextProvider,{parentName:"woocommerce/all-products",parentClassName:"wc-block-grid",children:[(0,m.jsx)(to.StoreNoticesContainer,{context:"wc/all-products"}),(0,m.jsx)(jo,{attributes:e,urlParameterSuffix:t})]})}}const No=So;o(4695);const Eo=qt("woocommerce/all-products"),Po=(0,m.jsx)(s.A,{icon:c.A}),Ao=(0,a.compose)(i.withSpokenMessages,(0,me.withSelect)(((e,{clientId:t})=>{const{getBlock:o}=e("core/block-editor");return{block:o(t)}})),(0,me.withDispatch)((e=>{const{replaceInnerBlocks:t}=e("core/block-editor");return{replaceInnerBlocks:t}})))((({block:e,attributes:t,setAttributes:o,debouncedSpeak:a,replaceInnerBlocks:u})=>{const[p,h]=(0,d.useState)(!1),[g,w]=(0,d.useState)([]),x=(0,l.useBlockProps)({className:Mt("wc-block-all-products",t)});if(0===F.r7.productCount)return((e,t)=>(0,m.jsxs)(i.Placeholder,{className:"wc-block-products",icon:t,label:e,children:[(0,m.jsx)("p",{children:(0,n.__)("You haven't published any products to list here yet.","woocommerce")}),(0,m.jsxs)(i.Button,{className:"wc-block-products__add-product-button",variant:"secondary",href:z.ADMIN_URL+"post-new.php?post_type=product",target:"_top",children:[(0,n.__)("Add new product","woocommerce")+" ",(0,m.jsx)(s.A,{icon:ut.A})]}),(0,m.jsx)(i.Button,{className:"wc-block-products__read_more_button",variant:"tertiary",href:"https://woocommerce.com/document/managing-products/",target:"_blank",children:(0,n.__)("Learn more","woocommerce")})]}))($t.title,(0,m.jsx)(s.A,{icon:c.A}));const _=()=>{h(!p),p||a((0,n.__)("Showing All Products block preview.","woocommerce"))};return(0,m.jsxs)("div",{...x,children:[(0,m.jsx)(l.BlockControls,{children:(0,m.jsx)(i.ToolbarGroup,{controls:[{icon:"edit",title:(0,n.__)("Edit the layout of each product","woocommerce"),onClick:()=>_(),isActive:p}]})}),(()=>{const{columns:e,rows:r,alignButtons:s}=t;return(0,m.jsxs)(l.InspectorControls,{children:[(0,m.jsx)(i.PanelBody,{title:(0,n.__)("Layout Settings","woocommerce"),initialOpen:!0,children:(0,m.jsx)(Gt,{columns:e,rows:r,alignButtons:s,setAttributes:o,minColumns:(0,z.getSetting)("minColumns",1),maxColumns:(0,z.getSetting)("maxColumns",6),minRows:(0,z.getSetting)("minRows",1),maxRows:(0,z.getSetting)("maxRows",6)})}),(0,m.jsxs)(i.PanelBody,{title:(0,n.__)("Content Settings","woocommerce"),children:[Kt(t,o),Zt(t,o)]})]},"inspector")})(),p?(()=>{const s={template:t.layoutConfig,templateLock:!1,allowedBlocks:Object.keys(Eo)};return 0!==t.layoutConfig.length&&(s.renderAppender=!1),(0,m.jsxs)(i.Placeholder,{icon:Po,label:$t.title,children:[(0,n.__)("Display all products from your store as a grid.","woocommerce"),(0,m.jsxs)("div",{className:"wc-block-all-products-grid-item-template",children:[(0,m.jsx)(i.Tip,{children:(0,n.__)("Edit the blocks inside the example below to change the content displayed for all products within the product grid.","woocommerce")}),(0,m.jsx)(ne.InnerBlockLayoutContextProvider,{parentName:"woocommerce/all-products",parentClassName:"wc-block-grid",children:(0,m.jsx)("div",{className:"wc-block-grid wc-block-layout has-1-columns",children:(0,m.jsx)("ul",{className:"wc-block-grid__products",children:(0,m.jsx)("li",{className:"wc-block-grid__product",children:(0,m.jsx)(ne.ProductDataContextProvider,{product:Ut[0],children:(0,m.jsx)(l.InnerBlocks,{...s})})})})})}),(0,m.jsxs)("div",{className:"wc-block-all-products__actions",children:[(0,m.jsx)(i.Button,{className:"wc-block-all-products__done-button",variant:"primary",onClick:()=>{o({layoutConfig:Jt(e.innerBlocks)}),w(e.innerBlocks),_()},children:(0,n.__)("Done","woocommerce")}),(0,m.jsx)(i.Button,{className:"wc-block-all-products__cancel-button",variant:"tertiary",onClick:()=>{u(e.clientId,g,!1),_()},children:(0,n.__)("Cancel","woocommerce")}),(0,m.jsx)(i.Button,{className:"wc-block-all-products__reset-button",icon:Po,label:(0,n.__)("Reset layout to default","woocommerce"),onClick:()=>{const t=[];Yt.map((([e,o])=>(t.push((0,r.createBlock)(e,o)),!0))),u(e.clientId,t,!1),w(e.innerBlocks)},children:(0,n.__)("Reset Layout","woocommerce")})]})]})]})})():(()=>{const{layoutConfig:e}=t,o=e&&0!==e.length,r=$t.title;return o?(0,m.jsx)(i.Disabled,{children:(0,m.jsx)(No,{attributes:t})}):((e,t)=>(0,m.jsx)(i.Placeholder,{className:"wc-block-products",icon:t,label:e,children:(0,n.__)("The content for this block is hidden due to block settings.","woocommerce")}))(r,Po)})()]})})),To={columns:(0,z.getSetting)("defaultColumns",3),rows:(0,z.getSetting)("defaultRows",3),alignButtons:!1,contentVisibility:{orderBy:!0},orderby:"date",layoutConfig:Yt,isPreview:!1},{name:Io}=$t,Bo={icon:{src:(0,m.jsx)(s.A,{icon:c.A,className:"wc-block-editor-components-block-icon"})},edit:Ao,save:function({attributes:e}){const t={};Object.keys(e).sort().forEach((o=>{t[o]=e[o]}));const o=l.useBlockProps.save({className:Mt("wc-block-all-products",e),"data-attributes":JSON.stringify(t)});return(0,m.jsx)("div",{...o,children:(0,m.jsx)(l.InnerBlocks.Content,{})})},deprecated:Ht,defaults:To};(0,r.registerBlockType)(Io,Bo)},5841:(e,t,o)=>{"use strict";o.d(t,{Nm:()=>a,fb:()=>p,p3:()=>i});var r=o(7723),s=o(4921),c=o(3993),n=o(790);const i=e=>{const t=parseFloat(e.average_rating);return Number.isFinite(t)&&t>0?t:0},a=e=>{const t=(0,c.isNumber)(e.review_count)?e.review_count:parseInt(e.review_count,10);return Number.isFinite(t)&&t>0?t:0},l=e=>({width:e/5*100+"%"}),d=({className:e,parentClassName:t})=>{const o=l(0);return(0,n.jsxs)("div",{className:(0,s.A)(`${e}__norating-container`,`${t}-product-rating__norating-container`),children:[(0,n.jsx)("div",{className:`${e}__norating`,role:"img",children:(0,n.jsx)("span",{style:o})}),(0,n.jsx)("span",{children:(0,r.__)("No Reviews","woocommerce")})]})},u=e=>{const{className:t,rating:o,reviews:c,parentClassName:i}=e,a=l(o),d=(0,r.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,r.__)("Rated %f out of 5","woocommerce"),o),u={__html:(0,r.sprintf)(/* translators: %1$s is referring to the average rating value, %2$s is referring to the number of ratings */ /* translators: %1$s is referring to the average rating value, %2$s is referring to the number of ratings */
(0,r._n)("Rated %1$s out of 5 based on %2$s customer rating","Rated %1$s out of 5 based on %2$s customer ratings",c,"woocommerce"),(0,r.sprintf)('<strong class="rating">%f</strong>',o),(0,r.sprintf)('<span class="rating">%d</span>',c))};return(0,n.jsx)("div",{className:(0,s.A)(`${t}__stars`,`${i}__product-rating__stars`),role:"img","aria-label":d,children:(0,n.jsx)("span",{style:a,dangerouslySetInnerHTML:u})})},m=e=>{const{className:t,reviews:o}=e,s=(0,r.sprintf)(/* translators: %s is referring to the total of reviews for a product */ /* translators: %s is referring to the total of reviews for a product */
(0,r._n)("(%s customer review)","(%s customer reviews)",o,"woocommerce"),o);return(0,n.jsx)("span",{className:`${t}__reviews_count`,children:s})},p=e=>{const{className:t="wc-block-components-product-rating",showReviewCount:o,showMockedReviews:r,parentClassName:c="",rating:i,reviews:a,styleProps:l,textAlign:p}=e,h=(0,s.A)(l.className,t,{[`${c}__product-rating`]:c,[`has-text-align-${p}`]:p}),g=r&&(0,n.jsx)(d,{className:t,parentClassName:c}),w=a?(0,n.jsx)(u,{className:t,rating:i,reviews:a,parentClassName:c}):g,x=a&&o;return(0,n.jsx)("div",{className:h,style:l.style,children:(0,n.jsxs)("div",{className:`${t}__container`,children:[w,x?(0,n.jsx)(m,{className:t,reviews:a}):null]})})}},6070:(e,t,o)=>{"use strict";o.d(t,{Hw:()=>p,Vo:()=>i,XK:()=>n,iI:()=>u,r7:()=>s,sW:()=>c});var r=o(5703);const s=(0,r.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),c=s.pluginUrl+"assets/images/",n=s.pluginUrl+"assets/client/blocks/",i=(r.STORE_PAGES.shop,r.STORE_PAGES.checkout,r.STORE_PAGES.checkout,r.STORE_PAGES.privacy,r.STORE_PAGES.privacy,r.STORE_PAGES.terms,r.STORE_PAGES.terms,r.STORE_PAGES.cart,r.STORE_PAGES.cart?.permalink),a=(r.STORE_PAGES.myaccount?.permalink?r.STORE_PAGES.myaccount.permalink:(0,r.getSetting)("wpLoginUrl","/wp-login.php"),(0,r.getSetting)("localPickupEnabled",!1),(0,r.getSetting)("shippingMethodsExist",!1),(0,r.getSetting)("shippingEnabled",!0),(0,r.getSetting)("countries",{})),l=(0,r.getSetting)("countryData",{}),d={...Object.fromEntries(Object.keys(l).filter((e=>!0===l[e].allowBilling)).map((e=>[e,a[e]||""]))),...Object.fromEntries(Object.keys(l).filter((e=>!0===l[e].allowShipping)).map((e=>[e,a[e]||""])))},u=(Object.fromEntries(Object.keys(d).map((e=>[e,l[e].states||{}]))),Object.fromEntries(Object.keys(d).map((e=>[e,l[e].locale||{}])))),m={address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]},p=(0,r.getSetting)("addressFieldsLocations",m).address;(0,r.getSetting)("addressFieldsLocations",m).contact,(0,r.getSetting)("addressFieldsLocations",m).order,(0,r.getSetting)("additionalOrderFields",{}),(0,r.getSetting)("additionalContactFields",{}),(0,r.getSetting)("additionalAddressFields",{})},8331:(e,t,o)=>{"use strict";o.d(t,{Hw:()=>r.Hw,Vo:()=>r.Vo,XK:()=>r.XK,iI:()=>r.iI,r7:()=>r.r7,sW:()=>r.sW});var r=o(6070)},2266:(e,t,o)=>{"use strict";o.d(t,{EF:()=>r,Ie:()=>n,UI:()=>c,ht:()=>i,j9:()=>s});const r="woocommerce/product-type-template-state",s="SWITCH_PRODUCT_TYPE",c="SET_PRODUCT_TYPES",n="REGISTER_LISTENER",i="UNREGISTER_LISTENER"},8207:(e,t,o)=>{"use strict";o.d(t,{M:()=>a});var r=o(7143),s=o(2266);const c=(0,o(7254).c)(),n={productTypes:{list:c,current:c[0]?.slug},listeners:[]},i={switchProductType:e=>({type:s.j9,current:e}),setProductTypes:e=>({type:s.UI,productTypes:e}),registerListener:e=>({type:s.Ie,listener:e}),unregisterListener:e=>({type:s.ht,listener:e})},a=(0,r.createReduxStore)(s.EF,{reducer:(e=n,t)=>{switch(t.type){case s.UI:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case s.j9:return{...e,productTypes:{...e.productTypes,current:t.current}};case s.Ie:return{...e,listeners:[...e.listeners,t.listener||""]};case s.ht:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:i,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});(0,r.select)(s.EF)||(0,r.register)(a)},2281:(e,t,o)=>{"use strict";o.d(t,{A:()=>c});var r=o(7143),s=o(8207);function c(){const{productTypes:e,current:t,registeredListeners:o}=(0,r.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:o,getRegisteredListeners:r}=e(s.M);return{productTypes:t(),current:o(),registeredListeners:r()}}),[]),{switchProductType:c,registerListener:n,unregisterListener:i}=(0,r.useDispatch)(s.M);return{productTypes:e,current:t,set:c,registeredListeners:o,registerListener:n,unregisterListener:i}}},7254:(e,t,o)=>{"use strict";o.d(t,{c:()=>s});const r=(0,o(5703).getSetting)("productTypes",{});function s(){return Object.keys(r).map((e=>({slug:e,label:r[e]})))}},7316:()=>{},1189:()=>{},5938:()=>{},660:()=>{},1986:()=>{},9556:()=>{},9835:()=>{},3744:()=>{},7545:()=>{},3081:()=>{},4053:()=>{},752:()=>{},4313:()=>{},3433:()=>{},4093:()=>{},1129:()=>{},3790:()=>{},3030:()=>{},3608:()=>{},1784:()=>{},7578:()=>{},8714:()=>{},6854:()=>{},3320:()=>{},959:()=>{},8501:()=>{},7525:()=>{},4695:()=>{},5653:()=>{},1939:()=>{},5022:()=>{},3324:()=>{},9969:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},4656:e=>{"use strict";e.exports=window.wc.blocksComponents},910:e=>{"use strict";e.exports=window.wc.priceFormat},7594:e=>{"use strict";e.exports=window.wc.wcBlocksData},415:e=>{"use strict";e.exports=window.wc.wcBlocksSharedContext},1616:e=>{"use strict";e.exports=window.wc.wcBlocksSharedHocs},5703:e=>{"use strict";e.exports=window.wc.wcSettings},3993:e=>{"use strict";e.exports=window.wc.wcTypes},195:e=>{"use strict";e.exports=window.wp.a11y},6004:e=>{"use strict";e.exports=window.wp.autop},4715:e=>{"use strict";e.exports=window.wp.blockEditor},6427:e=>{"use strict";e.exports=window.wp.components},9491:e=>{"use strict";e.exports=window.wp.compose},7143:e=>{"use strict";e.exports=window.wp.data},6087:e=>{"use strict";e.exports=window.wp.element},2619:e=>{"use strict";e.exports=window.wp.hooks},8537:e=>{"use strict";e.exports=window.wp.htmlEntities},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},5573:e=>{"use strict";e.exports=window.wp.primitives},9786:e=>{"use strict";e.exports=window.wp.styleEngine},3832:e=>{"use strict";e.exports=window.wp.url},9446:e=>{"use strict";e.exports=window.wp.wordcount},7746:e=>{"use strict";e.exports=JSON.parse('{"name":"woocommerce/product-image","title":"Product Image","description":"Display the main product image.","category":"woocommerce-product-elements","attributes":{"showProductLink":{"type":"boolean","default":true},"showSaleBadge":{"type":"boolean","default":true},"saleBadgeAlign":{"type":"string","default":"right"},"imageSizing":{"type":"string","default":"single"},"productId":{"type":"number","default":0},"isDescendentOfQueryLoop":{"type":"boolean","default":false},"isDescendentOfSingleProductBlock":{"type":"boolean","default":false},"width":{"type":"string"},"height":{"type":"string"},"scale":{"type":"string","default":"cover"},"aspectRatio":{"type":"string"}},"supports":{"interactivity":{"clientNavigation":true},"html":false,"__experimentalBorder":{"radius":true,"__experimentalSkipSerialization":true},"typography":{"fontSize":true,"__experimentalSkipSerialization":true},"spacing":{"margin":true,"padding":true},"dimensions":{"aspectRatio":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wc-block-components-product-image"},"ancestor":["woocommerce/all-products","woocommerce/single-product","woocommerce/product-template","core/post-template"],"usesContext":["query","queryId","postId"],"keywords":["WooCommerce"],"textdomain":"woocommerce","apiVersion":3,"$schema":"https://schemas.wp.org/trunk/block.json"}')},3925:e=>{"use strict";e.exports=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-title","version":"1.0.0","title":"Product Title","category":"woocommerce-product-elements","description":"Display the title of a product.","supports":{"html":false,"interactivity":{"clientNavigation":false},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontWeight":true,"__experimentalTextTransform":true,"__experimentalFontFamily":true},"color":{"text":true,"background":true,"link":false,"gradients":true,"__experimentalSkipSerialization":true},"spacing":{"margin":true,"__experimentalSkipSerialization":true},"__experimentalSelector":".wc-block-components-product-title"},"textdomain":"woocommerce","attributes":{"headingLevel":{"type":"number","default":2},"showProductLink":{"type":"boolean","default":true},"linkTarget":{"type":"string"},"productId":{"type":"number","default":0},"align":{"type":"string"}},"ancestor":["woocommerce/all-products"]}')}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return c[e].call(o.exports,o,o.exports,i),o.exports}i.m=c,e=[],i.O=(t,o,r,s)=>{if(!o){var c=1/0;for(d=0;d<e.length;d++){for(var[o,r,s]=e[d],n=!0,a=0;a<o.length;a++)(!1&s||c>=s)&&Object.keys(i.O).every((e=>i.O[e](o[a])))?o.splice(a--,1):(n=!1,s<c&&(c=s));if(n){e.splice(d--,1);var l=r();void 0!==l&&(t=l)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,r,s]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var s=Object.create(null);i.r(s);var c={};t=t||[null,o({}),o([]),o(o)];for(var n=2&r&&e;"object"==typeof n&&!~t.indexOf(n);n=o(n))Object.getOwnPropertyNames(n).forEach((t=>c[t]=()=>e[t]));return c.default=()=>e,i.d(s,c),s},i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,o)=>(i.f[o](e,t),t)),[])),i.u=e=>({345:"product-stock-indicator",462:"product-rating",2105:"product-title",2388:"product-price",2724:"product-sku",3895:"product-summary",4232:"product-image",4442:"product-sale-badge",7409:"product-button",8553:"product-rating-counter",8578:"product-rating-stars",8647:"product-average-rating"}[e]+".js?ver="+{345:"df5124dfa21ae95f6b99",462:"02cc37a867f60254e361",2105:"d92c3a074be89ba952d1",2388:"09f2252a0a85a89eec18",2724:"68c6a5abd5b457353eed",3895:"c99871176d228931e1e3",4232:"f0e2259d33e8c17d46b6",4442:"862a3a011cb666a90435",7409:"307e0212601fae2f5030",8553:"50f738006badde9aa053",8578:"b848aa5a3ddcb304776b",8647:"7c7c74fa303f8fca8df1"}[e]),i.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r={},s="webpackWcBlocksMainJsonp:",i.l=(e,t,o,c)=>{if(r[e])r[e].push(t);else{var n,a;if(void 0!==o)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var u=l[d];if(u.getAttribute("src")==e||u.getAttribute("data-webpack")==s+o){n=u;break}}n||(a=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,i.nc&&n.setAttribute("nonce",i.nc),n.setAttribute("data-webpack",s+o),n.src=e),r[e]=[t];var m=(t,o)=>{n.onerror=n.onload=null,clearTimeout(p);var s=r[e];if(delete r[e],n.parentNode&&n.parentNode.removeChild(n),s&&s.forEach((e=>e(o))),t)return t(o)},p=setTimeout(m.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=m.bind(null,n.onerror),n.onload=m.bind(null,n.onload),a&&document.head.appendChild(n)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=1001,(()=>{var e;i.g.importScripts&&(e=i.g.location+"");var t=i.g.document;if(!e&&t&&(t.currentScript&&"SCRIPT"===t.currentScript.tagName.toUpperCase()&&(e=t.currentScript.src),!e)){var o=t.getElementsByTagName("script");if(o.length)for(var r=o.length-1;r>-1&&(!e||!/^http(s?):/.test(e));)e=o[r--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),i.p=e})(),(()=>{var e={345:0,462:0,1001:0,2388:0,2724:0,3895:0,4442:0,7409:0,8553:0,8578:0,8647:0};i.f.j=(t,o)=>{var r=i.o(e,t)?e[t]:void 0;if(0!==r)if(r)o.push(r[2]);else{var s=new Promise(((o,s)=>r=e[t]=[o,s]));o.push(r[2]=s);var c=i.p+i.u(t),n=new Error;i.l(c,(o=>{if(i.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var s=o&&("load"===o.type?"missing":o.type),c=o&&o.target&&o.target.src;n.message="Loading chunk "+t+" failed.\n("+s+": "+c+")",n.name="ChunkLoadError",n.type=s,n.request=c,r[1](n)}}),"chunk-"+t,t)}},i.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[c,n,a]=o,l=0;if(c.some((t=>0!==e[t]))){for(r in n)i.o(n,r)&&(i.m[r]=n[r]);if(a)var d=a(i)}for(t&&t(o);l<c.length;l++)s=c[l],i.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return i.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var a=i.O(void 0,[94],(()=>i(4040)));a=i.O(a),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["all-products"]=a})();