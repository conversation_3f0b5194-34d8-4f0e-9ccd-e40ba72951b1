(()=>{var e,t,s,o={9018:(e,t,s)=>{"use strict";var o=s(7723),r=s(4921);const c=window.wp.blockEditor;var n=s(5573),a=s(790);const i=(0,a.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,a.jsx)("path",{d:"M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49c.37-.66-.11-1.48-.87-1.48H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45zM6.16 6h12.15l-2.76 5H8.53L6.16 6zM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2z"})]});var l=s(4530);const d=window.wp.blocks;var u=s(6087);const p=window.wc.wcSettings,m=(0,p.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),h=m.pluginUrl+"assets/images/",g=(m.pluginUrl,p.STORE_PAGES.shop?.permalink),_=p.STORE_PAGES.checkout?.id,w=(p.STORE_PAGES.checkout,p.STORE_PAGES.privacy,p.STORE_PAGES.privacy,p.STORE_PAGES.terms,p.STORE_PAGES.terms,p.STORE_PAGES.cart?.id),k=p.STORE_PAGES.cart?.permalink,b=(p.STORE_PAGES.myaccount?.permalink?p.STORE_PAGES.myaccount.permalink:(0,p.getSetting)("wpLoginUrl","/wp-login.php"),(0,p.getSetting)("localPickupEnabled",!1)),y=(0,p.getSetting)("shippingMethodsExist",!1),x=(0,p.getSetting)("shippingEnabled",!0),f=(0,p.getSetting)("countries",{}),v=(0,p.getSetting)("countryData",{}),j=Object.fromEntries(Object.keys(v).filter((e=>!0===v[e].allowBilling)).map((e=>[e,f[e]||""]))),S=Object.fromEntries(Object.keys(v).filter((e=>!0===v[e].allowShipping)).map((e=>[e,f[e]||""]))),C={...j,...S},E=Object.fromEntries(Object.keys(C).map((e=>[e,v[e].states||{}]))),N=Object.fromEntries(Object.keys(C).map((e=>[e,v[e].locale||{}]))),P={address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]},A=(0,p.getSetting)("addressFieldsLocations",P).address,R=((0,p.getSetting)("addressFieldsLocations",P).contact,(0,p.getSetting)("addressFieldsLocations",P).order,(0,p.getSetting)("additionalOrderFields",{}),(0,p.getSetting)("additionalContactFields",{}),(0,p.getSetting)("additionalAddressFields",{}),({imageUrl:e=`${h}/block-error.svg`,header:t=(0,o.__)("Oops!","woocommerce"),text:s=(0,o.__)("There was an error loading the content.","woocommerce"),errorMessage:r,errorMessagePrefix:c=(0,o.__)("Error:","woocommerce"),button:n,showErrorBlock:i=!0})=>i?(0,a.jsxs)("div",{className:"wc-block-error wc-block-components-error",children:[e&&(0,a.jsx)("img",{className:"wc-block-error__image wc-block-components-error__image",src:e,alt:""}),(0,a.jsxs)("div",{className:"wc-block-error__content wc-block-components-error__content",children:[t&&(0,a.jsx)("p",{className:"wc-block-error__header wc-block-components-error__header",children:t}),s&&(0,a.jsx)("p",{className:"wc-block-error__text wc-block-components-error__text",children:s}),r&&(0,a.jsxs)("p",{className:"wc-block-error__message wc-block-components-error__message",children:[c?c+" ":"",r]}),n&&(0,a.jsx)("p",{className:"wc-block-error__button wc-block-components-error__button",children:n})]})]}):null);s(5893);class I extends u.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return void 0!==e.statusText&&void 0!==e.status?{errorMessage:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("strong",{children:e.status}),": ",e.statusText]}),hasError:!0}:{errorMessage:e.message,hasError:!0}}render(){const{header:e,imageUrl:t,showErrorMessage:s=!0,showErrorBlock:o=!0,text:r,errorMessagePrefix:c,renderError:n,button:i}=this.props,{errorMessage:l,hasError:d}=this.state;return d?"function"==typeof n?n({errorMessage:l}):(0,a.jsx)(R,{showErrorBlock:o,errorMessage:s?l:null,header:e,imageUrl:t,text:r,errorMessagePrefix:c,button:i}):this.props.children}}const T=I,B=window.wp.data,M=(0,u.createContext)({isEditor:!1,currentPostId:0,currentView:"",previewData:{},getPreviewData:()=>({})}),O=()=>(0,u.useContext)(M),D=({children:e,currentPostId:t=0,previewData:s={},currentView:o="",isPreview:r=!1})=>{const c=(0,B.useSelect)((e=>t||e("core/editor").getCurrentPostId()),[t]),n=(0,u.useCallback)(((e,t={})=>s&&e in s?s[e]:t),[s]),i={isEditor:!0,currentPostId:c,currentView:o,previewData:s,getPreviewData:n,isPreview:r};return(0,a.jsx)(M.Provider,{value:i,children:e})},L=window.wp.plugins,F=window.wc.wcBlocksData;var V=s(1659),$=s.n(V);let z=function(e){return e.ADD_EVENT_CALLBACK="add_event_callback",e.REMOVE_EVENT_CALLBACK="remove_event_callback",e}({});const q={},H=(e=q,{type:t,eventType:s,id:o,callback:r,priority:c})=>{const n=e.hasOwnProperty(s)?new Map(e[s]):new Map;switch(t){case z.ADD_EVENT_CALLBACK:return n.set(o,{priority:c,callback:r}),{...e,[s]:n};case z.REMOVE_EVENT_CALLBACK:return n.delete(o),{...e,[s]:n}}},U=(e,t)=>(s,o=10)=>{const r=((e,t,s=10)=>({id:Math.floor(Math.random()*Date.now()).toString(),type:z.ADD_EVENT_CALLBACK,eventType:e,callback:t,priority:s}))(e,s,o);return t(r),()=>{var s;t((s=e,{id:r.id,type:z.REMOVE_EVENT_CALLBACK,eventType:s}))}},W=(0,u.createContext)({onPaymentProcessing:()=>()=>()=>{},onPaymentSetup:()=>()=>()=>{}}),Y=({children:e})=>{const{isProcessing:t,isIdle:s,isCalculating:o,hasError:r}=(0,B.useSelect)((e=>{const t=e(F.checkoutStore);return{isProcessing:t.isProcessing(),isIdle:t.isIdle(),hasError:t.hasError(),isCalculating:t.isCalculating()}})),{isPaymentReady:c}=(0,B.useSelect)((e=>{const t=e(F.paymentStore);return{isPaymentProcessing:t.isPaymentProcessing(),isPaymentReady:t.isPaymentReady()}})),{setValidationErrors:n}=(0,B.useDispatch)(F.validationStore),[i,l]=(0,u.useReducer)(H,{}),{onPaymentSetup:d}=(e=>(0,u.useMemo)((()=>({onPaymentSetup:U("payment_setup",e)})),[e]))(l),p=(0,u.useRef)(i);(0,u.useEffect)((()=>{p.current=i}),[i]);const{__internalSetPaymentProcessing:m,__internalSetPaymentIdle:h,__internalEmitPaymentProcessingEvent:g}=(0,B.useDispatch)(F.paymentStore);(0,u.useEffect)((()=>{!t||r||o||(m(),g(p.current,n))}),[t,r,o,m,g,n]),(0,u.useEffect)((()=>{s&&!c&&h()}),[s,c,h]),(0,u.useEffect)((()=>{r&&c&&h()}),[r,c,h]);const _={onPaymentProcessing:(0,u.useMemo)((()=>function(...e){return $()("onPaymentProcessing",{alternative:"onPaymentSetup",plugin:"WooCommerce Blocks"}),d(...e)}),[d]),onPaymentSetup:d};return(0,a.jsx)(W.Provider,{value:_,children:e})},G={NONE:"none",INVALID_ADDRESS:"invalid_address",UNKNOWN:"unknown_error"},Q={INVALID_COUNTRY:"woocommerce_rest_cart_shipping_rates_invalid_country",MISSING_COUNTRY:"woocommerce_rest_cart_shipping_rates_missing_country",INVALID_STATE:"woocommerce_rest_cart_shipping_rates_invalid_state"},K={shippingErrorStatus:{isPristine:!0,isValid:!1,hasInvalidAddress:!1,hasError:!1},dispatchErrorStatus:e=>e,shippingErrorTypes:G,onShippingRateSuccess:()=>()=>{},onShippingRateFail:()=>()=>{},onShippingRateSelectSuccess:()=>()=>{},onShippingRateSelectFail:()=>()=>{}},Z=(e,{type:t})=>Object.values(G).includes(t)?t:e,J="shipping_rates_success",X="shipping_rates_fail",ee="shipping_rate_select_success",te="shipping_rate_select_fail",se=e=>({onSuccess:U(J,e),onFail:U(X,e),onSelectSuccess:U(ee,e),onSelectFail:U(te,e)}),oe=window.wc.wcTypes;let re=function(e){return e.CART="wc/cart",e.CHECKOUT="wc/checkout",e.PAYMENTS="wc/checkout/payments",e.EXPRESS_PAYMENTS="wc/checkout/express-payments",e.CONTACT_INFORMATION="wc/checkout/contact-information",e.SHIPPING_ADDRESS="wc/checkout/shipping-address",e.BILLING_ADDRESS="wc/checkout/billing-address",e.SHIPPING_METHODS="wc/checkout/shipping-methods",e.CHECKOUT_ACTIONS="wc/checkout/checkout-actions",e.ORDER_INFORMATION="wc/checkout/order-information",e}({});const ce=async(e,t,s)=>{const o=((e,t)=>e[t]?Array.from(e[t].values()).sort(((e,t)=>e.priority-t.priority)):[])(e,t),r=[];for(const e of o)try{const t=await Promise.resolve(e.callback(s));"object"==typeof t&&r.push(t)}catch(e){console.error(e)}return!r.length||r};var ne=s(1824),ae=s.n(ne);const ie=window.wp.htmlEntities,le=Object.entries(N).reduce(((e,[t,s])=>(e[t]=Object.entries(s).reduce(((e,[t,s])=>(e[t]=(e=>{const t={};return void 0!==e.label&&(t.label=e.label),void 0!==e.required&&(t.required=e.required),void 0!==e.hidden&&(t.hidden=e.hidden),void 0===e.label||e.optionalLabel||(t.optionalLabel=(0,o.sprintf)(/* translators: %s Field label. */ /* translators: %s Field label. */
(0,o.__)("%s (optional)","woocommerce"),e.label)),e.index&&((0,oe.isNumber)(e.index)&&(t.index=e.index),(0,oe.isString)(e.index)&&(t.index=parseInt(e.index,10))),e.hidden&&(t.required=!1),t})(s),e)),{}),e)),{}),de=(e,t,s="")=>{const o=s&&void 0!==le[s]?le[s]:{};return e.map((e=>({key:e,...t&&e in t?t[e]:{},...o&&e in o?o[e]:{}}))).sort(((e,t)=>e.index-t.index))},ue=window.wp.url,pe=(e,t)=>e in t,me=e=>{const t=de(A,p.defaultFields,e.country),s=Object.assign({},e);return t.forEach((({key:t,hidden:o})=>{!0===o&&pe(t,e)&&(s[t]="")})),s},he=window.CustomEvent||null,ge=(e,t,s=!1,o=!1)=>{if("function"!=typeof jQuery)return()=>{};const r=()=>{((e,{bubbles:t=!1,cancelable:s=!1,element:o,detail:r={}})=>{if(!he)return;o||(o=document.body);const c=new he(e,{bubbles:t,cancelable:s,detail:r});o.dispatchEvent(c)})(t,{bubbles:s,cancelable:o})};return jQuery(document).on(e,r),()=>jQuery(document).off(e,r)},_e=e=>{const t=e?.detail;t&&t.preserveCartData||(0,B.dispatch)(F.cartStore).invalidateResolutionForStore()},we=e=>{(e?.persisted||"back_forward"===(window.performance&&window.performance.getEntriesByType("navigation").length?window.performance.getEntriesByType("navigation")[0].type:""))&&(0,B.dispatch)(F.cartStore).invalidateResolutionForStore()},ke=()=>{1===window.wcBlocksStoreCartListeners.count&&window.wcBlocksStoreCartListeners.remove(),window.wcBlocksStoreCartListeners.count--},be={first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},ye={...be,email:""},xe={total_items:"",total_items_tax:"",total_fees:"",total_fees_tax:"",total_discount:"",total_discount_tax:"",total_shipping:"",total_shipping_tax:"",total_price:"",total_tax:"",tax_lines:F.EMPTY_TAX_LINES,currency_code:"",currency_symbol:"",currency_minor_unit:2,currency_decimal_separator:"",currency_thousand_separator:"",currency_prefix:"",currency_suffix:""},fe=e=>Object.fromEntries(Object.entries(e).map((([e,t])=>[e,(0,ie.decodeEntities)(t)]))),ve={cartCoupons:F.EMPTY_CART_COUPONS,cartItems:F.EMPTY_CART_ITEMS,cartFees:F.EMPTY_CART_FEES,cartItemsCount:0,cartItemsWeight:0,crossSellsProducts:F.EMPTY_CART_CROSS_SELLS,cartNeedsPayment:!0,cartNeedsShipping:!0,cartItemErrors:F.EMPTY_CART_ITEM_ERRORS,cartTotals:xe,cartIsLoading:!0,cartErrors:F.EMPTY_CART_ERRORS,billingData:ye,billingAddress:ye,shippingAddress:be,shippingRates:F.EMPTY_SHIPPING_RATES,isLoadingRates:!1,cartHasCalculatedShipping:!1,paymentMethods:F.EMPTY_PAYMENT_METHODS,paymentRequirements:F.EMPTY_PAYMENT_REQUIREMENTS,receiveCart:()=>{},receiveCartContents:()=>{},extensions:F.EMPTY_EXTENSIONS},je=(e={shouldSelect:!0})=>{const{shouldSelect:t}=e,s=(0,u.useRef)(),o=(0,u.useRef)(ye),r=(0,u.useRef)(be);(0,u.useEffect)((()=>((()=>{if(window.wcBlocksStoreCartListeners||(window.wcBlocksStoreCartListeners={count:0,remove:()=>{}}),window.wcBlocksStoreCartListeners?.count>0)return void window.wcBlocksStoreCartListeners.count++;document.body.addEventListener("wc-blocks_added_to_cart",_e),document.body.addEventListener("wc-blocks_removed_from_cart",_e),window.addEventListener("pageshow",we);const e=ge("added_to_cart","wc-blocks_added_to_cart"),t=ge("removed_from_cart","wc-blocks_removed_from_cart");window.wcBlocksStoreCartListeners.count=1,window.wcBlocksStoreCartListeners.remove=()=>{document.body.removeEventListener("wc-blocks_added_to_cart",_e),document.body.removeEventListener("wc-blocks_removed_from_cart",_e),window.removeEventListener("pageshow",we),e(),t()}})(),ke)),[]);const c=(0,B.useSelect)(((e,{dispatch:s})=>{if(!t)return ve;const c=e(F.cartStore),n=c.getCartData(),a=c.getCartErrors(),i=c.getCartTotals(),l=!c.hasFinishedResolution("getCartData"),d=c.isCustomerDataUpdating(),{receiveCart:u,receiveCartContents:p}=s(F.cartStore),m=n.fees.length>0?n.fees.map((e=>fe(e))):F.EMPTY_CART_FEES,h=n.coupons.length>0?n.coupons.map((e=>({...e,label:e.code}))):F.EMPTY_CART_COUPONS,g=me(fe(n.billingAddress)),_=n.needsShipping?me(fe(n.shippingAddress)):g;return ae()(g,o.current)||(o.current=g),ae()(_,r.current)||(r.current=_),{cartCoupons:h,cartItems:n.items,crossSellsProducts:n.crossSells,cartFees:m,cartItemsCount:n.itemsCount,cartItemsWeight:n.itemsWeight,cartNeedsPayment:n.needsPayment,cartNeedsShipping:n.needsShipping,cartItemErrors:n.errors,cartTotals:i,cartIsLoading:l,cartErrors:a,billingData:o.current,billingAddress:o.current,shippingAddress:r.current,extensions:n.extensions,shippingRates:n.shippingRates,isLoadingRates:d,cartHasCalculatedShipping:n.hasCalculatedShipping,paymentRequirements:n.paymentRequirements,receiveCart:u,receiveCartContents:p}}),[t]);return s.current&&ae()(s.current,c)||(s.current=c),s.current},Se=e=>e.length,Ce=(0,p.getSetting)("collectableMethodIds",[]),Ee=e=>Ce.includes(e.method_id),Ne=e=>!!b&&(Array.isArray(e)?!!e.find((e=>Ce.includes(e))):Ce.includes(e)),Pe=e=>e.some((e=>!!e.shipping_rates.length)),Ae=e=>!!Pe(e)&&e.every((e=>e.shipping_rates.every((e=>Ee(e)))));var Re=s(923),Ie=s.n(Re);const Te=window.wp.hooks,Be=()=>({dispatchStoreEvent:(0,u.useCallback)(((e,t={})=>{try{(0,Te.doAction)(`experimental__woocommerce_blocks-${e}`,t)}catch(e){console.error(e)}}),[]),dispatchCheckoutEvent:(0,u.useCallback)(((e,t={})=>{try{(0,Te.doAction)(`experimental__woocommerce_blocks-checkout-${e}`,{...t,storeCart:(0,B.select)("wc/store/cart").getCartData()})}catch(e){console.error(e)}}),[])}),Me=()=>{const{shippingRates:e,needsShipping:t,hasCalculatedShipping:s,isLoadingRates:o,isCollectable:r,isSelectingRate:c}=(0,B.useSelect)((e=>{const t=e(F.cartStore),s=t.getShippingRates();return{shippingRates:s,needsShipping:t.getNeedsShipping(),hasCalculatedShipping:t.getHasCalculatedShipping(),isLoadingRates:t.isCustomerDataUpdating(),isCollectable:s.every((({shipping_rates:e})=>e.find((({method_id:e})=>Ne(e))))),isSelectingRate:t.isShippingRateBeingSelected()}}),[]),n=(0,u.useRef)({});(0,u.useEffect)((()=>{const t=(e=>Object.fromEntries(e.map((({package_id:e,shipping_rates:t})=>[e,t.find((e=>e.selected))?.rate_id||""]))))(e);(0,oe.isObject)(t)&&!Ie()(n.current,t)&&(n.current=t)}),[e]);const{selectShippingRate:a}=(0,B.useDispatch)(F.cartStore),i=Ne(Object.values(n.current).map((e=>e.split(":")[0]))),{dispatchCheckoutEvent:l}=Be(),d=(0,u.useCallback)(((e,t)=>{let s;void 0!==e&&(s=Ne(e.split(":")[0])?a(e,null):a(e,t),s.then((()=>{l("set-selected-shipping-rate",{shippingRateId:e})})).catch((e=>{(0,F.processErrorResponse)(e)})))}),[a,l]);return{isSelectingRate:c,selectedRates:n.current,selectShippingRate:d,shippingRates:e,needsShipping:t,hasCalculatedShipping:s,isLoadingRates:o,isCollectable:r,hasSelectedLocalPickup:i}},{NONE:Oe,INVALID_ADDRESS:De,UNKNOWN:Le}=G,Fe=(0,u.createContext)(K),Ve=()=>(0,u.useContext)(Fe),$e=({children:e})=>{const{__internalStartCalculation:t,__internalFinishCalculation:s}=(0,B.useDispatch)(F.checkoutStore),{shippingRates:o,isLoadingRates:r,cartErrors:c}=je(),{selectedRates:n,isSelectingRate:i}=Me(),[l,d]=(0,u.useReducer)(Z,Oe),[p,m]=(0,u.useReducer)(H,{}),h=(0,u.useRef)(p),g=(0,u.useMemo)((()=>({onShippingRateSuccess:se(m).onSuccess,onShippingRateFail:se(m).onFail,onShippingRateSelectSuccess:se(m).onSelectSuccess,onShippingRateSelectFail:se(m).onSelectFail})),[m]);(0,u.useEffect)((()=>{h.current=p}),[p]),(0,u.useEffect)((()=>{r?t():s()}),[r,t,s]),(0,u.useEffect)((()=>{i?t():s()}),[t,s,i]),(0,u.useEffect)((()=>{c.length>0&&c.some((e=>!(!e.code||!Object.values(Q).includes(e.code))))?d({type:De}):d({type:Oe})}),[c]);const _=(0,u.useMemo)((()=>({isPristine:l===Oe,isValid:l===Oe,hasInvalidAddress:l===De,hasError:l===Le||l===De})),[l]);(0,u.useEffect)((()=>{r||0!==o.length&&!_.hasError||ce(h.current,X,{hasInvalidAddress:_.hasInvalidAddress,hasError:_.hasError})}),[o,r,_.hasError,_.hasInvalidAddress]),(0,u.useEffect)((()=>{!r&&o.length>0&&!_.hasError&&ce(h.current,J,o)}),[o,r,_.hasError]),(0,u.useEffect)((()=>{i||(_.hasError?ce(h.current,te,{hasError:_.hasError,hasInvalidAddress:_.hasInvalidAddress}):ce(h.current,ee,n.current))}),[n,i,_.hasError,_.hasInvalidAddress]);const w={shippingErrorStatus:_,dispatchErrorStatus:d,shippingErrorTypes:G,...g};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(Fe.Provider,{value:w,children:e})})};function ze(e,t){const s=(0,u.useRef)();return(0,u.useEffect)((()=>{s.current===e||t&&!t(e,s.current)||(s.current=e)}),[e,t]),s.current}const qe=window.wp.notices,He=window.wc.blocksCheckoutEvents,Ue={},We={},Ye=()=>Ue,Ge=()=>We,Qe=(0,u.createContext)({onSubmit:()=>{},onCheckoutAfterProcessingWithSuccess:()=>()=>{},onCheckoutAfterProcessingWithError:()=>()=>{},onCheckoutBeforeProcessing:()=>()=>{},onCheckoutValidationBeforeProcessing:()=>()=>{},onCheckoutSuccess:()=>()=>{},onCheckoutFail:()=>()=>{},onCheckoutValidation:()=>()=>{}}),Ke=({children:e,redirectUrl:t})=>{const s=Ye(),o=Ge(),{isEditor:r}=O(),{__internalUpdateAvailablePaymentMethods:c}=(0,B.useDispatch)(F.paymentStore);(0,u.useEffect)((()=>{(r||0!==Object.keys(s).length||0!==Object.keys(o).length)&&c()}),[r,s,o,c]);const{__internalSetRedirectUrl:n,__internalEmitValidateEvent:i,__internalEmitAfterProcessingEvents:l,__internalSetBeforeProcessing:d}=(0,B.useDispatch)(F.checkoutStore),{checkoutRedirectUrl:p,checkoutStatus:m,isCheckoutBeforeProcessing:h,isCheckoutAfterProcessing:g,checkoutHasError:_,checkoutOrderId:w,checkoutOrderNotes:k,checkoutCustomerId:b}=(0,B.useSelect)((e=>{const t=e(F.checkoutStore);return{checkoutRedirectUrl:t.getRedirectUrl(),checkoutStatus:t.getCheckoutStatus(),isCheckoutBeforeProcessing:t.isBeforeProcessing(),isCheckoutAfterProcessing:t.isAfterProcessing(),checkoutHasError:t.hasError(),checkoutOrderId:t.getOrderId(),checkoutOrderNotes:t.getOrderNotes(),checkoutCustomerId:t.getCustomerId()}}));t&&t!==p&&n(t);const{setValidationErrors:y}=(0,B.useDispatch)(F.validationStore),{dispatchCheckoutEvent:x}=Be(),f=Object.values(re).filter((e=>e!==re.PAYMENTS&&e!==re.EXPRESS_PAYMENTS)),v=(0,B.useSelect)((e=>{const{getNotices:t}=e(qe.store);return f.reduce(((e,s)=>[...e,...t(s)]),[])}),[f]),{paymentNotices:j,expressPaymentNotices:S}=(0,B.useSelect)((e=>{const{getNotices:t}=e(qe.store);return{paymentNotices:t(re.PAYMENTS),expressPaymentNotices:t(re.EXPRESS_PAYMENTS)}}),[]),[C]=(0,u.useReducer)(H,{}),E=(0,u.useRef)(C),{onCheckoutValidation:N,onCheckoutSuccess:P,onCheckoutFail:A}=He.checkoutEvents;(0,u.useEffect)((()=>{E.current=C}),[C]);const R=(0,u.useMemo)((()=>function(...e){return $()("onCheckoutBeforeProcessing",{alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks"}),N(...e)}),[N]),I=(0,u.useMemo)((()=>function(...e){return $()("onCheckoutValidationBeforeProcessing",{since:"9.7.0",alternative:"onCheckoutValidation",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),N(...e)}),[N]),T=(0,u.useMemo)((()=>function(...e){return $()("onCheckoutAfterProcessingWithSuccess",{since:"9.7.0",alternative:"onCheckoutSuccess",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),P(...e)}),[P]),M=(0,u.useMemo)((()=>function(...e){return $()("onCheckoutAfterProcessingWithError",{since:"9.7.0",alternative:"onCheckoutFail",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8381"}),A(...e)}),[A]);(0,u.useEffect)((()=>{h&&i({setValidationErrors:y})}),[h,y,i]);const D=ze(m),L=ze(_);(0,u.useEffect)((()=>{m===D&&_===L||g&&l({notices:{checkoutNotices:v,paymentNotices:j,expressPaymentNotices:S}})}),[m,_,p,w,b,k,g,h,D,L,v,S,j,i,l]);const V={onSubmit:(0,u.useCallback)((()=>{x("submit"),d()}),[x,d]),onCheckoutBeforeProcessing:R,onCheckoutValidationBeforeProcessing:I,onCheckoutAfterProcessingWithSuccess:T,onCheckoutAfterProcessingWithError:M,onCheckoutSuccess:P,onCheckoutFail:A,onCheckoutValidation:N};return(0,a.jsx)(Qe.Provider,{value:V,children:e})},Ze=window.wp.apiFetch;var Je=s.n(Ze);(0,o.__)("Something went wrong. Please contact us to get assistance.","woocommerce");const Xe=window.wc.wcBlocksRegistry,et=(e,t,s)=>{const o=Object.keys(e).map((t=>({key:t,value:e[t]})),[]),r=`wc-${s}-new-payment-method`;return o.push({key:r,value:t}),o},tt=e=>{if(!e)return;const{__internalSetCustomerId:t}=(0,B.dispatch)(F.checkoutStore);Je().setNonce&&"function"==typeof Je().setNonce&&Je().setNonce(e),Je().setCartHash&&"function"==typeof Je().setCartHash&&Je().setCartHash(e),e?.get("User-ID")&&t(parseInt(e.get("User-ID")||"0",10))},st=()=>{const{customerData:e,isInitialized:t}=(0,B.useSelect)((e=>{const t=e(F.cartStore);return{customerData:t.getCustomerData(),isInitialized:t.hasFinishedResolution("getCartData")}})),{setShippingAddress:s,setBillingAddress:o}=(0,B.useDispatch)(F.cartStore);return{isInitialized:t,billingAddress:e.billingAddress,shippingAddress:e.shippingAddress,setBillingAddress:o,setShippingAddress:s}},ot=()=>{const{isEditor:e,getPreviewData:t}=O(),{needsShipping:s}=Me(),{useShippingAsBilling:o,prefersCollection:r,editingBillingAddress:c,editingShippingAddress:n}=(0,B.useSelect)((e=>({useShippingAsBilling:e(F.checkoutStore).getUseShippingAsBilling(),prefersCollection:e(F.checkoutStore).prefersCollection(),editingBillingAddress:e(F.checkoutStore).getEditingBillingAddress(),editingShippingAddress:e(F.checkoutStore).getEditingShippingAddress()}))),{__internalSetUseShippingAsBilling:a,setEditingBillingAddress:i,setEditingShippingAddress:l}=(0,B.useDispatch)(F.checkoutStore),{billingAddress:d,setBillingAddress:m,shippingAddress:h,setShippingAddress:g}=st(),_=(0,u.useCallback)((e=>{m({email:e})}),[m]),w=(0,p.getSetting)("forcedBillingAddress",!1);return{shippingAddress:h,billingAddress:d,setShippingAddress:g,setBillingAddress:m,setEmail:_,defaultFields:e?t("defaultFields",p.defaultFields):p.defaultFields,useShippingAsBilling:o,setUseShippingAsBilling:a,editingBillingAddress:c,editingShippingAddress:n,setEditingBillingAddress:i,setEditingShippingAddress:l,needsShipping:s,showShippingFields:!w&&s&&!r,showShippingMethods:s&&!r,showBillingFields:!s||!o||!!r,forcedBillingAddress:w,useBillingAsShipping:w||!!r}},rt=()=>{const{onCheckoutValidation:e}=He.checkoutEvents,{additionalFields:t,customerId:s,customerPassword:r,extensionData:c,hasError:n,isBeforeProcessing:a,isComplete:i,isProcessing:l,orderNotes:d,redirectUrl:p,shouldCreateAccount:m}=(0,B.useSelect)((e=>{const t=e(F.checkoutStore);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId(),customerPassword:t.getCustomerPassword(),extensionData:t.getExtensionData(),hasError:t.hasError(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),isProcessing:t.isProcessing(),orderNotes:t.getOrderNotes(),redirectUrl:t.getRedirectUrl(),shouldCreateAccount:t.getShouldCreateAccount()}}),[]),{__internalSetHasError:h,__internalProcessCheckoutResponse:g}=(0,B.useDispatch)(F.checkoutStore),_=(0,B.useSelect)((e=>e(F.validationStore).hasValidationErrors),[]),{shippingErrorStatus:w}=Ve(),{shippingAddress:k,billingAddress:b,useBillingAsShipping:y}=ot(),{cartNeedsPayment:x,cartNeedsShipping:f,receiveCartContents:v}=je(),{activePaymentMethod:j,paymentMethodData:S,isExpressPaymentMethodActive:C,hasPaymentError:E,isPaymentReady:N,shouldSavePayment:P}=(0,B.useSelect)((e=>{const t=e(F.paymentStore);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData(),isExpressPaymentMethodActive:t.isExpressPaymentMethodActive(),hasPaymentError:t.hasPaymentError(),isPaymentReady:t.isPaymentReady(),shouldSavePayment:t.getShouldSavePaymentMethod()}}),[]),A=(0,Xe.getPaymentMethods)(),R=(0,Xe.getExpressPaymentMethods)(),I=(0,u.useRef)(b),T=(0,u.useRef)(k),M=(0,u.useRef)(p),[O,D]=(0,u.useState)(!1),L=(0,u.useMemo)((()=>{const e={...R,...A};return e?.[j]?.paymentMethodId}),[j,R,A]),V=_()&&!C||E||w.hasError,$=!n&&!V&&(N||!x)&&l;(0,u.useEffect)((()=>{V===n||!l&&!a||C||h(V)}),[V,n,l,a,C,h]),(0,u.useEffect)((()=>{I.current=b,T.current=k,M.current=p}),[b,k,p]);const z=(0,u.useCallback)((()=>_()?void 0!==(0,B.select)(F.validationStore).getValidationError("shipping-rates-error")&&{type:oe.responseTypes.ERROR,errorMessage:(0,o.__)("Sorry, this order requires a shipping option.","woocommerce")}:E?{type:oe.responseTypes.ERROR,errorMessage:(0,o.__)("There was a problem with your payment option.","woocommerce"),context:"wc/checkout/payments"}:!w.hasError||{type:oe.responseTypes.ERROR,errorMessage:(0,o.__)("There was a problem with your shipping option.","woocommerce"),context:"wc/checkout/shipping-methods"}),[_,E,w.hasError]);(0,u.useEffect)((()=>{let t;return C||(t=e(z,0)),()=>{C||"function"!=typeof t||t()}}),[e,z,C]),(0,u.useEffect)((()=>{window.localStorage.removeItem("WOOCOMMERCE_CHECKOUT_IS_CUSTOMER_DATA_DIRTY"),M.current&&(window.location.href=M.current)}),[i]);const q=(0,u.useCallback)((async()=>{if(O)return;D(!0),(()=>{const e=(0,B.select)("wc/store/store-notices").getRegisteredContainers(),{removeNotice:t}=(0,B.dispatch)(qe.store),{getNotices:s}=(0,B.select)(qe.store);e.forEach((e=>{s(e).forEach((s=>{t(s.id,e)}))}))})();const e=x?{payment_method:L,payment_data:et(S,P,j)}:{},n=me(I.current),a=y?n:me(T.current),i={additional_fields:t,billing_address:n,create_account:m,customer_note:d,customer_password:r,extensions:{...c},shipping_address:f?a:void 0,...e};(0,F.clearCheckoutPutRequests)(),Je()({path:"/wc/store/v1/checkout",method:"POST",data:i,cache:"no-store",parse:!1}).then((e=>{if((0,oe.assertResponseIsValid)(e),tt(e.headers),!e.ok)throw e;return e.json()})).then((e=>{g(e),D(!1)})).catch((e=>{tt(e?.headers);try{e.json().then((e=>e)).then((e=>{e.data?.cart&&v(e.data.cart),(0,F.processErrorResponse)(e),g(e)}))}catch{let e=(0,o.__)("Something went wrong when placing the order. Check your email for order updates before retrying.","woocommerce");0!==s&&(e=(0,o.__)("Something went wrong when placing the order. Check your account's order history or your email for order updates before retrying.","woocommerce")),(0,F.processErrorResponse)({code:"unknown_error",message:e,data:null})}h(!0),D(!1)}))}),[O,x,L,S,P,j,d,m,s,r,c,t,f,v,h,g,y]);return(0,u.useEffect)((()=>{$&&!O&&q()}),[q,$,O]),null},ct=({children:e,redirectUrl:t})=>(0,a.jsx)(Ke,{redirectUrl:t,children:(0,a.jsx)($e,{children:(0,a.jsxs)(Y,{children:[e,(0,a.jsx)(T,{renderError:p.CURRENT_USER_IS_ADMIN?null:()=>null,children:(0,a.jsx)(L.PluginArea,{scope:"woocommerce-checkout"})}),(0,a.jsx)(rt,{})]})})}),nt=({children:e,redirectUrl:t})=>(0,a.jsx)(ct,{redirectUrl:t,children:e}),at={currency_code:p.SITE_CURRENCY.code,currency_symbol:p.SITE_CURRENCY.symbol,currency_minor_unit:p.SITE_CURRENCY.minorUnit,currency_decimal_separator:p.SITE_CURRENCY.decimalSeparator,currency_thousand_separator:p.SITE_CURRENCY.thousandSeparator,currency_prefix:p.SITE_CURRENCY.prefix,currency_suffix:p.SITE_CURRENCY.suffix},it=(e,t=2)=>{const s=p.SITE_CURRENCY.minorUnit;if(s===t||!e)return e;const o=Math.pow(10,s);return(Math.round(parseInt(e,10)/Math.pow(10,t))*o).toString()},lt=(0,p.getSetting)("localPickupEnabled",!1),dt=(0,p.getSetting)("localPickupText",(0,o.__)("Local pickup","woocommerce")),ut=(0,p.getSetting)("localPickupCost",""),pt=lt?(0,p.getSetting)("localPickupLocations",[]):[],mt=pt?Object.values(pt).map(((e,t)=>({...at,name:`${dt} (${e.name})`,description:"",delivery_time:"",price:it(ut,0)||"0",taxes:"0",rate_id:`pickup_location:${t+1}`,instance_id:t+1,meta_data:[{key:"pickup_location",value:e.name},{key:"pickup_address",value:e.formatted_address},{key:"pickup_details",value:e.details}],method_id:"pickup_location",selected:!1}))):[],ht=[{destination:{address_1:"",address_2:"",city:"",state:"",postcode:"",country:""},package_id:0,name:(0,o.__)("Shipping","woocommerce"),items:[{key:"33e75ff09dd601bbe69f351039152189",name:(0,o._x)("Beanie with Logo","example product in Cart Block","woocommerce"),quantity:2},{key:"6512bd43d9caa6e02c990b0a82652dca",name:(0,o._x)("Beanie","example product in Cart Block","woocommerce"),quantity:1}],shipping_rates:[{...at,name:(0,o.__)("Flat rate shipping","woocommerce"),description:"",delivery_time:"",price:it("500"),taxes:"0",rate_id:"flat_rate:0",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!1},{...at,name:(0,o.__)("Free shipping","woocommerce"),description:"",delivery_time:"",price:"0",taxes:"0",rate_id:"free_shipping:1",instance_id:0,meta_data:[],method_id:"flat_rate",selected:!0},...mt]}],gt=(0,p.getSetting)("displayCartPricesIncludingTax",!1),_t={coupons:[],shipping_rates:(0,p.getSetting)("shippingMethodsExist",!1)||(0,p.getSetting)("localPickupEnabled",!1)?ht:[],items:[{key:"1",id:1,type:"simple",quantity:2,catalog_visibility:"visible",name:(0,o.__)("Beanie","woocommerce"),summary:(0,o.__)("Beanie","woocommerce"),short_description:(0,o.__)("Warm hat for winter","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-beanie",permalink:"https://example.org",low_stock_remaining:2,backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:10,src:h+"previews/beanie.jpg",thumbnail:h+"previews/beanie.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,o.__)("Color","woocommerce"),value:(0,o.__)("Yellow","woocommerce")},{attribute:(0,o.__)("Size","woocommerce"),value:(0,o.__)("Small","woocommerce")}],prices:{...at,price:it(gt?"12000":"10000"),regular_price:it(gt?"120":"100"),sale_price:it(gt?"12000":"10000"),price_range:null,raw_prices:{precision:6,price:gt?"12000000":"10000000",regular_price:gt?"12000000":"10000000",sale_price:gt?"12000000":"10000000"}},totals:{...at,line_subtotal:it("2000"),line_subtotal_tax:it("400"),line_total:it("2000"),line_total_tax:it("400")},extensions:{},item_data:[]},{key:"2",id:2,type:"simple",quantity:1,catalog_visibility:"visible",name:(0,o.__)("Cap","woocommerce"),summary:(0,o.__)("Cap","woocommerce"),short_description:(0,o.__)("Lightweight baseball cap","woocommerce"),description:"Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",sku:"woo-cap",low_stock_remaining:null,permalink:"https://example.org",backorders_allowed:!1,show_backorder_badge:!1,sold_individually:!1,quantity_limits:{minimum:1,maximum:99,multiple_of:1,editable:!0},images:[{id:11,src:h+"previews/cap.jpg",thumbnail:h+"previews/cap.jpg",srcset:"",sizes:"",name:"",alt:""}],variation:[{attribute:(0,o.__)("Color","woocommerce"),value:(0,o.__)("Orange","woocommerce")}],prices:{...at,price:it(gt?"2400":"2000"),regular_price:it(gt?"2400":"2000"),sale_price:it(gt?"2400":"2000"),price_range:null,raw_prices:{precision:6,price:gt?"24000000":"20000000",regular_price:gt?"24000000":"20000000",sale_price:gt?"24000000":"20000000"}},totals:{...at,line_subtotal:it("2000"),line_subtotal_tax:it("400"),line_total:it("2000"),line_total_tax:it("400")},extensions:{},item_data:[]}],cross_sells:[{id:1,name:(0,o.__)("Polo","woocommerce"),slug:"polo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-polo",short_description:(0,o.__)("Polo","woocommerce"),description:(0,o.__)("Polo","woocommerce"),on_sale:!1,prices:{...at,price:it(gt?"24000":"20000"),regular_price:it(gt?"24000":"20000"),sale_price:it(gt?"12000":"10000"),price_range:null},price_html:"",average_rating:"4.5",review_count:2,images:[{id:17,src:h+"previews/polo.jpg",thumbnail:h+"previews/polo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:2,name:(0,o.__)("Long Sleeve Tee","woocommerce"),slug:"long-sleeve-tee",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-long-sleeve-tee",short_description:(0,o.__)("Long Sleeve Tee","woocommerce"),description:(0,o.__)("Long Sleeve Tee","woocommerce"),on_sale:!1,prices:{...at,price:it(gt?"30000":"25000"),regular_price:it(gt?"30000":"25000"),sale_price:it(gt?"30000":"25000"),price_range:null},price_html:"",average_rating:"4",review_count:2,images:[{id:17,src:h+"previews/long-sleeve-tee.jpg",thumbnail:h+"previews/long-sleeve-tee.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:3,name:(0,o.__)("Hoodie with Zipper","woocommerce"),slug:"hoodie-with-zipper",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-zipper",short_description:(0,o.__)("Hoodie with Zipper","woocommerce"),description:(0,o.__)("Hoodie with Zipper","woocommerce"),on_sale:!0,prices:{...at,price:it(gt?"15000":"12500"),regular_price:it(gt?"30000":"25000"),sale_price:it(gt?"15000":"12500"),price_range:null},price_html:"",average_rating:"1",review_count:2,images:[{id:17,src:h+"previews/hoodie-with-zipper.jpg",thumbnail:h+"previews/hoodie-with-zipper.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:4,name:(0,o.__)("Hoodie with Logo","woocommerce"),slug:"hoodie-with-logo",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-logo",short_description:(0,o.__)("Polo","woocommerce"),description:(0,o.__)("Polo","woocommerce"),on_sale:!1,prices:{...at,price:it(gt?"4500":"4250"),regular_price:it(gt?"4500":"4250"),sale_price:it(gt?"4500":"4250"),price_range:null},price_html:"",average_rating:"5",review_count:2,images:[{id:17,src:h+"previews/hoodie-with-logo.jpg",thumbnail:h+"previews/hoodie-with-logo.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:5,name:(0,o.__)("Hoodie with Pocket","woocommerce"),slug:"hoodie-with-pocket",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-hoodie-with-pocket",short_description:(0,o.__)("Hoodie with Pocket","woocommerce"),description:(0,o.__)("Hoodie with Pocket","woocommerce"),on_sale:!0,prices:{...at,price:it(gt?"3500":"3250"),regular_price:it(gt?"4500":"4250"),sale_price:it(gt?"3500":"3250"),price_range:null},price_html:"",average_rating:"3.75",review_count:4,images:[{id:17,src:h+"previews/hoodie-with-pocket.jpg",thumbnail:h+"previews/hoodie-with-pocket.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}},{id:6,name:(0,o.__)("T-Shirt","woocommerce"),slug:"t-shirt",parent:0,type:"simple",variation:"",permalink:"https://example.org",sku:"woo-t-shirt",short_description:(0,o.__)("T-Shirt","woocommerce"),description:(0,o.__)("T-Shirt","woocommerce"),on_sale:!1,prices:{...at,price:it(gt?"1800":"1500"),regular_price:it(gt?"1800":"1500"),sale_price:it(gt?"1800":"1500"),price_range:null},price_html:"",average_rating:"3",review_count:2,images:[{id:17,src:h+"previews/tshirt.jpg",thumbnail:h+"previews/tshirt.jpg",srcset:"",sizes:"",name:"",alt:""}],categories:[],tags:[],attributes:[],variations:[],has_options:!1,is_purchasable:!0,is_in_stock:!0,is_on_backorder:!1,low_stock_remaining:null,sold_individually:!1,add_to_cart:{text:"",description:"",url:"",minimum:1,maximum:99,multiple_of:1}}],fees:[{id:"fee",name:(0,o.__)("Fee","woocommerce"),totals:{...at,total:it("100"),total_tax:it("20")}}],items_count:3,items_weight:0,needs_payment:!0,needs_shipping:x,has_calculated_shipping:!0,shipping_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",phone:""},billing_address:{first_name:"",last_name:"",company:"",address_1:"",address_2:"",city:"",state:"",postcode:"",country:"",email:"",phone:""},totals:{...at,total_items:it("4000"),total_items_tax:it("800"),total_fees:it("100"),total_fees_tax:it("20"),total_discount:"0",total_discount_tax:"0",total_shipping:"0",total_shipping_tax:"0",total_tax:it("820"),total_price:it("4920"),tax_lines:[{name:(0,o.__)("Sales tax","woocommerce"),rate:"20%",price:it("820")}]},errors:[],payment_methods:["cod","bacs","cheque"],payment_requirements:["products"],extensions:{}},wt=window.wc.blocksCheckout,kt=(0,a.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,a.jsxs)("g",{fill:"none",fillRule:"evenodd",children:[(0,a.jsx)("path",{d:"M0 0h24v24H0z"}),(0,a.jsx)("path",{fill:"currentColor",fillRule:"nonzero",d:"M15.55 13c.75 0 1.41-.41 1.75-1.03l3.58-6.49A.996.996 0 0 0 20.01 4H5.21l-.94-2H1v2h2l3.6 7.59-1.35 2.44C4.52 15.37 5.48 17 7 17h12v-2H7l1.1-2h7.45ZM7 18c-1.1 0-1.99.9-1.99 2S5.9 22 7 22s2-.9 2-2-.9-2-2-2Zm10 0c-1.1 0-1.99.9-1.99 2s.89 2 1.99 2 2-.9 2-2-.9-2-2-2Z"})]})," "]});var bt=s(9491);const yt=(0,u.createContext)({hasContainerWidth:!1,containerClassName:"",isMobile:!1,isSmall:!1,isMedium:!1,isLarge:!1}),xt=({children:e,className:t=""})=>{const[s,o]=(()=>{const[e,{width:t}]=(0,bt.useResizeObserver)();let s="";return t>700?s="is-large":t>520?s="is-medium":t>400?s="is-small":t&&(s="is-mobile"),[e,s]})(),c={hasContainerWidth:""!==o,containerClassName:o,isMobile:"is-mobile"===o,isSmall:"is-small"===o,isMedium:"is-medium"===o,isLarge:"is-large"===o};return(0,a.jsx)(yt.Provider,{value:c,children:(0,a.jsxs)("div",{className:(0,r.A)(t,o),children:[s,e]})})};s(9163);const ft=({children:e,className:t})=>(0,a.jsx)(xt,{className:(0,r.A)("wc-block-components-sidebar-layout",t),children:e}),vt=["core/paragraph","core/image","core/separator"],jt=e=>{const t=(0,wt.applyCheckoutFilter)({filterName:"additionalCartCheckoutInnerBlockTypes",defaultValue:[],extensions:(0,B.select)(F.cartStore).getCartData().extensions,arg:{block:e},validation:e=>{if(Array.isArray(e)&&e.every((e=>"string"==typeof e)))return!0;throw new Error("allowedBlockTypes filters must return an array of strings.")}});return Array.from(new Set([...(0,d.getBlockTypes)().filter((t=>(t?.parent||[]).includes(e))).map((({name:e})=>e)),...vt,...t]))},St=({clientId:e,registeredBlocks:t,defaultTemplate:s=[]})=>{const o=(0,u.useRef)(t),r=(0,u.useRef)(s),c=(0,B.useRegistry)(),{isPreview:n}=O();(0,u.useEffect)((()=>{let t=!1;if(n)return;const{replaceInnerBlocks:s}=(0,B.dispatch)("core/block-editor");return c.subscribe((()=>{if(!c.select("core/block-editor").getBlock(e))return;const n=c.select("core/block-editor").getBlocks(e);if(0===n.length&&r.current.length>0&&!t){const o=(0,d.createBlocksFromInnerBlocksTemplate)(r.current);if(0!==o.length)return t=!0,void s(e,o)}const a=o.current.map((e=>(0,d.getBlockType)(e))),i=((e,t)=>{const s=t.filter((e=>e&&(({attributes:e})=>Boolean(e.lock?.remove||e.lock?.default?.remove))(e))),o=[];return s.forEach((t=>{if(void 0===t)return;const s=e.find((e=>e.name===t.name));s||o.push(t)})),o})(n,a);if(0===i.length)return;let l=-1;const u=i.map((e=>{const t=r.current.findIndex((([t])=>t===e.name)),s=(0,d.createBlock)(e.name);return-1===l&&(l=(({defaultTemplatePosition:e,innerBlocks:t,currentDefaultTemplate:s})=>{switch(e){case-1:return t.length;case 0:return 0;default:const o=s.current[e-1],r=t.findIndex((({name:e})=>e===o[0]));return-1===r?e:r+1}})({defaultTemplatePosition:t,innerBlocks:n,currentDefaultTemplate:r})),s}));c.batch((()=>{c.dispatch("core/block-editor").insertBlocks(u,l,e)}))}),"core/block-editor")}),[e,n,c])};s(398);const Ct=(0,u.createContext)({hasDarkControls:!1});(0,d.registerBlockType)("woocommerce/filled-cart-block",{icon:{src:(0,a.jsx)(l.A,{icon:kt,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,c.useBlockProps)(),{currentView:s}=O(),{hasDarkControls:o}=(0,u.useContext)(Ct),n=jt(wt.innerBlockAreas.FILLED_CART),i=[["woocommerce/cart-items-block",{},[]],["woocommerce/cart-totals-block",{},[]]];return St({clientId:e,registeredBlocks:n,defaultTemplate:i}),(0,a.jsx)("div",{...t,hidden:"woocommerce/filled-cart-block"!==s,children:(0,a.jsx)(ft,{className:(0,r.A)("wc-block-cart",{"has-dark-controls":o}),children:(0,a.jsx)(c.InnerBlocks,{allowedBlocks:n,template:i,templateLock:"insert"})})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})})});var Et=s(4782);const Nt=(0,u.forwardRef)((({children:e,className:t=""},s)=>(0,a.jsx)("div",{ref:s,className:(0,r.A)("wc-block-components-main",t),children:e})));(0,d.registerBlockType)("woocommerce/cart-items-block",{icon:{src:(0,a.jsx)(l.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,c.useBlockProps)({className:"wc-block-cart__main"}),s=jt(wt.innerBlockAreas.CART_ITEMS),o=[["woocommerce/cart-line-items-block",{},[]],["woocommerce/cart-cross-sells-block",{},[]]];return St({clientId:e,registeredBlocks:s,defaultTemplate:o}),(0,a.jsx)(Nt,{...t,children:(0,a.jsx)(c.InnerBlocks,{allowedBlocks:s,template:o,templateLock:!1,renderAppender:c.InnerBlocks.ButtonBlockAppender})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})})});var Pt=s(8107),At=s(4347);const Rt=["BUTTON","FIELDSET","INPUT","OPTGROUP","OPTION","SELECT","TEXTAREA","A"],It=({children:e,style:t={},...s})=>{const o=(0,u.useRef)(null),r=()=>{o.current&&Pt.focus.focusable.find(o.current).forEach((e=>{Rt.includes(e.nodeName)&&e.setAttribute("tabindex","-1"),e.hasAttribute("contenteditable")&&e.setAttribute("contenteditable","false")}))},c=(0,At.YQ)(r,0,{leading:!0});return(0,u.useLayoutEffect)((()=>{let e;return r(),o.current&&(e=new window.MutationObserver(c),e.observe(o.current,{childList:!0,attributes:!0,subtree:!0})),()=>{e&&e.disconnect(),c.cancel()}}),[c]),(0,a.jsx)("div",{ref:o,"aria-disabled":"true",style:{userSelect:"none",pointerEvents:"none",cursor:"normal",...t},...s,children:e})};var Tt=s(195),Bt=s(8558);s(9959);const Mt=({className:e,quantity:t=1,minimum:s=1,maximum:c,onChange:n=()=>{},step:i=1,itemName:l="",disabled:d,editable:p})=>{const m=(0,r.A)("wc-block-components-quantity-selector",e),h=(0,u.useRef)(null),g=(0,u.useRef)(null),_=(0,u.useRef)(null),w=void 0!==c,k=!d&&t-i>=s,b=!d&&(!w||t+i<=c),y=(0,u.useCallback)((e=>{let t=e;w&&(t=Math.min(t,Math.floor(c/i)*i)),t=Math.max(t,Math.ceil(s/i)*i),t=Math.floor(t/i)*i,t!==e&&n(t)}),[w,c,s,n,i]),x=(0,At.YQ)(y,300);(0,u.useLayoutEffect)((()=>{y(t)}),[t,y]);const f=(0,u.useCallback)((e=>{const s=void 0!==typeof e.key?"ArrowDown"===e.key:e.keyCode===Bt.DOWN,o=void 0!==typeof e.key?"ArrowUp"===e.key:e.keyCode===Bt.UP;s&&k&&(e.preventDefault(),n(t-i)),o&&b&&(e.preventDefault(),n(t+i))}),[t,n,b,k,i]);return(0,a.jsxs)("div",{className:m,children:[(0,a.jsx)("input",{ref:h,className:"wc-block-components-quantity-selector__input",disabled:d,readOnly:!p,type:"number",step:i,min:s,max:c,value:t,onKeyDown:f,onChange:e=>{let s=parseInt(e.target.value,10);s=isNaN(s)?t:s,s!==t&&(n(s),x(s))},"aria-label":(0,o.sprintf)(/* translators: %s refers to the item name in the cart. */ /* translators: %s refers to the item name in the cart. */
(0,o.__)("Quantity of %s in your cart.","woocommerce"),l)}),p&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{ref:g,"aria-label":(0,o.sprintf)(/* translators: %s refers to the item name in the cart. */ /* translators: %s refers to the item name in the cart. */
(0,o.__)("Reduce quantity of %s","woocommerce"),l),className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--minus",disabled:!k,onClick:()=>{const e=t-i;n(e),(0,Tt.speak)((0,o.sprintf)(/* translators: %s refers to the item's new quantity in the cart. */ /* translators: %s refers to the item's new quantity in the cart. */
(0,o.__)("Quantity reduced to %s.","woocommerce"),e)),y(e)},children:"－"}),(0,a.jsx)("button",{ref:_,"aria-label":(0,o.sprintf)(/* translators: %s refers to the item's name in the cart. */ /* translators: %s refers to the item's name in the cart. */
(0,o.__)("Increase quantity of %s","woocommerce"),l),disabled:!b,className:"wc-block-components-quantity-selector__button wc-block-components-quantity-selector__button--plus",onClick:()=>{const e=t+i;n(e),(0,Tt.speak)((0,o.sprintf)(/* translators: %s refers to the item's new quantity in the cart. */ /* translators: %s refers to the item's new quantity in the cart. */
(0,o.__)("Quantity increased to %s.","woocommerce"),e)),y(e)},children:"＋"})]})]})},Ot=window.wc.blocksComponents,Dt=window.wc.priceFormat;s(8501);const Lt=({currency:e,maxPrice:t,minPrice:s,priceClassName:c,priceStyle:n={}})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"screen-reader-text",children:(0,o.sprintf)(/* translators: %1$s min price, %2$s max price */ /* translators: %1$s min price, %2$s max price */
(0,o.__)("Price between %1$s and %2$s","woocommerce"),(0,Dt.formatPrice)(s),(0,Dt.formatPrice)(t))}),(0,a.jsxs)("span",{"aria-hidden":!0,children:[(0,a.jsx)(Ot.FormattedMonetaryAmount,{className:(0,r.A)("wc-block-components-product-price__value",c),currency:e,value:s,style:n})," — ",(0,a.jsx)(Ot.FormattedMonetaryAmount,{className:(0,r.A)("wc-block-components-product-price__value",c),currency:e,value:t,style:n})]})]}),Ft=({currency:e,regularPriceClassName:t,regularPriceStyle:s,regularPrice:c,priceClassName:n,priceStyle:i,price:l})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"screen-reader-text",children:(0,o.__)("Previous price:","woocommerce")}),(0,a.jsx)(Ot.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,a.jsx)("del",{className:(0,r.A)("wc-block-components-product-price__regular",t),style:s,children:e}),value:c}),(0,a.jsx)("span",{className:"screen-reader-text",children:(0,o.__)("Discounted price:","woocommerce")}),(0,a.jsx)(Ot.FormattedMonetaryAmount,{currency:e,renderText:e=>(0,a.jsx)("ins",{className:(0,r.A)("wc-block-components-product-price__value","is-discounted",n),style:i,children:e}),value:l})]}),Vt=({align:e,className:t,currency:s,format:o="<price/>",maxPrice:c,minPrice:n,price:i,priceClassName:l,priceStyle:d,regularPrice:p,regularPriceClassName:m,regularPriceStyle:h,style:g})=>{const _=(0,r.A)(t,"price","wc-block-components-product-price",{[`wc-block-components-product-price--align-${e}`]:e});o.includes("<price/>")||(o="<price/>",console.error("Price formats need to include the `<price/>` tag."));const w=p&&i&&i<p;let k=(0,a.jsx)("span",{className:(0,r.A)("wc-block-components-product-price__value",l)});return w?k=(0,a.jsx)(Ft,{currency:s,price:i,priceClassName:l,priceStyle:d,regularPrice:p,regularPriceClassName:m,regularPriceStyle:h}):void 0!==n&&void 0!==c?k=(0,a.jsx)(Lt,{currency:s,maxPrice:c,minPrice:n,priceClassName:l,priceStyle:d}):i&&(k=(0,a.jsx)(Ot.FormattedMonetaryAmount,{className:(0,r.A)("wc-block-components-product-price__value",l),currency:s,value:i,style:d})),(0,a.jsx)("span",{className:_,style:g,children:(0,u.createInterpolateElement)(o,{price:k})})};s(959);const $t=({className:e="",disabled:t=!1,name:s,permalink:o="",target:c,rel:n,style:i,onClick:l,disabledTagName:d="span",...u})=>{const p=(0,r.A)("wc-block-components-product-name",e),m=d;if(t){const e=u;return(0,a.jsx)(m,{className:p,...e,dangerouslySetInnerHTML:{__html:s}})}return(0,a.jsx)("a",{className:p,href:o,target:c,...u,dangerouslySetInnerHTML:{__html:s},style:i})};var zt=s(6513);s(7605);const qt=({children:e,className:t})=>(0,a.jsx)("div",{className:(0,r.A)("wc-block-components-product-badge",t),children:e}),Ht=()=>(0,a.jsx)(qt,{className:"wc-block-components-product-backorder-badge",children:(0,o.__)("Available on backorder","woocommerce")}),Ut=({image:e={},fallbackAlt:t=""})=>{const s=e.thumbnail?{src:e.thumbnail,alt:(0,ie.decodeEntities)(e.alt)||t||"Product Image"}:{src:p.PLACEHOLDER_IMG_SRC,alt:""};return(0,a.jsx)("img",{...s,alt:s.alt})},Wt=({lowStockRemaining:e})=>e?(0,a.jsx)(qt,{className:"wc-block-components-product-low-stock-badge",children:(0,o.sprintf)(/* translators: %d stock amount (number of items in stock for product) */ /* translators: %d stock amount (number of items in stock for product) */
(0,o.__)("%d left in stock","woocommerce"),e)}):null;var Yt=s(7356);s(3692);const Gt=({details:e=[]})=>{if(!Array.isArray(e))return null;if(0===(e=e.filter((e=>!e.hidden))).length)return null;let t="ul",s="li";return 1===e.length&&(t="div",s="div"),(0,a.jsx)(t,{className:"wc-block-components-product-details",children:e.map((e=>{const t=e?.key||e.name||"",o=e?.className||(t?`wc-block-components-product-details__${(0,Yt.c)(t)}`:"");return(0,a.jsxs)(s,{className:o,children:[t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"wc-block-components-product-details__name",children:[(0,ie.decodeEntities)(t),":"]})," "]}),(0,a.jsx)("span",{className:"wc-block-components-product-details__value",children:(0,ie.decodeEntities)(e.display||e.value)})]},t+(e.display||e.value))}))})};var Qt=s(3240),Kt=s.n(Qt);const Zt=["a","b","em","i","strong","p","br"],Jt=["target","href","rel","name","download"],Xt=(e,t)=>{const s=t?.tags||Zt,o=t?.attr||Jt;return Kt().sanitize(e,{ALLOWED_TAGS:s,ALLOWED_ATTR:o})},es=window.wp.autop,ts=e=>e.replace(/<\/?[a-z][^>]*?>/gi,""),ss=(e,t)=>e.replace(/[\s|\.\,]+$/i,"")+t,os=window.wp.wordcount,rs=["a","b","em","i","strong","p","br","ul","ol","li","h1","h2","h3","h4","h5","h6","pre","blockquote","img"],cs=["target","href","rel","name","download","src","class","alt","style"],ns=({source:e,maxLength:t=15,countType:s="words",className:o="",style:r={}})=>{const c=(0,u.useMemo)((()=>((e,t=15,s="words")=>{const o=(0,es.autop)(e);if((0,os.count)(o,s)<=t)return o;const r=(e=>{const t=e.indexOf("</p>");return-1===t?e:e.substr(0,t+4)})(o);return(0,os.count)(r,s)<=t?r:"words"===s?((e,t,s="&hellip;",o=!0)=>{const r=ts(e),c=r.split(" ").splice(0,t).join(" ");return c===r?o?(0,es.autop)(r):r:o?(0,es.autop)(ss(c,s)):ss(c,s)})(r,t):((e,t,s=!0,o="&hellip;",r=!0)=>{const c=ts(e),n=c.slice(0,t);if(n===c)return r?(0,es.autop)(c):c;if(s)return(0,es.autop)(ss(n,o));const a=n.match(/([\s]+)/g),i=a?a.length:0,l=c.slice(0,t+i);return r?(0,es.autop)(ss(l,o)):ss(l,o)})(r,t,"characters_including_spaces"===s)})(e,t,s)),[e,t,s]);return(0,a.jsx)(u.RawHTML,{style:r,className:o,children:Xt(c,{tags:rs,attr:cs})})},as=({className:e,shortDescription:t="",fullDescription:s=""})=>{const o=t||s;return o?(0,a.jsx)(ns,{className:e,source:o,maxLength:15,countType:m.wordCountType||"words"}):null};s(8879);const is=({shortDescription:e="",fullDescription:t="",itemData:s=[],variation:o=[]})=>(0,a.jsxs)("div",{className:"wc-block-components-product-metadata",children:[(0,a.jsx)(as,{className:"wc-block-components-product-metadata__description",shortDescription:e,fullDescription:t}),(0,a.jsx)(Gt,{details:s}),(0,a.jsx)(Gt,{details:o.map((({attribute:e="",value:t})=>({key:e,value:t})))})]}),ls=({currency:e,saleAmount:t,format:s="<price/>"})=>{if(!t||t<=0)return null;s.includes("<price/>")||(s="<price/>",console.error("Price formats need to include the `<price/>` tag."));const r=(0,o.sprintf)(/* translators: %s will be replaced by the discount amount */ /* translators: %s will be replaced by the discount amount */
(0,o.__)("Save %s","woocommerce"),s);return(0,a.jsx)(qt,{className:"wc-block-components-sale-badge",children:(0,u.createInterpolateElement)(r,{price:(0,a.jsx)(Ot.FormattedMonetaryAmount,{currency:e,value:t})})})},ds=(e,t)=>e.convertPrecision(t.minorUnit).getAmount(),us=(0,u.forwardRef)((({lineItem:e,onRemove:t=()=>{},tabIndex:s},c)=>{const{name:n="",catalog_visibility:i="visible",short_description:l="",description:d="",low_stock_remaining:m=null,show_backorder_badge:h=!1,quantity_limits:g={minimum:1,maximum:99,multiple_of:1,editable:!0},sold_individually:_=!1,permalink:w="",images:k=[],variation:b=[],item_data:y=[],prices:x={currency_code:"USD",currency_minor_unit:2,currency_symbol:"$",currency_prefix:"$",currency_suffix:"",currency_decimal_separator:".",currency_thousand_separator:",",price:"0",regular_price:"0",sale_price:"0",price_range:null,raw_prices:{precision:6,price:"0",regular_price:"0",sale_price:"0"}},totals:f={currency_code:"USD",currency_minor_unit:2,currency_symbol:"$",currency_prefix:"$",currency_suffix:"",currency_decimal_separator:".",currency_thousand_separator:",",line_subtotal:"0",line_subtotal_tax:"0"},extensions:v}=e,{quantity:j,setItemQuantity:S,removeItem:C,isPendingDelete:E}=(e=>{const t={key:"",quantity:1};(e=>(0,oe.isObject)(e)&&(0,oe.objectHasProp)(e,"key")&&(0,oe.objectHasProp)(e,"quantity")&&(0,oe.isString)(e.key)&&(0,oe.isNumber)(e.quantity))(e)&&(t.key=e.key,t.quantity=e.quantity);const{key:s="",quantity:o=1}=t,{cartErrors:r}=je(),{__internalStartCalculation:c,__internalFinishCalculation:n}=(0,B.useDispatch)(F.checkoutStore),[a,i]=(0,u.useState)(o),[l]=(0,At.d7)(a,400),d=ze(l),{removeItemFromCart:p,changeCartItemQuantity:m}=(0,B.useDispatch)(F.cartStore);(0,u.useEffect)((()=>i(o)),[o]);const h=(0,B.useSelect)((e=>{if(!s)return{quantity:!1,delete:!1};const t=e(F.cartStore);return{quantity:t.isItemPendingQuantity(s),delete:t.isItemPendingDelete(s)}}),[s]),g=(0,u.useCallback)((()=>s?p(s).catch((e=>{(0,F.processErrorResponse)(e)})):Promise.resolve(!1)),[s,p]);return(0,u.useEffect)((()=>{s&&(0,oe.isNumber)(d)&&Number.isFinite(d)&&d!==l&&m(s,l).catch((e=>{(0,F.processErrorResponse)(e)}))}),[s,m,l,d]),(0,u.useEffect)((()=>(h.delete?c():n(),()=>{h.delete&&n()})),[n,c,h.delete]),(0,u.useEffect)((()=>(h.quantity||l!==a?c():n(),()=>{(h.quantity||l!==a)&&n()})),[c,n,h.quantity,l,a]),{isPendingDelete:h.delete,quantity:a,setItemQuantity:i,removeItem:g,cartItemQuantityErrors:r}})(e),{dispatchStoreEvent:N}=Be(),{receiveCart:P,...A}=je(),R=(0,u.useMemo)((()=>({context:"cart",cartItem:e,cart:A})),[e,A]),I=(0,Dt.getCurrencyFromPriceResponse)(x),T=(0,wt.applyCheckoutFilter)({filterName:"itemName",defaultValue:n,extensions:v,arg:R}),M=(0,zt.A)({amount:parseInt(x.raw_prices.regular_price,10),precision:x.raw_prices.precision}),O=(0,zt.A)({amount:parseInt(x.raw_prices.price,10),precision:x.raw_prices.precision}),D=M.subtract(O),L=D.multiply(j),V=(0,Dt.getCurrencyFromPriceResponse)(f);let $=parseInt(f.line_subtotal,10);(0,p.getSetting)("displayCartPricesIncludingTax",!1)&&($+=parseInt(f.line_subtotal_tax,10));const z=(0,zt.A)({amount:$,precision:V.minorUnit}),q=k.length?k[0]:{},H="hidden"===i||"search"===i,U=(0,wt.applyCheckoutFilter)({filterName:"cartItemClass",defaultValue:"",extensions:v,arg:R}),W=(0,wt.applyCheckoutFilter)({filterName:"cartItemPrice",defaultValue:"<price/>",extensions:v,arg:R,validation:wt.productPriceValidation}),Y=(0,wt.applyCheckoutFilter)({filterName:"subtotalPriceFormat",defaultValue:"<price/>",extensions:v,arg:R,validation:wt.productPriceValidation}),G=(0,wt.applyCheckoutFilter)({filterName:"saleBadgePriceFormat",defaultValue:"<price/>",extensions:v,arg:R,validation:wt.productPriceValidation}),Q=(0,wt.applyCheckoutFilter)({filterName:"showRemoveItemLink",defaultValue:!0,extensions:v,arg:R});return(0,a.jsxs)("tr",{className:(0,r.A)("wc-block-cart-items__row",U,{"is-disabled":E}),ref:c,tabIndex:s,children:[(0,a.jsx)("td",{className:"wc-block-cart-item__image","aria-hidden":!(0,oe.objectHasProp)(q,"alt")||!q.alt,children:H?(0,a.jsx)(Ut,{image:q,fallbackAlt:T}):(0,a.jsx)("a",{href:w,tabIndex:-1,children:(0,a.jsx)(Ut,{image:q,fallbackAlt:T})})}),(0,a.jsx)("td",{className:"wc-block-cart-item__product",children:(0,a.jsxs)("div",{className:"wc-block-cart-item__wrap",children:[(0,a.jsx)($t,{disabled:E||H,name:T,permalink:w}),h?(0,a.jsx)(Ht,{}):!!m&&(0,a.jsx)(Wt,{lowStockRemaining:m}),(0,a.jsx)("div",{className:"wc-block-cart-item__prices",children:(0,a.jsx)(Vt,{currency:I,regularPrice:ds(M,I),price:ds(O,I),format:Y})}),(0,a.jsx)(ls,{currency:I,saleAmount:ds(D,I),format:G}),(0,a.jsx)(is,{shortDescription:l,fullDescription:d,itemData:y,variation:b}),(0,a.jsxs)("div",{className:"wc-block-cart-item__quantity",children:[!_&&(0,a.jsx)(Mt,{disabled:E,editable:g.editable,quantity:j,minimum:g.minimum,maximum:g.maximum,step:g.multiple_of,onChange:t=>{S(t),N("cart-set-item-quantity",{product:e,quantity:t})},itemName:T}),Q&&(0,a.jsx)("button",{className:"wc-block-cart-item__remove-link","aria-label":(0,o.sprintf)(/* translators: %s refers to the item's name in the cart. */ /* translators: %s refers to the item's name in the cart. */
(0,o.__)("Remove %s from cart","woocommerce"),T),onClick:()=>{t(),C(),N("cart-remove-item",{product:e,quantity:j}),(0,Tt.speak)((0,o.sprintf)(/* translators: %s refers to the item name in the cart. */ /* translators: %s refers to the item name in the cart. */
(0,o.__)("%s has been removed from your cart.","woocommerce"),T))},disabled:E,children:(0,o.__)("Remove item","woocommerce")})]})]})}),(0,a.jsx)("td",{className:"wc-block-cart-item__total",children:(0,a.jsxs)("div",{className:"wc-block-cart-item__total-price-and-sale-badge-wrapper",children:[(0,a.jsx)(Vt,{currency:V,format:W,price:z.getAmount()}),j>1&&(0,a.jsx)(ls,{currency:I,saleAmount:ds(L,I),format:G})]})})]})})),ps=us;s(359);const ms=[...Array(3)].map(((_x,e)=>(0,a.jsx)(ps,{lineItem:{}},e))),hs=e=>{const t={};return e.forEach((({key:e})=>{t[e]=(0,u.createRef)()})),t},gs=({lineItems:e=[],isLoading:t=!1,className:s})=>{const c=(0,u.useRef)(null),n=(0,u.useRef)(hs(e));(0,u.useEffect)((()=>{n.current=hs(e)}),[e]);const i=e=>()=>{n?.current&&e&&n.current[e].current instanceof HTMLElement?n.current[e].current.focus():c.current instanceof HTMLElement&&c.current.focus()},l=t?ms:e.map(((t,s)=>{const o=e.length>s+1?e[s+1].key:null;return(0,a.jsx)(ps,{lineItem:t,onRemove:i(o),ref:n.current[t.key],tabIndex:-1},t.key)}));return(0,a.jsxs)("table",{className:(0,r.A)("wc-block-cart-items",s),ref:c,tabIndex:-1,children:[(0,a.jsx)("caption",{className:"screen-reader-text",children:(0,a.jsx)("h2",{children:(0,o.__)("Products in cart","woocommerce")})}),(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"wc-block-cart-items__header",children:[(0,a.jsx)("th",{className:"wc-block-cart-items__header-image",children:(0,a.jsx)("span",{children:(0,o.__)("Product","woocommerce")})}),(0,a.jsx)("th",{className:"wc-block-cart-items__header-product",children:(0,a.jsx)("span",{children:(0,o.__)("Details","woocommerce")})}),(0,a.jsx)("th",{className:"wc-block-cart-items__header-total",children:(0,a.jsx)("span",{children:(0,o.__)("Total","woocommerce")})})]})}),(0,a.jsx)("tbody",{children:l})]})},_s=({className:e})=>{const{cartItems:t,cartIsLoading:s}=je();return(0,a.jsx)(gs,{className:e,lineItems:t,isLoading:s})};(0,d.registerBlockType)("woocommerce/cart-line-items-block",{icon:{src:(0,a.jsx)(l.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsx)("div",{...s,children:(0,a.jsx)(It,{children:(0,a.jsx)(_s,{className:t})})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})}),(0,d.registerBlockType)("woocommerce/cart-cross-sells-block",{icon:{src:(0,a.jsx)(l.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:()=>{const e=(0,c.useBlockProps)({className:"wc-block-cart__cross-sells"}),t=[["core/heading",{content:(0,o.__)("You may be interested in…","woocommerce"),level:2,fontSize:"large"},[]],["woocommerce/cart-cross-sells-products-block",{},[]]];return(0,a.jsx)("div",{...e,children:(0,a.jsx)(c.InnerBlocks,{template:t,templateLock:!1})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})})});const ws=window.wp.components,ks=window.wc.wcBlocksSharedContext,bs=window.wp.styleEngine;function ys(e={}){const t={};return(0,bs.getCSSRules)(e,{selector:""}).forEach((e=>{t[e.key]=e.value})),t}function xs(e,t){return e&&t?`has-${(0,Yt.c)(t)}-${e}`:""}const fs=e=>{const t=(e=>{const t=(0,oe.isObject)(e)?e:{style:{}};let s=t.style;return(0,oe.isString)(s)&&(s=JSON.parse(s)||{}),(0,oe.isObject)(s)||(s={}),{...t,style:s}})(e),s=function(e){const{backgroundColor:t,textColor:s,gradient:o,style:c}=e,n=xs("background-color",t),a=xs("color",s),i=function(e){if(e)return`has-${e}-gradient-background`}(o),l=i||c?.color?.gradient;return{className:(0,r.A)(a,i,{[n]:!l&&!!n,"has-text-color":s||c?.color?.text,"has-background":t||c?.color?.background||o||c?.color?.gradient,"has-link-color":(0,oe.isObject)(c?.elements?.link)?c?.elements?.link?.color:void 0}),style:ys({color:c?.color||{}})}}(t),o=function(e){const t=e.style?.border||{},s=function(e){const{borderColor:t,style:s}=e,o=t?xs("border-color",t):"";return(0,r.A)({"has-border-color":!!t||!!s?.border?.color,[o]:!!o})}(e);return{className:s,style:ys({border:t})}}(t),c=function(e){return{className:void 0,style:ys({spacing:e.style?.spacing||{}})}}(t),n=(e=>{const t=(0,oe.isObject)(e.style.typography)?e.style.typography:{},s=(0,oe.isString)(t.fontFamily)?t.fontFamily:"";return{className:e.fontFamily?`has-${e.fontFamily}-font-family`:s,style:{fontSize:e.fontSize?`var(--wp--preset--font-size--${e.fontSize})`:t.fontSize,fontStyle:t.fontStyle,fontWeight:t.fontWeight,letterSpacing:t.letterSpacing,lineHeight:t.lineHeight,textDecoration:t.textDecoration,textTransform:t.textTransform}}})(t);return{className:(0,r.A)(n.className,s.className,o.className,c.className),style:{...n.style,...s.style,...o.style,...c.style}}},vs=window.wc.wcBlocksSharedHocs;s(4313);const js=(0,vs.withProductDataContext)((e=>{const{className:t,align:s}=e,c=fs(e),{parentClassName:n}=(0,ks.useInnerBlockLayoutContext)(),{product:i}=(0,ks.useProductDataContext)();if(!(i.id&&i.on_sale||e.isDescendentOfSingleProductTemplate))return null;const l="string"==typeof s?`wc-block-components-product-sale-badge--align-${s}`:"";return(0,a.jsx)("div",{className:(0,r.A)("wc-block-components-product-sale-badge",t,l,{[`${n}__product-onsale`]:n},c.className),style:c.style,children:(0,a.jsx)(Ot.Label,{label:(0,o.__)("Sale","woocommerce"),screenReaderLabel:(0,o.__)("Product on sale","woocommerce")})})}));s(1189);let Ss=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});const Cs=e=>(0,a.jsx)("img",{...e,src:p.PLACEHOLDER_IMG_SRC,alt:e.alt,width:void 0,height:void 0}),Es=({image:e,loaded:t,showFullSize:s,fallbackAlt:o,width:r,scale:c,height:n,aspectRatio:i})=>{const{thumbnail:l,src:d,srcset:u,sizes:p,alt:m}=e||{},h={alt:m||o,hidden:!t,src:l,...s&&{src:d,srcSet:u,sizes:p}},g={height:n,width:r,objectFit:c,aspectRatio:i};return(0,a.jsxs)(a.Fragment,{children:[h.src&&(0,a.jsx)("img",{style:g,"data-testid":"product-image",...h}),!e&&(0,a.jsx)(Cs,{style:g,alt:h.alt})]})},Ns=e=>{const{className:t,imageSizing:s=Ss.SINGLE,showProductLink:c=!0,showSaleBadge:n,saleBadgeAlign:i="right",height:l,width:d,scale:p,aspectRatio:m,style:h,...g}=e,_=fs(e),{parentClassName:w}=(0,ks.useInnerBlockLayoutContext)(),{product:k,isLoading:b}=(0,ks.useProductDataContext)(),{dispatchStoreEvent:y}=Be();if(!k.id)return(0,a.jsx)("div",{className:(0,r.A)(t,"wc-block-components-product-image",{[`${w}__product-image`]:w},_.className),style:_.style,children:(0,a.jsx)(Cs,{})});const x=!!k.images.length,f=x?k.images[0]:null,v=c?"a":u.Fragment,j=(0,o.sprintf)(/* translators: %s is referring to the product name */ /* translators: %s is referring to the product name */
(0,o.__)("Link to %s","woocommerce"),k.name),S={href:k.permalink,...!x&&{"aria-label":j},onClick:()=>{y("product-view-link",{product:k})}};return(0,a.jsx)("div",{className:(0,r.A)(t,"wc-block-components-product-image",{[`${w}__product-image`]:w},_.className),style:_.style,children:(0,a.jsxs)(v,{...c&&S,children:[!!n&&(0,a.jsx)(js,{align:i,...g}),(0,a.jsx)(Es,{fallbackAlt:(0,ie.decodeEntities)(k.name),image:f,loaded:!b,showFullSize:s!==Ss.THUMBNAIL,width:d,height:l,scale:p,aspectRatio:(0,oe.objectHasProp)(h,"dimensions")&&(0,oe.objectHasProp)(h.dimensions,"aspectRatio")&&(0,oe.isString)(h.dimensions.aspectRatio)?h.dimensions.aspectRatio:m})]})})};(0,vs.withProductDataContext)(Ns),s(7578);const Ps=({children:e,headingLevel:t,elementType:s=`h${t}`,...o})=>(0,a.jsx)(s,{...o,children:e}),As=e=>{const{className:t,headingLevel:s=2,showProductLink:o=!0,linkTarget:c,align:n}=e,i=fs(e),{parentClassName:l}=(0,ks.useInnerBlockLayoutContext)(),{product:d}=(0,ks.useProductDataContext)(),{dispatchStoreEvent:u}=Be();return d.id?(0,a.jsx)(Ps,{headingLevel:s,className:(0,r.A)(t,i.className,"wc-block-components-product-title",{[`${l}__product-title`]:l,[`wc-block-components-product-title--align-${n}`]:n}),style:i.style,children:(0,a.jsx)($t,{disabled:!o,name:d.name,permalink:d.permalink,target:c,onClick:()=>{u("product-view-link",{product:d})}})}):(0,a.jsx)(Ps,{headingLevel:s,className:(0,r.A)(t,i.className,"wc-block-components-product-title",{[`${l}__product-title`]:l,[`wc-block-components-product-title--align-${n}`]:n}),style:i.style})},Rs=((0,vs.withProductDataContext)(As),e=>({width:e/5*100+"%"})),Is=({className:e,parentClassName:t})=>{const s=Rs(0);return(0,a.jsxs)("div",{className:(0,r.A)(`${e}__norating-container`,`${t}-product-rating__norating-container`),children:[(0,a.jsx)("div",{className:`${e}__norating`,role:"img",children:(0,a.jsx)("span",{style:s})}),(0,a.jsx)("span",{children:(0,o.__)("No Reviews","woocommerce")})]})},Ts=e=>{const{className:t,rating:s,reviews:c,parentClassName:n}=e,i=Rs(s),l=(0,o.sprintf)(/* translators: %f is referring to the average rating value */ /* translators: %f is referring to the average rating value */
(0,o.__)("Rated %f out of 5","woocommerce"),s),d={__html:(0,o.sprintf)(/* translators: %1$s is referring to the average rating value, %2$s is referring to the number of ratings */ /* translators: %1$s is referring to the average rating value, %2$s is referring to the number of ratings */
(0,o._n)("Rated %1$s out of 5 based on %2$s customer rating","Rated %1$s out of 5 based on %2$s customer ratings",c,"woocommerce"),(0,o.sprintf)('<strong class="rating">%f</strong>',s),(0,o.sprintf)('<span class="rating">%d</span>',c))};return(0,a.jsx)("div",{className:(0,r.A)(`${t}__stars`,`${n}__product-rating__stars`),role:"img","aria-label":l,children:(0,a.jsx)("span",{style:i,dangerouslySetInnerHTML:d})})},Bs=e=>{const{className:t,reviews:s}=e,r=(0,o.sprintf)(/* translators: %s is referring to the total of reviews for a product */ /* translators: %s is referring to the total of reviews for a product */
(0,o._n)("(%s customer review)","(%s customer reviews)",s,"woocommerce"),s);return(0,a.jsx)("span",{className:`${t}__reviews_count`,children:r})},Ms=e=>{const{className:t="wc-block-components-product-rating",showReviewCount:s,showMockedReviews:o,parentClassName:c="",rating:n,reviews:i,styleProps:l,textAlign:d}=e,u=(0,r.A)(l.className,t,{[`${c}__product-rating`]:c,[`has-text-align-${d}`]:d}),p=o&&(0,a.jsx)(Is,{className:t,parentClassName:c}),m=i?(0,a.jsx)(Ts,{className:t,rating:n,reviews:i,parentClassName:c}):p,h=i&&s;return(0,a.jsx)("div",{className:u,style:l.style,children:(0,a.jsxs)("div",{className:`${t}__container`,children:[m,h?(0,a.jsx)(Bs,{className:t,reviews:i}):null]})})};s(7545);const Os=e=>{const{textAlign:t="",shouldDisplayMockedReviewsWhenProductHasNoReviews:s}=e,o=fs(e),{parentClassName:r}=(0,ks.useInnerBlockLayoutContext)(),{product:c}=(0,ks.useProductDataContext)(),n=(e=>{const t=parseFloat(e.average_rating);return Number.isFinite(t)&&t>0?t:0})(c),i=(e=>{const t=(0,oe.isNumber)(e.review_count)?e.review_count:parseInt(e.review_count,10);return Number.isFinite(t)&&t>0?t:0})(c);return(0,a.jsx)(Ms,{className:"wc-block-components-product-rating-stars",showMockedReviews:s,styleProps:o,parentClassName:r,reviews:i,rating:n,textAlign:t})},Ds=((0,vs.withProductDataContext)(Os),e=>{const{className:t,textAlign:s,isDescendentOfSingleProductTemplate:o}=e,c=fs(e),{parentName:n,parentClassName:i}=(0,ks.useInnerBlockLayoutContext)(),{product:l}=(0,ks.useProductDataContext)(),d="woocommerce/all-products"===n,u=o&&!("woocommerce/add-to-cart-with-options-grouped-product-selector-item"===n),p=(0,r.A)("wc-block-components-product-price",t,c.className,{[`${i}__product-price`]:i});if(!l.id&&!o){const e=(0,a.jsx)(Vt,{align:s,className:p});return d?(0,a.jsx)("div",{className:"wp-block-woocommerce-product-price",children:e}):e}const m=l.prices,h=u?(0,Dt.getCurrencyFromPriceResponse)():(0,Dt.getCurrencyFromPriceResponse)(m),g="5000",_=m.price!==m.regular_price,w=(0,r.A)({[`${i}__product-price__value`]:i,[`${i}__product-price__value--on-sale`]:_}),k=(0,a.jsx)(Vt,{align:s,className:p,style:c.style,regularPriceStyle:c.style,priceStyle:c.style,priceClassName:w,currency:h,price:u?g:m.price,minPrice:m?.price_range?.min_amount,maxPrice:m?.price_range?.max_amount,regularPrice:u?g:m.regular_price,regularPriceClassName:(0,r.A)({[`${i}__product-price__regular`]:i})});return d?(0,a.jsx)("div",{className:"wp-block-woocommerce-product-price",children:k}):k}),Ls=(e,t)=>{const s=e.find((({id:e})=>e===t));return s?s.quantity:0};s(7316);const Fs="woocommerce/product-type-template-state",Vs="SWITCH_PRODUCT_TYPE",$s="SET_PRODUCT_TYPES",zs="REGISTER_LISTENER",qs="UNREGISTER_LISTENER",Hs=(0,p.getSetting)("productTypes",{}),Us=Object.keys(Hs).map((e=>({slug:e,label:Hs[e]}))),Ws={productTypes:{list:Us,current:Us[0]?.slug},listeners:[]},Ys={switchProductType:e=>({type:Vs,current:e}),setProductTypes:e=>({type:$s,productTypes:e}),registerListener:e=>({type:zs,listener:e}),unregisterListener:e=>({type:qs,listener:e})},Gs=(0,B.createReduxStore)(Fs,{reducer:(e=Ws,t)=>{switch(t.type){case $s:return{...e,productTypes:{...e.productTypes,list:t.productTypes||[]}};case Vs:return{...e,productTypes:{...e.productTypes,current:t.current}};case zs:return{...e,listeners:[...e.listeners,t.listener||""]};case qs:return{...e,listeners:e.listeners.filter((e=>e!==t.listener))};default:return e}},actions:Ys,selectors:{getProductTypes:e=>e.productTypes.list,getCurrentProductType:e=>e.productTypes.list.find((t=>t.slug===e.productTypes.current)),getRegisteredListeners:e=>e.listeners}});(0,B.select)(Fs)||(0,B.register)(Gs);const Qs=({product:e,isDescendantOfAddToCartWithOptions:t,className:s,style:c})=>{const{id:n,permalink:i,add_to_cart:l,has_options:d,is_purchasable:m,is_in_stock:h}=e,{dispatchStoreEvent:g}=Be(),{cartQuantity:_,addingToCart:w,addToCart:b}=(e=>{const{addItemToCart:t}=(0,B.useDispatch)(F.cartStore),{cartItems:s,cartIsLoading:o}=je(),{createErrorNotice:r,removeNotice:c}=(0,B.useDispatch)("core/notices"),[n,a]=(0,u.useState)(!1),i=(0,u.useRef)(Ls(s,e));return(0,u.useEffect)((()=>{const t=Ls(s,e);t!==i.current&&(i.current=t)}),[s,e]),{cartQuantity:Number.isFinite(i.current)?i.current:0,addingToCart:n,cartIsLoading:o,addToCart:(s=1)=>(a(!0),t(e,s).then((()=>{c("add-to-cart")})).catch((e=>{r((0,ie.decodeEntities)(e.message),{id:"add-to-cart",context:"wc/all-products",isDismissible:!0})})).finally((()=>{a(!1)})))}})(n),y=Number.isFinite(_)&&_>0,x=!d&&m&&h,f=(0,ie.decodeEntities)(l?.description||""),v=(({cartQuantity:e,productCartDetails:t,isDescendantOfAddToCartWithOptions:s})=>Number.isFinite(e)&&e>0?(0,o.sprintf)(/* translators: %s number of products in cart. */ /* translators: %s number of products in cart. */
(0,o._n)("%d in cart","%d in cart",e,"woocommerce"),e):s&&t?.single_text?t?.single_text:t?.text||(0,o.__)("Add to cart","woocommerce"))({cartQuantity:_,productCartDetails:l,isDescendantOfAddToCartWithOptions:t}),j=x?"button":"a",S={};return x?S.onClick=async()=>{await b(),g("cart-add-item",{product:e});const{cartRedirectAfterAdd:t}=(0,p.getSetting)("productsSettings");t&&(window.location.href=k)}:(S.href=i,S.rel="nofollow",S.onClick=()=>{g("product-view-link",{product:e})}),(0,a.jsx)(j,{...S,"aria-label":f,disabled:w,className:(0,r.A)(s,"wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",{loading:w,added:y}),style:c,children:v})},Ks=({className:e,style:t})=>(0,a.jsx)("button",{className:(0,r.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button","wc-block-components-product-button__button--placeholder",e),style:t,disabled:!0,children:(0,o.__)("Add to cart","woocommerce")}),Zs=({className:e,style:t,blockClientId:s})=>{const{current:c,registerListener:n,unregisterListener:i}=function(){const{productTypes:e,current:t,registeredListeners:s}=(0,B.useSelect)((e=>{const{getProductTypes:t,getCurrentProductType:s,getRegisteredListeners:o}=e(Gs);return{productTypes:t(),current:s(),registeredListeners:o()}}),[]),{switchProductType:o,registerListener:r,unregisterListener:c}=(0,B.useDispatch)(Gs);return{productTypes:e,current:t,set:o,registeredListeners:s,registerListener:r,unregisterListener:c}}();(0,u.useEffect)((()=>{if(s)return n(s),()=>{i(s)}}),[s,n,i]);const l="external"===c?.slug?(0,o.__)("Buy product","woocommerce"):(0,o.__)("Add to cart","woocommerce");return(0,a.jsx)("button",{className:(0,r.A)("wp-block-button__link","wp-element-button","add_to_cart_button","wc-block-components-product-button__button",e),style:t,disabled:!0,children:l})},Js=e=>{const{className:t,textAlign:s,blockClientId:o}=e,c=fs(e),{parentClassName:n}=(0,ks.useInnerBlockLayoutContext)(),{isLoading:i,product:l}=(0,ks.useProductDataContext)();return(0,a.jsx)("div",{className:(0,r.A)(t,"wp-block-button","wc-block-components-product-button",{[`${n}__product-add-to-cart`]:n,[`align-${s}`]:s}),children:i?(0,a.jsx)(Ks,{className:c.className,style:c.style}):(0,a.jsx)(a.Fragment,{children:l.id?(0,a.jsx)(Qs,{product:l,style:c.style,className:c.className,isDescendantOfAddToCartWithOptions:e["woocommerce/isDescendantOfAddToCartWithOptions"]}):(0,a.jsx)(Zs,{style:c.style,className:c.className,isLoading:i,blockClientId:o})})})},Xs=((0,vs.withProductDataContext)(Js),({product:e})=>(0,a.jsx)("div",{className:"cross-sells-product",children:(0,a.jsx)(ks.InnerBlockLayoutContextProvider,{parentName:"woocommerce/cart-cross-sells-block",parentClassName:"wp-block-cart-cross-sells-product",children:(0,a.jsxs)(ks.ProductDataContextProvider,{isLoading:!1,product:e,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(Ns,{className:"",showSaleBadge:!0,productId:e.id,showProductLink:!0,saleBadgeAlign:"left",imageSizing:Ss.SINGLE,isDescendentOfQueryLoop:!1,scale:"cover",aspectRatio:"1:1"}),(0,a.jsx)(As,{align:"",headingLevel:3,showProductLink:!0}),(0,a.jsx)(Os,{isDescendentOfQueryLoop:!1,isDescendentOfSingleProductBlock:!1,productId:e.id,postId:0,shouldDisplayMockedReviewsWhenProductHasNoReviews:!1}),(0,a.jsx)(Ds,{})]}),(0,a.jsx)(Js,{})]})})})),eo=({products:e,columns:t})=>{const s=e.map(((e,s)=>s>=t?null:(0,a.jsx)(Xs,{isLoading:!1,product:e},e.id)));return(0,a.jsx)("div",{children:s})},to={A:3},so=({className:e,columns:t})=>{const{crossSellsProducts:s}=je();return void 0===t&&(t=to.A),(0,a.jsx)(eo,{className:e,columns:t,products:s})};s(535),s(7128),(0,d.registerBlockType)("woocommerce/cart-cross-sells-products-block",{icon:{src:(0,a.jsx)(l.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{className:s,columns:r}=e,n=(0,c.useBlockProps)();return(0,a.jsxs)("div",{...n,children:[(0,a.jsx)(c.InspectorControls,{children:(0,a.jsx)(ws.PanelBody,{title:(0,o.__)("Settings","woocommerce"),children:(0,a.jsx)(ws.RangeControl,{label:(0,o.__)("Cross-Sells products to show","woocommerce"),value:r,onChange:e=>t({columns:e}),min:(0,p.getSetting)("minColumns",1),max:(0,p.getSetting)("maxColumns",6)})})}),(0,a.jsx)(It,{children:(0,a.jsx)(so,{columns:r,className:s})})]})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});const oo=(0,u.forwardRef)((({children:e,className:t=""},s)=>(0,a.jsx)("div",{ref:s,className:(0,r.A)("wc-block-components-sidebar",t),children:e})));function ro(e){const t=(0,u.useRef)(e);return Ie()(e,t.current)||(t.current=e),t.current}s(5675),(0,d.registerBlockType)("woocommerce/cart-totals-block",{icon:{src:(0,a.jsx)(l.A,{icon:Et.A,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,c.useBlockProps)({className:"wc-block-cart__sidebar"}),s=jt(wt.innerBlockAreas.CART_TOTALS),o=[["woocommerce/cart-order-summary-block",{},[]],["woocommerce/cart-express-payment-block",{},[]],["woocommerce/proceed-to-checkout-block",{},[]],["woocommerce/cart-accepted-payment-methods-block",{},[]]];return St({clientId:e,registeredBlocks:s,defaultTemplate:o}),(0,a.jsx)(oo,{...t,children:(0,a.jsx)(c.InnerBlocks,{allowedBlocks:s,template:o,templateLock:!1,renderAppender:c.InnerBlocks.ButtonBlockAppender})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})})});const co=(e=!1)=>{const{paymentMethodsInitialized:t,expressPaymentMethodsInitialized:s,availablePaymentMethods:o,availableExpressPaymentMethods:r}=(0,B.useSelect)((e=>{const t=e(F.paymentStore);return{paymentMethodsInitialized:t.paymentMethodsInitialized(),expressPaymentMethodsInitialized:t.expressPaymentMethodsInitialized(),availableExpressPaymentMethods:t.getAvailableExpressPaymentMethods(),availablePaymentMethods:t.getAvailablePaymentMethods()}})),c=Object.values(o).map((({name:e})=>e)),n=Object.values(r).map((({name:e})=>e)),a=(0,Xe.getPaymentMethods)(),i=(0,Xe.getExpressPaymentMethods)(),l=Object.keys(a).reduce(((e,t)=>(c.includes(t)&&(e[t]=a[t]),e)),{}),d=Object.keys(i).reduce(((e,t)=>(n.includes(t)&&(e[t]=i[t]),e)),{}),u=ro(l),p=ro(d);return{paymentMethods:e?p:u,isInitialized:e?s:t}},no=()=>co(!0);var ao=s(7035);const io={warning:"#F0B849",error:"#CC1818",success:"#46B450",info:"#0073AA"},lo=({status:e="warning",...t})=>(0,a.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",...t,children:[(0,a.jsx)("path",{d:"M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z",stroke:io[e],strokeWidth:"1.5"}),(0,a.jsx)("path",{d:"M13 7H11V13H13V7Z",fill:io[e]}),(0,a.jsx)("path",{d:"M13 15H11V17H13V15Z",fill:io[e]})]});s(4459);const uo=({href:e,title:t,description:s,warning:r})=>(0,a.jsxs)("a",{href:e,className:"wc-block-editor-components-external-link-card",target:"_blank",rel:"noreferrer",children:[(0,a.jsxs)("span",{className:"wc-block-editor-components-external-link-card__content",children:[(0,a.jsx)("strong",{className:"wc-block-editor-components-external-link-card__title",children:t}),s&&(0,a.jsx)("span",{className:"wc-block-editor-components-external-link-card__description",dangerouslySetInnerHTML:{__html:Xt(s)}}),r?(0,a.jsxs)("span",{className:"wc-block-editor-components-external-link-card__warning",children:[(0,a.jsx)(l.A,{icon:(0,a.jsx)(lo,{status:"error"})}),(0,a.jsx)("span",{children:r})]}):null]}),(0,a.jsx)(ws.VisuallyHidden,{as:"span",children:/* translators: accessibility text */ /* translators: accessibility text */
(0,o.__)("(opens in a new tab)","woocommerce")}),(0,a.jsx)(l.A,{icon:ao.A,className:"wc-block-editor-components-external-link-card__icon"})]}),po=["height","borderRadius"],mo=e=>{const t=(0,B.select)(F.paymentStore).getAvailableExpressPaymentMethods();return Object.values(t).reduce(((t,s)=>t||s?.supportsStyle.some((t=>e.includes(t)))),!1)},ho=({attributes:e,setAttributes:t})=>{const{buttonHeight:s,buttonBorderRadius:r}=e;return(0,a.jsxs)(a.Fragment,{children:[mo(["height"])&&(0,a.jsx)(ws.RadioControl,{label:(0,o.__)("Button height","woocommerce"),selected:s,options:[{label:(0,o.__)("Small (40px)","woocommerce"),value:"40"},{label:(0,o.__)("Medium (48px)","woocommerce"),value:"48"},{label:(0,o.__)("Large (55px)","woocommerce"),value:"55"}],onChange:e=>t({buttonHeight:e})}),mo(["borderRadius"])&&(0,a.jsx)("div",{className:"border-radius-control-container",children:(0,a.jsx)(c.HeightControl,{label:(0,o.__)("Button border radius","woocommerce"),value:r,onChange:e=>{const s=e.replace("px","");t({buttonBorderRadius:s})}})})]})},go=({attributes:e,setAttributes:t})=>e.showButtonStyles?(0,a.jsx)(ho,{attributes:e,setAttributes:t}):null,_o=()=>{const e=(0,B.select)(F.paymentStore).getAvailableExpressPaymentMethods();return Object.entries(e).length<1?(0,a.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,o.__)("You currently have no express payment integrations active.","woocommerce")}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,o.__)("You currently have the following express payment integrations active.","woocommerce")}),Object.values(e).map((e=>(0,a.jsx)(uo,{href:`${p.ADMIN_URL}admin.php?page=wc-settings&tab=checkout&section=${encodeURIComponent(e.gatewayId)}`,title:e.title,description:e.description},e.name)))]})},wo=(0,a.jsxs)(a.Fragment,{children:[(0,o.__)("Apply uniform styles","woocommerce")," ",(0,a.jsx)("span",{className:"express-payment-styles-beta-badge",children:"Beta"})]}),ko=({attributes:e,setAttributes:t})=>(0,a.jsxs)(c.InspectorControls,{children:[mo(po)&&(0,a.jsxs)(ws.PanelBody,{title:(0,o.__)("Button Settings","woocommerce"),className:"express-payment-button-settings",children:[(0,a.jsx)(ws.ToggleControl,{label:wo,checked:e.showButtonStyles,onChange:()=>t({showButtonStyles:!e.showButtonStyles}),help:(0,o.__)("Sets a consistent style for express payment buttons.","woocommerce")}),(0,a.jsxs)(ws.Notice,{status:"warning",isDismissible:!1,className:"wc-block-checkout__notice express-payment-styles-notice",children:[(0,a.jsxs)("strong",{children:[(0,o.__)("Note","woocommerce"),":"]})," ",(0,o.__)("Some payment methods might not yet support all style controls","woocommerce")]}),(0,a.jsx)(go,{attributes:e,setAttributes:t})]}),(0,a.jsx)(ws.PanelBody,{title:(0,o.__)("Express Payment Methods","woocommerce"),children:(0,a.jsx)(_o,{})})]});s(9961);const bo=({children:e,className:t,screenReaderLabel:s,showSpinner:c=!1,isLoading:n=!0})=>(0,a.jsxs)("div",{className:(0,r.A)(t,{"wc-block-components-loading-mask":n}),children:[n&&c&&(0,a.jsx)(Ot.Spinner,{}),(0,a.jsx)("div",{className:(0,r.A)({"wc-block-components-loading-mask__children":n}),"aria-hidden":n,children:e}),n&&(0,a.jsx)("span",{className:"screen-reader-text",children:s||(0,o.__)("Loading…","woocommerce")})]}),yo=(0,a.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsxs)("g",{fill:"none",fillRule:"evenodd",children:[(0,a.jsx)("path",{d:"M0 0h24v24H0z"}),(0,a.jsx)("path",{fill:"#000",fillRule:"nonzero",d:"M17.3 8v1c1 .2 1.4.9 1.4 1.7h-1c0-.6-.3-1-1-1-.8 0-1.3.4-1.3.9 0 .4.3.6 1.4 1 1 .2 2 .6 2 1.9 0 .9-.6 1.4-1.5 1.5v1H16v-1c-.9-.1-1.6-.7-1.7-1.7h1c0 .6.4 1 1.3 1 1 0 1.2-.5 1.2-.8 0-.4-.2-.8-1.3-1.1-1.3-.3-2.1-.8-2.1-1.8 0-.9.7-1.5 1.6-1.6V8h1.3zM12 10v1H6v-1h6zm2-2v1H6V8h8zM2 4v16h20V4H2zm2 14V6h16v12H4z"}),(0,a.jsx)("path",{stroke:"#000",strokeLinecap:"round",d:"M6 16c2.6 0 3.9-3 1.7-3-2 0-1 3 1.5 3 1 0 1-.8 2.8-.8"})]})});var xo=s(6600),fo=s(8486),vo=s(6208);s(9287);const jo={bank:xo.A,bill:fo.A,card:vo.A,checkPayment:yo},So=({icon:e="",text:t=""})=>{const s=!!e,o=(0,u.useCallback)((e=>s&&(0,oe.isString)(e)&&(0,oe.objectHasProp)(jo,e)),[s]),c=(0,r.A)("wc-block-components-payment-method-label",{"wc-block-components-payment-method-label--with-icon":s});return(0,a.jsxs)("span",{className:c,children:[o(e)?(0,a.jsx)(l.A,{icon:jo[e]}):e,t]})},Co=e=>`wc-block-components-payment-method-icon wc-block-components-payment-method-icon--${e}`,Eo=({id:e,src:t=null,alt:s=""})=>t?(0,a.jsx)("img",{className:Co(e),src:t,alt:s}):null,No=[{id:"alipay",alt:"Alipay",src:h+"payment-methods/alipay.svg"},{id:"amex",alt:"American Express",src:h+"payment-methods/amex.svg"},{id:"bancontact",alt:"Bancontact",src:h+"payment-methods/bancontact.svg"},{id:"diners",alt:"Diners Club",src:h+"payment-methods/diners.svg"},{id:"discover",alt:"Discover",src:h+"payment-methods/discover.svg"},{id:"eps",alt:"EPS",src:h+"payment-methods/eps.svg"},{id:"giropay",alt:"Giropay",src:h+"payment-methods/giropay.svg"},{id:"ideal",alt:"iDeal",src:h+"payment-methods/ideal.svg"},{id:"jcb",alt:"JCB",src:h+"payment-methods/jcb.svg"},{id:"laser",alt:"Laser",src:h+"payment-methods/laser.svg"},{id:"maestro",alt:"Maestro",src:h+"payment-methods/maestro.svg"},{id:"mastercard",alt:"Mastercard",src:h+"payment-methods/mastercard.svg"},{id:"multibanco",alt:"Multibanco",src:h+"payment-methods/multibanco.svg"},{id:"p24",alt:"Przelewy24",src:h+"payment-methods/p24.svg"},{id:"sepa",alt:"Sepa",src:h+"payment-methods/sepa.svg"},{id:"sofort",alt:"Sofort",src:h+"payment-methods/sofort.svg"},{id:"unionpay",alt:"Union Pay",src:h+"payment-methods/unionpay.svg"},{id:"visa",alt:"Visa",src:h+"payment-methods/visa.svg"},{id:"wechat",alt:"WeChat",src:h+"payment-methods/wechat.svg"}];s(6983);const Po=({icons:e=[],align:t="center",className:s})=>{const o=(e=>{const t={};return e.forEach((e=>{let s={};"string"==typeof e&&(s={id:e,alt:e,src:null}),"object"==typeof e&&(s={id:e.id||"",alt:e.alt||"",src:e.src||null}),s.id&&(0,oe.isString)(s.id)&&!t[s.id]&&(t[s.id]=s)})),Object.values(t)})(e);if(0===o.length)return null;const c=(0,r.A)("wc-block-components-payment-method-icons",{"wc-block-components-payment-method-icons--align-left":"left"===t,"wc-block-components-payment-method-icons--align-right":"right"===t},s);return(0,a.jsx)("div",{className:c,children:o.map((e=>{const t={...e,...(s=e.id,No.find((e=>e.id===s))||{})};var s;return(0,a.jsx)(Eo,{...t},"payment-method-icon-"+e.id)}))})},Ao=(e="")=>{const{cartCoupons:t,cartIsLoading:s}=je(),{createErrorNotice:r}=(0,B.useDispatch)("core/notices"),{createNotice:c}=(0,B.useDispatch)("core/notices"),{setValidationErrors:n}=(0,B.useDispatch)(F.validationStore),{isApplyingCoupon:a,isRemovingCoupon:i}=(0,B.useSelect)((e=>{const t=e(F.cartStore);return{isApplyingCoupon:t.isApplyingCoupon(),isRemovingCoupon:t.isRemovingCoupon()}})),{applyCoupon:l,removeCoupon:d}=(0,B.useDispatch)(F.cartStore),u=(0,B.useSelect)((e=>e(F.checkoutStore).getOrderId()));return{appliedCoupons:t,isLoading:s,applyCoupon:t=>l(t).then((()=>((0,wt.applyCheckoutFilter)({filterName:"showApplyCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&c("info",(0,o.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,o.__)('Coupon code "%s" has been applied to your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((e=>{const t=(e=>u&&u>0&&e?.data?.details?.checkout?e.data.details.checkout:e?.data?.details?.cart?e.data.details.cart:e.message)(e);return n({coupon:{message:(0,ie.decodeEntities)(t),hidden:!1}}),Promise.resolve(!1)})),removeCoupon:t=>d(t).then((()=>((0,wt.applyCheckoutFilter)({filterName:"showRemoveCouponNotice",defaultValue:!0,arg:{couponCode:t,context:e}})&&c("info",(0,o.sprintf)(/* translators: %s coupon code. */ /* translators: %s coupon code. */
(0,o.__)('Coupon code "%s" has been removed from your cart.',"woocommerce"),t),{id:"coupon-form",type:"snackbar",context:e}),Promise.resolve(!0)))).catch((t=>(r(t.message,{id:"coupon-form",context:e}),Promise.resolve(!1)))),isApplyingCoupon:a,isRemovingCoupon:i}},Ro=(e,t)=>{const s=[],r=(t,s)=>{const o=s+"_tax",r=(0,oe.objectHasProp)(e,s)&&(0,oe.isString)(e[s])?parseInt(e[s],10):0;return{key:s,label:t,value:r,valueWithTax:r+((0,oe.objectHasProp)(e,o)&&(0,oe.isString)(e[o])?parseInt(e[o],10):0)}};return s.push(r((0,o.__)("Subtotal:","woocommerce"),"total_items")),s.push(r((0,o.__)("Fees:","woocommerce"),"total_fees")),s.push(r((0,o.__)("Discount:","woocommerce"),"total_discount")),s.push({key:"total_tax",label:(0,o.__)("Taxes:","woocommerce"),value:parseInt(e.total_tax,10),valueWithTax:parseInt(e.total_tax,10)}),t&&s.push(r((0,o.__)("Shipping:","woocommerce"),"total_shipping")),s};class Io extends u.Component{state={errorMessage:"",hasError:!1};static getDerivedStateFromError(e){return{errorMessage:e.message,hasError:!0}}render(){const{hasError:e,errorMessage:t}=this.state,{isEditor:s}=this.props;if(e){let e=(0,o.__)("We are experiencing difficulties with this payment method. Please contact us for assistance.","woocommerce");(s||p.CURRENT_USER_IS_ADMIN)&&(e=t||(0,o.__)("There was an error with this payment method. Please verify it's configured correctly.","woocommerce"));const r=[{id:"0",content:e,isDismissible:!1,status:"error"}];return(0,a.jsx)(Ot.StoreNoticesContainer,{additionalNotices:r,context:re.PAYMENTS})}return this.props.children}}const To=Io,Bo="wc/store/payment",Mo=(0,u.createContext)({showButtonStyles:!1,buttonHeight:"48",buttonBorderRadius:"4"}),Oo=()=>{const{isEditor:e}=O(),{showButtonStyles:t,buttonHeight:s,buttonBorderRadius:r}=(0,u.useContext)(Mo),c=t?{height:s,borderRadius:r}:void 0,{activePaymentMethod:n,paymentMethodData:i}=(0,B.useSelect)((e=>{const t=e(Bo);return{activePaymentMethod:t.getActivePaymentMethod(),paymentMethodData:t.getPaymentMethodData()}})),{__internalSetActivePaymentMethod:l,__internalSetExpressPaymentStarted:d,__internalSetPaymentIdle:m,__internalSetPaymentError:h,__internalSetPaymentMethodData:g,__internalSetExpressPaymentError:_}=(0,B.useDispatch)(Bo),{paymentMethods:w}=no(),k=(()=>{const{onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutAfterProcessingWithSuccess:s,onCheckoutAfterProcessingWithError:r,onSubmit:c}=(0,u.useContext)(Qe),{onCheckoutValidation:n,onCheckoutSuccess:a,onCheckoutFail:i}=He.checkoutEvents,{isCalculating:l,isComplete:d,isIdle:m,isProcessing:h,customerId:g}=(0,B.useSelect)((e=>{const t=e(F.checkoutStore);return{isComplete:t.isComplete(),isIdle:t.isIdle(),isProcessing:t.isProcessing(),customerId:t.getCustomerId(),isCalculating:t.isCalculating()}})),{paymentStatus:_,activePaymentMethod:w,shouldSavePayment:k}=(0,B.useSelect)((e=>{const t=e(F.paymentStore);return{paymentStatus:{get isPristine(){return $()("isPristine",{since:"9.6.0",alternative:"isIdle",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentIdle()},isIdle:t.isPaymentIdle(),isStarted:t.isExpressPaymentStarted(),isProcessing:t.isPaymentProcessing(),get isFinished(){return $()("isFinished",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()||t.isPaymentReady()},hasError:t.hasPaymentError(),get hasFailed(){return $()("hasFailed",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.hasPaymentError()},get isSuccessful(){return $()("isSuccessful",{since:"9.6.0",plugin:"WooCommerce Blocks",link:"https://github.com/woocommerce/woocommerce-blocks/pull/8110"}),t.isPaymentReady()},isReady:t.isPaymentReady(),isDoingExpressPayment:t.isExpressPaymentMethodActive()},activePaymentMethod:t.getActivePaymentMethod(),shouldSavePayment:t.getShouldSavePaymentMethod()}})),{__internalSetExpressPaymentError:b}=(0,B.useDispatch)(F.paymentStore),{onPaymentProcessing:y,onPaymentSetup:x}=(0,u.useContext)(W),{shippingErrorStatus:f,shippingErrorTypes:v,onShippingRateSuccess:j,onShippingRateFail:S,onShippingRateSelectSuccess:C,onShippingRateSelectFail:E}=Ve(),{shippingRates:N,isLoadingRates:P,selectedRates:A,isSelectingRate:R,selectShippingRate:I,needsShipping:T}=Me(),{billingAddress:M,shippingAddress:O}=(0,B.useSelect)((e=>e(F.cartStore).getCustomerData())),{setShippingAddress:D}=(0,B.useDispatch)(F.cartStore),{cartItems:L,cartFees:V,cartTotals:z,extensions:q}=je(),{appliedCoupons:H}=Ao(),U=(0,u.useRef)(Ro(z,T)),Y=(0,u.useRef)({label:(0,o.__)("Total","woocommerce"),value:parseInt(z.total_price,10)});(0,u.useEffect)((()=>{U.current=Ro(z,T),Y.current={label:(0,o.__)("Total","woocommerce"),value:parseInt(z.total_price,10)}}),[z,T]);const G=(0,u.useCallback)(((e="")=>{$()("setExpressPaymentError should only be used by Express Payment Methods (using the provided onError handler).",{alternative:"",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),b(e)}),[b]);return{activePaymentMethod:w,billing:{appliedCoupons:H,billingAddress:M,billingData:M,cartTotal:Y.current,cartTotalItems:U.current,currency:(0,Dt.getCurrencyFromPriceResponse)(z),customerId:g,displayPricesIncludingTax:(0,p.getSetting)("displayCartPricesIncludingTax",!1)},cartData:{cartItems:L,cartFees:V,extensions:q},checkoutStatus:{isCalculating:l,isComplete:d,isIdle:m,isProcessing:h},components:{LoadingMask:bo,PaymentMethodIcons:Po,PaymentMethodLabel:So,ValidationInputError:Ot.ValidationInputError},emitResponse:{noticeContexts:re,responseTypes:oe.responseTypes},eventRegistration:{onCheckoutAfterProcessingWithError:r,onCheckoutAfterProcessingWithSuccess:s,onCheckoutBeforeProcessing:e,onCheckoutValidationBeforeProcessing:t,onCheckoutSuccess:a,onCheckoutFail:i,onCheckoutValidation:n,onPaymentProcessing:y,onPaymentSetup:x,onShippingRateFail:S,onShippingRateSelectFail:E,onShippingRateSelectSuccess:C,onShippingRateSuccess:j},onSubmit:c,paymentStatus:_,setExpressPaymentError:G,shippingData:{isSelectingRate:R,needsShipping:T,selectedRates:A,setSelectedRates:I,setShippingAddress:D,shippingAddress:O,shippingRates:N,shippingRatesLoading:P},shippingStatus:{shippingErrorStatus:f,shippingErrorTypes:v},shouldSavePayment:k}})(),b=(0,u.useRef)(n),y=(0,u.useRef)(i),x=(0,u.useCallback)((e=>()=>{b.current=n,y.current=i,d(),l(e)}),[n,i,l,d]),f=(0,u.useCallback)((()=>{m(),l(b.current,y.current)}),[l,m]),v=(0,u.useCallback)((e=>{h(),g(e),_(e),l(b.current,y.current)}),[l,h,g,_]),j=(0,u.useCallback)(((e="")=>{$()("Express Payment Methods should use the provided onError handler instead.",{alternative:"onError",plugin:"woocommerce-gutenberg-products-block",link:"https://github.com/woocommerce/woocommerce-gutenberg-products-block/pull/4228"}),e?v(e):_("")}),[_,v]),S=Object.entries(w),C=S.length>0?S.map((([t,s])=>{const o=e?s.edit:s.content;return(0,u.isValidElement)(o)?(0,a.jsx)("li",{id:`express-payment-method-${t}`,children:(0,u.cloneElement)(o,{...k,onClick:x(t),onClose:f,onError:v,setExpressPaymentError:j,buttonAttributes:c})},t):null})):(0,a.jsx)("li",{children:(0,o.__)("No registered Payment Methods","woocommerce")},"noneRegistered");return(0,a.jsx)(To,{isEditor:e,children:(0,a.jsx)("ul",{className:"wc-block-components-express-payment__event-buttons",children:C})})};s(2831);const Do=()=>{const{paymentMethods:e,isInitialized:t}=no(),{isCalculating:s,isProcessing:r,isAfterProcessing:c,isBeforeProcessing:n,isComplete:i,hasError:l}=(0,B.useSelect)((e=>{const t=e(F.checkoutStore);return{isCalculating:t.isCalculating(),isProcessing:t.isProcessing(),isAfterProcessing:t.isAfterProcessing(),isBeforeProcessing:t.isBeforeProcessing(),isComplete:t.isComplete(),hasError:t.hasError()}})),d=(0,B.useSelect)((e=>e(F.paymentStore).isExpressPaymentMethodActive()));if(!t||t&&0===Object.keys(e).length)return null;const u=r||c||n||i&&!l;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(bo,{isLoading:s||u||d,children:(0,a.jsx)("div",{className:"wc-block-components-express-payment wc-block-components-express-payment--cart",children:(0,a.jsxs)("div",{className:"wc-block-components-express-payment__content",children:[(0,a.jsx)(Ot.StoreNoticesContainer,{context:re.EXPRESS_PAYMENTS}),(0,a.jsx)(Oo,{})]})})}),(0,a.jsx)("div",{className:"wc-block-components-express-payment-continue-rule wc-block-components-express-payment-continue-rule--cart",children:(0,o.__)("Or","woocommerce")})]})},Lo=({className:e})=>{const{cartNeedsPayment:t}=je();return t?(0,a.jsx)("div",{className:(0,r.A)("wc-block-cart__payment-options",e),children:(0,a.jsx)(Do,{})}):null};s(7467);const Fo=(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"currentColor",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{stroke:"#1E1E1E",strokeLinejoin:"round",strokeWidth:"1.5",d:"M18.25 12a6.25 6.25 0 1 1-12.5 0 6.25 6.25 0 0 1 12.5 0Z"}),(0,a.jsx)("path",{fill:"#1E1E1E",d:"M10 3h4v3h-4z"}),(0,a.jsx)("rect",{width:"1.5",height:"5",x:"11.25",y:"8",fill:"#1E1E1E",rx:".75"}),(0,a.jsx)("path",{fill:"#1E1E1E",d:"m15.7 4.816 1.66 1.078-1.114 1.718-1.661-1.078z"})]});(0,d.registerBlockType)("woocommerce/cart-express-payment-block",{icon:{src:(0,a.jsx)(l.A,{style:{fill:"none"},icon:Fo,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{paymentMethods:s,isInitialized:o}=no(),n=Object.keys(s).length>0,i=(0,c.useBlockProps)({className:(0,r.A)({"wp-block-woocommerce-cart-express-payment-block--has-express-payment-methods":n})}),{className:l,showButtonStyles:d,buttonHeight:u,buttonBorderRadius:p}=e;return o&&n?(0,a.jsxs)("div",{...i,children:[(0,a.jsx)(ko,{attributes:e,setAttributes:t}),(0,a.jsx)(Mo.Provider,{value:{showButtonStyles:d,buttonHeight:u,buttonBorderRadius:p},children:(0,a.jsx)(Lo,{className:l})})]}):null},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});var Vo=s(6012);const $o=(0,o.__)("Proceed to Checkout","woocommerce"),zo={checkoutPageId:{type:"number",default:0},lock:{type:"object",default:{move:!0,remove:!0}},buttonLabel:{type:"string",default:$o}};var qo=s(111);s(6882);const Ho=(0,u.forwardRef)(((e,t)=>{const{className:s,children:o,variant:c="contained",removeTextWrap:n=!1,...i}=e,l=(0,r.A)("wc-block-components-button","wp-element-button",s,c);if("href"in e)return(0,a.jsx)(qo.$,{render:(0,a.jsx)("a",{ref:t,href:e.href,children:(0,a.jsx)("div",{className:"wc-block-components-button__text",children:o})}),className:l,...i});const d=n?e.children:(0,a.jsx)("div",{className:"wc-block-components-button__text",children:e.children});return(0,a.jsx)(qo.$,{ref:t,className:l,...i,children:d})})),Uo=({onChange:e,placeholder:t,value:s,children:o,...r})=>(0,a.jsxs)(Ho,{...r,children:[(0,a.jsx)(c.RichText,{multiline:!1,allowedFormats:[],value:s,placeholder:t,onChange:e}),o]}),Wo=(e,t)=>{if(!e.title.raw)return e.slug;const s=1===t.filter((t=>t.title.raw===e.title.raw)).length;return e.title.raw+(s?"":` - ${e.slug}`)},Yo=({setPageId:e,pageId:t,labels:s})=>{const r=(0,B.useSelect)((e=>e("core").getEntityRecords("postType","page",{status:"publish",orderby:"title",order:"asc",per_page:100})),[])||null;return r?(0,a.jsx)(ws.PanelBody,{title:s.title,children:(0,a.jsx)(ws.SelectControl,{label:(0,o.__)("Link to","woocommerce"),value:t,options:[{label:s.default,value:0},...r.map((e=>({label:Wo(e,r),value:parseInt(e.id,10)})))],onChange:t=>e(parseInt(t,10))})}):null};s(2766),(0,d.registerBlockType)("woocommerce/proceed-to-checkout-block",{icon:{src:(0,a.jsx)(l.A,{icon:Vo.A,className:"wc-block-editor-components-block-icon"})},attributes:zo,edit:({attributes:e,setAttributes:t})=>{const s=(0,c.useBlockProps)(),{checkoutPageId:r=0,buttonLabel:n}=e,{current:i}=(0,u.useRef)(r),l=(0,B.useSelect)((e=>i||e("core/editor").getCurrentPostId()),[i]);return(0,a.jsxs)("div",{...s,children:[(0,a.jsx)(c.InspectorControls,{children:!(l===w&&0===i)&&(0,a.jsx)(Yo,{pageId:r,setPageId:e=>t({checkoutPageId:e}),labels:{title:(0,o.__)("Proceed to Checkout button","woocommerce"),default:(0,o.__)("WooCommerce Checkout Page","woocommerce")}})}),(0,a.jsx)(Uo,{className:"wc-block-cart__submit-button",value:n,placeholder:$o,onChange:e=>{t({buttonLabel:e})}})]})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});const Go=(0,a.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{d:"M22.7 22.7l-20-20L2 2l-.7-.7L0 2.5 4.4 7l2.2 4.7L5.2 14A2 2 0 007 17h7.5l1.3 1.4a2 2 0 102.8 2.8l2.9 2.8 1.2-1.3zM7.4 15a.2.2 0 01-.2-.3l.9-1.7h2.4l2 2h-5zm8.2-2a2 2 0 001.7-1l3.6-6.5.1-.5c0-.6-.4-1-1-1H6.5l9 9zM7 18a2 2 0 100 4 2 2 0 000-4z"}),(0,a.jsx)("path",{fill:"none",d:"M0 0h24v24H0z"})]}),Qo=g?["core/paragraph",{align:"center",content:(0,o.sprintf)(/* translators: %s is the link to the store product directory. */ /* translators: %s is the link to the store product directory. */
(0,o.__)('<a href="%s">Browse store</a>',"woocommerce"),g),dropCap:!1}]:null,Ko=[["core/heading",{textAlign:"center",content:(0,o.__)("Your cart is currently empty!","woocommerce"),level:2,className:"with-empty-cart-icon wc-block-cart__empty-cart__title"}],Qo,["core/separator",{className:"is-style-dots"}],["core/heading",{textAlign:"center",content:(0,o.__)("New in store","woocommerce"),level:2}],["woocommerce/product-new",{columns:4,rows:1}]].filter(Boolean);s(9685),(0,d.registerBlockType)("woocommerce/empty-cart-block",{icon:{src:(0,a.jsx)(l.A,{icon:Go,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,c.useBlockProps)(),{currentView:s}=O(),o=jt(wt.innerBlockAreas.EMPTY_CART);return St({clientId:e,registeredBlocks:o,defaultTemplate:Ko}),(0,a.jsx)("div",{...t,hidden:"woocommerce/empty-cart-block"!==s,children:(0,a.jsx)(c.InnerBlocks,{template:Ko,templateLock:!1,renderAppender:c.InnerBlocks.ButtonBlockAppender})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})})});const Zo=e=>Object.values(e).reduce(((e,t)=>(null!==t.icons&&(e=e.concat(t.icons)),e)),[]),Jo=({className:e})=>{const{paymentMethods:t}=co(!1);return(0,a.jsx)(Po,{className:e,icons:Zo(t)})};(0,d.registerBlockType)("woocommerce/cart-accepted-payment-methods-block",{icon:{src:(0,a.jsx)(l.A,{icon:vo.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsx)("div",{...s,children:(0,a.jsx)(Jo,{className:t})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});const Xo=(0,a.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",fill:"none",children:[(0,a.jsx)("path",{stroke:"currentColor",strokeWidth:"1.5",fill:"none",d:"M6 3.75h12c.69 0 1.25.56 1.25 1.25v14c0 .69-.56 1.25-1.25 1.25H6c-.69 0-1.25-.56-1.25-1.25V5c0-.69.56-1.25 1.25-1.25z"}),(0,a.jsx)("path",{fill:"currentColor",fillRule:"evenodd",d:"M6.9 7.5A1.1 1.1 0 018 6.4h8a1.1 1.1 0 011.1 1.1v2a1.1 1.1 0 01-1.1 1.1H8a1.1 1.1 0 01-1.1-1.1v-2zm1.2.1v1.8h7.8V7.6H8.1z",clipRule:"evenodd"}),(0,a.jsx)("path",{fill:"currentColor",d:"M8.5 12h1v1h-1v-1zM8.5 14h1v1h-1v-1zM8.5 16h1v1h-1v-1zM11.5 12h1v1h-1v-1zM11.5 14h1v1h-1v-1zM11.5 16h1v1h-1v-1zM14.5 12h1v1h-1v-1zM14.5 14h1v1h-1v-1zM14.5 16h1v1h-1v-1z"})]});s(1962);const er=({instanceId:e,isLoading:t=!1,onSubmit:s,displayCouponForm:c=!1})=>{const[n,i]=(0,u.useState)(""),[l,d]=(0,u.useState)(c),p=`wc-block-components-totals-coupon__input-${e}`,{validationErrorId:m}=(0,B.useSelect)((t=>({validationErrorId:t(F.validationStore).getValidationErrorId(e)})),[e]),h=(0,u.useRef)(null);return(0,a.jsx)(Ot.Panel,{className:"wc-block-components-totals-coupon",initialOpen:l,hasBorder:!1,headingLevel:2,title:(0,o.__)("Add a coupon","woocommerce"),state:[l,d],children:(0,a.jsx)(bo,{screenReaderLabel:(0,o.__)("Applying coupon…","woocommerce"),isLoading:t,showSpinner:!1,children:(0,a.jsxs)("div",{className:"wc-block-components-totals-coupon__content",children:[(0,a.jsxs)("form",{className:"wc-block-components-totals-coupon__form",id:"wc-block-components-totals-coupon__form",children:[(0,a.jsx)(Ot.ValidatedTextInput,{id:p,errorId:"coupon",className:"wc-block-components-totals-coupon__input",label:(0,o.__)("Enter code","woocommerce"),value:n,ariaDescribedBy:m||"",onChange:e=>{i(e)},focusOnMount:!0,validateOnMount:!1,showError:!1,ref:h}),(0,a.jsxs)(Ho,{className:(0,r.A)("wc-block-components-totals-coupon__button",{"wc-block-components-totals-coupon__button--loading":t}),disabled:t||!n,onClick:e=>{e.preventDefault(),void 0!==s?s(n)?.then((e=>{e?(i(""),d(!1)):h.current?.focus&&h.current.focus()})):(i(""),d(!0))},type:"submit",children:[t&&(0,a.jsx)(Ot.Spinner,{}),(0,o.__)("Apply","woocommerce")]})]}),(0,a.jsx)(Ot.ValidationInputError,{propertyName:"coupon",elementId:e})]})})})};s(619);const tr={context:"summary"},sr=({cartCoupons:e=[],currency:t,isRemovingCoupon:s,removeCoupon:r,values:c})=>{const{total_discount:n,total_discount_tax:i}=c,l=parseInt(n,10),d=(0,wt.applyCheckoutFilter)({arg:tr,filterName:"coupons",defaultValue:e});if(!l&&0===d.length)return null;const u=parseInt(i,10),m=(0,p.getSetting)("displayCartPricesIncludingTax",!1)?l+u:l;return(0,a.jsx)(Ot.TotalsItem,{className:"wc-block-components-totals-discount",currency:t,description:0!==d.length&&(0,a.jsx)(bo,{screenReaderLabel:(0,o.__)("Removing coupon…","woocommerce"),isLoading:s,showSpinner:!1,children:(0,a.jsx)("ul",{className:"wc-block-components-totals-discount__coupon-list",children:d.map((e=>(0,a.jsx)(Ot.RemovableChip,{className:"wc-block-components-totals-discount__coupon-list-item",text:e.label,screenReaderText:(0,o.sprintf)(/* translators: %s Coupon code. */ /* translators: %s Coupon code. */
(0,o.__)("Coupon: %s","woocommerce"),e.label),disabled:s,onRemove:()=>{r(e.code)},radius:"large",ariaLabel:(0,o.sprintf)(/* translators: %s is a coupon code. */ /* translators: %s is a coupon code. */
(0,o.__)('Remove coupon "%s"',"woocommerce"),e.label)},"coupon-"+e.code)))})}),label:m?(0,o.__)("Discount","woocommerce"):(0,o.__)("Coupons","woocommerce"),value:m?-1*m:"-"})};s(8413);const or=({currency:e,values:t,className:s})=>{const c=(0,p.getSetting)("taxesEnabled",!0)&&(0,p.getSetting)("displayCartPricesIncludingTax",!1),{total_price:n,total_tax:i,tax_lines:l}=t,{receiveCart:d,...m}=je(),h=(0,wt.applyCheckoutFilter)({filterName:"totalLabel",defaultValue:(0,o.__)("Total","woocommerce"),extensions:m.extensions,arg:{cart:m}}),g=(0,wt.applyCheckoutFilter)({filterName:"totalValue",defaultValue:"<price/>",extensions:m.extensions,arg:{cart:m},validation:wt.productPriceValidation}),_=(0,a.jsx)(Ot.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:parseInt(n,10)}),w=(0,u.createInterpolateElement)(g,{price:_}),k=parseInt(i,10),b=l&&l.length>0?(0,o.sprintf)(/* translators: %s is a list of tax rates */ /* translators: %s is a list of tax rates */
(0,o.__)("Including %s","woocommerce"),l.map((({name:t,price:s})=>`${(0,Dt.formatPrice)(s,e)} ${t}`)).join(", ")):(0,o.__)("Including <TaxAmount/> in taxes","woocommerce");return(0,a.jsx)(Ot.TotalsItem,{className:(0,r.A)("wc-block-components-totals-footer-item",s),currency:e,label:h,value:w,description:c&&0!==k&&(0,a.jsx)("p",{className:"wc-block-components-totals-footer-item-tax",children:(0,u.createInterpolateElement)(b,{TaxAmount:(0,a.jsx)(Ot.FormattedMonetaryAmount,{className:"wc-block-components-totals-footer-item-tax-value",currency:e,value:k})})})})},rr=()=>{const{shippingRates:e}=je(),t=(e=>e.flatMap((e=>e.shipping_rates.filter((e=>e.selected)).flatMap((e=>e.name)))))(e);return t?(0,a.jsx)("div",{className:"wc-block-components-totals-shipping__via",children:(0,ie.decodeEntities)(t.filter(((e,s)=>t.indexOf(e)===s)).join(", "))}):null};let cr=null;s(8349),s(4249);var nr=s(2174);s(8306);const ar=e=>{const{onChange:t,options:s,label:c,value:n="",className:i,size:d,errorId:p,required:m,errorMessage:h=(0,o.__)("Please select a valid option","woocommerce"),placeholder:g,..._}=e,w=(0,u.useCallback)((e=>{t(e.target.value)}),[t]),k=(0,wt.getFieldLabel)(c),b=(0,u.useMemo)((()=>({value:"",label:null!=g?g:(0,o.sprintf)(
// translators: %s will be label of the field. For example "country/region".
// translators: %s will be label of the field. For example "country/region".
(0,o.__)("Select a %s","woocommerce"),k),disabled:!!m})),[g,m,k]),y=(0,u.useId)(),x=_.id||`wc-blocks-components-select-${y}`,f=p||x,v=(0,u.useMemo)((()=>m&&n?s:[b].concat(s)),[m,n,b,s]),{setValidationErrors:j,clearValidationError:S}=(0,B.useDispatch)(F.validationStore),{error:C,validationErrorId:E}=(0,B.useSelect)((e=>{const t=e(F.validationStore);return{error:t.getValidationError(f),validationErrorId:t.getValidationErrorId(f)}}),[f]);(0,u.useEffect)((()=>(!m||n?S(f):j({[f]:{message:h,hidden:!0}}),()=>{S(f)})),[S,n,f,h,m,j]);const N=(0,B.useSelect)((e=>e(F.validationStore).getValidationError(f||"")||{hidden:!0}),[f]);return(0,a.jsxs)("div",{className:(0,r.A)(i,{"has-error":!N.hidden}),children:[(0,a.jsx)("div",{className:"wc-blocks-components-select",children:(0,a.jsxs)("div",{className:"wc-blocks-components-select__container",children:[(0,a.jsx)("label",{htmlFor:x,className:"wc-blocks-components-select__label",children:c}),(0,a.jsx)("select",{className:"wc-blocks-components-select__select",id:x,size:void 0!==d?d:1,onChange:w,value:n,"aria-invalid":!(!C?.message||C?.hidden),"aria-errormessage":E,..._,children:v.map((e=>(0,a.jsx)("option",{value:e.value,"data-alternate-values":`[${e.label}]`,disabled:void 0!==e.disabled&&e.disabled,children:e.label},e.value)))}),(0,a.jsx)(l.A,{className:"wc-blocks-components-select__expand",icon:nr.A})]})}),(0,a.jsx)(Ot.ValidationInputError,{propertyName:f})]})},ir=({className:e,countries:t,id:s,errorId:o,label:c,onChange:n,value:i="",autoComplete:l="off",required:d=!1})=>{const p=(0,u.useMemo)((()=>Object.entries(t).map((([e,t])=>({value:e,label:(0,ie.decodeEntities)(t)})))),[t]);return(0,a.jsx)(ar,{className:(0,r.A)(e,"wc-block-components-country-input"),id:s,errorId:o,label:c||"",onChange:n,options:p,value:i,required:d,autoComplete:l})},lr=e=>{const{...t}=e;return(0,a.jsx)(ir,{countries:C,...t})},dr=e=>(0,a.jsx)(ir,{countries:C,...e});s(3930);const ur=(e,t)=>{const s=t.find((t=>t.label.toLocaleUpperCase()===e.toLocaleUpperCase()||t.value.toLocaleUpperCase()===e.toLocaleUpperCase()));return s?s.value:""},pr=({className:e,id:t,states:s,country:o,label:c,onChange:n,autoComplete:i="off",value:l="",required:d=!1})=>{const p=s[o],m=(0,u.useMemo)((()=>p&&Object.keys(p).length>0?Object.keys(p).map((e=>({value:e,label:(0,ie.decodeEntities)(p[e])}))):[]),[p]),h=(0,u.useCallback)((e=>{const t=m.length>0?ur(e,m):e;t!==l&&n(t)}),[n,m,l]),g=(0,u.useRef)(l);return(0,u.useEffect)((()=>{g.current!==l&&(g.current=l)}),[l]),(0,u.useEffect)((()=>{if(m.length>0&&g.current){const e=ur(g.current,m);e!==g.current&&h(e)}}),[m,h]),m.length>0?(0,a.jsx)(ar,{className:(0,r.$)(e,"wc-block-components-state-input"),options:m,label:c||"",id:t,onChange:h,value:l,autoComplete:i,required:d}):(0,a.jsx)(Ot.ValidatedTextInput,{className:e,id:t,label:c,onChange:h,autoComplete:i,value:l,required:d})},mr=e=>{const{...t}=e;return(0,a.jsx)(pr,{states:E,...t})},hr=e=>(0,a.jsx)(pr,{states:E,...e});s(2770);const gr=({field:e,props:t,onChange:s,value:r})=>{var c;const n=null!==(c=e?.required)&&void 0!==c&&c,i=ze(n),[l,d]=(0,u.useState)((()=>Boolean(r)||n)),p=(0,wt.getFieldLabel)(e.label);(0,u.useEffect)((()=>{i!==n&&d(Boolean(r)||n)}),[r,i,n]);const m=(0,u.useCallback)((e=>{s(e),d(!0)}),[s]);return(0,a.jsx)(u.Fragment,{children:l?(0,a.jsx)(Ot.ValidatedTextInput,{...t,type:e.type,label:n?e.label:e.optionalLabel,className:"wc-block-components-address-form__address_2",value:r,onChange:e=>s(e)}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(qo.$,{render:(0,a.jsx)("span",{}),className:"wc-block-components-address-form__address_2-toggle",onClick:()=>d(!0),children:(0,o.sprintf)(
// translators: %s: address 2 field label.
// translators: %s: address 2 field label.
(0,o.__)("+ Add %s","woocommerce"),p)}),(0,a.jsx)("input",{type:"text",tabIndex:-1,className:"wc-block-components-address-form__address_2-hidden-input","aria-hidden":"true","aria-label":e.label,autoComplete:e.autocomplete,id:t?.id,value:r,onChange:e=>m(e.target.value)})]})})},_r=(e,t,s)=>({id:`${t}-${e?.key}`.replaceAll("/","-"),errorId:`${s}_${e?.key}`,label:(e?.required?e?.label:e?.optionalLabel)||"",autoCapitalize:e?.autocapitalize,autoComplete:e?.autocomplete,errorMessage:e?.errorMessage||"",required:e?.required,placeholder:e?.placeholder,className:`wc-block-components-address-form__${e?.key}`.replaceAll("/","-"),...e?.attributes}),wr=(e,t,s)=>{const o=t.find((t=>t.key===e)),r=(0,oe.objectHasProp)(s,e)?s[e]:"";return o?{field:{...o,key:e},value:r}:null},kr=(e,t)=>(0,oe.isObject)(e[t])&&Object.keys(e[t]).length>0,br=({formId:e,address1:t,address2:s,addressType:o,onChange:r})=>{const c=_r(t.field,e,o),n=_r(s.field,e,o);return(0,a.jsxs)(a.Fragment,{children:[t&&(0,a.jsx)(Ot.ValidatedTextInput,{...c,type:t.field.type,label:t.field.label,className:"wc-block-components-address-form__address_1",value:t.value,onChange:e=>r("address_1",e)}),s.field&&!s.field.hidden&&(0,a.jsx)(gr,{field:s.field,props:n,onChange:e=>r("address_2",e),value:s.value})]})};var yr=s(7740);const xr=e=>((e,t)=>Object.entries(e).reduce(((e,[s,o])=>({...e,[t(0,s)]:o})),{}))(e,((e,t)=>(0,yr.L)(t))),fr=e=>{const t=(e=>{const t=(0,u.useRef)({cart:{},checkout:{},customer:{}}),s=(0,B.useSelect)((t=>{const s=t(F.cartStore),o=t(F.checkoutStore),r=t(F.paymentStore),c=s.getCartData(),{coupons:n,shippingRates:a,shippingAddress:i,billingAddress:l,items:d,itemsCount:u,itemsWeight:p,needsShipping:m,totals:h}=c,g={cart:{coupons:n.map((e=>e.code)),shippingRates:[...new Set(a.map((e=>e.shipping_rates.find((e=>e.selected))?.rate_id)).filter(Boolean))],items:d.map((e=>Array(e.quantity).fill(e.id))).flat(),itemsType:[...new Set(d.map((e=>e.type)))],itemsCount:u,itemsWeight:p,needsShipping:m,prefersCollection:"boolean"==typeof o.prefersCollection()&&o.prefersCollection(),totals:{totalPrice:Number(h.total_price),totalTax:Number(h.total_tax)},extensions:c.extensions},checkout:{createAccount:o.getShouldCreateAccount(),customerNote:o.getOrderNotes(),additionalFields:o.getAdditionalFields(),paymentMethod:r.getActivePaymentMethod()},customer:{id:o.getCustomerId(),billingAddress:l,shippingAddress:i,..."billing"===e||"shipping"===e?{address:"billing"===e?l:i}:{}}};return{cart:xr(g.cart),checkout:xr(g.checkout),customer:xr(g.customer)}}),[e]);return t.current&&ae()(t.current,s)||(t.current=s),t.current})(e);return window.schemaParser?{parser:window.schemaParser,data:t}:{parser:null,data:t}},vr=(e,t,s,o="")=>{const r=(0,u.useRef)([]),{parser:c,data:n}=fr(s),a=de(e,t,o).map((e=>{const s=t[e.key]||{};if(c){if(kr(s,"required")){let t={};t=Object.keys(s.required).some((e=>"cart"===e||"checkout"===e||"customer"===e))?{type:"object",properties:s.required}:s.required;try{const s=c.validate(t,n);e.required=s}catch(e){p.CURRENT_USER_IS_ADMIN&&console.error(e)}}if(kr(s,"hidden")){const t={type:"object",properties:s.hidden};try{const s=c.validate(t,n);e.hidden=s}catch(e){p.CURRENT_USER_IS_ADMIN&&console.error(e)}}}return e}));if(!r.current||!ae()(r.current,a)){const e=a.map((e=>({...e,hidden:"boolean"==typeof e.hidden&&e.hidden,required:"boolean"==typeof e.required&&e.required})));r.current=e}return r.current},jr={};function Sr(e){let t=e;return function(e){const s=t;return t=e,s}}const Cr=Sr(),Er=Sr(),Nr=({id:e="",fields:t,onChange:s,addressType:c="shipping",values:n,children:i,isEditing:l,ariaDescribedBy:d=""})=>{const p=(0,bt.useInstanceId)(Nr),m=(0,u.useRef)(!0),{defaultFields:h}=ot(),g=ro(t),_=ro("country"in n?n.country:""),w=vr(g,h,c,_),k=ze(w),b=ze(l),y=ze(n),x=(0,u.useRef)({}),{errors:f,previousErrors:v}=((e,t,s)=>{const{parser:r,data:c}=fr(t),n=(0,u.useRef)(jr),a=ze(n.current);if(!c)return{errors:n.current,previousErrors:void 0};let i;if(s)i=s;else switch(t){case"billing":case"shipping":i=c.customer.address||{};break;case"contact":case"order":i=c.checkout.additional_fields||{};break;default:i={}}const l=e.reduce(((e,t)=>(kr(t,"validation")&&!t.hidden&&(t.required||i[t.key])&&(e[t.key]=t.validation),e)),{});let d=jr;if(Object.keys(l).length>0&&r){const s={type:"object",properties:{}};switch(t){case"shipping":s.properties={customer:{type:"object",properties:{shipping_address:{type:"object",properties:l}}}};break;case"billing":s.properties={customer:{type:"object",properties:{billing_address:{type:"object",properties:l}}}};break;default:s.properties={checkout:{type:"object",properties:{additional_fields:{type:"object",properties:l}}}}}const n=r.compile(s),a=n(c);d=!a&&n.errors?((e,t)=>e.reduce(((e,s)=>{var r;const c=(n=s.instancePath,n.split("/").pop()?.replace("~1","/"));var n;const a=t.find((e=>e.key===c));if(!a||!c)return e;const i=(0,wt.getFieldLabel)(a.label),l=(0,o.sprintf)(
// translators: %s is the label of the field.
// translators: %s is the label of the field.
(0,o.__)("%s is invalid","woocommerce"),i);if(c)switch(s.keyword){case"errorMessage":e[c]=null!==(r=s.message)&&void 0!==r?r:l;break;case"pattern":e[c]=(0,o.sprintf)(
// translators: %1$s is the label of the field, %2$s is the pattern.
// translators: %1$s is the label of the field, %2$s is the pattern.
(0,o.__)("%1$s must match the pattern %2$s","woocommerce"),i,s.params.pattern);break;default:e[c]=l}return e}),{}))(n.errors,e):jr}const p=e.map((e=>d[e.key]?[e.key,d[e.key]]:e.hidden||!e.required&&!i[e.key]?null:"postcode"===e.key&&"country"in i&&!(0,wt.isPostcode)({postcode:i.postcode,country:i.country})?[e.key,(0,o.__)("Please enter a valid postcode","woocommerce")]:"email"===e.key&&"email"in i&&!(0,ue.isEmail)(i.email)?[e.key,(0,o.__)("Please enter a valid email address","woocommerce")]:null)).filter(oe.nonNullable);return ae()(n.current,Object.fromEntries(p))||(n.current=Object.fromEntries(p)),{errors:n.current,previousErrors:a}})(w,c,"shipping"===c?n:void 0);return(0,u.useEffect)((()=>{if(Object.entries(f).forEach((([e,t])=>{const s=x.current[e];t&&(s?.setErrorMessage(t),(0,B.select)(F.validationStore).getValidationError(`${c}_${e}`)||(0,B.dispatch)(F.validationStore).setValidationErrors({[`${c}_${e}`]:{message:t,hidden:!!s?.isFocused()}}))})),v){const e=[];Object.entries(v).forEach((([t])=>{const s=x.current[t];t in f||(e.push(`${c}_${t}`),s?.setErrorMessage(""))})),e.length&&(0,B.dispatch)(F.validationStore).clearValidationErrors(e)}}),[f,v,c,n]),(0,u.useEffect)((()=>{x.current?.postcode?.revalidate()}),[_]),(0,u.useEffect)((()=>{let t;if(!m.current&&l&&x.current&&b!==l){const s=w.find((e=>!1===e.hidden));if(!s)return;const{id:o}=_r(s,e||`${p}`,c),r=document.getElementById(o);r&&(t=setTimeout((()=>{r.focus()}),300))}return m.current=!1,()=>{clearTimeout(t)}}),[l,w,e,p,c,b]),(0,u.useEffect)((()=>{if(ae()(k,w))return;const e={...n,...Object.fromEntries(w.filter((e=>e.hidden)).map((e=>[e.key,""])))};Ie()(n,e)||s(e)}),[s,w,k,n]),(0,u.useEffect)((()=>{if((!ae()(k,w)||!ae()(y,n))&&("country"in n&&((e,t)=>{const s=`${e}_country`,r=(0,B.select)(F.validationStore).getValidationError(s),c=t.city||t.state||t.postcode;try{if(!t.country&&c)throw(0,o.__)("Please select your country","woocommerce");if("billing"===e&&t.country&&!Object.keys(j).includes(t.country))throw(0,o.__)("Sorry, we do not allow orders from the selected country","woocommerce");if("shipping"===e&&t.country&&!Object.keys(S).includes(t.country))throw(0,o.__)("Sorry, we do not ship orders to the selected country","woocommerce");r&&(0,B.dispatch)(F.validationStore).clearValidationError(s)}catch(e){r?(0,B.dispatch)(F.validationStore).showValidationError(s):(0,B.dispatch)(F.validationStore).setValidationErrors({[s]:{message:String(e),hidden:!1}})}})(c,n),"state"in n)){const e=w.find((e=>"state"===e.key));e&&((e,t,s)=>{const r=`${e}_state`,c=(0,B.select)(F.validationStore).getValidationError(r),n=s.required,a="shipping"===e?Cr(t):Er(t),i=!!a&&!Ie()(a,t);c?!n||t.state?(0,B.dispatch)(F.validationStore).clearValidationError(r):i||(0,B.dispatch)(F.validationStore).showValidationError(r):!c&&n&&!t.state&&t.country&&(0,B.dispatch)(F.validationStore).setValidationErrors({[r]:{message:(0,o.sprintf)(/* translators: %s will be the state field label in lowercase e.g. "state" */ /* translators: %s will be the state field label in lowercase e.g. "state" */
(0,o.__)("Please select a %s","woocommerce"),s.label.toLowerCase()),hidden:!0}})})(c,n,e)}}),[n,y,c,w,k]),e=e||`${p}`,(0,a.jsxs)("div",{id:e,className:"wc-block-components-address-form",children:[w.map((t=>{var o;if(t.hidden)return null;const i=_r(t,e,c),l=(e=>{const{autoCapitalize:t,autoComplete:s,placeholder:o,...r}=e;return r})(i);if("email"===t.key&&(i.id="email",i.errorId="billing_email"),"checkbox"===t.type){const e=t.key in n&&n[t.key],o={checked:Boolean(e),onChange:e=>{s({...n,[t.key]:e})},...l};return t.required?(0,a.jsx)(Ot.ValidatedCheckboxControl,{...t.errorMessage?{errorMessage:t.errorMessage}:{},...o},t.key):(0,a.jsx)(Ot.CheckboxControl,{...o},t.key)}if("address_1"===t.key&&"address_1"in n){const o=wr("address_1",w,n),r=wr("address_2",w,n);return(0,oe.isNull)(o)||(0,oe.isNull)(r)?null:(0,a.jsx)(br,{address1:o,address2:r,addressType:c,formId:e,onChange:(e,t)=>{s({...n,[e]:t})}},t.key)}if("address_2"===t.key)return null;if("country"===t.key&&"country"in n){const e="shipping"===c?dr:lr;return(0,a.jsx)(e,{...i,value:n.country,onChange:e=>{s({...n,country:e,state:"",postcode:""})}},t.key)}if("state"===t.key&&"state"in n&&"country"in n){const e="shipping"===c?hr:mr;return(0,a.jsx)(e,{...i,country:n.country,value:n.state,onChange:e=>s({...n,state:e})},t.key)}return"select"===t.type&&"options"in t?void 0===t.options?null:(0,a.jsx)(ar,{...i,label:i.label||"",className:(0,r.A)("wc-block-components-select-input",`wc-block-components-select-input-${t.key}`.replaceAll("/","-")),value:t.key in n?n[t.key]:"",onChange:e=>{s({...n,[t.key]:e})},options:t.options,required:t.required,errorMessage:i.errorMessage||void 0},t.key):(0,a.jsx)(Ot.ValidatedTextInput,{ref:e=>x.current[t.key]=e,...i,type:t.type,ariaDescribedBy:d,value:null!==(o=(0,ie.decodeEntities)(n[t.key]))&&void 0!==o?o:"",onChange:e=>s({...n,[t.key]:e}),customFormatter:e=>"postcode"===t.key?e.trimStart().toUpperCase():e},t.key)})),i]})},Pr=Nr,Ar=({address:e,onUpdate:t,onCancel:s,addressFields:r})=>{const[c,n]=(0,u.useState)(e),{showAllValidationErrors:i}=(0,B.useDispatch)(F.validationStore),l=function(e){const t=(0,u.useRef)(null),s=(0,u.useRef)(null),o=(0,u.useRef)(e);return(0,u.useEffect)((()=>{o.current=e}),[e]),(0,u.useCallback)((e=>{if(e)t.current=e,s.current=e.ownerDocument.activeElement;else if(s.current){const e=t.current?.contains(t.current?.ownerDocument.activeElement);var r;if(t.current?.isConnected&&!e&&(null!==(r=cr)&&void 0!==r||(cr=s.current)),o.current)o.current();else{const e=s.current;(e?.isConnected?e:cr)?.focus()}cr=null}}),[])}(),{hasValidationErrors:d,isCustomerDataUpdating:p}=(0,B.useSelect)((e=>({hasValidationErrors:e(F.validationStore).hasValidationErrors(),isCustomerDataUpdating:e(F.cartStore).isCustomerDataUpdating()})),[]),{defaultFields:m}=ot(),h=vr(r,m,"shipping",c.country),g=(0,u.useCallback)((()=>{for(const e of h)if(e.required&&!e.hidden){const t=c[e.key];if("string"==typeof t){if(""===t.trim())return!1;continue}return!1}return!0}),[h,c]),_=(0,u.useCallback)((o=>{if(o.preventDefault(),i(),!d&&g()){if(Ie()(c,e))return s();const o=Object.fromEntries(r.filter((e=>void 0!==c[e])).map((e=>[e,c[e]])));t(o)}}),[i,d,g,c,e,r,s,t]);return(0,a.jsxs)("form",{className:"wc-block-components-shipping-calculator-address",ref:l,children:[(0,a.jsx)(Pr,{fields:r,onChange:n,values:c}),(0,a.jsx)(Ho,{className:"wc-block-components-shipping-calculator-address__button",disabled:p,variant:"outlined",onClick:_,type:"submit",children:(0,o.__)("Check delivery options","woocommerce")})]})},Rr=(0,u.createContext)({shippingCalculatorID:"",showCalculator:!1,isShippingCalculatorOpen:!1,setIsShippingCalculatorOpen:()=>{}}),Ir=({onUpdate:e=()=>{},onCancel:t=()=>{},addressFields:s=["country","state","city","postcode"]})=>{const{shippingCalculatorID:o,showCalculator:r,setIsShippingCalculatorOpen:c}=(0,u.useContext)(Rr),{shippingAddress:n}=st(),i="wc/cart/shipping-calculator",l=(0,u.useCallback)((()=>{c(!1),t()}),[c,t]),d=(0,u.useCallback)((t=>{(0,B.dispatch)(F.cartStore).updateCustomerData({shipping_address:t},!1).then((()=>{(e=>{const{removeNotice:t}=(0,B.dispatch)(qe.store),{getNotices:s}=(0,B.select)(qe.store);s(e).forEach((s=>{t(s.id,e)}))})(i),c(!1),e(t)})).catch((e=>{(0,F.processErrorResponse)(e,i)}))}),[e,c]);return r?(0,a.jsxs)("div",{className:"wc-block-components-shipping-calculator",id:o,children:[(0,a.jsx)(Ot.StoreNoticesContainer,{context:i}),(0,a.jsx)(Ar,{address:n,addressFields:s,onCancel:l,onUpdate:d})]}):null},Tr=({title:e})=>{const{isShippingCalculatorOpen:t,setIsShippingCalculatorOpen:s}=(0,u.useContext)(Rr);return(0,a.jsx)(Ot.Panel,{className:"wc-block-components-totals-shipping-panel",initialOpen:!1,hasBorder:!1,title:e,state:[t,s],children:(0,a.jsx)(Ir,{})})},Br=e=>{const t=(e=>(0,p.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.total_shipping,10)+parseInt(e.total_shipping_tax,10):parseInt(e.total_shipping,10))(e);return 0===t?(0,a.jsx)("strong",{children:(0,o.__)("Free","woocommerce")}):t},Mr=()=>{const{shippingRates:e,shippingAddress:t}=je(),s=(0,B.useSelect)((e=>e(F.checkoutStore).prefersCollection())),r=Pe(e),{showCalculator:c}=(0,u.useContext)(Rr),n=s?(e=>{const t=(e||[]).flatMap((e=>e.shipping_rates)).find((e=>e.selected&&Ee(e)));if((0,oe.isObject)(t)&&(0,oe.objectHasProp)(t,"meta_data")){const e=t.meta_data.find((e=>"pickup_address"===e.key));if((0,oe.isObject)(e)&&(0,oe.objectHasProp)(e,"value")&&e.value)return e.value}return""})(e):(e=>{if(0===Object.values(e).length)return null;const t=(0,oe.isString)(C[e.country])?(0,ie.decodeEntities)(C[e.country]):"",s=(0,oe.isObject)(E[e.country])&&(0,oe.isString)(E[e.country][e.state])?(0,ie.decodeEntities)(E[e.country][e.state]):e.state,o=[];return o.push(e.postcode.toUpperCase()),o.push(e.city),o.push(s),o.push(t),o.filter(Boolean).join(", ")||null})(t),i=r?
// Translators: <address/> is the formatted shipping address.
// Translators: <address/> is the formatted shipping address.
(0,o.__)("Delivers to <address/>","woocommerce"):
// Translators: <address/> is the formatted shipping address.
// Translators: <address/> is the formatted shipping address.
(0,o.__)("No delivery options available for <address/>","woocommerce"),l=((e,t=[])=>{if(!e.country)return!1;const s=de(A,p.defaultFields,e.country);return(t.length>0?s.filter((({key:e})=>t.includes(e))):s).every((({key:t,hidden:s,required:o})=>!0===s||!1===o||pe(t,e)&&""!==e[t]))})(t,["state","city","country","postcode"]),d=(0,p.getSetting)("shippingCostRequiresAddress",!1)&&!l,m=s?
// Translators: <address/> is the pickup location.
// Translators: <address/> is the pickup location.
(0,o.__)("Collection from <address/>","woocommerce"):i,h=(0,a.jsx)("p",{className:"wc-block-components-totals-shipping-address-summary",children:n&&!d?(0,u.createInterpolateElement)(m,{address:(0,a.jsx)("strong",{children:n})}):(0,a.jsx)(a.Fragment,{children:(0,o.__)("Enter address to check delivery options","woocommerce")})});return(0,a.jsx)("div",{className:"wc-block-components-shipping-address",children:c&&(0,a.jsx)(Tr,{title:h})})};s(6562);const Or=({label:e=(0,o.__)("Shipping","woocommerce"),placeholder:t=null,collaterals:s=null})=>{const{cartTotals:r,shippingRates:c}=je(),n=Pe(c);return(0,a.jsx)("div",{className:"wc-block-components-totals-shipping",children:(0,a.jsx)(Ot.TotalsItem,{label:e,value:n?Br(r):t,description:(0,a.jsxs)(a.Fragment,{children:[!!n&&(0,a.jsx)(rr,{}),(0,a.jsx)(Mr,{}),s&&(0,a.jsx)("div",{className:"wc-block-components-totals-shipping__collaterals",children:s})]}),currency:(0,Dt.getCurrencyFromPriceResponse)(r)})})},Dr=()=>{const{extensions:e,receiveCart:t,...s}=je(),o={extensions:e,cart:s,context:"woocommerce/cart"};return(0,a.jsx)(wt.ExperimentalOrderMeta.Slot,{...o})},Lr=JSON.parse('{"xY":{"align":false,"html":false,"multiple":false,"reusable":false,"inserter":false,"lock":false},"uK":{"lock":{"type":"object","default":{"remove":true,"move":true}}}}'),Fr=[{attributes:Lr.uK,save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})}),supports:Lr.xY,migrate:({attributes:e})=>[e,[(0,d.createBlock)("woocommerce/cart-order-summary-heading-block",{content:(0,o.__)("Cart totals","woocommerce")},[]),(0,d.createBlock)("woocommerce/cart-order-summary-coupon-form-block",{},[]),(0,d.createBlock)("woocommerce/cart-order-summary-totals-block",{},[(0,d.createBlock)("woocommerce/cart-order-summary-subtotal-block",{},[]),(0,d.createBlock)("woocommerce/cart-order-summary-fee-block",{},[]),(0,d.createBlock)("woocommerce/cart-order-summary-discount-block",{},[]),(0,d.createBlock)("woocommerce/cart-order-summary-shipping-block",{},[]),(0,d.createBlock)("woocommerce/cart-order-summary-taxes-block",{},[])])]],isEligible:(e,t)=>!t.some((e=>"woocommerce/cart-order-summary-totals-block"===e.name))}],Vr=Fr;(0,d.registerBlockType)("woocommerce/cart-order-summary-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,c.useBlockProps)(),{cartTotals:s}=je(),r=(0,Dt.getCurrencyFromPriceResponse)(s),n=jt(wt.innerBlockAreas.CART_ORDER_SUMMARY),i=[["woocommerce/cart-order-summary-heading-block",{content:(0,o.__)("Cart totals","woocommerce")},[]],["woocommerce/cart-order-summary-coupon-form-block",{},[]],["woocommerce/cart-order-summary-totals-block",{},[]]];return St({clientId:e,registeredBlocks:n,defaultTemplate:i}),(0,a.jsxs)("div",{...t,children:[(0,a.jsx)(c.InnerBlocks,{allowedBlocks:n,template:i}),(0,a.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,a.jsx)(or,{currency:r,values:s})}),(0,a.jsx)(Dr,{})]})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})}),deprecated:Vr});const $r=({className:e=""})=>{const{cartTotals:t}=je(),s=(0,Dt.getCurrencyFromPriceResponse)(t);return(0,a.jsx)(Ot.TotalsWrapper,{className:e,children:(0,a.jsx)(Ot.Subtotal,{currency:s,values:t})})};(0,d.registerBlockType)("woocommerce/cart-order-summary-subtotal-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsx)("div",{...s,children:(0,a.jsx)($r,{className:t})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})}),s(2115),(0,d.registerBlockType)("woocommerce/cart-order-summary-totals-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({clientId:e})=>{const t=(0,c.useBlockProps)(),s=jt(wt.innerBlockAreas.CART_ORDER_SUMMARY_TOTALS),o=[["woocommerce/cart-order-summary-subtotal-block",{},[]],["woocommerce/cart-order-summary-fee-block",{},[]],["woocommerce/cart-order-summary-discount-block",{},[]],["woocommerce/cart-order-summary-shipping-block",{},[]],["woocommerce/cart-order-summary-taxes-block",{},[]]];return St({clientId:e,registeredBlocks:s,defaultTemplate:o}),(0,a.jsx)("div",{...t,children:(0,a.jsx)(c.InnerBlocks,{allowedBlocks:s,template:o})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save(),children:(0,a.jsx)(c.InnerBlocks.Content,{})})});const zr=({className:e})=>{const{cartFees:t,cartTotals:s}=je(),o=(0,Dt.getCurrencyFromPriceResponse)(s);return(0,a.jsx)(Ot.TotalsWrapper,{className:e,children:(0,a.jsx)(Ot.TotalsFees,{currency:o,cartFees:t})})};(0,d.registerBlockType)("woocommerce/cart-order-summary-fee-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsx)("div",{...s,children:(0,a.jsx)(zr,{className:t})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});const qr=()=>{const{extensions:e,receiveCart:t,...s}=je(),o={extensions:e,cart:s,context:"woocommerce/cart"};return(0,a.jsx)(wt.ExperimentalDiscountsMeta.Slot,{...o})},Hr=({className:e})=>{const{cartTotals:t,cartCoupons:s}=je(),{removeCoupon:o,isRemovingCoupon:r}=Ao("wc/cart"),c=(0,Dt.getCurrencyFromPriceResponse)(t);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Ot.TotalsWrapper,{className:e,children:(0,a.jsx)(sr,{cartCoupons:s,currency:c,isRemovingCoupon:r,removeCoupon:o,values:t})}),(0,a.jsx)(qr,{})]})};(0,d.registerBlockType)("woocommerce/cart-order-summary-discount-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsx)("div",{...s,children:(0,a.jsx)(Hr,{className:t})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});var Ur=s(5614),Wr=(s(7575),s(8034)),Yr=s(2624),Gr=s(4144);const Qr=e=>{switch(e){case"success":case"warning":case"info":case"default":return"polite";default:return"assertive"}},Kr=e=>{switch(e){case"success":return Wr.A;case"warning":case"info":case"error":return Yr.A;default:return Gr.A}},Zr=({className:e,status:t="default",children:s,spokenMessage:c=s,onRemove:n=()=>{},isDismissible:i=!0,politeness:d=Qr(t),summary:p})=>(((e,t)=>{const s="string"==typeof e?e:(0,u.renderToString)(e);(0,u.useEffect)((()=>{s&&(0,Tt.speak)(s,t)}),[s,t])})(c,d),(0,a.jsxs)("div",{className:(0,r.A)(e,"wc-block-components-notice-banner","is-"+t,{"is-dismissible":i}),children:[(0,a.jsx)(l.A,{icon:Kr(t)}),(0,a.jsxs)("div",{className:"wc-block-components-notice-banner__content",children:[p&&(0,a.jsx)("p",{className:"wc-block-components-notice-banner__summary",children:p}),s]}),!!i&&(0,a.jsx)(Ho,{className:"wc-block-components-notice-banner__dismiss","aria-label":(0,o.__)("Dismiss this notice","woocommerce"),onClick:e=>{"function"==typeof e?.preventDefault&&e.preventDefault&&e.preventDefault(),n()},removeTextWrap:!0,children:(0,a.jsx)(l.A,{icon:Ur.A})})]})),Jr=e=>{const t=(0,p.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10);let s=(0,a.jsxs)(a.Fragment,{children:[Number.isFinite(t)&&(0,a.jsx)(Ot.FormattedMonetaryAmount,{currency:(0,Dt.getCurrencyFromPriceResponse)(e),value:t}),(0,a.jsxs)("span",{className:"wc-block-components-shipping-rates-control__package__delivery_time",children:[Number.isFinite(t)&&e.delivery_time?" — ":null,(0,ie.decodeEntities)(e.delivery_time)]})]});return 0===t&&(s=(0,a.jsxs)("span",{className:"wc-block-components-shipping-rates-control__package__description--free",children:[(0,o.__)("Free","woocommerce"),(0,a.jsx)("span",{className:"wc-block-components-shipping-rates-control__package__delivery_time",children:e.delivery_time&&" — "+(0,ie.decodeEntities)(e.delivery_time)})]})),{label:(0,ie.decodeEntities)(e.name),value:e.rate_id,description:s}},Xr=({className:e="",noResultsMessage:t,onSelectRate:s,rates:o,renderOption:r=Jr,selectedRate:c,disabled:n=!1,highlightChecked:i=!1})=>{const l=c?.rate_id||"",d=ze(l),[p,m]=(0,u.useState)(null!=l?l:"");return(0,u.useEffect)((()=>{l&&l!==d&&l!==p&&m(l)}),[l,p,d]),(0,u.useEffect)((()=>{!p&&o.length>0&&(m(o[0].rate_id),s(o[0].rate_id))}),[s,o,p]),0===o.length?t:(0,a.jsx)(Ot.RadioControl,{className:e,onChange:e=>{m(e),s(e)},highlightChecked:i,disabled:n,selected:p,options:o.map(r),descriptionStackingDirection:"column"})},ec=({packageData:e})=>(0,a.jsx)("ul",{className:"wc-block-components-shipping-rates-control__package-items",children:Object.values(e.items).map((e=>{const t=(0,ie.decodeEntities)(e.name),s=e.quantity;return(0,a.jsx)("li",{className:"wc-block-components-shipping-rates-control__package-item",children:(0,a.jsx)(Ot.Label,{label:s>1?`${t} × ${s}`:`${t}`,allowHTML:!0,screenReaderLabel:(0,o.sprintf)(/* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */ /* translators: %1$s name of the product (ie: Sunglasses), %2$d number of units in the current cart package */
(0,o._n)("%1$s (%2$d unit)","%1$s (%2$d units)",s,"woocommerce"),t,s)})},e.key)}))});s(2793);const tc=({packageId:e,className:t="",noResultsMessage:s,renderOption:o,packageData:c,collapsible:n,showItems:i,highlightChecked:l=!1})=>{const{selectShippingRate:d,isSelectingRate:p,shippingRates:m}=Me(),h=m?.length||1,[g,_]=(0,u.useState)(0),w=h>1||g>1;(0,u.useEffect)((()=>{const e=()=>{_(document.querySelectorAll(".wc-block-components-shipping-rates-control__package").length)};e();const t=new MutationObserver(e);return t.observe(document.body,{childList:!0,subtree:!0}),()=>{t.disconnect()}}),[]);const k=null!=i?i:w,b=null!=n?n:w,{selectedOptionNumber:y,selectedOption:x}=(0,u.useMemo)((()=>({selectedOptionNumber:c?.shipping_rates?.findIndex((e=>e?.selected)),selectedOption:c?.shipping_rates?.find((e=>e?.selected))})),[c?.shipping_rates]),f=b||k?(0,a.jsxs)("div",{className:"wc-block-components-shipping-rates-control__package-header",children:[(0,a.jsx)("div",{className:"wc-block-components-shipping-rates-control__package-title",dangerouslySetInnerHTML:{__html:Xt(c.name)}}),b&&(0,a.jsx)("div",{className:"wc-block-components-totals-shipping__via",children:(0,ie.decodeEntities)(x?.name)}),k&&(0,a.jsx)(ec,{packageData:c})]}):null,v=(0,u.useCallback)((t=>{d(t,e)}),[e,d]),j={className:t,noResultsMessage:s,rates:c.shipping_rates,onSelectRate:v,selectedRate:c.shipping_rates.find((e=>e.selected)),renderOption:o,disabled:p,highlightChecked:l};return b?(0,a.jsx)(Ot.Panel,{className:(0,r.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":p}),initialOpen:!1,title:f,children:(0,a.jsx)(Xr,{...j})}):(0,a.jsxs)("div",{className:(0,r.A)("wc-block-components-shipping-rates-control__package",t,{"wc-block-components-shipping-rates-control__package--disabled":p,"wc-block-components-shipping-rates-control__package--first-selected":!p&&0===y,"wc-block-components-shipping-rates-control__package--last-selected":!p&&y===c?.shipping_rates?.length-1}),children:[f,(0,a.jsx)(Xr,{...j})]})},sc=({packages:e,showItems:t,collapsible:s,noResultsMessage:o,renderOption:r,context:c=""})=>e.length?(0,a.jsx)(a.Fragment,{children:e.map((({package_id:e,...n})=>(0,a.jsx)(tc,{highlightChecked:"woocommerce/cart"!==c,packageId:e,packageData:n,collapsible:s,showItems:t,noResultsMessage:o,renderOption:r},e)))}):null,oc=({shippingRates:e,isLoadingRates:t,className:s,collapsible:r,showItems:c,noResultsMessage:n=(0,a.jsx)(a.Fragment,{}),renderOption:i,context:l})=>{const d=(e=>e.reduce((function(e,t){return e+t.shipping_rates.length}),0))(e),p=Se(e),m=ze(d),h=ze(p);(0,u.useEffect)((()=>{var e,s;t||m===d&&h===p||(s=d,1===(e=p)?(0,Tt.speak)((0,o.sprintf)(/* translators: %d number of shipping options found. */ /* translators: %d number of shipping options found. */
(0,o._n)("%d shipping option was found.","%d shipping options were found.",s,"woocommerce"),s)):(0,Tt.speak)((0,o.sprintf)(/* translators: %d number of shipping packages packages. */ /* translators: %d number of shipping packages packages. */
(0,o._n)("Shipping option searched for %d package.","Shipping options searched for %d packages.",e,"woocommerce"),e)+" "+(0,o.sprintf)(/* translators: %d number of shipping options available. */ /* translators: %d number of shipping options available. */
(0,o._n)("%d shipping option was found","%d shipping options were found",s,"woocommerce"),s)))}),[t,d,p,m,h]);const{extensions:g,receiveCart:_,...w}=je(),k={className:s,collapsible:r,showItems:c,noResultsMessage:n,renderOption:i,extensions:g,cart:w,components:{ShippingRatesControlPackage:tc},context:l},{isEditor:b}=O(),{hasSelectedLocalPickup:y,selectedRates:x}=Me(),f=(0,oe.isObject)(x)?Object.values(x):[],v=f.every((e=>e===f[0]));return(0,a.jsxs)(bo,{isLoading:t,screenReaderLabel:(0,o.__)("Loading shipping rates…","woocommerce"),showSpinner:!0,children:[y&&"woocommerce/cart"===l&&e.length>1&&!v&&!b&&(0,a.jsx)(Zr,{className:"wc-block-components-notice",isDismissible:!1,status:"warning",children:(0,o.__)("Multiple shipments must have the same pickup location","woocommerce")}),(0,a.jsx)(wt.ExperimentalOrderShippingPackages.Slot,{...k}),(0,a.jsx)(wt.ExperimentalOrderShippingPackages,{children:(0,a.jsx)(sc,{packages:e,noResultsMessage:n,renderOption:i})})]})},rc=()=>{const{shippingRates:e,isLoadingRates:t}=je();return(0,a.jsxs)("fieldset",{className:"wc-block-components-totals-shipping__fieldset",children:[(0,a.jsx)("legend",{className:"screen-reader-text",children:(0,o.__)("Shipping options","woocommerce")}),(0,a.jsx)(oc,{className:"wc-block-components-totals-shipping__options",shippingRates:e,isLoadingRates:t,context:"woocommerce/cart"})]})},cc=({className:e})=>{const{isEditor:t}=O(),{cartNeedsShipping:s,shippingRates:r}=je(),[c,n]=(0,u.useState)(!1);if(!s)return null;if(t&&0===Se(r))return null;const i=(0,p.getSetting)("isShippingCalculatorEnabled",!0)&&y,l=(e=>!!Pe(e)&&e.every((e=>e.shipping_rates.every((e=>!e.selected||Ee(e))))))(r);return(0,a.jsx)(wt.TotalsWrapper,{className:e,children:(0,a.jsx)(Rr.Provider,{value:{showCalculator:i,shippingCalculatorID:"shipping-calculator-form-wrapper",isShippingCalculatorOpen:c,setIsShippingCalculatorOpen:n},children:(0,a.jsx)(Or,{label:l?(0,o.__)("Pickup","woocommerce"):(0,o.__)("Delivery","woocommerce"),placeholder:i?null:(0,a.jsx)("span",{className:"wc-block-components-shipping-placeholder__value",children:(0,o.__)("Calculated at checkout","woocommerce")}),collaterals:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(rc,{}),!i&&Ae(r)&&(0,a.jsx)("div",{className:"wc-block-components-totals-shipping__delivery-options-notice",children:(0,o.__)("Delivery options will be calculated during checkout","woocommerce")})]})})})})};(0,d.registerBlockType)("woocommerce/cart-order-summary-shipping-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsxs)("div",{...s,children:[(0,a.jsx)(c.InspectorControls,{children:!!x&&(0,a.jsxs)(ws.PanelBody,{title:(0,o.__)("Shipping Calculations","woocommerce"),children:[(0,a.jsx)("p",{className:"wc-block-checkout__controls-text",children:(0,o.__)("Options that control shipping can be managed in your store settings.","woocommerce")}),(0,a.jsx)(ws.ExternalLink,{href:`${p.ADMIN_URL}admin.php?page=wc-settings&tab=shipping&section=options`,children:(0,o.__)("Manage shipping options","woocommerce")})," "]})}),(0,a.jsx)(It,{children:(0,a.jsx)(cc,{className:t})})]})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});var nc=s(1686);const ac=({className:e})=>{const t=(0,p.getSetting)("couponsEnabled",!0),{applyCoupon:s,isApplyingCoupon:o}=Ao("wc/cart");return t?(0,a.jsx)(Ot.TotalsWrapper,{className:e,children:(0,a.jsx)(er,{onSubmit:s,isLoading:o,instanceId:"coupon"})}):null};(0,d.registerBlockType)("woocommerce/cart-order-summary-coupon-form-block",{icon:{src:(0,a.jsx)(l.A,{icon:nc.A,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e})=>{const{className:t}=e,s=(0,c.useBlockProps)();return(0,a.jsx)("div",{...s,children:(0,a.jsx)(It,{children:(0,a.jsx)(ac,{className:t})})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})});const ic=({className:e,showRateAfterTaxName:t})=>{const{cartTotals:s}=je();if((0,p.getSetting)("displayCartPricesIncludingTax",!1)||parseInt(s.total_tax,10)<=0)return null;const o=(0,Dt.getCurrencyFromPriceResponse)(s);return(0,a.jsx)(Ot.TotalsWrapper,{className:e,children:(0,a.jsx)(Ot.TotalsTaxes,{showRateAfterTaxName:t,currency:o,values:s})})},lc={showRateAfterTaxName:{type:"boolean",default:(0,p.getSetting)("displayCartPricesIncludingTax",!1)},lock:{type:"object",default:{remove:!0,move:!1}}};(0,d.registerBlockType)("woocommerce/cart-order-summary-taxes-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},attributes:lc,edit:({attributes:e,setAttributes:t})=>{const{className:s,showRateAfterTaxName:r}=e,n=(0,c.useBlockProps)(),i=(0,p.getSetting)("taxesEnabled"),l=(0,p.getSetting)("displayItemizedTaxes",!1),d=(0,p.getSetting)("displayCartPricesIncludingTax",!1);return(0,a.jsxs)("div",{...n,children:[(0,a.jsx)(c.InspectorControls,{children:i&&l&&!d&&(0,a.jsx)(ws.PanelBody,{title:(0,o.__)("Taxes","woocommerce"),children:(0,a.jsx)(ws.ToggleControl,{label:(0,o.__)("Show rate after tax name","woocommerce"),help:(0,o.__)("Show the percentage rate alongside each tax line in the summary.","woocommerce"),checked:r,onChange:()=>t({showRateAfterTaxName:!r})})})}),(0,a.jsx)(ic,{className:s,showRateAfterTaxName:r})]})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})}),s(3580),(0,d.registerBlockType)("woocommerce/cart-order-summary-heading-block",{icon:{src:(0,a.jsx)(l.A,{icon:Xo,className:"wc-block-editor-components-block-icon"})},edit:({attributes:e,setAttributes:t})=>{const{content:s="",className:o=""}=e,n=(0,c.useBlockProps)();return(0,a.jsx)("div",{...n,children:(0,a.jsx)("h2",{className:(0,r.A)(o,"wc-block-cart__totals-title"),children:(0,a.jsx)(c.PlainText,{className:"",value:s,onChange:e=>t({content:e}),style:{backgroundColor:"transparent"}})})})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save()})}),s(9184);const dc=(e,t=!0)=>{t?window.document.body.classList.add(e):window.document.body.classList.remove(e)},uc=({attributes:e,setAttributes:t})=>{const{hasDarkControls:s,showFormStepNumbers:r}=e;return(0,a.jsx)(c.InspectorControls,{children:(0,a.jsxs)(ws.PanelBody,{title:(0,o.__)("Style","woocommerce"),children:[(0,a.jsx)(ws.ToggleControl,{label:(0,o.__)("Show form step numbers","woocommerce"),checked:r,onChange:()=>t({showFormStepNumbers:!r})}),(0,a.jsx)(ws.ToggleControl,{label:(0,o.__)("Dark mode inputs","woocommerce"),help:(0,o.__)("Inputs styled specifically for use on dark background colors.","woocommerce"),checked:s,onChange:()=>t({hasDarkControls:!s})})]})})},pc=window.wp.editor,mc=window.wp.coreData;function hc({block:e}){const t="checkout"===e?_:w,s="checkout"===e?"woocommerce_checkout_page_id":"woocommerce_cart_page_id",{saveEntityRecord:r}=(0,B.useDispatch)(mc.store),{editPost:c,savePost:n}=(0,B.useDispatch)(pc.store),{slug:i,postPublished:l,currentPostId:d}=(0,B.useSelect)((s=>{const{getEntityRecord:o}=s(mc.store),{isCurrentPostPublished:r,getCurrentPostId:c}=s(pc.store);return{slug:o("postType","page",t)?.slug||e,postPublished:r(),currentPostId:c()}}),[]),[p,m]=(0,u.useState)("pristine"),h=(0,u.useCallback)((()=>{m("updating"),Promise.resolve().then((()=>Je()({path:`/wc/v3/settings/advanced/${s}`,method:"GET"}))).catch((e=>{"rest_setting_setting_invalid"===e.code&&m("error")})).then((()=>{if(!l)return c({status:"publish"}),n()})).then((()=>Je()({path:`/wc/v3/settings/advanced/${s}`,method:"POST",data:{value:d.toString()}}))).then((()=>{if(0!==t)return r("postType","page",{id:t,slug:`${i}-2`})})).then((()=>c({slug:i}))).then((()=>n())).then((()=>m("updated")))}),[l,c,n,s,d,t,r,i]);let g;return g="checkout"===e?(0,u.createInterpolateElement)((0,o.__)("If you would like to use this block as your default checkout, <a>update your page settings</a>.","woocommerce"),{a:(0,a.jsx)("a",{href:"#",onClick:h,children:(0,o.__)("update your page settings","woocommerce")})}):(0,u.createInterpolateElement)((0,o.__)("If you would like to use this block as your default cart, <a>update your page settings</a>.","woocommerce"),{a:(0,a.jsx)("a",{href:"#",onClick:h,children:(0,o.__)("update your page settings","woocommerce")})}),"string"==typeof pagenow&&"site-editor"===pagenow||d===t||"dismissed"===p?null:(0,a.jsx)(ws.Notice,{className:"wc-default-page-notice",status:"updated"===p?"success":"info",onRemove:()=>m("dismissed"),spokenMessage:"updated"===p?(0,o.__)("Page settings updated","woocommerce"):g,children:"updated"===p?(0,o.__)("Page settings updated","woocommerce"):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("p",{children:g})})})}s(3650);const gc=[],_c=e=>{const[t,s,o]=(()=>{const e={};(0,p.getSetting)("incompatibleExtensions")&&(0,p.getSetting)("incompatibleExtensions").forEach((t=>{e[t.id]=t.title}));const t=Object.keys(e),s=t.length;return[e,t,s]})(),[r,c,n]=(()=>{const{incompatiblePaymentMethods:e}=(0,B.useSelect)((e=>{const{getIncompatiblePaymentMethods:t}=e(F.paymentStore);return{incompatiblePaymentMethods:t()}}),[]),t=Object.keys(e);return[e,t,t.length]})(),a={...t,...r},i=[...s,...c],l=o+n,[d,m]=((e,t)=>{const[s,o]=(0,u.useState)((()=>{const s=window.localStorage.getItem(e);if(s)try{return JSON.parse(s)}catch{console.error(`Value for key '${e}' could not be retrieved from localStorage because it can't be parsed.`)}return t}));return(0,u.useEffect)((()=>{try{window.localStorage.setItem(e,JSON.stringify(s))}catch{console.error(`Value for key '${e}' could not be saved in localStorage because it can't be converted into a string.`)}}),[e,s]),[s,o]})("wc-blocks_dismissed_incompatible_extensions_notices",gc),[h,g]=(0,u.useState)(!1),_=d.some((t=>{return Object.keys(t).includes(e)&&(s=t[e],o=i,s.length===o.length&&new Set([...s,...o]).size===s.length);var s,o})),w=0===l||_;return(0,u.useEffect)((()=>{g(!w),w||_||m((t=>t.reduce(((t,s)=>(Object.keys(s).includes(e)||t.push(s),t)),[])))}),[w,_,m,e]),[h,()=>{const t=new Set(d);t.add({[e]:i}),m([...t])},(k=a,Object.fromEntries(Object.entries(k).sort((([,e],[,t])=>e.localeCompare(t))))),l];var k};var wc=s(1244),kc=s.n(wc);kc()("wc-admin:tracks:stats");const bc=kc()("wc-admin:tracks");function yc(e,t){if(bc("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!!window._tkq&&!!window.wcTracks&&!!window.wcTracks.isEnabled}),!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)return!1;window.wcTracks.recordEvent(e,t)}const xc=({blocks:e,findCondition:t})=>{for(const s of e){if(t(s))return s;if(s.innerBlocks){const e=xc({blocks:s.innerBlocks,findCondition:t});if(e)return e}}},fc=({blockType:e="woocommerce/cart"})=>"woocommerce/cart"===e?(0,a.jsx)("p",{children:(0,o.__)("If you continue, the cart block will be replaced with the classic experience powered by shortcodes. This means that you may lose customizations that you made to the cart block.","woocommerce")}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("p",{children:(0,o.__)("If you continue, the checkout block will be replaced with the classic experience powered by shortcodes. This means that you may lose:","woocommerce")}),(0,a.jsxs)("ul",{className:"cross-list",children:[(0,a.jsx)("li",{children:(0,o.__)("Customizations and updates to the block","woocommerce")}),(0,a.jsx)("li",{children:(0,o.__)("Additional local pickup options created for the new checkout","woocommerce")})]})]});function vc({block:e,clientId:t,type:s}){const{createInfoNotice:r}=(0,B.useDispatch)(qe.store),{replaceBlock:n,selectBlock:i}=(0,B.useDispatch)(c.store),[l,p]=(0,u.useState)(!1),m=()=>p(!1),{undo:h}=(0,B.useDispatch)(mc.store),[,,g,_]=_c(e),w="woocommerce/cart"===e,k=w?(0,o.__)("Switch to classic cart","woocommerce"):(0,o.__)("Switch to classic checkout","woocommerce"),b=w?(0,o.__)("Switched to classic cart.","woocommerce"):(0,o.__)("Switched to classic checkout.","woocommerce"),y=w?"cart":"checkout",x={shortcode:y,notice:"incompatible"===s?"incompatible_notice":"generic_notice",incompatible_extensions_count:_,incompatible_extensions_names:JSON.stringify(g)},{getBlocks:f}=(0,B.useSelect)((e=>({getBlocks:e(c.store).getBlocks})),[]),v=()=>{h(),yc("switch_to_classic_shortcode_undo",x)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ws.Button,{variant:"secondary",onClick:()=>{yc("switch_to_classic_shortcode_click",x),p(!0)},children:k}),l&&(0,a.jsxs)(ws.Modal,{size:"medium",title:k,onRequestClose:m,className:"wc-blocks-switch-to-classic-shortcode-modal-content",children:[(0,a.jsx)(fc,{blockType:e}),(0,a.jsxs)(ws.TabbableContainer,{className:"wc-blocks-switch-to-classic-shortcode-modal-actions",children:[(0,a.jsx)(ws.Button,{variant:"primary",isDestructive:!0,onClick:()=>{n(t,(0,d.createBlock)("woocommerce/classic-shortcode",{shortcode:y})),yc("switch_to_classic_shortcode_confirm",x),(()=>{const e=xc({blocks:f(),findCondition:e=>"woocommerce/classic-shortcode"===e.name});e&&i(e.clientId)})(),r(b,{actions:[{label:(0,o.__)("Undo","woocommerce"),onClick:v}],type:"snackbar"}),m()},children:(0,o.__)("Switch","woocommerce")})," ",(0,a.jsx)(ws.Button,{variant:"secondary",onClick:()=>{yc("switch_to_classic_shortcode_cancel",x),m()},children:(0,o.__)("Cancel","woocommerce")})]})]})]})}function jc({block:e,clientId:t}){const[s,r,c,n]=_c(e);if(!s)return null;const i=(0,a.jsx)(a.Fragment,{children:n>1?(0,u.createInterpolateElement)((0,o.__)("Some active extensions do not yet support this block. This may impact the shopper experience. <a>Learn more</a>","woocommerce"),{a:(0,a.jsx)(ws.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-store-editing/customizing-cart-and-checkout/#incompatible-extensions/"})}):(0,u.createInterpolateElement)((0,o.sprintf)(
// translators: %s is the name of the extension.
// translators: %s is the name of the extension.
(0,o.__)("<strong>%s</strong> does not yet support this block. This may impact the shopper experience. <a>Learn more</a>","woocommerce"),Object.values(c)[0]),{strong:(0,a.jsx)("strong",{}),a:(0,a.jsx)(ws.ExternalLink,{href:"https://woocommerce.com/document/woocommerce-store-editing/customizing-cart-and-checkout/#incompatible-extensions/"})})}),d=Object.entries(c),p=d.length-2;return(0,a.jsx)(ws.Notice,{className:"wc-blocks-incompatible-extensions-notice",status:"warning",onRemove:r,spokenMessage:i,children:(0,a.jsxs)("div",{className:"wc-blocks-incompatible-extensions-notice__content",children:[(0,a.jsx)(l.A,{className:"wc-blocks-incompatible-extensions-notice__warning-icon",icon:(0,a.jsx)(lo,{})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{children:i}),n>1&&(0,a.jsx)("ul",{children:d.slice(0,2).map((([e,t])=>(0,a.jsx)("li",{className:"wc-blocks-incompatible-extensions-notice__element",children:t},e)))}),d.length>2&&(0,a.jsxs)("details",{children:[(0,a.jsxs)("summary",{children:[(0,a.jsx)("span",{children:(0,o.sprintf)(
// translators: %s is the number of incompatible extensions.
// translators: %s is the number of incompatible extensions.
(0,o._n)("%s more incompatibility","%s more incompatibilities",p,"woocommerce"),p)}),(0,a.jsx)(l.A,{icon:nr.A})]}),(0,a.jsx)("ul",{children:d.slice(2).map((([e,t])=>(0,a.jsx)("li",{className:"wc-blocks-incompatible-extensions-notice__element",children:t},e)))})]}),(0,a.jsx)(vc,{block:e,clientId:t,type:"incompatible"})]})]})})}s(4490),s(6342);var Sc=s(3791);s(4268);const Cc=({text:e,title:t=(0,o.__)("Feedback?","woocommerce"),url:s})=>{const[r,c]=(0,u.useState)(!1);return(0,u.useEffect)((()=>{c(!0)}),[]),(0,a.jsx)(a.Fragment,{children:r&&(0,a.jsxs)("div",{className:"wc-block-feedback-prompt",children:[(0,a.jsx)(l.A,{icon:Sc.A}),(0,a.jsx)("h2",{className:"wc-block-feedback-prompt__title",children:t}),(0,a.jsx)("p",{className:"wc-block-feedback-prompt__text",children:e}),(0,a.jsxs)("a",{href:s,className:"wc-block-feedback-prompt__link",rel:"noreferrer noopener",target:"_blank",children:[(0,o.__)("Give us your feedback.","woocommerce"),(0,a.jsx)(l.A,{icon:ao.A,size:16})]})]})})},Ec=()=>(0,a.jsx)(Cc,{text:(0,o.__)("We are currently working on improving our cart and checkout blocks to provide merchants with the tools and customization options they need.","woocommerce"),url:"https://github.com/woocommerce/woocommerce/discussions/new?category=checkout-flow&labels=type%3A+product%20feedback"}),Nc=(0,bt.createHigherOrderComponent)((e=>t=>{const{clientId:s,name:o,isSelected:r}=t,{isCart:n,isCheckout:i,parentId:l}=(0,B.useSelect)((e=>{const{getBlockParentsByBlockName:t,getBlockName:o}=e(c.store),r=t(s,["woocommerce/cart","woocommerce/checkout"]).reduce(((e,t)=>(e[o(t)]=t,e)),{}),n=o(s),a=Object.keys(r).includes("woocommerce/cart"),i=Object.keys(r).includes("woocommerce/checkout"),l="woocommerce/cart"===n||a,d=l?"woocommerce/cart":"woocommerce/checkout";return{isCart:l,isCheckout:"woocommerce/checkout"===n||i,parentId:n===d?s:r[d]}}));return o.startsWith("woocommerce/")&&r&&(n||i)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.InspectorControls,{children:[(0,a.jsx)(jc,{block:n?"woocommerce/cart":"woocommerce/checkout",clientId:l}),(0,a.jsx)(hc,{block:i?"checkout":"cart"}),(0,a.jsx)(Ec,{})]}),(0,a.jsx)(e,{...t},"edit")]}):(0,a.jsx)(e,{...t},"edit")}),"withSidebarNotices");(0,Te.hasFilter)("editor.BlockEdit","woocommerce/add/sidebar-compatibility-notice")||(0,Te.addFilter)("editor.BlockEdit","woocommerce/add/sidebar-compatibility-notice",Nc,11);const Pc=(0,a.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{fill:"none",d:"M0 0h24v24H0V0z"}),(0,a.jsx)("path",{d:"M12 6a9.77 9.77 0 0 1 8.82 5.5C19.17 14.87 15.79 17 12 17s-7.17-2.13-8.82-5.5A9.77 9.77 0 0 1 12 6m0-2C7 4 2.73 7.11 1 11.5 2.73 15.89 7 19 12 19s9.27-3.11 11-7.5C21.27 7.11 17 4 12 4zm0 5a2.5 2.5 0 0 1 0 5 2.5 2.5 0 0 1 0-5m0-2c-2.48 0-4.5 2.02-4.5 4.5S9.52 16 12 16s4.5-2.02 4.5-4.5S14.48 7 12 7z"})]}),Ac=(e,t,s=!0)=>{const{updateBlockAttributes:o,selectBlock:r}=(0,B.dispatch)("core/block-editor");o(e,{currentView:t}),s&&r((0,B.select)("core/block-editor").getBlock(e)?.innerBlocks.find((e=>e.name===t))?.clientId||e)},Rc={views:[],currentView:"",viewClientId:""},Ic=(e,t=10,s=0)=>{const o=s+1;if(o>t)return Rc;const{getBlockAttributes:r,getBlockRootClientId:c}=(0,B.select)("core/block-editor"),n=c(e);if(null===n||""===n)return Rc;const a=r(n);return a?void 0!==a.editorViews?{views:a.editorViews,currentView:a.currentView||a.editorViews[0].view,viewClientId:n}:Ic(n,t,o):Rc},Tc=({currentView:e,views:t,clientId:s})=>{const{getBlockName:r,getSelectedBlockClientId:n,getBlockParentsByBlockName:i}=(0,B.useSelect)((e=>{const t=e("core/block-editor");return{getBlockName:t.getBlockName,getSelectedBlockClientId:t.getSelectedBlockClientId,getBlockParentsByBlockName:t.getBlockParentsByBlockName}}),[]),d=n(),p=((e,t)=>t.find((t=>t.view===e)))(e,t)||t[0],m=p.label;return(0,u.useLayoutEffect)((()=>{const o=d?r(d):null;if(!o||e===o)return;const c=t.map((e=>e.view));if(c.includes(o))return void Ac(s,o);const n=i(d,c),a=1===n.length?r(n[0]):null;a&&e!==a&&Ac(s,a,!1)}),[s,e,r,i,d,t]),(0,a.jsx)(c.BlockControls,{children:(0,a.jsx)(ws.ToolbarGroup,{children:(0,a.jsx)(ws.ToolbarDropdownMenu,{label:(0,o.__)("Switch view","woocommerce"),text:m,icon:(0,a.jsx)(l.A,{icon:Pc,style:{marginRight:"8px"}}),controls:t.map((t=>({...t,title:(0,a.jsx)("span",{style:{marginLeft:"8px"},children:t.label}),isActive:t.view===e,onClick:()=>{Ac(s,t.view)}})))})})})};(0,Te.hasFilter)("editor.BlockEdit","woocommerce/with-view-switcher")||(0,Te.addFilter)("editor.BlockEdit","woocommerce/with-view-switcher",(e=>t=>{const{clientId:s}=t,{views:o,currentView:r,viewClientId:c}=(0,B.useSelect)((e=>{const t=e("core/block-editor").getBlockAttributes(s);return t?.editorViews?{views:t.editorViews,currentView:t.currentView,viewClientId:s}:Ic(s)}));return 0===o.length?(0,a.jsx)(e,{...t}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Tc,{currentView:r,views:o,clientId:c}),(0,a.jsx)(e,{...t})]})}),11),(0,Te.hasFilter)("blocks.registerBlockType","core/lock/addAttribute")||(0,B.subscribe)((()=>{const e=(0,B.select)(c.store);if(!e)return;const t=e.getSelectedBlock();t&&(dc("wc-lock-selected-block--remove",!!t?.attributes?.lock?.remove),dc("wc-lock-selected-block--move",!!t?.attributes?.lock?.move))}));const Bc=["woocommerce/filled-cart-block","woocommerce/empty-cart-block"];s(7826);const Mc={isPreview:{type:"boolean",default:!1},currentView:{type:"string",default:"woocommerce/filled-cart-block",source:"readonly"},editorViews:{type:"object",default:[{view:"woocommerce/filled-cart-block",label:(0,o.__)("Filled Cart","woocommerce"),icon:(0,a.jsx)(l.A,{icon:kt})},{view:"woocommerce/empty-cart-block",label:(0,o.__)("Empty Cart","woocommerce"),icon:(0,a.jsx)(l.A,{icon:Go})}]},hasDarkControls:{type:"boolean",default:(0,p.getSetting)("hasDarkEditorStyleSupport",!1)},isShippingCalculatorEnabled:{type:"boolean",default:(0,p.getSetting)("isShippingCalculatorEnabled",!0)},checkoutPageId:{type:"number",default:0},showRateAfterTaxName:{type:"boolean",default:!0},align:{type:"string",default:"wide"}},Oc={title:(0,o.__)("Cart","woocommerce"),apiVersion:3,icon:{src:(0,a.jsx)(l.A,{icon:i,className:"wc-block-editor-components-block-icon"})},category:"woocommerce",keywords:[(0,o.__)("WooCommerce","woocommerce")],description:(0,o.__)("Shopping cart.","woocommerce"),supports:{align:["wide"],html:!1,multiple:!1},example:{attributes:{isPreview:!0},viewportWidth:800},attributes:Mc,edit:({clientId:e,className:t,attributes:s,setAttributes:n})=>{const{hasDarkControls:i,currentView:l,isPreview:p=!1}=s,m=((e={})=>{const t=(0,u.useRef)(),s=(0,c.useBlockProps)({ref:t,...e});return(({ref:e})=>{const t=(0,Te.hasFilter)("blocks.registerBlockType","core/lock/addAttribute"),s=e.current;(0,u.useEffect)((()=>{if(s&&!t)return s.addEventListener("keydown",e,{capture:!0,passive:!1}),()=>{s.removeEventListener("keydown",e,{capture:!0})};function e(e){const{keyCode:t,target:s}=e;if(!(s instanceof HTMLElement))return;if(t!==Bt.BACKSPACE&&t!==Bt.DELETE)return;if((0,Pt.isTextField)(s))return;const o=s;if(void 0===o.dataset.block)return;const r=(e=>{if(!e)return!1;const{getBlock:t}=(0,B.select)(c.store),s=t(e);if("boolean"==typeof s?.attributes?.lock?.remove)return s.attributes.lock.remove;const o=(0,d.getBlockType)(s.name);return"boolean"==typeof o?.attributes?.lock?.default?.remove&&o?.attributes?.lock?.default?.remove})(o.dataset.block);r&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation())}}),[s,t])})({ref:t}),s})({className:(0,r.A)(t,"wp-block-woocommerce-cart",{"is-editor-preview":p})}),h=(0,u.useRef)((0,ue.getQueryArg)(window.location.href,"focus"));return(0,u.useEffect)((()=>{"cart"!==h.current||(0,B.select)("core/block-editor").hasSelectedBlock()||((0,B.dispatch)("core/block-editor").selectBlock(e),(0,B.dispatch)("core/interface").enableComplementaryArea("core/edit-site","edit-site/block-inspector"))}),[e]),(0,a.jsxs)("div",{...m,children:[(0,a.jsx)(c.InspectorControls,{children:(0,a.jsx)(uc,{attributes:s,setAttributes:n})}),(0,a.jsx)(T,{header:(0,o.__)("Cart Block Error","woocommerce"),text:(0,o.__)("There was an error whilst rendering the cart block. If this problem continues, try re-creating the block.","woocommerce"),showErrorMessage:!0,errorMessagePrefix:(0,o.__)("Error message:","woocommerce"),children:(0,a.jsx)(D,{previewData:{previewCart:_t},currentView:l,isPreview:!!p,children:(0,a.jsx)(Ct.Provider,{value:{hasDarkControls:i},children:(0,a.jsx)(wt.SlotFillProvider,{children:(0,a.jsx)(nt,{children:(0,a.jsx)(c.InnerBlocks,{allowedBlocks:Bc,template:[["woocommerce/filled-cart-block",{},[]],["woocommerce/empty-cart-block",{},[]]],templateLock:"insert"})})})})})})]})},save:()=>(0,a.jsx)("div",{...c.useBlockProps.save({className:"is-loading"}),children:(0,a.jsx)(c.InnerBlocks.Content,{})}),transforms:{to:[{type:"block",blocks:["woocommerce/classic-shortcode"],transform:e=>(0,d.createBlock)("woocommerce/classic-shortcode",{shortcode:"cart",align:e.align},[])}]},deprecated:[{attributes:Mc,save:({attributes:e})=>(0,a.jsx)("div",{className:(0,r.A)("is-loading",e.className),children:(0,a.jsx)(c.InnerBlocks.Content,{})}),migrate:(e,t)=>{const{checkoutPageId:s,align:o}=e;return[e,[(0,d.createBlock)("woocommerce/filled-cart-block",{align:o},[(0,d.createBlock)("woocommerce/cart-items-block"),(0,d.createBlock)("woocommerce/cart-totals-block",{},[(0,d.createBlock)("woocommerce/cart-order-summary-block",{}),(0,d.createBlock)("woocommerce/cart-express-payment-block"),(0,d.createBlock)("woocommerce/proceed-to-checkout-block",{checkoutPageId:s}),(0,d.createBlock)("woocommerce/cart-accepted-payment-methods-block")])]),(0,d.createBlock)("woocommerce/empty-cart-block",{align:o},t)]]},isEligible:(e,t)=>!t.find((e=>"woocommerce/filled-cart-block"===e.name))}]};(0,d.registerBlockType)("woocommerce/cart",Oc)},7316:()=>{},1189:()=>{},7545:()=>{},4313:()=>{},7578:()=>{},5893:()=>{},6882:()=>{},359:()=>{},2770:()=>{},6983:()=>{},9287:()=>{},7605:()=>{},3692:()=>{},8879:()=>{},8349:()=>{},2793:()=>{},1962:()=>{},619:()=>{},8413:()=>{},6562:()=>{},4249:()=>{},9961:()=>{},7575:()=>{},959:()=>{},8501:()=>{},9959:()=>{},8306:()=>{},9163:()=>{},3930:()=>{},2831:()=>{},9184:()=>{},535:()=>{},7128:()=>{},7467:()=>{},3580:()=>{},2115:()=>{},5675:()=>{},9685:()=>{},398:()=>{},2766:()=>{},7826:()=>{},3650:()=>{},4459:()=>{},4268:()=>{},6342:()=>{},4490:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},195:e=>{"use strict";e.exports=window.wp.a11y},9491:e=>{"use strict";e.exports=window.wp.compose},1659:e=>{"use strict";e.exports=window.wp.deprecated},8107:e=>{"use strict";e.exports=window.wp.dom},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},8558:e=>{"use strict";e.exports=window.wp.keycodes},5573:e=>{"use strict";e.exports=window.wp.primitives}},r={};function c(e){var t=r[e];if(void 0!==t)return t.exports;var s=r[e]={exports:{}};return o[e].call(s.exports,s,s.exports,c),s.exports}c.m=o,e=[],c.O=(t,s,o,r)=>{if(!s){var n=1/0;for(d=0;d<e.length;d++){for(var[s,o,r]=e[d],a=!0,i=0;i<s.length;i++)(!1&r||n>=r)&&Object.keys(c.O).every((e=>c.O[e](s[i])))?s.splice(i--,1):(a=!1,r<n&&(n=r));if(a){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[s,o,r]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},s=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var r=Object.create(null);c.r(r);var n={};t=t||[null,s({}),s([]),s(s)];for(var a=2&o&&e;"object"==typeof a&&!~t.indexOf(a);a=s(a))Object.getOwnPropertyNames(a).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,c.d(r,n),r},c.d=(e,t)=>{for(var s in t)c.o(t,s)&&!c.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=7949,(()=>{var e={7949:0};c.O.j=t=>0===e[t];var t=(t,s)=>{var o,r,[n,a,i]=s,l=0;if(n.some((t=>0!==e[t]))){for(o in a)c.o(a,o)&&(c.m[o]=a[o]);if(i)var d=i(c)}for(t&&t(s);l<n.length;l++)r=n[l],c.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return c.O(d)},s=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];s.forEach(t.bind(null,0)),s.push=t.bind(null,s.push.bind(s))})();var n=c.O(void 0,[94],(()=>c(9018)));n=c.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{}).cart=n})();