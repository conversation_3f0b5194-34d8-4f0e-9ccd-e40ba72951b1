/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={93057:(e,t,r)=>{"use strict";var o=r(14563);t.A=function(){const e=(0,n.default)();return{__unstableAcquireStoreLock:function(t,r,{exclusive:o}){return()=>e.acquire(t,r,o)},__unstableReleaseStoreLock:function(t){return()=>e.release(t)}}};var n=o(r(5718))},5718:(e,t,r)=>{"use strict";var o=r(14563);Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){let e=(0,n.default)(void 0,{type:"@@INIT"});function t(){for(const t of(0,s.getPendingLockRequests)(e)){const{store:r,path:o,exclusive:i,notifyAcquired:a}=t;if((0,s.isLockAvailable)(e,r,o,{exclusive:i})){const s={store:r,path:o,exclusive:i};e=(0,n.default)(e,{type:"GRANT_LOCK_REQUEST",lock:s,request:t}),a(s)}}}return{acquire:function(r,o,s){return new Promise((i=>{e=(0,n.default)(e,{type:"ENQUEUE_LOCK_REQUEST",request:{store:r,path:o,exclusive:s,notifyAcquired:i}}),t()}))},release:function(r){e=(0,n.default)(e,{type:"RELEASE_LOCK",lock:r}),t()}}};var n=o(r(88462)),s=r(70552)},88462:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e=n,t){switch(t.type){case"ENQUEUE_LOCK_REQUEST":{const{request:r}=t;return{...e,requests:[r,...e.requests]}}case"GRANT_LOCK_REQUEST":{const{lock:r,request:n}=t,{store:s,path:i}=n,a=[s,...i],c=(0,o.deepCopyLocksTreePath)(e.tree,a),u=(0,o.getNode)(c,a);return u.locks=[...u.locks,r],{...e,requests:e.requests.filter((e=>e!==n)),tree:c}}case"RELEASE_LOCK":{const{lock:r}=t,n=[r.store,...r.path],s=(0,o.deepCopyLocksTreePath)(e.tree,n),i=(0,o.getNode)(s,n);return i.locks=i.locks.filter((e=>e!==r)),{...e,tree:s}}}return e};var o=r(94979);const n={requests:[],tree:{locks:[],children:{}}}},70552:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getPendingLockRequests=function(e){return e.requests},t.isLockAvailable=function(e,t,r,{exclusive:n}){const s=[t,...r],i=e.tree;for(const e of(0,o.iteratePath)(i,s))if((0,o.hasConflictingLock)({exclusive:n},e.locks))return!1;const a=(0,o.getNode)(i,s);if(!a)return!0;for(const e of(0,o.iterateDescendants)(a))if((0,o.hasConflictingLock)({exclusive:n},e.locks))return!1;return!0};var o=r(94979)},94979:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.deepCopyLocksTreePath=function(e,t){const r={...e};let o=r;for(const e of t)o.children={...o.children,[e]:{locks:[],children:{},...o.children[e]}},o=o.children[e];return r},t.getNode=function(e,t){let r=e;for(const e of t){const t=r.children[e];if(!t)return null;r=t}return r},t.hasConflictingLock=function({exclusive:e},t){return!(!e||!t.length)||!(e||!t.filter((e=>e.exclusive)).length)},t.iterateDescendants=function*(e){const t=Object.values(e.children);for(;t.length;){const e=t.pop();yield e,t.push(...Object.values(e.children))}},t.iteratePath=function*(e,t){let r=e;yield r;for(const e of t){const t=r.children[e];if(!t)break;yield t,r=t}}},40368:(e,t,r)=>{"use strict";var o=r(40885),n=r(11548),s=n(o("String.prototype.indexOf"));e.exports=function(e,t){var r=o(e,!!t);return"function"==typeof r&&s(e,".prototype.")>-1?n(r):r}},11548:(e,t,r)=>{"use strict";var o=r(72418),n=r(40885),s=r(73745),i=n("%TypeError%"),a=n("%Function.prototype.apply%"),c=n("%Function.prototype.call%"),u=n("%Reflect.apply%",!0)||o.call(c,a),l=n("%Object.defineProperty%",!0),d=n("%Math.max%");if(l)try{l({},"a",{value:1})}catch(e){l=null}e.exports=function(e){if("function"!=typeof e)throw new i("a function is required");var t=u(o,c,arguments);return s(t,1+d(0,e.length-(arguments.length-1)),!0)};var E=function(){return u(o,a,arguments)};l?l(e.exports,"apply",{value:E}):e.exports.apply=E},51470:e=>{var t={utf8:{stringToBytes:function(e){return t.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(t.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}};e.exports=t},84738:e=>{var t,r;t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&r.rotl(e,8)|4278255360&r.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=r.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],r=0,o=0;r<e.length;r++,o+=8)t[o>>>5]|=e[r]<<24-o%32;return t},wordsToBytes:function(e){for(var t=[],r=0;r<32*e.length;r+=8)t.push(e[r>>>5]>>>24-r%32&255);return t},bytesToHex:function(e){for(var t=[],r=0;r<e.length;r++)t.push((e[r]>>>4).toString(16)),t.push((15&e[r]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],r=0;r<e.length;r+=2)t.push(parseInt(e.substr(r,2),16));return t},bytesToBase64:function(e){for(var r=[],o=0;o<e.length;o+=3)for(var n=e[o]<<16|e[o+1]<<8|e[o+2],s=0;s<4;s++)8*o+6*s<=8*e.length?r.push(t.charAt(n>>>6*(3-s)&63)):r.push("=");return r.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var r=[],o=0,n=0;o<e.length;n=++o%4)0!=n&&r.push((t.indexOf(e.charAt(o-1))&Math.pow(2,-2*n+8)-1)<<2*n|t.indexOf(e.charAt(o))>>>6-2*n);return r}},e.exports=r},91555:(e,t,r)=>{"use strict";var o=r(87612)(),n=r(40885),s=o&&n("%Object.defineProperty%",!0);if(s)try{s({},"a",{value:1})}catch(e){s=!1}var i=n("%SyntaxError%"),a=n("%TypeError%"),c=r(8632);e.exports=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new a("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new a("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new a("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new a("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new a("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new a("`loose`, if provided, must be a boolean");var o=arguments.length>3?arguments[3]:null,n=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,l=arguments.length>6&&arguments[6],d=!!c&&c(e,t);if(s)s(e,t,{configurable:null===u&&d?d.configurable:!u,enumerable:null===o&&d?d.enumerable:!o,value:r,writable:null===n&&d?d.writable:!n});else{if(!l&&(o||n||u))throw new i("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}}},65786:e=>{"use strict";var t=Object.prototype.toString,r=Math.max,o=function(e,t){for(var r=[],o=0;o<e.length;o+=1)r[o]=e[o];for(var n=0;n<t.length;n+=1)r[n+e.length]=t[n];return r};e.exports=function(e){var n=this;if("function"!=typeof n||"[object Function]"!==t.apply(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var s,i=function(e){for(var t=[],r=1,o=0;r<e.length;r+=1,o+=1)t[o]=e[r];return t}(arguments),a=r(0,n.length-i.length),c=[],u=0;u<a;u++)c[u]="$"+u;if(s=Function("binder","return function ("+function(e){for(var t="",r=0;r<e.length;r+=1)t+=e[r],r+1<e.length&&(t+=",");return t}(c)+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof s){var t=n.apply(this,o(i,arguments));return Object(t)===t?t:this}return n.apply(e,o(i,arguments))})),n.prototype){var l=function(){};l.prototype=n.prototype,s.prototype=new l,l.prototype=null}return s}},72418:(e,t,r)=>{"use strict";var o=r(65786);e.exports=Function.prototype.bind||o},40885:(e,t,r)=>{"use strict";var o,n=SyntaxError,s=Function,i=TypeError,a=function(e){try{return s('"use strict"; return ('+e+").constructor;")()}catch(e){}},c=Object.getOwnPropertyDescriptor;if(c)try{c({},"")}catch(e){c=null}var u=function(){throw new i},l=c?function(){try{return u}catch(e){try{return c(arguments,"callee").get}catch(e){return u}}}():u,d=r(13518)(),E=r(64310)(),p=Object.getPrototypeOf||(E?function(e){return e.__proto__}:null),S={},T="undefined"!=typeof Uint8Array&&p?p(Uint8Array):o,y={"%AggregateError%":"undefined"==typeof AggregateError?o:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?o:ArrayBuffer,"%ArrayIteratorPrototype%":d&&p?p([][Symbol.iterator]()):o,"%AsyncFromSyncIteratorPrototype%":o,"%AsyncFunction%":S,"%AsyncGenerator%":S,"%AsyncGeneratorFunction%":S,"%AsyncIteratorPrototype%":S,"%Atomics%":"undefined"==typeof Atomics?o:Atomics,"%BigInt%":"undefined"==typeof BigInt?o:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?o:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?o:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?o:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?o:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?o:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?o:FinalizationRegistry,"%Function%":s,"%GeneratorFunction%":S,"%Int8Array%":"undefined"==typeof Int8Array?o:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?o:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?o:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":d&&p?p(p([][Symbol.iterator]())):o,"%JSON%":"object"==typeof JSON?JSON:o,"%Map%":"undefined"==typeof Map?o:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&d&&p?p((new Map)[Symbol.iterator]()):o,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?o:Promise,"%Proxy%":"undefined"==typeof Proxy?o:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?o:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?o:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&d&&p?p((new Set)[Symbol.iterator]()):o,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?o:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":d&&p?p(""[Symbol.iterator]()):o,"%Symbol%":d?Symbol:o,"%SyntaxError%":n,"%ThrowTypeError%":l,"%TypedArray%":T,"%TypeError%":i,"%Uint8Array%":"undefined"==typeof Uint8Array?o:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?o:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?o:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?o:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?o:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?o:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?o:WeakSet};if(p)try{null.error}catch(e){var _=p(p(e));y["%Error.prototype%"]=_}var g=function e(t){var r;if("%AsyncFunction%"===t)r=a("async function () {}");else if("%GeneratorFunction%"===t)r=a("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=a("async function* () {}");else if("%AsyncGenerator%"===t){var o=e("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===t){var n=e("%AsyncGenerator%");n&&p&&(r=p(n.prototype))}return y[t]=r,r},f={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},R=r(72418),m=r(63206),O=R.call(Function.call,Array.prototype.concat),h=R.call(Function.apply,Array.prototype.splice),I=R.call(Function.call,String.prototype.replace),A=R.call(Function.call,String.prototype.slice),P=R.call(Function.call,RegExp.prototype.exec),C=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,U=/\\(\\)?/g,v=function(e,t){var r,o=e;if(m(f,o)&&(o="%"+(r=f[o])[0]+"%"),m(y,o)){var s=y[o];if(s===S&&(s=g(o)),void 0===s&&!t)throw new i("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:s}}throw new n("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new i("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new i('"allowMissing" argument must be a boolean');if(null===P(/^%?[^%]*%?$/,e))throw new n("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=A(e,0,1),r=A(e,-1);if("%"===t&&"%"!==r)throw new n("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new n("invalid intrinsic syntax, expected opening `%`");var o=[];return I(e,C,(function(e,t,r,n){o[o.length]=r?I(n,U,"$1"):t||e})),o}(e),o=r.length>0?r[0]:"",s=v("%"+o+"%",t),a=s.name,u=s.value,l=!1,d=s.alias;d&&(o=d[0],h(r,O([0,1],d)));for(var E=1,p=!0;E<r.length;E+=1){var S=r[E],T=A(S,0,1),_=A(S,-1);if(('"'===T||"'"===T||"`"===T||'"'===_||"'"===_||"`"===_)&&T!==_)throw new n("property names with quotes must have matching quotes");if("constructor"!==S&&p||(l=!0),m(y,a="%"+(o+="."+S)+"%"))u=y[a];else if(null!=u){if(!(S in u)){if(!t)throw new i("base intrinsic for "+e+" exists, but the property is not available.");return}if(c&&E+1>=r.length){var g=c(u,S);u=(p=!!g)&&"get"in g&&!("originalValue"in g.get)?g.get:u[S]}else p=m(u,S),u=u[S];p&&!l&&(y[a]=u)}}return u}},8632:(e,t,r)=>{"use strict";var o=r(40885)("%Object.getOwnPropertyDescriptor%",!0);if(o)try{o([],"length")}catch(e){o=null}e.exports=o},87612:(e,t,r)=>{"use strict";var o=r(40885)("%Object.defineProperty%",!0),n=function(){if(o)try{return o({},"a",{value:1}),!0}catch(e){return!1}return!1};n.hasArrayLengthDefineBug=function(){if(!n())return null;try{return 1!==o([],"length",{value:1}).length}catch(e){return!0}},e.exports=n},64310:e=>{"use strict";var t={foo:{}},r=Object;e.exports=function(){return{__proto__:t}.foo===t.foo&&!({__proto__:null}instanceof r)}},13518:(e,t,r)=>{"use strict";var o="undefined"!=typeof Symbol&&Symbol,n=r(71108);e.exports=function(){return"function"==typeof o&&"function"==typeof Symbol&&"symbol"==typeof o("foo")&&"symbol"==typeof Symbol("bar")&&n()}},71108:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var o=Object.getOwnPropertySymbols(e);if(1!==o.length||o[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(e,t);if(42!==n.value||!0!==n.enumerable)return!1}return!0}},63206:(e,t,r)=>{"use strict";var o=Function.prototype.call,n=Object.prototype.hasOwnProperty,s=r(72418);e.exports=s.call(o,n)},62468:e=>{function t(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(t(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&t(e.slice(0,0))}(e)||!!e._isBuffer)}},16961:(e,t,r)=>{var o,n,s,i,a;o=r(84738),n=r(51470).utf8,s=r(62468),i=r(51470).bin,(a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?i.stringToBytes(e):n.stringToBytes(e):s(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var r=o.bytesToWords(e),c=8*e.length,u=1732584193,l=-271733879,d=-1732584194,E=271733878,p=0;p<r.length;p++)r[p]=16711935&(r[p]<<8|r[p]>>>24)|4278255360&(r[p]<<24|r[p]>>>8);r[c>>>5]|=128<<c%32,r[14+(c+64>>>9<<4)]=c;var S=a._ff,T=a._gg,y=a._hh,_=a._ii;for(p=0;p<r.length;p+=16){var g=u,f=l,R=d,m=E;u=S(u,l,d,E,r[p+0],7,-680876936),E=S(E,u,l,d,r[p+1],12,-389564586),d=S(d,E,u,l,r[p+2],17,606105819),l=S(l,d,E,u,r[p+3],22,-1044525330),u=S(u,l,d,E,r[p+4],7,-176418897),E=S(E,u,l,d,r[p+5],12,1200080426),d=S(d,E,u,l,r[p+6],17,-1473231341),l=S(l,d,E,u,r[p+7],22,-45705983),u=S(u,l,d,E,r[p+8],7,1770035416),E=S(E,u,l,d,r[p+9],12,-1958414417),d=S(d,E,u,l,r[p+10],17,-42063),l=S(l,d,E,u,r[p+11],22,-1990404162),u=S(u,l,d,E,r[p+12],7,1804603682),E=S(E,u,l,d,r[p+13],12,-40341101),d=S(d,E,u,l,r[p+14],17,-1502002290),u=T(u,l=S(l,d,E,u,r[p+15],22,1236535329),d,E,r[p+1],5,-165796510),E=T(E,u,l,d,r[p+6],9,-1069501632),d=T(d,E,u,l,r[p+11],14,643717713),l=T(l,d,E,u,r[p+0],20,-373897302),u=T(u,l,d,E,r[p+5],5,-701558691),E=T(E,u,l,d,r[p+10],9,38016083),d=T(d,E,u,l,r[p+15],14,-660478335),l=T(l,d,E,u,r[p+4],20,-405537848),u=T(u,l,d,E,r[p+9],5,568446438),E=T(E,u,l,d,r[p+14],9,-1019803690),d=T(d,E,u,l,r[p+3],14,-187363961),l=T(l,d,E,u,r[p+8],20,1163531501),u=T(u,l,d,E,r[p+13],5,-1444681467),E=T(E,u,l,d,r[p+2],9,-51403784),d=T(d,E,u,l,r[p+7],14,1735328473),u=y(u,l=T(l,d,E,u,r[p+12],20,-1926607734),d,E,r[p+5],4,-378558),E=y(E,u,l,d,r[p+8],11,-2022574463),d=y(d,E,u,l,r[p+11],16,1839030562),l=y(l,d,E,u,r[p+14],23,-35309556),u=y(u,l,d,E,r[p+1],4,-1530992060),E=y(E,u,l,d,r[p+4],11,1272893353),d=y(d,E,u,l,r[p+7],16,-155497632),l=y(l,d,E,u,r[p+10],23,-1094730640),u=y(u,l,d,E,r[p+13],4,681279174),E=y(E,u,l,d,r[p+0],11,-358537222),d=y(d,E,u,l,r[p+3],16,-722521979),l=y(l,d,E,u,r[p+6],23,76029189),u=y(u,l,d,E,r[p+9],4,-640364487),E=y(E,u,l,d,r[p+12],11,-421815835),d=y(d,E,u,l,r[p+15],16,530742520),u=_(u,l=y(l,d,E,u,r[p+2],23,-995338651),d,E,r[p+0],6,-198630844),E=_(E,u,l,d,r[p+7],10,1126891415),d=_(d,E,u,l,r[p+14],15,-1416354905),l=_(l,d,E,u,r[p+5],21,-57434055),u=_(u,l,d,E,r[p+12],6,1700485571),E=_(E,u,l,d,r[p+3],10,-1894986606),d=_(d,E,u,l,r[p+10],15,-1051523),l=_(l,d,E,u,r[p+1],21,-2054922799),u=_(u,l,d,E,r[p+8],6,1873313359),E=_(E,u,l,d,r[p+15],10,-30611744),d=_(d,E,u,l,r[p+6],15,-1560198380),l=_(l,d,E,u,r[p+13],21,1309151649),u=_(u,l,d,E,r[p+4],6,-145523070),E=_(E,u,l,d,r[p+11],10,-1120210379),d=_(d,E,u,l,r[p+2],15,718787259),l=_(l,d,E,u,r[p+9],21,-343485551),u=u+g>>>0,l=l+f>>>0,d=d+R>>>0,E=E+m>>>0}return o.endian([u,l,d,E])})._ff=function(e,t,r,o,n,s,i){var a=e+(t&r|~t&o)+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._gg=function(e,t,r,o,n,s,i){var a=e+(t&o|r&~o)+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._hh=function(e,t,r,o,n,s,i){var a=e+(t^r^o)+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._ii=function(e,t,r,o,n,s,i){var a=e+(r^(t|~o))+(n>>>0)+i;return(a<<s|a>>>32-s)+t},a._blocksize=16,a._digestsize=16,e.exports=function(e,t){if(null==e)throw new Error("Illegal argument "+e);var r=o.wordsToBytes(a(e,t));return t&&t.asBytes?r:t&&t.asString?i.bytesToString(r):o.bytesToHex(r)}},83282:(e,t,r)=>{var o="function"==typeof Map&&Map.prototype,n=Object.getOwnPropertyDescriptor&&o?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,s=o&&n&&"function"==typeof n.get?n.get:null,i=o&&Map.prototype.forEach,a="function"==typeof Set&&Set.prototype,c=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=a&&c&&"function"==typeof c.get?c.get:null,l=a&&Set.prototype.forEach,d="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,E="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,p="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,S=Boolean.prototype.valueOf,T=Object.prototype.toString,y=Function.prototype.toString,_=String.prototype.match,g=String.prototype.slice,f=String.prototype.replace,R=String.prototype.toUpperCase,m=String.prototype.toLowerCase,O=RegExp.prototype.test,h=Array.prototype.concat,I=Array.prototype.join,A=Array.prototype.slice,P=Math.floor,C="function"==typeof BigInt?BigInt.prototype.valueOf:null,U=Object.getOwnPropertySymbols,v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,N="function"==typeof Symbol&&"object"==typeof Symbol.iterator,w="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,D=Object.prototype.propertyIsEnumerable,b=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function k(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||O.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var o=e<0?-P(-e):P(e);if(o!==e){var n=String(o),s=g.call(t,n.length+1);return f.call(n,r,"$&_")+"."+f.call(f.call(s,/([0-9]{3})/g,"$&_"),/_$/,"")}}return f.call(t,r,"$&_")}var G=r(70123),L=G.custom,M=Q(L)?L:null;function F(e,t,r){var o="double"===(r.quoteStyle||t)?'"':"'";return o+e+o}function q(e){return f.call(String(e),/"/g,"&quot;")}function x(e){return!("[object Array]"!==K(e)||w&&"object"==typeof e&&w in e)}function j(e){return!("[object RegExp]"!==K(e)||w&&"object"==typeof e&&w in e)}function Q(e){if(N)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!v)return!1;try{return v.call(e),!0}catch(e){}return!1}e.exports=function e(t,o,n,a){var c=o||{};if($(c,"quoteStyle")&&"single"!==c.quoteStyle&&"double"!==c.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if($(c,"maxStringLength")&&("number"==typeof c.maxStringLength?c.maxStringLength<0&&c.maxStringLength!==1/0:null!==c.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var T=!$(c,"customInspect")||c.customInspect;if("boolean"!=typeof T&&"symbol"!==T)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if($(c,"indent")&&null!==c.indent&&"\t"!==c.indent&&!(parseInt(c.indent,10)===c.indent&&c.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if($(c,"numericSeparator")&&"boolean"!=typeof c.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var R=c.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return H(t,c);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var O=String(t);return R?k(t,O):O}if("bigint"==typeof t){var P=String(t)+"n";return R?k(t,P):P}var U=void 0===c.depth?5:c.depth;if(void 0===n&&(n=0),n>=U&&U>0&&"object"==typeof t)return x(t)?"[Array]":"[Object]";var L,V=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=I.call(Array(e.indent+1)," ")}return{base:r,prev:I.call(Array(t+1),r)}}(c,n);if(void 0===a)a=[];else if(Y(a,t)>=0)return"[Circular]";function W(t,r,o){if(r&&(a=A.call(a)).push(r),o){var s={depth:c.depth};return $(c,"quoteStyle")&&(s.quoteStyle=c.quoteStyle),e(t,s,n+1,a)}return e(t,c,n+1,a)}if("function"==typeof t&&!j(t)){var ee=function(e){if(e.name)return e.name;var t=_.call(y.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}(t),te=X(t,W);return"[Function"+(ee?": "+ee:" (anonymous)")+"]"+(te.length>0?" { "+I.call(te,", ")+" }":"")}if(Q(t)){var re=N?f.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):v.call(t);return"object"!=typeof t||N?re:B(re)}if((L=t)&&"object"==typeof L&&("undefined"!=typeof HTMLElement&&L instanceof HTMLElement||"string"==typeof L.nodeName&&"function"==typeof L.getAttribute)){for(var oe="<"+m.call(String(t.nodeName)),ne=t.attributes||[],se=0;se<ne.length;se++)oe+=" "+ne[se].name+"="+F(q(ne[se].value),"double",c);return oe+=">",t.childNodes&&t.childNodes.length&&(oe+="..."),oe+"</"+m.call(String(t.nodeName))+">"}if(x(t)){if(0===t.length)return"[]";var ie=X(t,W);return V&&!function(e){for(var t=0;t<e.length;t++)if(Y(e[t],"\n")>=0)return!1;return!0}(ie)?"["+Z(ie,V)+"]":"[ "+I.call(ie,", ")+" ]"}if(function(e){return!("[object Error]"!==K(e)||w&&"object"==typeof e&&w in e)}(t)){var ae=X(t,W);return"cause"in Error.prototype||!("cause"in t)||D.call(t,"cause")?0===ae.length?"["+String(t)+"]":"{ ["+String(t)+"] "+I.call(ae,", ")+" }":"{ ["+String(t)+"] "+I.call(h.call("[cause]: "+W(t.cause),ae),", ")+" }"}if("object"==typeof t&&T){if(M&&"function"==typeof t[M]&&G)return G(t,{depth:U-n});if("symbol"!==T&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!s||!e||"object"!=typeof e)return!1;try{s.call(e);try{u.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var ce=[];return i&&i.call(t,(function(e,r){ce.push(W(r,t,!0)+" => "+W(e,t))})),z("Map",s.call(t),ce,V)}if(function(e){if(!u||!e||"object"!=typeof e)return!1;try{u.call(e);try{s.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var ue=[];return l&&l.call(t,(function(e){ue.push(W(e,t))})),z("Set",u.call(t),ue,V)}if(function(e){if(!d||!e||"object"!=typeof e)return!1;try{d.call(e,d);try{E.call(e,E)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return J("WeakMap");if(function(e){if(!E||!e||"object"!=typeof e)return!1;try{E.call(e,E);try{d.call(e,d)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return J("WeakSet");if(function(e){if(!p||!e||"object"!=typeof e)return!1;try{return p.call(e),!0}catch(e){}return!1}(t))return J("WeakRef");if(function(e){return!("[object Number]"!==K(e)||w&&"object"==typeof e&&w in e)}(t))return B(W(Number(t)));if(function(e){if(!e||"object"!=typeof e||!C)return!1;try{return C.call(e),!0}catch(e){}return!1}(t))return B(W(C.call(t)));if(function(e){return!("[object Boolean]"!==K(e)||w&&"object"==typeof e&&w in e)}(t))return B(S.call(t));if(function(e){return!("[object String]"!==K(e)||w&&"object"==typeof e&&w in e)}(t))return B(W(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r.g)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==K(e)||w&&"object"==typeof e&&w in e)}(t)&&!j(t)){var le=X(t,W),de=b?b(t)===Object.prototype:t instanceof Object||t.constructor===Object,Ee=t instanceof Object?"":"null prototype",pe=!de&&w&&Object(t)===t&&w in t?g.call(K(t),8,-1):Ee?"Object":"",Se=(de||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(pe||Ee?"["+I.call(h.call([],pe||[],Ee||[]),": ")+"] ":"");return 0===le.length?Se+"{}":V?Se+"{"+Z(le,V)+"}":Se+"{ "+I.call(le,", ")+" }"}return String(t)};var V=Object.prototype.hasOwnProperty||function(e){return e in this};function $(e,t){return V.call(e,t)}function K(e){return T.call(e)}function Y(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,o=e.length;r<o;r++)if(e[r]===t)return r;return-1}function H(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,o="... "+r+" more character"+(r>1?"s":"");return H(g.call(e,0,t.maxStringLength),t)+o}return F(f.call(f.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,W),"single",t)}function W(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+R.call(t.toString(16))}function B(e){return"Object("+e+")"}function J(e){return e+" { ? }"}function z(e,t,r,o){return e+" ("+t+") {"+(o?Z(r,o):I.call(r,", "))+"}"}function Z(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+I.call(e,","+r)+"\n"+t.prev}function X(e,t){var r=x(e),o=[];if(r){o.length=e.length;for(var n=0;n<e.length;n++)o[n]=$(e,n)?t(e[n],e):""}var s,i="function"==typeof U?U(e):[];if(N){s={};for(var a=0;a<i.length;a++)s["$"+i[a]]=i[a]}for(var c in e)$(e,c)&&(r&&String(Number(c))===c&&c<e.length||N&&s["$"+c]instanceof Symbol||(O.call(/[^\w$]/,c)?o.push(t(c,e)+": "+t(e[c],e)):o.push(c+": "+t(e[c],e))));if("function"==typeof U)for(var u=0;u<i.length;u++)D.call(e,i[u])&&o.push("["+t(i[u])+"]: "+t(e[i[u]],e));return o}},24294:e=>{"use strict";var t=String.prototype.replace,r=/%20/g,o="RFC3986";e.exports={default:o,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:o}},4594:(e,t,r)=>{"use strict";var o=r(61007),n=r(84977),s=r(24294);e.exports={formats:s,parse:n,stringify:o}},84977:(e,t,r)=>{"use strict";var o=r(50323),n=Object.prototype.hasOwnProperty,s=Array.isArray,i={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:o.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},a=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},c=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},u=function(e,t,r,o){if(e){var s=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,a=r.depth>0&&/(\[[^[\]]*])/.exec(s),u=a?s.slice(0,a.index):s,l=[];if(u){if(!r.plainObjects&&n.call(Object.prototype,u)&&!r.allowPrototypes)return;l.push(u)}for(var d=0;r.depth>0&&null!==(a=i.exec(s))&&d<r.depth;){if(d+=1,!r.plainObjects&&n.call(Object.prototype,a[1].slice(1,-1))&&!r.allowPrototypes)return;l.push(a[1])}return a&&l.push("["+s.slice(a.index)+"]"),function(e,t,r,o){for(var n=o?t:c(t,r),s=e.length-1;s>=0;--s){var i,a=e[s];if("[]"===a&&r.parseArrays)i=[].concat(n);else{i=r.plainObjects?Object.create(null):{};var u="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,l=parseInt(u,10);r.parseArrays||""!==u?!isNaN(l)&&a!==u&&String(l)===u&&l>=0&&r.parseArrays&&l<=r.arrayLimit?(i=[])[l]=n:"__proto__"!==u&&(i[u]=n):i={0:n}}n=i}return n}(l,t,r,o)}};e.exports=function(e,t){var r=function(e){if(!e)return i;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?i.charset:e.charset;return{allowDots:void 0===e.allowDots?i.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:i.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:i.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:i.comma,decoder:"function"==typeof e.decoder?e.decoder:i.decoder,delimiter:"string"==typeof e.delimiter||o.isRegExp(e.delimiter)?e.delimiter:i.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:i.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:i.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:i.strictNullHandling}}(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var l="string"==typeof e?function(e,t){var r,u={__proto__:null},l=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,d=t.parameterLimit===1/0?void 0:t.parameterLimit,E=l.split(t.delimiter,d),p=-1,S=t.charset;if(t.charsetSentinel)for(r=0;r<E.length;++r)0===E[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===E[r]?S="utf-8":"utf8=%26%2310003%3B"===E[r]&&(S="iso-8859-1"),p=r,r=E.length);for(r=0;r<E.length;++r)if(r!==p){var T,y,_=E[r],g=_.indexOf("]="),f=-1===g?_.indexOf("="):g+1;-1===f?(T=t.decoder(_,i.decoder,S,"key"),y=t.strictNullHandling?null:""):(T=t.decoder(_.slice(0,f),i.decoder,S,"key"),y=o.maybeMap(c(_.slice(f+1),t),(function(e){return t.decoder(e,i.decoder,S,"value")}))),y&&t.interpretNumericEntities&&"iso-8859-1"===S&&(y=a(y)),_.indexOf("[]=")>-1&&(y=s(y)?[y]:y),n.call(u,T)?u[T]=o.combine(u[T],y):u[T]=y}return u}(e,r):e,d=r.plainObjects?Object.create(null):{},E=Object.keys(l),p=0;p<E.length;++p){var S=E[p],T=u(S,l[S],r,"string"==typeof e);d=o.merge(d,T,r)}return!0===r.allowSparse?d:o.compact(d)}},61007:(e,t,r)=>{"use strict";var o=r(2435),n=r(50323),s=r(24294),i=Object.prototype.hasOwnProperty,a={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},c=Array.isArray,u=Array.prototype.push,l=function(e,t){u.apply(e,c(t)?t:[t])},d=Date.prototype.toISOString,E=s.default,p={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:n.encode,encodeValuesOnly:!1,format:E,formatter:s.formatters[E],indices:!1,serializeDate:function(e){return d.call(e)},skipNulls:!1,strictNullHandling:!1},S={},T=function e(t,r,s,i,a,u,d,E,T,y,_,g,f,R,m,O){for(var h,I=t,A=O,P=0,C=!1;void 0!==(A=A.get(S))&&!C;){var U=A.get(t);if(P+=1,void 0!==U){if(U===P)throw new RangeError("Cyclic object value");C=!0}void 0===A.get(S)&&(P=0)}if("function"==typeof E?I=E(r,I):I instanceof Date?I=_(I):"comma"===s&&c(I)&&(I=n.maybeMap(I,(function(e){return e instanceof Date?_(e):e}))),null===I){if(a)return d&&!R?d(r,p.encoder,m,"key",g):r;I=""}if("string"==typeof(h=I)||"number"==typeof h||"boolean"==typeof h||"symbol"==typeof h||"bigint"==typeof h||n.isBuffer(I))return d?[f(R?r:d(r,p.encoder,m,"key",g))+"="+f(d(I,p.encoder,m,"value",g))]:[f(r)+"="+f(String(I))];var v,N=[];if(void 0===I)return N;if("comma"===s&&c(I))R&&d&&(I=n.maybeMap(I,d)),v=[{value:I.length>0?I.join(",")||null:void 0}];else if(c(E))v=E;else{var w=Object.keys(I);v=T?w.sort(T):w}for(var D=i&&c(I)&&1===I.length?r+"[]":r,b=0;b<v.length;++b){var k=v[b],G="object"==typeof k&&void 0!==k.value?k.value:I[k];if(!u||null!==G){var L=c(I)?"function"==typeof s?s(D,k):D:D+(y?"."+k:"["+k+"]");O.set(t,P);var M=o();M.set(S,O),l(N,e(G,L,s,i,a,u,"comma"===s&&R&&c(I)?null:d,E,T,y,_,g,f,R,m,M))}}return N};e.exports=function(e,t){var r,n=e,u=function(e){if(!e)return p;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||p.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=s.default;if(void 0!==e.format){if(!i.call(s.formatters,e.format))throw new TypeError("Unknown format option provided.");r=e.format}var o=s.formatters[r],n=p.filter;return("function"==typeof e.filter||c(e.filter))&&(n=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:p.addQueryPrefix,allowDots:void 0===e.allowDots?p.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:p.charsetSentinel,delimiter:void 0===e.delimiter?p.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:p.encode,encoder:"function"==typeof e.encoder?e.encoder:p.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:p.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:p.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:p.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:p.strictNullHandling}}(t);"function"==typeof u.filter?n=(0,u.filter)("",n):c(u.filter)&&(r=u.filter);var d,E=[];if("object"!=typeof n||null===n)return"";d=t&&t.arrayFormat in a?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var S=a[d];if(t&&"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var y="comma"===S&&t&&t.commaRoundTrip;r||(r=Object.keys(n)),u.sort&&r.sort(u.sort);for(var _=o(),g=0;g<r.length;++g){var f=r[g];u.skipNulls&&null===n[f]||l(E,T(n[f],f,S,y,u.strictNullHandling,u.skipNulls,u.encode?u.encoder:null,u.filter,u.sort,u.allowDots,u.serializeDate,u.format,u.formatter,u.encodeValuesOnly,u.charset,_))}var R=E.join(u.delimiter),m=!0===u.addQueryPrefix?"?":"";return u.charsetSentinel&&("iso-8859-1"===u.charset?m+="utf8=%26%2310003%3B&":m+="utf8=%E2%9C%93&"),R.length>0?m+R:""}},50323:(e,t,r)=>{"use strict";var o=r(24294),n=Object.prototype.hasOwnProperty,s=Array.isArray,i=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),a=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},o=0;o<e.length;++o)void 0!==e[o]&&(r[o]=e[o]);return r};e.exports={arrayToObject:a,assign:function(e,t){return Object.keys(t).reduce((function(e,r){return e[r]=t[r],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],o=0;o<t.length;++o)for(var n=t[o],i=n.obj[n.prop],a=Object.keys(i),c=0;c<a.length;++c){var u=a[c],l=i[u];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(t.push({obj:i,prop:u}),r.push(l))}return function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(s(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);t.obj[t.prop]=o}}}(t),e},decode:function(e,t,r){var o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},encode:function(e,t,r,n,s){if(0===e.length)return e;var a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var c="",u=0;u<a.length;++u){var l=a.charCodeAt(u);45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||s===o.RFC1738&&(40===l||41===l)?c+=a.charAt(u):l<128?c+=i[l]:l<2048?c+=i[192|l>>6]+i[128|63&l]:l<55296||l>=57344?c+=i[224|l>>12]+i[128|l>>6&63]+i[128|63&l]:(u+=1,l=65536+((1023&l)<<10|1023&a.charCodeAt(u)),c+=i[240|l>>18]+i[128|l>>12&63]+i[128|l>>6&63]+i[128|63&l])}return c},isBuffer:function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(s(e)){for(var r=[],o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)},merge:function e(t,r,o){if(!r)return t;if("object"!=typeof r){if(s(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!n.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var i=t;return s(t)&&!s(r)&&(i=a(t,o)),s(t)&&s(r)?(r.forEach((function(r,s){if(n.call(t,s)){var i=t[s];i&&"object"==typeof i&&r&&"object"==typeof r?t[s]=e(i,r,o):t.push(r)}else t[s]=r})),t):Object.keys(r).reduce((function(t,s){var i=r[s];return n.call(t,s)?t[s]=e(t[s],i,o):t[s]=i,t}),i)}}},73745:(e,t,r)=>{"use strict";var o=r(40885),n=r(91555),s=r(87612)(),i=r(8632),a=o("%TypeError%"),c=o("%Math.floor%");e.exports=function(e,t){if("function"!=typeof e)throw new a("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||c(t)!==t)throw new a("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],o=!0,u=!0;if("length"in e&&i){var l=i(e,"length");l&&!l.configurable&&(o=!1),l&&!l.writable&&(u=!1)}return(o||u||!r)&&(s?n(e,"length",t,!0,!0):n(e,"length",t)),e}},2435:(e,t,r)=>{"use strict";var o=r(40885),n=r(40368),s=r(83282),i=o("%TypeError%"),a=o("%WeakMap%",!0),c=o("%Map%",!0),u=n("WeakMap.prototype.get",!0),l=n("WeakMap.prototype.set",!0),d=n("WeakMap.prototype.has",!0),E=n("Map.prototype.get",!0),p=n("Map.prototype.set",!0),S=n("Map.prototype.has",!0),T=function(e,t){for(var r,o=e;null!==(r=o.next);o=r)if(r.key===t)return o.next=r.next,r.next=e.next,e.next=r,r};e.exports=function(){var e,t,r,o={assert:function(e){if(!o.has(e))throw new i("Side channel does not contain "+s(e))},get:function(o){if(a&&o&&("object"==typeof o||"function"==typeof o)){if(e)return u(e,o)}else if(c){if(t)return E(t,o)}else if(r)return function(e,t){var r=T(e,t);return r&&r.value}(r,o)},has:function(o){if(a&&o&&("object"==typeof o||"function"==typeof o)){if(e)return d(e,o)}else if(c){if(t)return S(t,o)}else if(r)return function(e,t){return!!T(e,t)}(r,o);return!1},set:function(o,n){a&&o&&("object"==typeof o||"function"==typeof o)?(e||(e=new a),l(e,o,n)):c?(t||(t=new c),p(t,o,n)):(r||(r={key:{},next:null}),function(e,t,r){var o=T(e,t);o?o.value=r:e.next={key:t,next:e.next,value:r}}(r,o,n))}};return o}},70123:()=>{},14563:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var s=t[o]={exports:{}};return e[o](s,s.exports,r),s.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};(()=>{"use strict";r.r(o),r.d(o,{COUNTRIES_STORE_NAME:()=>qs,DAY:()=>$e,EXPERIMENTAL_PRODUCT_ATTRIBUTES_STORE_NAME:()=>Ru,EXPERIMENTAL_PRODUCT_ATTRIBUTE_TERMS_STORE_NAME:()=>Gu,EXPERIMENTAL_PRODUCT_CATEGORIES_STORE_NAME:()=>Du,EXPERIMENTAL_PRODUCT_FORM_STORE_NAME:()=>_l,EXPERIMENTAL_PRODUCT_SHIPPING_CLASSES_STORE_NAME:()=>hu,EXPERIMENTAL_PRODUCT_TAGS_STORE_NAME:()=>vu,EXPERIMENTAL_PRODUCT_VARIATIONS_STORE_NAME:()=>zu,EXPERIMENTAL_SHIPPING_ZONES_STORE_NAME:()=>Pu,EXPERIMENTAL_TAX_CLASSES_STORE_NAME:()=>rl,EXPORT_STORE_NAME:()=>vE,HOUR:()=>Ve,IMPORT_STORE_NAME:()=>Wl,ITEMS_STORE_NAME:()=>Ni,MAX_PER_PAGE:()=>xe,MINUTE:()=>Qe,MONTH:()=>Ye,NAMESPACE:()=>Me,NAVIGATION_STORE_NAME:()=>di,NOTES_STORE_NAME:()=>Xn,ONBOARDING_STORE_NAME:()=>Tn,OPTIONS_STORE_NAME:()=>dr,ORDERS_STORE_NAME:()=>jc,PAYMENT_GATEWAYS_STORE_NAME:()=>zi,PAYMENT_SETTINGS_STORE_NAME:()=>fa,PLUGINS_STORE_NAME:()=>hr,PRODUCTS_STORE_NAME:()=>Oc,PaymentProviderType:()=>ol,QUERY_DEFAULTS:()=>He,REPORTS_STORE_NAME:()=>ys,REVIEWS_STORE_NAME:()=>kn,SECOND:()=>je,SETTINGS_STORE_NAME:()=>ut,SHIPPING_METHODS_STORE_NAME:()=>Ua,USER_STORE_NAME:()=>fe,WCS_NAMESPACE:()=>qe,WC_ADMIN_NAMESPACE:()=>Fe,WEEK:()=>Ke,WOOPAYMENTS_ONBOARDING_STORE_NAME:()=>Cl,countriesStore:()=>Fs,experimentalProductAttributeTermsStore:()=>ku,experimentalProductAttributesStore:()=>fu,experimentalProductCategoriesStore:()=>wu,experimentalProductFormStore:()=>yl,experimentalProductShippingClassesStore:()=>Ou,experimentalProductTagsStore:()=>Uu,experimentalProductVariationsStore:()=>Ju,experimentalSettingOptionsStore:()=>Vd,experimentalShippingZonesStore:()=>Au,experimentalTaxClassesStore:()=>tl,getFilterQuery:()=>iE,getLeaderboard:()=>sE,getReportChartData:()=>pE,getReportTableData:()=>yE,getReportTableQuery:()=>TE,getSummaryNumbers:()=>uE,getTooltipValueFormat:()=>SE,getVisibleTasks:()=>oE,importStore:()=>Hl,isRestApiError:()=>ot,itemsStore:()=>vi,notesStore:()=>Zn,onboardingStore:()=>Sn,optionsStore:()=>lr,ordersStore:()=>xc,paymentGatewaysStore:()=>Zi,paymentSettingsStore:()=>Ra,pluginNames:()=>Et,pluginsStore:()=>Or,productReadOnlyProperties:()=>NE,productsStore:()=>mc,reportsStore:()=>Ts,reviewsStore:()=>bn,searchItemsByString:()=>pi,settingsStore:()=>ct,shippingMethodsStore:()=>va,useOptionsHydration:()=>zd,useSettings:()=>Xd,useUser:()=>rE,useUserPreferences:()=>tE,userStore:()=>le.store,withCurrentUserHydration:()=>Wd,withNavigationHydration:()=>Bd,withOnboardingHydration:()=>Hd,withOptionsHydration:()=>Zd,withPluginsHydration:()=>Jd,withSettingsHydration:()=>Yd,woopaymentsOnboardingStore:()=>Ul});var e={};r.r(e),r.d(e,{getDirtyKeys:()=>Ce,getIsDirty:()=>Ue,getLastSettingsErrorForGroup:()=>De,getSetting:()=>we,getSettings:()=>Pe,getSettingsError:()=>be,getSettingsForGroup:()=>ve,getSettingsGroupNames:()=>Ae,isUpdateSettingsRequesting:()=>Ne});var t={};r.r(t),r.d(t,{clearIsDirty:()=>Xe,clearSettings:()=>rt,persistSettingsForGroup:()=>et,setIsRequesting:()=>Ze,updateAndPersistSettingsForGroup:()=>tt,updateErrorForGroup:()=>ze,updateSettingsForGroup:()=>Je});var n={};r.r(n),r.d(n,{getSettings:()=>st,getSettingsForGroup:()=>it});var s={};r.r(s),r.d(s,{getActivePlugins:()=>pt,getInstalledPlugins:()=>St,getJetpackConnectUrl:()=>ft,getJetpackConnectionData:()=>gt,getPaypalOnboardingStatus:()=>mt,getPluginInstallState:()=>Rt,getPluginsError:()=>yt,getRecommendedPlugins:()=>Ot,isJetpackConnected:()=>_t,isPluginsRequesting:()=>Tt});var i={};r.r(i),r.d(i,{activatePlugins:()=>xt,connectToJetpack:()=>Qt,connectToJetpackWithFailureRedirect:()=>$t,createErrorNotice:()=>Gt,deactivatePlugin:()=>Ht,dismissRecommendedPlugins:()=>Yt,installAndActivatePlugins:()=>jt,installJetpackAndConnect:()=>Vt,installPlugins:()=>qt,setError:()=>wt,setIsRequesting:()=>Nt,setPaypalOnboardingStatus:()=>Lt,setRecommendedPlugins:()=>Mt,updateActivePlugins:()=>Ut,updateInstalledPlugins:()=>vt,updateIsJetpackConnected:()=>Dt,updateJetpackConnectUrl:()=>kt,updateJetpackConnectionData:()=>bt});var a={};r.r(a),r.d(a,{getOption:()=>Bt,getOptionsRequestingError:()=>Jt,getOptionsUpdatingError:()=>Zt,isOptionsUpdating:()=>zt});var c={};r.r(c),r.d(c,{receiveOptions:()=>er,setIsUpdating:()=>or,setRequestingError:()=>tr,setUpdatingError:()=>rr,updateOptions:()=>nr});var u={};r.r(u),r.d(u,{getOption:()=>ur});var l={};r.r(l),r.d(l,{getActivePlugins:()=>pr,getInstalledPlugins:()=>Sr,getJetpackConnectUrl:()=>_r,getJetpackConnectionData:()=>yr,getPaypalOnboardingStatus:()=>fr,getRecommendedPlugins:()=>mr,isJetpackConnected:()=>Tr});var d={};r.r(d),r.d(d,{getCoreProfilerCompletedSteps:()=>Qr,getEmailPrefill:()=>qr,getFreeExtensions:()=>vr,getJetpackAuthUrl:()=>jr,getMostRecentCoreProfilerStep:()=>Vr,getOnboardingError:()=>Mr,getPaymentGatewaySuggestions:()=>Lr,getProductTypes:()=>xr,getProfileItems:()=>Nr,getProfileProgress:()=>wr,getTask:()=>Gr,getTaskList:()=>kr,getTaskLists:()=>Dr,getTaskListsByIds:()=>br,isOnboardingRequesting:()=>Fr});var E={};r.r(E),r.d(E,{actionTask:()=>Vo,actionTaskError:()=>Co,actionTaskRequest:()=>Uo,actionTaskSuccess:()=>vo,coreProfilerCompleted:()=>Jo,coreProfilerCompletedError:()=>Ho,coreProfilerCompletedRequest:()=>Wo,coreProfilerCompletedSuccess:()=>Bo,dismissTask:()=>Fo,dismissTaskError:()=>uo,dismissTaskRequest:()=>lo,dismissTaskSuccess:()=>Eo,getFreeExtensionsError:()=>Jr,getFreeExtensionsSuccess:()=>zr,getProductTypesError:()=>wo,getProductTypesSuccess:()=>No,getTaskListsError:()=>to,getTaskListsSuccess:()=>ro,hideTaskList:()=>xo,hideTaskListError:()=>yo,hideTaskListRequest:()=>_o,hideTaskListSuccess:()=>go,installAndActivatePluginsAsync:()=>$o,keepCompletedTaskList:()=>bo,keepCompletedTaskListSuccess:()=>ho,optimisticallyCompleteTask:()=>Qo,optimisticallyCompleteTaskRequest:()=>Oo,setEmailPrefill:()=>Po,setError:()=>Zr,setIsRequesting:()=>Xr,setJetpackAuthUrl:()=>Yo,setPaymentMethods:()=>Ao,setProfileItems:()=>eo,setProfileProgress:()=>Do,snoozeTask:()=>Lo,snoozeTaskError:()=>oo,snoozeTaskRequest:()=>no,snoozeTaskSuccess:()=>so,undoDismissTask:()=>qo,undoDismissTaskError:()=>po,undoDismissTaskRequest:()=>So,undoDismissTaskSuccess:()=>To,undoSnoozeTask:()=>Mo,undoSnoozeTaskError:()=>io,undoSnoozeTaskRequest:()=>ao,undoSnoozeTaskSuccess:()=>co,unhideTaskList:()=>jo,unhideTaskListError:()=>fo,unhideTaskListRequest:()=>Ro,unhideTaskListSuccess:()=>mo,updateCoreProfilerStep:()=>Go,updateProfileItems:()=>ko,updateStoreCurrencyAndMeasurementUnits:()=>Ko,visitedTask:()=>Io});var p={};r.r(p),r.d(p,{getCoreProfilerCompletedSteps:()=>en,getEmailPrefill:()=>rn,getFreeExtensions:()=>un,getJetpackAuthUrl:()=>dn,getMostRecentCoreProfilerStep:()=>tn,getPaymentGatewaySuggestions:()=>cn,getProductTypes:()=>ln,getProfileItems:()=>Zo,getProfileProgress:()=>Xo,getTask:()=>an,getTaskList:()=>sn,getTaskLists:()=>on,getTaskListsByIds:()=>nn});var S={};r.r(S),r.d(S,{getReview:()=>gn,getReviewError:()=>mn,getReviews:()=>_n,getReviewsError:()=>Rn,getReviewsTotalCount:()=>fn});var T={};r.r(T),r.d(T,{deleteReview:()=>Un,setError:()=>An,setReview:()=>In,setReviewIsUpdating:()=>hn,updateReview:()=>Cn,updateReviews:()=>Pn});var y={};r.r(y),r.d(y,{getReview:()=>Nn,getReviews:()=>vn,getReviewsTotalCount:()=>wn});var _={};r.r(_),r.d(_,{getNotes:()=>Ln,getNotesError:()=>Mn,isNotesRequesting:()=>Fn});var g={};r.r(g),r.d(g,{batchUpdateNotes:()=>Jn,removeAllNotes:()=>Bn,removeNote:()=>Wn,setError:()=>$n,setIsRequesting:()=>Kn,setNote:()=>xn,setNoteIsUpdating:()=>jn,setNotes:()=>Qn,setNotesQuery:()=>Vn,triggerNoteAction:()=>Hn,updateNote:()=>Yn});var f={};r.r(f),r.d(f,{getNotes:()=>zn});var R={};r.r(R),r.d(R,{getReportItems:()=>os,getReportItemsError:()=>rs,getReportStats:()=>ns,getReportStatsError:()=>ss});var m={};r.r(m),r.d(m,{setReportItems:()=>cs,setReportItemsError:()=>as,setReportStats:()=>us,setReportStatsError:()=>ls});var O={};r.r(O),r.d(O,{getReportItems:()=>Es,getReportStats:()=>ps});var h={};r.r(h),r.d(h,{geolocate:()=>Os,getCountries:()=>Rs,getCountry:()=>ms,getLocale:()=>fs,getLocales:()=>gs});var I={};r.r(I),r.d(I,{geolocationError:()=>ws,geolocationSuccess:()=>Ns,getCountriesError:()=>vs,getCountriesSuccess:()=>Us,getLocalesError:()=>Cs,getLocalesSuccess:()=>Ps});var A={};r.r(A),r.d(A,{geolocate:()=>Ms,getCountries:()=>Ls,getCountry:()=>Gs,getLocale:()=>bs,getLocales:()=>ks});var P={};r.r(P),r.d(P,{getFavorites:()=>Qs,getMenuItems:()=>js,getPersistedQuery:()=>$s,isNavigationRequesting:()=>Vs});var C={};r.r(C),r.d(C,{addFavorite:()=>ii,addFavoriteFailure:()=>Xs,addFavoriteRequest:()=>Zs,addFavoriteSuccess:()=>ei,addMenuItems:()=>Ws,getFavoritesFailure:()=>Bs,getFavoritesRequest:()=>Js,getFavoritesSuccess:()=>zs,onHistoryChange:()=>ni,onLoad:()=>si,removeFavorite:()=>ai,removeFavoriteFailure:()=>ri,removeFavoriteRequest:()=>ti,removeFavoriteSuccess:()=>oi,setMenuItems:()=>Hs});var U={};r.r(U),r.d(U,{getFavorites:()=>ci});var v={};r.r(v),r.d(v,{getItems:()=>Ti,getItemsError:()=>_i,getItemsTotalCount:()=>yi});var N={};r.r(N),r.d(N,{createProductFromTemplate:()=>Ii,setError:()=>Oi,setItem:()=>fi,setItems:()=>Ri,setItemsTotalCount:()=>mi,updateProductStock:()=>hi});var w={};r.r(w),r.d(w,{getItems:()=>Ai,getItemsTotalCount:()=>Pi,getReviewsTotalCount:()=>Ci});var D={};r.r(D),r.d(D,{getPaymentGatewayError:()=>Fi,getPaymentGatewayRequest:()=>Mi,getPaymentGatewaySuccess:()=>qi,getPaymentGatewaysError:()=>Li,getPaymentGatewaysRequest:()=>ki,getPaymentGatewaysSuccess:()=>Gi,updatePaymentGateway:()=>Vi,updatePaymentGatewayError:()=>Qi,updatePaymentGatewayRequest:()=>ji,updatePaymentGatewaySuccess:()=>xi});var b={};r.r(b),r.d(b,{getPaymentGateway:()=>Yi,getPaymentGateways:()=>Ki});var k={};r.r(k),r.d(k,{getPaymentGateway:()=>Hi,getPaymentGatewayError:()=>Bi,getPaymentGateways:()=>Wi,isPaymentGatewayUpdating:()=>Ji});var G={};r.r(G),r.d(G,{attachPaymentExtensionSuggestion:()=>na,getPaymentProvidersError:()=>ra,getPaymentProvidersRequest:()=>ea,getPaymentProvidersSuccess:()=>ta,hidePaymentExtensionSuggestion:()=>sa,setIsWooPayEligible:()=>aa,togglePaymentGateway:()=>oa,updateProviderOrdering:()=>ia});var L={};r.r(L),r.d(L,{getIsWooPayEligible:()=>da,getOfflinePaymentGateways:()=>ua,getPaymentProviders:()=>ca,getWooPayEligibility:()=>la});var M={};r.r(M),r.d(M,{getIsWooPayEligible:()=>_a,getOfflinePaymentGateways:()=>pa,getPaymentProviders:()=>Ea,getSuggestionCategories:()=>Ta,getSuggestions:()=>Sa,isFetching:()=>ya});var F={};r.r(F),r.d(F,{getShippingMethodsError:()=>Ia,getShippingMethodsRequest:()=>Oa,getShippingMethodsSuccess:()=>ha});var q={};r.r(q),r.d(q,{getShippingMethods:()=>Aa});var x={};r.r(x),r.d(x,{getShippingMethods:()=>Pa,isShippingMethodsUpdating:()=>Ca});var j={};r.r(j),r.d(j,{getCreateProductError:()=>ja,getDeleteProductError:()=>Va,getPermalinkParts:()=>Ka,getProduct:()=>Ma,getProducts:()=>Fa,getProductsError:()=>xa,getProductsTotalCount:()=>qa,getRelatedProducts:()=>Ya,getSuggestedProducts:()=>Ha,getUpdateProductError:()=>Qa,isPending:()=>$a});var Q={};r.r(Q),r.d(Q,{createProduct:()=>sc,createProductError:()=>Za,deleteProduct:()=>dc,deleteProductError:()=>lc,deleteProductStart:()=>cc,deleteProductSuccess:()=>uc,duplicateProduct:()=>ac,duplicateProductError:()=>Xa,getProductError:()=>za,getProductSuccess:()=>Ja,getProductsError:()=>rc,getProductsSuccess:()=>tc,getProductsTotalCountError:()=>nc,getProductsTotalCountSuccess:()=>oc,setSuggestedProductAction:()=>Ec,updateProduct:()=>ic,updateProductError:()=>ec});var V={};r.r(V),r.d(V,{getPermalinkParts:()=>fc,getProduct:()=>yc,getProducts:()=>Tc,getProductsTotalCount:()=>gc,getRelatedProducts:()=>_c,getSuggestedProducts:()=>Rc});var $={};r.r($),r.d($,{getOrders:()=>Cc,getOrdersError:()=>vc,getOrdersTotalCount:()=>Uc});var K={};r.r(K),r.d(K,{getOrderError:()=>bc,getOrderSuccess:()=>Dc,getOrdersError:()=>Gc,getOrdersSuccess:()=>kc,getOrdersTotalCountError:()=>Mc,getOrdersTotalCountSuccess:()=>Lc});var Y={};r.r(Y),r.d(Y,{getOrders:()=>Fc,getOrdersTotalCount:()=>qc});var H={};r.r(H),r.d(H,{batchUpdateProductVariations:()=>Hu,batchUpdateProductVariationsError:()=>Yu,generateProductVariations:()=>Ku,generateProductVariationsError:()=>Qu,generateProductVariationsRequest:()=>Vu,generateProductVariationsSuccess:()=>$u});var W={};r.r(W),r.d(W,{generateProductVariationsError:()=>Bu,isGeneratingVariations:()=>Wu});var B={};r.r(B),r.d(B,{getTaxClasses:()=>el});var J={};r.r(J),r.d(J,{getField:()=>il,getFields:()=>sl,getProductForm:()=>al});var z={};r.r(z),r.d(z,{getFieldsError:()=>dl,getFieldsSuccess:()=>ll,getProductFormError:()=>pl,getProductFormSuccess:()=>El});var Z={};r.r(Z),r.d(Z,{getFields:()=>Sl,getProductForm:()=>Tl});var X={};r.r(X),r.d(X,{getOnboardingData:()=>fl,getOnboardingDataError:()=>ml,isOnboardingDataRequestPending:()=>Rl});var ee={};r.r(ee),r.d(ee,{getOnboardingDataError:()=>Il,getOnboardingDataRequest:()=>Ol,getOnboardingDataSuccess:()=>hl});var te={};r.r(te),r.d(te,{getOnboardingData:()=>Al});var re={};r.r(re),r.d(re,{getFormSettings:()=>wl,getImportError:()=>kl,getImportStarted:()=>Nl,getImportStatus:()=>Dl,getImportTotals:()=>bl});var oe={};r.r(oe),r.d(oe,{setImportError:()=>jl,setImportPeriod:()=>Ml,setImportStarted:()=>Ll,setImportStatus:()=>ql,setImportTotals:()=>xl,setSkipPrevious:()=>Fl,updateImportation:()=>Ql});var ne={};r.r(ne),r.d(ne,{getImportStatus:()=>Vl,getImportTotals:()=>$l});var se={};r.r(se),r.d(se,{getEditedSettingIds:()=>rd,getGroup:()=>Jl,getGroupError:()=>sd,getGroups:()=>Bl,getSetting:()=>Xl,getSettingError:()=>id,getSettingValue:()=>ed,getSettings:()=>Zl,hasEditsForGroup:()=>ad,isGroupSaving:()=>od,isSettingEdited:()=>td,isSettingSaving:()=>nd});var ie={};r.r(ie),r.d(ie,{__unstableAcquireStoreLock:()=>bd,__unstableReleaseStoreLock:()=>kd,editSetting:()=>fd,editSettings:()=>md,receiveGroups:()=>_d,receiveSettings:()=>gd,revertEditedSetting:()=>Id,revertEditedSettingsGroup:()=>Ad,saveEditedSetting:()=>wd,saveEditedSettingsGroup:()=>Nd,saveSetting:()=>vd,saveSettingsGroup:()=>Ud,setError:()=>hd,setSaving:()=>Od});var ae={};r.r(ae),r.d(ae,{getGroups:()=>Gd,getSetting:()=>Md,getSettingValue:()=>Fd,getSettings:()=>Ld});var ce={};r.r(ce),r.d(ce,{getError:()=>hE,getExportId:()=>OE,isExportRequesting:()=>mE});var ue={};r.r(ue),r.d(ue,{setError:()=>CE,setExportId:()=>AE,setIsRequesting:()=>PE,startExport:()=>UE});const le=window.wp.coreData,de=window.wp.data,Ee=window.wp.dataControls,pe="wc/admin/settings",Se=window.wp.url,Te=window.wp.apiFetch;var ye=r.n(Te);const _e=e=>({type:"FETCH_WITH_HEADERS",options:e}),ge={...Ee.controls,FETCH_WITH_HEADERS:e=>ye()({...e.options,parse:!1}).then((e=>Promise.all([e.headers,e.status,e.json()]))).then((([e,t,r])=>({headers:e,status:t,data:r}))).catch((e=>e.json().then((e=>{throw e}))))},fe="core";function Re(e,t){if(t){if(Array.isArray(t))return[...t].sort();if("object"==typeof t)return Object.entries(t).sort().reduce(((e,[t,r])=>({...e,[t]:r})),{})}return t}function me(e,...t){return`${e}:${JSON.stringify(t,Re).replace(/\\"/g,'"')}`}function Oe(e,t){const{_fields:r,page:o,per_page:n,order:s,orderby:i,...a}=t;return me(e,a)}function*he(e,t){const r=(0,Se.addQueryArgs)(e,t),o=-1===t.per_page,n=o?Ee.apiFetch:_e,s=yield n({path:r,method:"GET"});if(o&&!("data"in s))return{items:s,totalCount:s.length};if(!o&&"data"in s){const e=parseInt(s.headers.get("x-wp-total")||"",10);return{items:s.data,totalCount:e}}}function*Ie(e){if(!(yield de.controls.resolveSelect(le.store,"getCurrentUser")).capabilities[e])throw new Error(`User does not have ${e} capability.`)}const Ae=e=>[...new Set(Object.keys(e).map((e=>function(e){const t=e.indexOf(":");return t<0?e:e.substring(0,t)}(e))))],Pe=(e,t)=>{const r={},o=e[t]&&e[t].data||[];return Array.isArray(o)&&0!==o.length?(o.forEach((o=>{r[o]=e[me(t,o)].data})),r):r},Ce=(e,t)=>e[t].dirty||[],Ue=(e,t,r=[])=>{const o=Ce(e,t);return 0!==o.length&&r.some((e=>o.includes(e)))},ve=(e,t,r)=>{const o=Pe(e,t);return r.reduce(((e,t)=>(e[t]=o[t]||{},e)),{})},Ne=(e,t)=>e[t]&&Boolean(e[t].isRequesting);function we(e,t,r,o=!1,n=(e,t)=>e){const s=me(t,r);return n(e[s]&&e[s].data||o,o)}const De=(e,t)=>{const r=e[t].data;return Array.isArray(r)&&0!==r.length?[...r].pop().error:e[t].error},be=(e,t,r)=>r?e[me(t,r)].error||!1:e[t]&&e[t].error||!1,ke=window.wp.i18n,Ge=window.lodash,Le="/jetpack/v4",Me="/wc-analytics",Fe="/wc-admin",qe="/wc/v1",xe=100,je=1e3,Qe=60*je,Ve=60*Qe,$e=24*Ve,Ke=7*$e,Ye=365*$e/12,He={pageSize:25,period:"month",compare:"previous_year",noteTypes:["info","marketing","survey","warning"]},We={UPDATE_SETTINGS_FOR_GROUP:"UPDATE_SETTINGS_FOR_GROUP",UPDATE_ERROR_FOR_GROUP:"UPDATE_ERROR_FOR_GROUP",CLEAR_SETTINGS:"CLEAR_SETTINGS",SET_IS_REQUESTING:"SET_IS_REQUESTING",CLEAR_IS_DIRTY:"CLEAR_IS_DIRTY"},Be=de.controls&&de.controls.resolveSelect?de.controls.resolveSelect:Ee.select;function Je(e,t,r=new Date){return{type:We.UPDATE_SETTINGS_FOR_GROUP,group:e,data:t,time:r}}function ze(e,t,r,o=new Date){return{type:We.UPDATE_ERROR_FOR_GROUP,group:e,data:t,error:r,time:o}}function Ze(e,t){return{type:We.SET_IS_REQUESTING,group:e,isRequesting:t}}function Xe(e){return{type:We.CLEAR_IS_DIRTY,group:e}}function*et(e){yield Ze(e,!0);const t=yield Be(pe,"getDirtyKeys",e);if(0===t.length)return void(yield Ze(e,!1));const r=yield Be(pe,"getSettingsForGroup",e,t),o=`${Me}/settings/${e}/batch`,n=t.reduce(((e,t)=>{const o=Object.keys(r[t]).map((e=>({id:e,value:r[t][e]})));return(0,Ge.concat)(e,o)}),[]);try{const t=yield(0,Ee.apiFetch)({path:o,method:"POST",data:{update:n}});if(yield Ze(e,!1),!t)throw new Error((0,ke.__)("There was a problem updating your settings.","woocommerce"));yield Xe(e)}catch(t){throw yield ze(e,null,t),yield Ze(e,!1),t}}function*tt(e,t){yield Ze(e,!0),yield Je(e,t),yield*et(e)}function rt(){return{type:We.CLEAR_SETTINGS}}const ot=e=>void 0!==e.code&&void 0!==e.message,nt=de.controls&&de.controls.dispatch?de.controls.dispatch:Ee.dispatch;function*st(e){yield nt(pe,"setIsRequesting",e,!0);try{const t=Me+"/settings/"+e,r=(yield(0,Ee.apiFetch)({path:t,method:"GET"})).reduce(((e,t)=>(e[t.id]=t.value,e)),{});return Je(e,{[e]:r})}catch(t){if(t instanceof Error||ot(t))return ze(e,null,t.message);throw`Unexpected error ${t}`}}function*it(e){return st(e)}const at=(e,{group:t,groupIds:r,data:o,time:n,error:s})=>(r.forEach((r=>{e[me(t,r)]={data:o[r],lastReceived:n,error:s}})),e),ct=(0,de.createReduxStore)(pe,{reducer:(e={},t)=>{const r={};switch(t.type){case We.SET_IS_REQUESTING:e={...e,[t.group]:{...e[t.group],isRequesting:t.isRequesting}};break;case We.CLEAR_IS_DIRTY:e={...e,[t.group]:{...e[t.group],dirty:[]}};break;case We.UPDATE_SETTINGS_FOR_GROUP:case We.UPDATE_ERROR_FOR_GROUP:const{data:o,group:n,time:s}=t,i=o?Object.keys(o):[],a=t.type===We.UPDATE_ERROR_FOR_GROUP?t.error:null;if(null===o)e={...e,[n]:{data:e[n]?e[n].data:[],error:a,lastReceived:s}};else{const t=e[n];e={...e,[n]:{data:t&&t.data&&Array.isArray(t.data)?[...t.data,...i]:i,error:a,lastReceived:s,isRequesting:e[n]?.isRequesting||!1,dirty:e[n]&&e[n].dirty?(0,Ge.union)(e[n].dirty,i):i},...at(r,{group:n,groupIds:i,data:o,time:s,error:a})}}break;case We.CLEAR_SETTINGS:e={}}return e},actions:t,controls:Ee.controls,selectors:e,resolvers:n});(0,de.register)(ct);const ut=pe,lt="wc/admin/plugins",dt="/wc-paypal/v1",Et={"facebook-for-woocommerce":(0,ke.__)("Facebook for WooCommerce","woocommerce"),jetpack:(0,ke.__)("Jetpack","woocommerce"),"klarna-checkout-for-woocommerce":(0,ke.__)("Klarna Checkout for WooCommerce","woocommerce"),"klarna-payments-for-woocommerce":(0,ke.__)("Klarna Payments for WooCommerce","woocommerce"),"mailchimp-for-woocommerce":(0,ke.__)("Mailchimp for WooCommerce","woocommerce"),"creative-mail-by-constant-contact":(0,ke.__)("Creative Mail for WooCommerce","woocommerce"),"woocommerce-gateway-paypal-express-checkout":(0,ke.__)("WooCommerce PayPal","woocommerce"),"woocommerce-gateway-stripe":(0,ke.__)("WooCommerce Stripe","woocommerce"),"woocommerce-payfast-gateway":(0,ke.__)("WooCommerce Payfast","woocommerce"),"woocommerce-payments":(0,ke.__)("WooPayments","woocommerce"),"woocommerce-services":(0,ke.__)("WooCommerce Shipping & Tax","woocommerce"),"woocommerce-services:shipping":(0,ke.__)("WooCommerce Shipping & Tax","woocommerce"),"woocommerce-services:tax":(0,ke.__)("WooCommerce Shipping & Tax","woocommerce"),"woocommerce-shipstation-integration":(0,ke.__)("WooCommerce ShipStation Gateway","woocommerce"),"woocommerce-mercadopago":(0,ke.__)("Mercado Pago payments for WooCommerce","woocommerce"),"google-listings-and-ads":(0,ke.__)("Google for WooCommerce","woocommerce"),"woo-razorpay":(0,ke.__)("Razorpay","woocommerce"),mailpoet:(0,ke.__)("MailPoet","woocommerce"),"pinterest-for-woocommerce":(0,ke.__)("Pinterest for WooCommerce","woocommerce"),"tiktok-for-business:alt":(0,ke.__)("TikTok for WooCommerce","woocommerce"),codistoconnect:(0,ke.__)("Omnichannel for WooCommerce","woocommerce")},pt=e=>e.active||[],St=e=>e.installed||[],Tt=(e,t)=>e.requesting[t]||!1,yt=(e,t)=>e.errors[t]||!1,_t=e=>e.jetpackConnection,gt=e=>e.jetpackConnectionData,ft=(e,t)=>e.jetpackConnectUrls[t.redirect_url],Rt=(e,t)=>e.active.includes(t)?"activated":e.installed.includes(t)?"installed":"unavailable",mt=e=>e.paypalOnboardingStatus,Ot=(e,t)=>e.recommended[t],ht=window.wc.tracks;var It;!function(e){e.UPDATE_ACTIVE_PLUGINS="UPDATE_ACTIVE_PLUGINS",e.UPDATE_INSTALLED_PLUGINS="UPDATE_INSTALLED_PLUGINS",e.SET_IS_REQUESTING="SET_IS_REQUESTING",e.SET_ERROR="SET_ERROR",e.UPDATE_JETPACK_CONNECTION="UPDATE_JETPACK_CONNECTION",e.UPDATE_JETPACK_CONNECT_URL="UPDATE_JETPACK_CONNECT_URL",e.UPDATE_JETPACK_CONNECTION_DATA="UPDATE_JETPACK_CONNECTION_DATA",e.SET_PAYPAL_ONBOARDING_STATUS="SET_PAYPAL_ONBOARDING_STATUS",e.SET_RECOMMENDED_PLUGINS="SET_RECOMMENDED_PLUGINS"}(It||(It={}));class At extends Error{data;constructor(e,t){super(e),this.data=t}}const Pt=(e,t)=>"object"==typeof t&&null!==t&&e[0]in t,Ct=(e="install",t,r)=>(0,ke.sprintf)((0,ke._n)("Could not %(actionType)s %(pluginName)s plugin, %(error)s","Could not %(actionType)s the following plugins: %(pluginName)s with these Errors: %(error)s",Object.keys(t).length||1,"woocommerce"),{actionType:e,pluginName:t.join(", "),error:r});function Ut(e,t=!1){return{type:It.UPDATE_ACTIVE_PLUGINS,active:e,replace:t}}function vt(e,t=!1){return{type:It.UPDATE_INSTALLED_PLUGINS,installed:e,replace:t}}function Nt(e,t){return{type:It.SET_IS_REQUESTING,selector:e,isRequesting:t}}function wt(e,t){return{type:It.SET_ERROR,selector:e,error:t}}function Dt(e){return{type:It.UPDATE_JETPACK_CONNECTION,jetpackConnection:e}}function bt(e){return{type:It.UPDATE_JETPACK_CONNECTION_DATA,results:e}}function kt(e,t){return{type:It.UPDATE_JETPACK_CONNECT_URL,jetpackConnectUrl:t,redirectUrl:e}}const Gt=e=>de.controls.dispatch("core/notices","createNotice","error",e);function Lt(e){return{type:It.SET_PAYPAL_ONBOARDING_STATUS,paypalOnboardingStatus:e}}function Mt(e,t){return{type:It.SET_RECOMMENDED_PLUGINS,recommendedType:e,plugins:t}}function*Ft(e,t,r){let o;switch(o=Pt(t,r)?Object.values(r).join(", \n"):ot(r)||r instanceof Error?r.message:JSON.stringify(r),e){case"install":(0,ht.recordEvent)("install_plugins_error",{plugins:t.join(", "),message:o});break;case"activate":(0,ht.recordEvent)("activate_plugins_error",{plugins:t.join(", "),message:o})}throw new At(Ct(e,t,o),r)}function*qt(e,t=!1,r){yield Nt("installPlugins",!0);try{const o=yield(0,Ee.apiFetch)({path:`${Fe}/plugins/install`,method:"POST",data:{plugins:e.join(","),async:t,source:r}});if(o.data.installed?.length&&(yield vt(o.data.installed)),o.errors?.errors&&Object.keys(o.errors.errors).length)throw o.errors.errors;return o}catch(t){yield wt("installPlugins",t),yield Ft("install",e,t)}finally{yield Nt("installPlugins",!1)}}function*xt(e){yield Nt("activatePlugins",!0);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/plugins/activate`,method:"POST",data:{plugins:e.join(",")}});if(t.data.activated.length&&(yield Ut(t.data.activated)),Object.keys(t.errors.errors).length)throw t.errors.errors;return t}catch(t){yield wt("activatePlugins",t),yield Ft("activate",e,t)}finally{yield Nt("activatePlugins",!1)}}function*jt(e,t){try{const r=yield de.controls.dispatch(lt,"installPlugins",e,!1,t),o=yield de.controls.dispatch(lt,"activatePlugins",e),n={...o,data:{...o.data,...r.data}};if(r.success&&Object.keys(r.data.results).length&&o.success&&o.data.activated.length)if(1===o.data.activated.length){const e=o.data.activated[0],t=o.data.plugin_details?.[e];n.message=t?(0,ke.sprintf)((0,ke.__)("%1$s (%2$s) was successfully installed and activated.","woocommerce"),t.name,t.version):(0,ke.__)("A plugin was successfully installed and activated.","woocommerce")}else n.message=(0,ke.__)("Plugins were successfully installed and activated.","woocommerce");return n}catch(e){throw e}}function*Qt(e){const t=yield de.controls.resolveSelect(lt,"getJetpackConnectUrl",{redirect_url:e("admin.php?page=wc-admin")}),r=yield de.controls.resolveSelect(lt,"getPluginsError","getJetpackConnectUrl");if(r)throw new Error(r);return t}function*Vt(e,t){try{yield de.controls.dispatch(lt,"installPlugins",["jetpack"]),yield de.controls.dispatch(lt,"activatePlugins",["jetpack"]);const e=yield de.controls.dispatch(lt,"connectToJetpack",t);window.location.href=e}catch(t){if(!(t instanceof Error))throw t;yield e(t.message)}}function*$t(e,t,r){try{const e=yield de.controls.dispatch(lt,"connectToJetpack",r);window.location.href=e}catch(r){if(!(r instanceof Error))throw r;yield t(r.message),window.location.href=e}}const Kt=["payments"];function*Yt(e){if(!Kt.includes(e))return[];const t=yield de.controls.resolveSelect(lt,"getRecommendedPlugins",e);let r;yield Mt(e,[]);try{const e=Fe+"/payment-gateway-suggestions/dismiss";r=yield(0,Ee.apiFetch)({path:e,method:"POST"})}catch(e){r=!1}return r||(yield Mt(e,t)),r}function*Ht(e){try{yield(0,Ee.apiFetch)({path:`/wp/v2/plugins/${e}`,method:"POST",data:{status:"inactive"}})}catch(e){throw e}}const Wt="wc/admin/options",Bt=(e,t)=>e[t],Jt=(e,t)=>e.requestingErrors[t]||!1,zt=e=>e.isUpdating||!1,Zt=e=>e.updatingError||!1,Xt={RECEIVE_OPTIONS:"RECEIVE_OPTIONS",SET_IS_REQUESTING:"SET_IS_REQUESTING",SET_IS_UPDATING:"SET_IS_UPDATING",SET_REQUESTING_ERROR:"SET_REQUESTING_ERROR",SET_UPDATING_ERROR:"SET_UPDATING_ERROR"};function er(e){return{type:Xt.RECEIVE_OPTIONS,options:e}}function tr(e,t){return{type:Xt.SET_REQUESTING_ERROR,error:e,name:t}}function rr(e){return{type:Xt.SET_UPDATING_ERROR,error:e}}function or(e){return{type:Xt.SET_IS_UPDATING,isUpdating:e}}function*nr(e){try{yield or(!0);const t=yield(0,Ee.apiFetch)({path:Fe+"/options",method:"POST",data:e});if(yield or(!1),"object"!=typeof t)throw new Error(`Invalid update options response from server: ${t}`);return yield er(e),{success:!0,...t}}catch(e){if(yield rr(e),"object"!=typeof e)throw new Error(`Unexpected error: ${e}`);return{success:!1,...e}}}const sr=e=>({type:"BATCH_FETCH",optionName:e});let ir=[];const ar={},cr={...Ee.controls,BATCH_FETCH:async({optionName:e})=>(ir.push(e),await(async e=>new Promise((async(t,r)=>(e=>{let t,r=null;const o=(...o)=>{r=o,t&&clearTimeout(t),t=setTimeout((()=>{t=null,r&&e(...r)}),100)};return o.flush=()=>{t&&r&&(e(...r),clearTimeout(t),t=null)},o})((()=>{if(ar.hasOwnProperty(e))return ar[e].then(t).catch(r);0===ir.length&&ir.push(e);const o=[...new Set(ir)],n=o.join(","),s=ye()({path:`${Fe}/options?options=${n}`});o.forEach((async e=>{ar[e]=s;try{await s}catch(e){}finally{delete ar[e]}})),ir=[],s.then(t).catch(r)}))())))(e))};function*ur(e){try{const t=yield sr(e);yield er(t)}catch(t){yield tr(t,e)}}const lr=(0,de.createReduxStore)(Wt,{reducer:(e={isUpdating:!1,requestingErrors:{}},t)=>{switch(t.type){case Xt.RECEIVE_OPTIONS:e={...e,...t.options};break;case Xt.SET_IS_UPDATING:e={...e,isUpdating:t.isUpdating};break;case Xt.SET_REQUESTING_ERROR:e={...e,requestingErrors:{[t.name]:t.error}};break;case Xt.SET_UPDATING_ERROR:e={...e,error:t.error,updatingError:t.error,isUpdating:!1}}return e},actions:c,controls:cr,selectors:a,resolvers:u});(0,de.register)(lr);const dr=Wt,Er=de.controls&&de.controls.resolveSelect?de.controls.resolveSelect:Ee.select;function*pr(){yield Nt("getActivePlugins",!0);try{yield Ie("manage_woocommerce");const e=Fe+"/plugins/active",t=yield(0,Ee.apiFetch)({path:e,method:"GET"});yield Ut(t.plugins,!0)}catch(e){yield wt("getActivePlugins",e)}}function*Sr(){yield Nt("getInstalledPlugins",!0);try{yield Ie("manage_woocommerce");const e=Fe+"/plugins/installed",t=yield(0,Ee.apiFetch)({path:e,method:"GET"});yield vt(t.plugins,!0)}catch(e){yield wt("getInstalledPlugins",e)}}function*Tr(){yield Nt("isJetpackConnected",!0);try{const e=Le+"/connection",t=yield(0,Ee.apiFetch)({path:e,method:"GET"});yield Dt(t.hasConnectedOwner)}catch(e){yield wt("isJetpackConnected",e)}yield Nt("isJetpackConnected",!1)}function*yr(){yield Nt("getJetpackConnectionData",!0);try{const e=yield Er(Or,"isJetpackConnected");yield Ie(e?"read":"manage_options");const t=Le+"/connection/data",r=yield(0,Ee.apiFetch)({path:t,method:"GET"});yield bt(r)}catch(e){yield wt("getJetpackConnectionData",e)}yield Nt("getJetpackConnectionData",!1)}function*_r(e){yield Nt("getJetpackConnectUrl",!0);try{const t=(0,Se.addQueryArgs)(Fe+"/plugins/connect-jetpack",e),r=yield(0,Ee.apiFetch)({path:t,method:"GET"});yield kt(e.redirect_url,r.connectAction)}catch(e){yield wt("getJetpackConnectUrl",e)}yield Nt("getJetpackConnectUrl",!1)}function*gr(){const e=yield Er(dr,"getOption","woocommerce-ppcp-settings"),t=e.merchant_email_production&&e.merchant_id_production&&e.client_id_production&&e.client_secret_production;yield Lt({production:{state:t?"onboarded":"unknown",onboarded:!!t}})}function*fr(){yield Nt("getPaypalOnboardingStatus",!0);const e=yield Er(Or,"getPluginsError","getPaypalOnboardingStatus");if(e&&e.data&&404===e.data.status)yield gr();else try{const e=dt+"/onboarding/get-status",t=yield(0,Ee.apiFetch)({path:e,method:"GET"});yield Lt(t)}catch(e){yield gr(),yield wt("getPaypalOnboardingStatus",e)}yield Nt("getPaypalOnboardingStatus",!1)}const Rr=["payments"];function*mr(e){if(!Rr.includes(e))return[];yield Nt("getRecommendedPlugins",!0);try{const t=Fe+"/payment-gateway-suggestions",r=yield(0,Ee.apiFetch)({path:t,method:"GET"});yield Mt(e,r)}catch(e){yield wt("getRecommendedPlugins",e)}yield Nt("getRecommendedPlugins",!1)}const Or=(0,de.createReduxStore)(lt,{reducer:(e={active:[],installed:[],requesting:{},errors:{},jetpackConnectUrls:{},recommended:{}},t)=>{if(t&&"type"in t)switch(t.type){case It.UPDATE_ACTIVE_PLUGINS:e={...e,active:t.replace?t.active:(0,Ge.concat)(e.active,t.active),requesting:{...e.requesting,getActivePlugins:!1,activatePlugins:!1},errors:{...e.errors,getActivePlugins:!1,activatePlugins:!1}};break;case It.UPDATE_INSTALLED_PLUGINS:e={...e,installed:t.replace?t.installed:(0,Ge.concat)(e.installed,t.installed),requesting:{...e.requesting,getInstalledPlugins:!1,installPlugins:!1},errors:{...e.errors,getInstalledPlugins:!1,installPlugin:!1}};break;case It.SET_IS_REQUESTING:e={...e,requesting:{...e.requesting,[t.selector]:t.isRequesting}};break;case It.SET_ERROR:e={...e,requesting:{...e.requesting,[t.selector]:!1},errors:{...e.errors,[t.selector]:t.error}};break;case It.UPDATE_JETPACK_CONNECTION:e={...e,jetpackConnection:t.jetpackConnection};break;case It.UPDATE_JETPACK_CONNECTION_DATA:e={...e,jetpackConnectionData:t.results};break;case It.UPDATE_JETPACK_CONNECT_URL:e={...e,jetpackConnectUrls:{...e.jetpackConnectUrls,[t.redirectUrl]:t.jetpackConnectUrl}};break;case It.SET_PAYPAL_ONBOARDING_STATUS:e={...e,paypalOnboardingStatus:t.paypalOnboardingStatus};break;case It.SET_RECOMMENDED_PLUGINS:e={...e,recommended:{...e.recommended,[t.recommendedType]:t.plugins}}}return e},actions:i,controls:Ee.controls,selectors:s,resolvers:l});(0,de.register)(Or);const hr=lt,Ir="wc/admin/onboarding";var Ar={};function Pr(e){return[e]}function Cr(e,t,r){var o;if(e.length!==t.length)return!1;for(o=r;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}function Ur(e,t){var r,o=t||Pr;function n(){r=new WeakMap}function s(){var t,n,s,i,a,c=arguments.length;for(i=new Array(c),s=0;s<c;s++)i[s]=arguments[s];for(t=function(e){var t,o,n,s,i,a=r,c=!0;for(t=0;t<e.length;t++){if(!(i=o=e[t])||"object"!=typeof i){c=!1;break}a.has(o)?a=a.get(o):(n=new WeakMap,a.set(o,n),a=n)}return a.has(Ar)||((s=function(){var e={clear:function(){e.head=null}};return e}()).isUniqueByDependants=c,a.set(Ar,s)),a.get(Ar)}(a=o.apply(null,i)),t.isUniqueByDependants||(t.lastDependants&&!Cr(a,t.lastDependants,0)&&t.clear(),t.lastDependants=a),n=t.head;n;){if(Cr(n.args,i,1))return n!==t.head&&(n.prev.next=n.next,n.next&&(n.next.prev=n.prev),n.next=t.head,n.prev=null,t.head.prev=n,t.head=n),n.val;n=n.next}return n={val:e.apply(null,i)},i[0]=null,n.args=i,t.head&&(t.head.prev=n,n.next=t.head),t.head=n,n.val}return s.getDependants=o,s.clear=n,n(),s}const vr=e=>e.freeExtensions||[],Nr=e=>e.profileItems||{},wr=e=>e.profileProgress||{},Dr=Ur((e=>Object.values(e.taskLists)),(e=>[e.taskLists])),br=Ur(((e,t)=>t.map((t=>e.taskLists[t]))),((e,t)=>t.map((t=>e.taskLists[t])))),kr=(e,t)=>e.taskLists[t],Gr=(e,t)=>Object.keys(e.taskLists).reduce(((r,o)=>r||e.taskLists[o].tasks.find((e=>e.id===t))),void 0),Lr=e=>e.paymentMethods||[],Mr=(e,t)=>e.errors[t]||!1,Fr=(e,t)=>e.requesting[t]||!1,qr=e=>e.emailPrefill||"",xr=e=>e.productTypes||{},jr=(e,t)=>e.jetpackAuthUrls[t.redirectUrl]||"",Qr=Ur((e=>e.profileProgress||{}),(e=>[e.profileProgress])),Vr=Ur((e=>{const t=e.profileProgress||{};return Object.entries(t).sort(((e,t)=>{const r=new Date(e[1].completed_at);return new Date(t[1].completed_at).getTime()-r.getTime()}))[0]?.[0]||null}),(e=>[e.profileProgress])),$r={SET_ERROR:"SET_ERROR",SET_IS_REQUESTING:"SET_IS_REQUESTING",SET_PROFILE_ITEMS:"SET_PROFILE_ITEMS",SET_EMAIL_PREFILL:"SET_EMAIL_PREFILL",GET_PAYMENT_METHODS_SUCCESS:"GET_PAYMENT_METHODS_SUCCESS",GET_PRODUCT_TYPES_SUCCESS:"GET_PRODUCT_TYPES_SUCCESS",GET_PRODUCT_TYPES_ERROR:"GET_PRODUCT_TYPES_ERROR",GET_FREE_EXTENSIONS_ERROR:"GET_FREE_EXTENSIONS_ERROR",GET_FREE_EXTENSIONS_SUCCESS:"GET_FREE_EXTENSIONS_SUCCESS",GET_TASK_LISTS_ERROR:"GET_TASK_LISTS_ERROR",GET_TASK_LISTS_SUCCESS:"GET_TASK_LISTS_SUCCESS",DISMISS_TASK_ERROR:"DISMISS_TASK_ERROR",DISMISS_TASK_REQUEST:"DISMISS_TASK_REQUEST",DISMISS_TASK_SUCCESS:"DISMISS_TASK_SUCCESS",UNDO_DISMISS_TASK_ERROR:"UNDO_DISMISS_TASK_ERROR",UNDO_DISMISS_TASK_REQUEST:"UNDO_DISMISS_TASK_REQUEST",UNDO_DISMISS_TASK_SUCCESS:"UNDO_DISMISS_TASK_SUCCESS",SNOOZE_TASK_ERROR:"SNOOZE_TASK_ERROR",SNOOZE_TASK_REQUEST:"SNOOZE_TASK_REQUEST",SNOOZE_TASK_SUCCESS:"SNOOZE_TASK_SUCCESS",UNDO_SNOOZE_TASK_ERROR:"UNDO_SNOOZE_TASK_ERROR",UNDO_SNOOZE_TASK_REQUEST:"UNDO_SNOOZE_TASK_REQUEST",UNDO_SNOOZE_TASK_SUCCESS:"UNDO_SNOOZE_TASK_SUCCESS",HIDE_TASK_LIST_ERROR:"HIDE_TASK_LIST_ERROR",HIDE_TASK_LIST_REQUEST:"HIDE_TASK_LIST_REQUEST",HIDE_TASK_LIST_SUCCESS:"HIDE_TASK_LIST_SUCCESS",UNHIDE_TASK_LIST_ERROR:"UNHIDE_TASK_LIST_ERROR",UNHIDE_TASK_LIST_REQUEST:"UNHIDE_TASK_LIST_REQUEST",UNHIDE_TASK_LIST_SUCCESS:"UNHIDE_TASK_LIST_SUCCESS",OPTIMISTICALLY_COMPLETE_TASK_REQUEST:"OPTIMISTICALLY_COMPLETE_TASK_REQUEST",ACTION_TASK_ERROR:"ACTION_TASK_ERROR",ACTION_TASK_REQUEST:"ACTION_TASK_REQUEST",ACTION_TASK_SUCCESS:"ACTION_TASK_SUCCESS",VISITED_TASK:"VISITED_TASK",KEEP_COMPLETED_TASKS_REQUEST:"KEEP_COMPLETED_TASKS_REQUEST",KEEP_COMPLETED_TASKS_SUCCESS:"KEEP_COMPLETED_TASKS_SUCCESS",SET_JETPACK_AUTH_URL:"SET_JETPACK_AUTH_URL",CORE_PROFILER_COMPLETED_REQUEST:"CORE_PROFILER_COMPLETED_REQUEST",CORE_PROFILER_COMPLETED_SUCCESS:"CORE_PROFILER_COMPLETED_SUCCESS",CORE_PROFILER_COMPLETED_ERROR:"CORE_PROFILER_COMPLETED_ERROR",SET_PROFILE_PROGRESS:"SET_PROFILE_PROGRESS"},Kr=window.wp.hooks;var Yr=r(4594);const Hr=window.wp.deprecated;var Wr=r.n(Hr);class Br{filteredTasks;tasks;constructor(){this.filteredTasks=(0,Kr.applyFilters)("woocommerce_admin_onboarding_task_list",[],function(){const e=window.location&&window.location.search;if(!e)return{};const t=e.substring(1);return(0,Yr.parse)(t)}()),this.filteredTasks&&this.filteredTasks.length>0&&Wr()("woocommerce_admin_onboarding_task_list",{version:"2.10.0",alternative:"TaskLists::add_task()",plugin:"@woocommerce/data"}),this.tasks=this.filteredTasks.reduce(((e,t)=>({...e,[t.key]:t})),{})}hasDeprecatedTasks(){return this.filteredTasks.length>0}getPostData(){return this.hasDeprecatedTasks()?{extended_tasks:this.filteredTasks.map((e=>({title:e.title,content:e.content,additional_info:e.additionalInfo,time:e.time,level:e.level?parseInt(e.level,10):3,list_id:e.type||"extended",can_view:e.visible,id:e.key,is_snoozeable:e.allowRemindMeLater,is_dismissable:e.isDismissable,is_complete:e.completed})))}:null}mergeDeprecatedCallbackFunctions(e){if(this.filteredTasks.length>0)for(const t of e)t.tasks=t.tasks.map((e=>this.tasks&&this.tasks[e.id]?{...this.tasks[e.id],...e,isDeprecated:!0}:e));return e}static possiblyPruneTaskData(e,t){return e.time||e.title?e:t.reduce(((t,r)=>({...t,[r]:e[r]})),{id:e.id})}}function Jr(e){return{type:$r.GET_FREE_EXTENSIONS_ERROR,error:e}}function zr(e){return{type:$r.GET_FREE_EXTENSIONS_SUCCESS,freeExtensions:e}}function Zr(e,t){return{type:$r.SET_ERROR,selector:e,error:t}}function Xr(e,t){return{type:$r.SET_IS_REQUESTING,selector:e,isRequesting:t}}function eo(e,t=!1){return{type:$r.SET_PROFILE_ITEMS,profileItems:e,replace:t}}function to(e){return{type:$r.GET_TASK_LISTS_ERROR,error:e}}function ro(e){return{type:$r.GET_TASK_LISTS_SUCCESS,taskLists:e}}function oo(e,t){return{type:$r.SNOOZE_TASK_ERROR,taskId:e,error:t}}function no(e){return{type:$r.SNOOZE_TASK_REQUEST,taskId:e}}function so(e){return{type:$r.SNOOZE_TASK_SUCCESS,task:e}}function io(e,t){return{type:$r.UNDO_SNOOZE_TASK_ERROR,taskId:e,error:t}}function ao(e){return{type:$r.UNDO_SNOOZE_TASK_REQUEST,taskId:e}}function co(e){return{type:$r.UNDO_SNOOZE_TASK_SUCCESS,task:e}}function uo(e,t){return{type:$r.DISMISS_TASK_ERROR,taskId:e,error:t}}function lo(e){return{type:$r.DISMISS_TASK_REQUEST,taskId:e}}function Eo(e){return{type:$r.DISMISS_TASK_SUCCESS,task:e}}function po(e,t){return{type:$r.UNDO_DISMISS_TASK_ERROR,taskId:e,error:t}}function So(e){return{type:$r.UNDO_DISMISS_TASK_REQUEST,taskId:e}}function To(e){return{type:$r.UNDO_DISMISS_TASK_SUCCESS,task:e}}function yo(e,t){return{type:$r.HIDE_TASK_LIST_ERROR,taskListId:e,error:t}}function _o(e){return{type:$r.HIDE_TASK_LIST_REQUEST,taskListId:e}}function go(e){return{type:$r.HIDE_TASK_LIST_SUCCESS,taskList:e,taskListId:e.id}}function fo(e,t){return{type:$r.UNHIDE_TASK_LIST_ERROR,taskListId:e,error:t}}function Ro(e){return{type:$r.UNHIDE_TASK_LIST_REQUEST,taskListId:e}}function mo(e){return{type:$r.UNHIDE_TASK_LIST_SUCCESS,taskList:e,taskListId:e.id}}function Oo(e){return{type:$r.OPTIMISTICALLY_COMPLETE_TASK_REQUEST,taskId:e}}function ho(e,t){return{type:$r.KEEP_COMPLETED_TASKS_SUCCESS,taskListId:e,keepCompletedTaskList:t}}function Io(e){return{type:$r.VISITED_TASK,taskId:e}}function Ao(e){return{type:$r.GET_PAYMENT_METHODS_SUCCESS,paymentMethods:e}}function Po(e){return{type:$r.SET_EMAIL_PREFILL,emailPrefill:e}}function Co(e,t){return{type:$r.ACTION_TASK_ERROR,taskId:e,error:t}}function Uo(e){return{type:$r.ACTION_TASK_REQUEST,taskId:e}}function vo(e){return{type:$r.ACTION_TASK_SUCCESS,task:e}}function No(e){return{type:$r.GET_PRODUCT_TYPES_SUCCESS,productTypes:e}}function wo(e){return{type:$r.GET_PRODUCT_TYPES_ERROR,error:e}}function Do(e){return{type:$r.SET_PROFILE_PROGRESS,profileProgress:e}}function*bo(e){const t=yield de.controls.dispatch(Wt,"updateOptions",{woocommerce_task_list_keep_completed:"yes"});t&&t.success&&(yield ho(e,"yes"))}function*ko(e){yield Xr("updateProfileItems",!0),yield Zr("updateProfileItems",null);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/profile`,method:"POST",data:e});if(t&&"success"===t.status)return yield eo(e),yield Xr("updateProfileItems",!1),t;throw new Error}catch(e){throw yield Zr("updateProfileItems",e),yield Xr("updateProfileItems",!1),e}finally{yield(0,de.dispatch)(lr).invalidateResolution("getOption",["woocommerce_onboarding_profile"]),yield(0,de.dispatch)(Sn).invalidateResolution("getProfileItems",[])}}function*Go(e){yield Xr("updateCoreProfilerStep",!0),yield Zr("updateCoreProfilerStep",null);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/profile/progress/core-profiler/complete`,method:"POST",data:{step:e}});if(t&&"success"===t.status)return yield Xr("updateCoreProfilerStep",!1),t;throw new Error}catch(e){throw yield Zr("updateCoreProfilerStep",e),yield Xr("updateCoreProfilerStep",!1),e}finally{yield(0,de.dispatch)(Sn).invalidateResolution("getProfileProgress",[]),yield(0,de.dispatch)(Sn).invalidateResolution("getCoreProfilerCompletedSteps",[]),yield(0,de.dispatch)(Sn).invalidateResolution("getMostRecentCoreProfilerStep",[])}}function*Lo(e){yield no(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/snooze`,method:"POST"});yield so(Br.possiblyPruneTaskData(t,["isSnoozed","isDismissed","snoozedUntil"]))}catch(t){throw yield oo(e,t),new Error}}function*Mo(e){yield ao(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/undo_snooze`,method:"POST"});yield co(Br.possiblyPruneTaskData(t,["isSnoozed","isDismissed","snoozedUntil"]))}catch(t){throw yield io(e,t),new Error}}function*Fo(e){yield lo(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/dismiss`,method:"POST"});yield Eo(Br.possiblyPruneTaskData(t,["isDismissed","isSnoozed"]))}catch(t){throw yield uo(e,t),new Error}}function*qo(e){yield So(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/undo_dismiss`,method:"POST"});yield To(Br.possiblyPruneTaskData(t,["isDismissed","isSnoozed"]))}catch(t){throw yield po(e,t),new Error}}function*xo(e){yield _o(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/hide`,method:"POST"});yield go(t)}catch(t){throw yield yo(e,t),new Error}}function*jo(e){yield Ro(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/unhide`,method:"POST"});yield mo(t)}catch(t){throw yield fo(e,t),new Error}}function*Qo(e){yield Oo(e)}function*Vo(e){yield Uo(e);try{const t=yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/tasks/${e}/action`,method:"POST"});yield vo(Br.possiblyPruneTaskData(t,["isActioned"]))}catch(t){throw yield Co(e,t),new Error}}function*$o(e,t){yield Xr("installAndActivatePluginsAsync",!0);try{return yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/plugins/install-and-activate-async`,method:"POST",data:{plugins:e,source:t}})}catch(e){throw e}finally{yield Xr("installAndActivatePluginsAsync",!1)}}function*Ko(e){yield Xr("updateStoreCurrencyAndMeasurementUnits",!0);try{return yield(0,Ee.apiFetch)({path:`${Fe}/onboarding/profile/update-store-currency-and-measurement-units`,method:"POST",data:{country_code:e}})}catch(e){throw e}finally{yield Xr("updateStoreCurrencyAndMeasurementUnits",!1)}}function Yo(e,t,r=""){return{type:$r.SET_JETPACK_AUTH_URL,results:e,redirectUrl:t,from:r}}function Ho(e){return{type:$r.CORE_PROFILER_COMPLETED_ERROR,error:e}}function Wo(){return{type:$r.CORE_PROFILER_COMPLETED_REQUEST}}function Bo(){return{type:$r.CORE_PROFILER_COMPLETED_SUCCESS}}function*Jo(){yield Wo();try{yield(0,Ee.apiFetch)({path:`${Fe}/launch-your-store/initialize-coming-soon`,method:"POST"})}catch(e){throw yield Ho(e),e}finally{yield Bo()}}const zo=de.controls&&de.controls.resolveSelect?de.controls.resolveSelect:Ee.select;function*Zo(){try{const e=yield(0,Ee.apiFetch)({path:Fe+"/onboarding/profile",method:"GET"});yield eo(e,!0)}catch(e){yield Zr("getProfileItems",e)}}function*Xo(){try{const e=yield(0,Ee.apiFetch)({path:Fe+"/onboarding/profile/progress",method:"GET"});yield Do(e.core_profiler_completed_steps)}catch(e){yield Zr("getProfileProgress",e)}}function*en(){yield zo(Ir,"getProfileProgress")}function*tn(){yield zo(Ir,"getProfileProgress")}function*rn(){try{const e=yield(0,Ee.apiFetch)({path:Fe+"/onboarding/profile/experimental_get_email_prefill",method:"GET"});yield Po(e.email)}catch(e){yield Zr("getEmailPrefill",e)}}function*on(){const e=new Br;try{yield Ie("manage_woocommerce");const t=yield(0,Ee.apiFetch)({path:Fe+"/onboarding/tasks",method:e.hasDeprecatedTasks()?"POST":"GET",data:e.getPostData()});e.mergeDeprecatedCallbackFunctions(t),yield ro(t)}catch(e){yield to(e)}}function*nn(){yield zo(Ir,"getTaskLists")}function*sn(){yield zo(Ir,"getTaskLists")}function*an(){yield zo(Ir,"getTaskLists")}function*cn(e=!1){let t=Fe+"/payment-gateway-suggestions";e&&(t+="?force_default_suggestions=true");try{const e=yield(0,Ee.apiFetch)({path:t,method:"GET"});yield Ao(e)}catch(e){yield Zr("getPaymentGatewaySuggestions",e)}}function*un(){try{const e=yield(0,Ee.apiFetch)({path:Fe+"/onboarding/free-extensions",method:"GET"});yield zr(e)}catch(e){yield Jr(e)}}function*ln(){try{const e=yield(0,Ee.apiFetch)({path:Fe+"/onboarding/product-types",method:"GET"});yield No(e)}catch(e){yield wo(e)}}function*dn(e){try{var t;let r=Fe+"/onboarding/plugins/jetpack-authorization-url?redirect_url="+encodeURIComponent(e.redirectUrl);e.from&&(r+="&from="+e.from);const o=yield(0,Ee.apiFetch)({path:r,method:"GET"});yield Yo(o,e.redirectUrl,null!==(t=e.from)&&void 0!==t?t:"")}catch(e){yield Zr("getJetpackAuthUrl",e)}}const En={errors:{},freeExtensions:[],profileItems:{business_extensions:null,completed:null,industry:null,number_employees:null,other_platform:null,other_platform_name:null,product_count:null,product_types:null,revenue:null,selling_venues:null,setup_client:null,skipped:null,theme:null,wccom_connected:null,is_agree_marketing:null,store_email:null,is_store_country_set:null},profileProgress:{},emailPrefill:"",paymentMethods:[],productTypes:{},requesting:{},taskLists:{},jetpackAuthUrls:{}},pn=(e,t)=>Object.keys(e).reduce(((r,o)=>({...r,[o]:{...e[o],tasks:e[o].tasks.map((e=>t.id===e.id?{...e,...t}:e))}})),{...e}),Sn=(0,de.createReduxStore)(Ir,{reducer:(e=En,t)=>{switch(t.type){case $r.SET_PROFILE_ITEMS:return{...e,profileItems:t.replace?t.profileItems:{...e.profileItems,...t.profileItems}};case $r.SET_PROFILE_PROGRESS:return{...e,profileProgress:t.profileProgress};case $r.SET_EMAIL_PREFILL:return{...e,emailPrefill:t.emailPrefill};case $r.SET_ERROR:return{...e,errors:{...e.errors,[t.selector]:t.error}};case $r.SET_IS_REQUESTING:return{...e,requesting:{...e.requesting,[t.selector]:t.isRequesting}};case $r.GET_PAYMENT_METHODS_SUCCESS:return{...e,paymentMethods:t.paymentMethods};case $r.GET_PRODUCT_TYPES_SUCCESS:return{...e,productTypes:t.productTypes};case $r.GET_PRODUCT_TYPES_ERROR:return{...e,errors:{...e.errors,productTypes:t.error}};case $r.GET_FREE_EXTENSIONS_ERROR:return{...e,errors:{...e.errors,getFreeExtensions:t.error}};case $r.GET_FREE_EXTENSIONS_SUCCESS:return{...e,freeExtensions:t.freeExtensions};case $r.GET_TASK_LISTS_ERROR:return{...e,errors:{...e.errors,getTaskLists:t.error}};case $r.GET_TASK_LISTS_SUCCESS:return{...e,taskLists:t.taskLists.reduce(((e,t)=>({...e,[t.id]:t})),e.taskLists||{})};case $r.DISMISS_TASK_ERROR:return{...e,errors:{...e.errors,dismissTask:t.error},taskLists:pn(e.taskLists,{id:t.taskId,isDismissed:!1})};case $r.DISMISS_TASK_REQUEST:return{...e,requesting:{...e.requesting,dismissTask:!0},taskLists:pn(e.taskLists,{id:t.taskId,isDismissed:!0})};case $r.DISMISS_TASK_SUCCESS:return{...e,requesting:{...e.requesting,dismissTask:!1},taskLists:pn(e.taskLists,t.task)};case $r.UNDO_DISMISS_TASK_ERROR:return{...e,errors:{...e.errors,undoDismissTask:t.error},taskLists:pn(e.taskLists,{id:t.taskId,isDismissed:!0})};case $r.UNDO_DISMISS_TASK_REQUEST:return{...e,requesting:{...e.requesting,undoDismissTask:!0},taskLists:pn(e.taskLists,{id:t.taskId,isDismissed:!1})};case $r.UNDO_DISMISS_TASK_SUCCESS:return{...e,requesting:{...e.requesting,undoDismissTask:!1},taskLists:pn(e.taskLists,t.task)};case $r.SNOOZE_TASK_ERROR:return{...e,errors:{...e.errors,snoozeTask:t.error},taskLists:pn(e.taskLists,{id:t.taskId,isSnoozed:!1})};case $r.SNOOZE_TASK_REQUEST:return{...e,requesting:{...e.requesting,snoozeTask:!0},taskLists:pn(e.taskLists,{id:t.taskId,isSnoozed:!0})};case $r.SNOOZE_TASK_SUCCESS:return{...e,requesting:{...e.requesting,snoozeTask:!1},taskLists:pn(e.taskLists,t.task)};case $r.UNDO_SNOOZE_TASK_ERROR:return{...e,errors:{...e.errors,undoSnoozeTask:t.error},taskLists:pn(e.taskLists,{id:t.taskId,isSnoozed:!0})};case $r.UNDO_SNOOZE_TASK_REQUEST:return{...e,requesting:{...e.requesting,undoSnoozeTask:!0},taskLists:pn(e.taskLists,{id:t.taskId,isSnoozed:!1})};case $r.UNDO_SNOOZE_TASK_SUCCESS:return{...e,requesting:{...e.requesting,undoSnoozeTask:!1},taskLists:pn(e.taskLists,t.task)};case $r.HIDE_TASK_LIST_ERROR:return{...e,errors:{...e.errors,hideTaskList:t.error},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!1,isVisible:!0}}};case $r.HIDE_TASK_LIST_REQUEST:return{...e,requesting:{...e.requesting,hideTaskList:!0},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!0,isVisible:!1}}};case $r.HIDE_TASK_LIST_SUCCESS:return{...e,requesting:{...e.requesting,hideTaskList:!1},taskLists:{...e.taskLists,[t.taskListId]:t.taskList}};case $r.UNHIDE_TASK_LIST_ERROR:return{...e,errors:{...e.errors,unhideTaskList:t.error},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!0,isVisible:!1}}};case $r.UNHIDE_TASK_LIST_REQUEST:return{...e,requesting:{...e.requesting,unhideTaskList:!0},taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],isHidden:!1,isVisible:!0}}};case $r.UNHIDE_TASK_LIST_SUCCESS:return{...e,requesting:{...e.requesting,unhideTaskList:!1},taskLists:{...e.taskLists,[t.taskListId]:t.taskList}};case $r.KEEP_COMPLETED_TASKS_SUCCESS:return{...e,taskLists:{...e.taskLists,[t.taskListId]:{...e.taskLists[t.taskListId],keepCompletedTaskList:t.keepCompletedTaskList}}};case $r.OPTIMISTICALLY_COMPLETE_TASK_REQUEST:return{...e,taskLists:pn(e.taskLists,{id:t.taskId,isComplete:!0})};case $r.VISITED_TASK:return{...e,taskLists:pn(e.taskLists,{id:t.taskId,isVisited:!0})};case $r.ACTION_TASK_ERROR:return{...e,errors:{...e.errors,actionTask:t.error},taskLists:pn(e.taskLists,{id:t.taskId,isActioned:!1})};case $r.ACTION_TASK_REQUEST:return{...e,requesting:{...e.requesting,actionTask:!0},taskLists:pn(e.taskLists,{id:t.taskId,isActioned:!0})};case $r.ACTION_TASK_SUCCESS:return{...e,requesting:{...e.requesting,actionTask:!1},taskLists:pn(e.taskLists,t.task)};case $r.SET_JETPACK_AUTH_URL:return{...e,jetpackAuthUrls:{...e.jetpackAuthUrls,[t.redirectUrl]:t.results}};case $r.CORE_PROFILER_COMPLETED_REQUEST:return{...e,requesting:{...e.requesting,coreProfilerCompleted:!0}};case $r.CORE_PROFILER_COMPLETED_SUCCESS:return{...e,requesting:{...e.requesting,coreProfilerCompleted:!1}};case $r.CORE_PROFILER_COMPLETED_ERROR:return{...e,errors:{...e.errors,coreProfilerCompleted:t.error},requesting:{...e.requesting,coreProfilerCompleted:!1}};default:return e}},actions:E,controls:Ee.controls,selectors:d,resolvers:p});(0,de.register)(Sn);const Tn=Ir,yn="wc/admin/reviews",_n=(e,t)=>{const r=JSON.stringify(t);return(e.reviews[r]&&e.reviews[r].data||[]).map((t=>e.data[t]))},gn=(e,t)=>e.data[t],fn=(e,t)=>{const r=JSON.stringify(t);return e.reviews[r]&&e.reviews[r].totalCount},Rn=(e,t)=>{const r=JSON.stringify(t);return e.errors[r]},mn=(e,t)=>e.errors[t],On={UPDATE_REVIEWS:"UPDATE_REVIEWS",SET_REVIEW:"SET_REVIEW",SET_ERROR:"SET_ERROR",SET_REVIEW_IS_UPDATING:"SET_REVIEW_IS_UPDATING"};function hn(e,t){return{type:On.SET_REVIEW_IS_UPDATING,reviewId:e,isUpdating:t}}function In(e,t){return{type:On.SET_REVIEW,reviewId:e,reviewData:t}}function An(e,t){return{type:On.SET_ERROR,query:e,error:t}}function Pn(e,t,r){return{type:On.UPDATE_REVIEWS,reviews:t,query:e,totalCount:r}}function*Cn(e,t,r){yield hn(e,!0);try{const o=(0,Se.addQueryArgs)(`${Me}/products/reviews/${e}`,r||{}),n=yield(0,Ee.apiFetch)({path:o,method:"PUT",data:t});yield In(e,n),yield hn(e,!1)}catch(t){throw yield An("updateReview",t),yield hn(e,!1),new Error}}function*Un(e){yield hn(e,!0);try{const t=`${Me}/products/reviews/${e}`,r=yield(0,Ee.apiFetch)({path:t,method:"DELETE"});return yield In(e,r),yield hn(e,!1),r}catch(t){throw yield An("deleteReview",t),yield hn(e,!1),new Error}}function*vn(e){try{const t=(0,Se.addQueryArgs)(`${Me}/products/reviews`,e),r=yield _e({path:t,method:"GET"}),o=r.headers.get("x-wp-total");if(void 0===o)throw new Error("Malformed response from server. 'x-wp-total' header is missing when retrieving ./products/reviews.");const n=parseInt(o,10);yield Pn(e,r.data,n)}catch(t){yield An(JSON.stringify(e),t)}}function*Nn(e){try{const t=(0,Se.addQueryArgs)(`wc/v3/products/reviews/${e}`),r=yield _e({path:t,method:"GET"});yield In(e,r.data)}catch(t){yield An(JSON.stringify(e),t)}}function*wn(e){yield vn(e)}const Dn={reviews:{},errors:{},data:{}},bn=(0,de.createReduxStore)(yn,{reducer:(e=Dn,t)=>{switch(t.type){case On.UPDATE_REVIEWS:const r=[],o=t.reviews.reduce(((t,o)=>(r.push(o.id),t[o.id]={...e.data[o.id]||{},...o},t)),{});return{...e,reviews:{...e.reviews,[JSON.stringify(t.query)]:{data:r,totalCount:t.totalCount}},data:{...e.data,...o}};case On.SET_REVIEW:return{...e,data:{...e.data,[t.reviewId]:t.reviewData}};case On.SET_ERROR:return{...e,errors:{...e.errors,[t.query]:t.error}};case On.SET_REVIEW_IS_UPDATING:return{...e,data:{...e.data,[t.reviewId]:{...e.data[t.reviewId],isUpdating:t.isUpdating}}};default:return e}},actions:T,controls:ge,selectors:S,resolvers:y});(0,de.register)(bn);const kn=yn,Gn="wc/admin/notes",Ln=Ur(((e,t)=>(e.noteQueries[JSON.stringify(t)]||[]).map((t=>e.notes[t]))),((e,t)=>[e.noteQueries[JSON.stringify(t)],e.notes])),Mn=(e,t)=>e.errors[t]||!1,Fn=(e,t)=>e.requesting[t]||!1,qn={SET_ERROR:"SET_ERROR",SET_NOTE:"SET_NOTE",SET_NOTE_IS_UPDATING:"SET_NOTE_IS_UPDATING",SET_NOTES:"SET_NOTES",SET_NOTES_QUERY:"SET_NOTES_QUERY",SET_IS_REQUESTING:"SET_IS_REQUESTING"};function xn(e,t){return{type:qn.SET_NOTE,noteId:e,noteFields:t}}function jn(e,t){return{type:qn.SET_NOTE_IS_UPDATING,noteId:e,isUpdating:t}}function Qn(e){return{type:qn.SET_NOTES,notes:e}}function Vn(e,t){return{type:qn.SET_NOTES_QUERY,query:e,noteIds:t}}function $n(e,t){return{type:qn.SET_ERROR,error:t,selector:e}}function Kn(e,t){return{type:qn.SET_IS_REQUESTING,selector:e,isRequesting:t}}function*Yn(e,t){yield Kn("updateNote",!0),yield jn(e,!0);try{const r=`${Me}/admin/notes/${e}`,o=yield(0,Ee.apiFetch)({path:r,method:"PUT",data:t});yield xn(e,o),yield Kn("updateNote",!1),yield jn(e,!1)}catch(t){throw yield $n("updateNote",t),yield Kn("updateNote",!1),yield jn(e,!1),new Error}}function*Hn(e,t){yield Kn("triggerNoteAction",!0);const r=`${Me}/admin/notes/${e}/action/${t}`;try{const t=yield(0,Ee.apiFetch)({path:r,method:"POST"});yield Yn(e,t),yield Kn("triggerNoteAction",!1)}catch(e){throw yield $n("triggerNoteAction",e),yield Kn("triggerNoteAction",!1),new Error}}function*Wn(e){yield Kn("removeNote",!0),yield jn(e,!0);try{const t=`${Me}/admin/notes/delete/${e}`,r=yield(0,Ee.apiFetch)({path:t,method:"DELETE"});return yield xn(e,r),yield Kn("removeNote",!1),r}catch(t){throw yield $n("removeNote",t),yield Kn("removeNote",!1),yield jn(e,!1),new Error}}function*Bn(e={}){yield Kn("removeAllNotes",!0);try{const t=(0,Se.addQueryArgs)(`${Me}/admin/notes/delete/all`,e),r=yield(0,Ee.apiFetch)({path:t,method:"DELETE"});return yield Qn(r),yield Kn("removeAllNotes",!1),r}catch(e){throw yield $n("removeAllNotes",e),yield Kn("removeAllNotes",!1),new Error}}function*Jn(e,t){yield Kn("batchUpdateNotes",!0);try{const r=`${Me}/admin/notes/update`,o=yield(0,Ee.apiFetch)({path:r,method:"PUT",data:{noteIds:e,...t}});yield Qn(o),yield Kn("batchUpdateNotes",!1)}catch(e){throw yield $n("updateNote",e),yield Kn("batchUpdateNotes",!1),new Error}}function*zn(e={}){const t=(0,Se.addQueryArgs)(`${Me}/admin/notes`,e);try{yield Ie("manage_woocommerce");const r=yield(0,Ee.apiFetch)({path:t});yield Qn(r),yield Vn(e,r.map((e=>e.id)))}catch(e){yield $n("getNotes",e)}}const Zn=(0,de.createReduxStore)(Gn,{reducer:(e={errors:{},noteQueries:{},notes:{},requesting:{}},t)=>{switch(t.type){case qn.SET_NOTES:e={...e,notes:{...e.notes,...t.notes.reduce(((e,t)=>(e[t.id]=t,e)),{})}};break;case qn.SET_NOTES_QUERY:e={...e,noteQueries:{...e.noteQueries,[JSON.stringify(t.query)]:t.noteIds}};break;case qn.SET_ERROR:e={...e,errors:{...e.errors,[t.selector]:t.error}};break;case qn.SET_NOTE:e={...e,notes:{...e.notes,[t.noteId]:t.noteFields}};break;case qn.SET_NOTE_IS_UPDATING:e={...e,notes:{...e.notes,[t.noteId]:{...e.notes[t.noteId],isUpdating:t.isUpdating}}};break;case qn.SET_IS_REQUESTING:e={...e,requesting:{...e.requesting,[t.selector]:t.isRequesting}}}return e},actions:g,controls:Ee.controls,selectors:_,resolvers:f});(0,de.register)(Zn);const Xn=Gn,es="wc/admin/reports",ts={},rs=(e,t,r)=>{const o=me(t,r);return e.itemErrors[o]||!1},os=(e,t,r)=>{const o=me(t,r);return e.items[o]||ts},ns=(e,t,r)=>{const o=me(t,r);return e.stats[o]||ts},ss=(e,t,r)=>{const o=me(t,r);return e.statErrors[o]||!1},is={SET_ITEM_ERROR:"SET_ITEM_ERROR",SET_STAT_ERROR:"SET_STAT_ERROR",SET_REPORT_ITEMS:"SET_REPORT_ITEMS",SET_REPORT_STATS:"SET_REPORT_STATS"};function as(e,t,r){const o=me(e,t);return{type:is.SET_ITEM_ERROR,resourceName:o,error:r}}function cs(e,t,r){const o=me(e,t);return{type:is.SET_REPORT_ITEMS,resourceName:o,items:r}}function us(e,t,r){const o=me(e,t);return{type:is.SET_REPORT_STATS,resourceName:o,stats:r}}function ls(e,t,r){const o=me(e,t);return{type:is.SET_STAT_ERROR,resourceName:o,error:r}}const ds=(e,t,r)=>r.map((r=>{const o=t.headers.get(r);if(void 0===o)throw new Error(`Malformed response from server. '${r}' header is missing when retrieving ./report/${e}.`);return parseInt(o,10)}));function*Es(e,t){const r={parse:!1,path:(0,Se.addQueryArgs)(`${Me}/reports/${e}`,t)};if("performance-indicators"!==e||t.stats)try{const o=yield _e(r),n=o.data,[s,i]=ds(e,o,["x-wp-total","x-wp-totalpages"]);yield cs(e,t,{data:n,totalResults:s,totalPages:i})}catch(r){yield as(e,t,r)}else yield cs(e,t,{data:[],totalResults:0,totalPages:0})}function*ps(e,t){const r={parse:!1,path:(0,Se.addQueryArgs)(`${Me}/reports/${e}/stats`,t)};try{const o=yield _e(r),n=o.data,[s,i]=ds(e,o,["x-wp-total","x-wp-totalpages"]);yield us(e,t,{data:n,totalResults:s,totalPages:i})}catch(r){yield ls(e,t,r)}}const Ss={itemErrors:{},items:{},statErrors:{},stats:{}},Ts=(0,de.createReduxStore)(es,{reducer:(e=Ss,t)=>{switch(t.type){case is.SET_REPORT_ITEMS:return{...e,items:{...e.items,[t.resourceName]:t.items}};case is.SET_REPORT_STATS:return{...e,stats:{...e.stats,[t.resourceName]:t.stats}};case is.SET_ITEM_ERROR:return{...e,itemErrors:{...e.itemErrors,[t.resourceName]:t.error}};case is.SET_STAT_ERROR:return{...e,statErrors:{...e.statErrors,[t.resourceName]:t.error}};default:return e}},actions:m,controls:ge,selectors:R,resolvers:O});(0,de.register)(Ts);const ys=es,_s="wc/admin/countries",gs=e=>e.locales,fs=(e,t)=>{const r=t.split(":")[0];return e.locales[r]},Rs=e=>e.countries,ms=(e,t)=>e.countries.find((e=>e.code===t)),Os=e=>e.geolocation;var hs,Is;(Is=hs||(hs={})).GET_LOCALES_ERROR="GET_LOCALES_ERROR",Is.GET_LOCALES_SUCCESS="GET_LOCALES_SUCCESS",Is.GET_COUNTRIES_ERROR="GET_COUNTRIES_ERROR",Is.GET_COUNTRIES_SUCCESS="GET_COUNTRIES_SUCCESS",Is.GEOLOCATION_SUCCESS="GEOLOCATION_SUCCESS",Is.GEOLOCATION_ERROR="GEOLOCATION_ERROR";const As=hs;function Ps(e){return{type:As.GET_LOCALES_SUCCESS,locales:e}}function Cs(e){return{type:As.GET_LOCALES_ERROR,error:e}}function Us(e){return{type:As.GET_COUNTRIES_SUCCESS,countries:e}}function vs(e){return{type:As.GET_COUNTRIES_ERROR,error:e}}function Ns(e){return{type:As.GEOLOCATION_SUCCESS,geolocation:e}}function ws(e){return{type:As.GEOLOCATION_ERROR,error:e}}const Ds=de.controls&&de.controls.resolveSelect?de.controls.resolveSelect:Ee.select;function*bs(){yield Ds(_s,"getLocales")}function*ks(){try{const e=Me+"/data/countries/locales";return Ps(yield(0,Ee.apiFetch)({path:e,method:"GET"}))}catch(e){return Cs(e)}}function*Gs(){yield Ds(_s,"getCountries")}function*Ls(){try{const e=Me+"/data/countries";return Us(yield(0,Ee.apiFetch)({path:e,method:"GET"}))}catch(e){return vs(e)}}const Ms=()=>async({dispatch:e})=>{try{const t=`https://public-api.wordpress.com/geo/?v=${(new Date).getTime()}`,r=await fetch(t,{method:"GET"}),o=await r.json();e.geolocationSuccess(o)}catch(t){e.geolocationError(t)}},Fs=(0,de.createReduxStore)(_s,{reducer:(e={errors:{},locales:{},countries:[],geolocation:void 0},t)=>{switch(t.type){case As.GET_LOCALES_SUCCESS:e={...e,locales:t.locales};break;case As.GET_LOCALES_ERROR:e={...e,errors:{...e.errors,locales:t.error}};break;case As.GET_COUNTRIES_SUCCESS:e={...e,countries:t.countries};break;case As.GET_COUNTRIES_ERROR:e={...e,errors:{...e.errors,countries:t.error}};break;case As.GEOLOCATION_SUCCESS:e={...e,geolocation:t.geolocation};break;case As.GEOLOCATION_ERROR:e={...e,errors:{...e.errors,geolocation:t.error}}}return e},actions:I,controls:Ee.controls,selectors:h,resolvers:A});(0,de.register)(Fs);const qs=_s,xs="woocommerce-navigation",js=e=>(0,Kr.applyFilters)("woocommerce_navigation_menu_items",e.menuItems),Qs=e=>e.favorites||[],Vs=(e,t)=>e.requesting[t]||!1,$s=e=>e.persistedQuery||{},Ks=window.wc.navigation,Ys={ADD_MENU_ITEMS:"ADD_MENU_ITEMS",SET_MENU_ITEMS:"SET_MENU_ITEMS",ON_HISTORY_CHANGE:"ON_HISTORY_CHANGE",ADD_FAVORITE_FAILURE:"ADD_FAVORITE_FAILURE",ADD_FAVORITE_REQUEST:"ADD_FAVORITE_REQUEST",ADD_FAVORITE_SUCCESS:"ADD_FAVORITE_SUCCESS",GET_FAVORITES_FAILURE:"GET_FAVORITES_FAILURE",GET_FAVORITES_REQUEST:"GET_FAVORITES_REQUEST",GET_FAVORITES_SUCCESS:"GET_FAVORITES_SUCCESS",REMOVE_FAVORITE_FAILURE:"REMOVE_FAVORITE_FAILURE",REMOVE_FAVORITE_REQUEST:"REMOVE_FAVORITE_REQUEST",REMOVE_FAVORITE_SUCCESS:"REMOVE_FAVORITE_SUCCESS"};function Hs(e){return{type:Ys.SET_MENU_ITEMS,menuItems:e}}function Ws(e){return{type:Ys.ADD_MENU_ITEMS,menuItems:e}}function Bs(e){return{type:Ys.GET_FAVORITES_FAILURE,error:e}}function Js(e){return{type:Ys.GET_FAVORITES_REQUEST,favorites:e}}function zs(e){return{type:Ys.GET_FAVORITES_SUCCESS,favorites:e}}function Zs(e){return{type:Ys.ADD_FAVORITE_REQUEST,favorite:e}}function Xs(e,t){return{type:Ys.ADD_FAVORITE_FAILURE,favorite:e,error:t}}function ei(e){return{type:Ys.ADD_FAVORITE_SUCCESS,favorite:e}}function ti(e){return{type:Ys.REMOVE_FAVORITE_REQUEST,favorite:e}}function ri(e,t){return{type:Ys.REMOVE_FAVORITE_FAILURE,favorite:e,error:t}}function oi(e){return{type:Ys.REMOVE_FAVORITE_SUCCESS,favorite:e}}function*ni(){const e=(0,Ks.getPersistedQuery)();if(!Object.keys(e).length)return null;yield{type:Ys.ON_HISTORY_CHANGE,persistedQuery:e}}function*si(){yield ni()}function*ii(e){yield Zs(e);try{const t=yield ye()({path:`${Fe}/navigation/favorites/me`,method:"POST",data:{item_id:e}});if(t)return yield ei(e),t;throw new Error}catch(t){throw yield Xs(e,t),new Error}}function*ai(e){yield ti(e);try{const t=yield ye()({path:`${Fe}/navigation/favorites/me`,method:"DELETE",data:{item_id:e}});if(t)return yield oi(e),t;throw new Error}catch(t){throw yield ri(e,t),new Error}}function*ci(){yield Js();try{const e=yield(0,Ee.apiFetch)({path:`${Fe}/navigation/favorites/me`});if(e)return void(yield zs(e));throw new Error}catch(e){throw yield Bs(e),new Error}}function ui(e){const t={};for(const r in e){const o=e[r];t[r]="function"==typeof o?function(...e){return"onLoad"!==r&&Wr()("Navigation store",{}),o.apply(this,e)}:o}return t}const li=(0,de.createReduxStore)(xs,{reducer:(e={error:null,menuItems:[],favorites:[],requesting:{},persistedQuery:{}},t)=>{switch(t.type){case Ys.SET_MENU_ITEMS:e={...e,menuItems:t.menuItems};break;case Ys.ADD_MENU_ITEMS:e={...e,menuItems:[...e.menuItems,...t.menuItems]};break;case Ys.ON_HISTORY_CHANGE:e={...e,persistedQuery:t.persistedQuery};break;case Ys.GET_FAVORITES_FAILURE:e={...e,requesting:{...e.requesting,getFavorites:!1}};break;case Ys.GET_FAVORITES_REQUEST:e={...e,requesting:{...e.requesting,getFavorites:!0}};break;case Ys.GET_FAVORITES_SUCCESS:e={...e,favorites:t.favorites,requesting:{...e.requesting,getFavorites:!1}};break;case Ys.ADD_FAVORITE_FAILURE:e={...e,error:t.error,requesting:{...e.requesting,addFavorite:!1}};break;case Ys.ADD_FAVORITE_REQUEST:e={...e,requesting:{...e.requesting,addFavorite:!0}};break;case Ys.ADD_FAVORITE_SUCCESS:const r=e.favorites.includes(t.favorite)?e.favorites:[...e.favorites,t.favorite];e={...e,favorites:r,menuItems:e.menuItems.map((e=>e.id===t.favorite?{...e,menuId:"favorites"}:e)),requesting:{...e.requesting,addFavorite:!1}};break;case Ys.REMOVE_FAVORITE_FAILURE:e={...e,requesting:{...e.requesting,error:t.error,removeFavorite:!1}};break;case Ys.REMOVE_FAVORITE_REQUEST:e={...e,requesting:{...e.requesting,removeFavorite:!0}};break;case Ys.REMOVE_FAVORITE_SUCCESS:const o=e.favorites.filter((e=>e!==t.favorite));e={...e,favorites:o,menuItems:e.menuItems.map((e=>e.id===t.favorite?{...e,menuId:"plugins"}:e)),requesting:{...e.requesting,removeFavorite:!1}}}return e},actions:ui(C),controls:Ee.controls,selectors:ui(P),resolvers:U});(0,de.register)(li),(async()=>{const{onLoad:e,onHistoryChange:t}=(0,de.dispatch)(xs);await e(),(0,Ks.addHistoryListener)((async()=>{setTimeout((async()=>{await t()}),0)}))})();const di=xs,Ei="wc/admin/items";function pi(e,t,r,o={}){const{getItems:n,getItemsError:s,isResolving:i}=e,a={};let c=!1,u=!1;return r.forEach((e=>{const r={search:e,per_page:10,...o};n(t,r).forEach(((e,t)=>{a[t]=e})),i("getItems",[t,r])&&(c=!0),s(t,r)&&(u=!0)})),{items:a,isRequesting:c,isError:u}}function Si(e,t){const{_fields:r,page:o,per_page:n,...s}=t;return me("total-"+e,{...s})}const Ti=Ur(((e,t,r,o=new Map)=>{const n=me(t,r);let s;return e.items[n]&&"object"==typeof e.items[n]&&(s=e.items[n].data),s?s.reduce(((r,o)=>(r.set(o,e.data[t]?.[o]),r)),new Map):o}),((e,t,r)=>{const o=me(t,r);return[e.items[o]]})),yi=(e,t,r,o=0)=>{const n=Si(t,r);return e.items.hasOwnProperty(n)?e.items[n]:o},_i=(e,t,r)=>{const o=me(t,r);return e.errors[o]},gi={SET_ITEM:"SET_ITEM",SET_ITEMS:"SET_ITEMS",SET_ITEMS_TOTAL_COUNT:"SET_ITEMS_TOTAL_COUNT",SET_ERROR:"SET_ERROR"};function fi(e,t,r){return{type:gi.SET_ITEM,id:t,item:r,itemType:e}}function Ri(e,t,r,o){return{type:gi.SET_ITEMS,items:r,itemType:e,query:t,totalCount:o}}function mi(e,t,r){return{type:gi.SET_ITEMS_TOTAL_COUNT,itemType:e,query:t,totalCount:r}}function Oi(e,t,r){return{type:gi.SET_ERROR,itemType:e,query:t,error:r}}function*hi(e,t){const r={...e,stock_quantity:t},{id:o,parent_id:n,type:s}=r;yield fi("products",o,r);let i=Me;i+="variation"===s?`/products/${n}/variations/${o}`:`/products/${o}`;try{return yield(0,Ee.apiFetch)({path:i,method:"PUT",data:r}),!0}catch(t){return yield fi("products",o,e),yield Oi("products",{id:o},t),!1}}function*Ii(e,t){try{const r=(0,Se.addQueryArgs)(`${Fe}/onboarding/tasks/create_product_from_template`,t||{}),o=yield(0,Ee.apiFetch)({path:r,method:"POST",data:e});return yield fi("products",o.id,o),o}catch(e){throw yield Oi("createProductFromTemplate",t,e),e}}function*Ai(e,t){try{const r="categories"===e?"products/categories":e,{items:o,totalCount:n}=yield he(`${Me}/${r}`,t);yield mi(e,t,n),yield Ri(e,t,o)}catch(r){yield Oi(e,t,r)}}function*Pi(e,t){try{const r={...t,page:1,per_page:1},o="categories"===e?"products/categories":e,{totalCount:n}=yield he(`${Me}/${o}`,r);yield mi(e,t,n)}catch(r){yield Oi(e,t,r)}}function*Ci(e,t){yield Pi(e,t)}const Ui={items:{},errors:{},data:{}},vi=(0,de.createReduxStore)(Ei,{reducer:(e=Ui,t)=>{switch(t.type){case gi.SET_ITEM:const r=e.data[t.itemType]||{};return{...e,data:{...e.data,[t.itemType]:{...r,[t.id]:{...r[t.id]||{},...t.item}}}};case gi.SET_ITEMS:const o=[],n=t.items.reduce(((e,t)=>(o.push(t.id),e[t.id]=t,e)),{}),s=me(t.itemType,t.query);return{...e,items:{...e.items,[s]:{data:o}},data:{...e.data,[t.itemType]:{...e.data[t.itemType],...n}}};case gi.SET_ITEMS_TOTAL_COUNT:const i=Si(t.itemType,t.query);return{...e,items:{...e.items,[i]:t.totalCount}};case gi.SET_ERROR:return{...e,errors:{...e.errors,[me(t.itemType,t.query)]:t.error}};default:return e}},actions:N,controls:ge,selectors:v,resolvers:w});(0,de.register)(vi);const Ni=Ei;var wi;!function(e){e.GET_PAYMENT_GATEWAYS_REQUEST="GET_PAYMENT_GATEWAYS_REQUEST",e.GET_PAYMENT_GATEWAYS_SUCCESS="GET_PAYMENT_GATEWAYS_SUCCESS",e.GET_PAYMENT_GATEWAYS_ERROR="GET_PAYMENT_GATEWAYS_ERROR",e.UPDATE_PAYMENT_GATEWAY_REQUEST="UPDATE_PAYMENT_GATEWAY_REQUEST",e.UPDATE_PAYMENT_GATEWAY_SUCCESS="UPDATE_PAYMENT_GATEWAY_SUCCESS",e.UPDATE_PAYMENT_GATEWAY_ERROR="UPDATE_PAYMENT_GATEWAY_ERROR",e.GET_PAYMENT_GATEWAY_REQUEST="GET_PAYMENT_GATEWAY_REQUEST",e.GET_PAYMENT_GATEWAY_SUCCESS="GET_PAYMENT_GATEWAY_SUCCESS",e.GET_PAYMENT_GATEWAY_ERROR="GET_PAYMENT_GATEWAY_ERROR"}(wi||(wi={}));const Di="wc/payment-gateways",bi="wc/v3";function ki(){return{type:wi.GET_PAYMENT_GATEWAYS_REQUEST}}function Gi(e){return{type:wi.GET_PAYMENT_GATEWAYS_SUCCESS,paymentGateways:e}}function Li(e){return{type:wi.GET_PAYMENT_GATEWAYS_ERROR,error:e}}function Mi(){return{type:wi.GET_PAYMENT_GATEWAY_REQUEST}}function Fi(e){return{type:wi.GET_PAYMENT_GATEWAY_ERROR,error:e}}function qi(e){return{type:wi.GET_PAYMENT_GATEWAY_SUCCESS,paymentGateway:e}}function xi(e){return{type:wi.UPDATE_PAYMENT_GATEWAY_SUCCESS,paymentGateway:e}}function ji(){return{type:wi.UPDATE_PAYMENT_GATEWAY_REQUEST}}function Qi(e){return{type:wi.UPDATE_PAYMENT_GATEWAY_ERROR,error:e}}function*Vi(e,t){try{yield ji();const r=yield(0,Ee.apiFetch)({method:"PUT",path:bi+"/payment_gateways/"+e,body:JSON.stringify(t)});if(r&&r.id===e)return yield xi(r),r}catch(e){throw yield Qi(e),e}}const $i=de.controls&&de.controls.dispatch?de.controls.dispatch:Ee.dispatch;function*Ki(){yield ki();try{const e=yield(0,Ee.apiFetch)({path:bi+"/payment_gateways"});yield Gi(e);for(let t=0;t<e.length;t++)yield $i(Di,"finishResolution","getPaymentGateway",[e[t].id])}catch(e){yield Li(e)}}function*Yi(e){yield Mi();try{const t=yield(0,Ee.apiFetch)({path:bi+"/payment_gateways/"+e});if(t&&t.id)return yield qi(t),t}catch(e){yield Fi(e)}}function Hi(e,t){return e.paymentGateways.find((e=>e.id===t))}function Wi(e){return e.paymentGateways}function Bi(e,t){return e.errors[t]||null}function Ji(e){return e.isUpdating||!1}const zi=Di,Zi=(0,de.createReduxStore)(Di,{actions:D,selectors:k,resolvers:b,controls:Ee.controls,reducer:(e={paymentGateways:[],isUpdating:!1,errors:{}},t)=>{if(t&&"type"in t)switch(t.type){case wi.GET_PAYMENT_GATEWAYS_REQUEST:case wi.GET_PAYMENT_GATEWAY_REQUEST:return e;case wi.GET_PAYMENT_GATEWAYS_SUCCESS:return{...e,paymentGateways:t.paymentGateways};case wi.GET_PAYMENT_GATEWAYS_ERROR:return{...e,errors:{...e.errors,getPaymentGateways:t.error}};case wi.GET_PAYMENT_GATEWAY_ERROR:return{...e,errors:{...e.errors,getPaymentGateway:t.error}};case wi.UPDATE_PAYMENT_GATEWAY_REQUEST:return{...e,isUpdating:!0};case wi.UPDATE_PAYMENT_GATEWAY_SUCCESS:case wi.GET_PAYMENT_GATEWAY_SUCCESS:return function(e,t){const r=e.paymentGateways.findIndex((e=>e.id===t.id));return-1===r?{...e,paymentGateways:[...e.paymentGateways,t],isUpdating:!1}:{...e,paymentGateways:[...e.paymentGateways.slice(0,r),t,...e.paymentGateways.slice(r+1)],isUpdating:!1}}(e,t.paymentGateway);case wi.UPDATE_PAYMENT_GATEWAY_ERROR:return{...e,errors:{...e.errors,updatePaymentGateway:t.error},isUpdating:!1}}return e}});var Xi;function ea(){return{type:Xi.GET_PAYMENT_PROVIDERS_REQUEST}}function ta(e,t,r,o){return{type:Xi.GET_PAYMENT_PROVIDERS_SUCCESS,providers:e,offlinePaymentGateways:t,suggestions:r,suggestionCategories:o}}function ra(e){return{type:Xi.GET_PAYMENT_PROVIDERS_ERROR,error:e}}function*oa(e,t,r){try{return yield ye()({url:t,method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"woocommerce_toggle_gateway_enabled",security:r,gateway_id:e})})}catch(e){throw e}}function*na(e){try{return yield ye()({url:e,method:"POST"})}catch(e){throw e}}function*sa(e){try{return yield ye()({url:e,method:"POST"})}catch(e){throw e}}function ia(e){try{ye()({path:Fe+"/settings/payments/providers/order",method:"POST",data:{order_map:e}})}catch(e){throw e}return{type:Xi.UPDATE_PROVIDER_ORDERING}}function aa(e){return{type:Xi.SET_IS_ELIGIBLE,isEligible:e}}function*ca(e){yield ea();try{const t=yield(0,Ee.apiFetch)({path:Fe+"/settings/payments/providers?"+(e?`location=${e}`:"")});yield ta(t.providers,t.offline_payment_methods,t.suggestions,t.suggestion_categories)}catch(e){yield ra(e)}}function*ua(e){yield ca(e)}function*la(){return yield(0,Ee.apiFetch)({path:`${Fe}/settings/payments/woopayments/woopay-eligibility`})}function*da(){const e=yield la();yield aa(e.is_eligible)}function Ea(e,t){return e.providers}function pa(e){return e.offlinePaymentGateways}function Sa(e){return e.suggestions}function Ta(e){return e.suggestionCategories}function ya(e){return e.isFetching||!1}(0,de.register)(Zi),function(e){e.GET_PAYMENT_PROVIDERS_REQUEST="GET_PAYMENT_PROVIDERS_REQUEST",e.GET_PAYMENT_PROVIDERS_SUCCESS="GET_PAYMENT_PROVIDERS_SUCCESS",e.GET_PAYMENT_PROVIDERS_ERROR="GET_PAYMENT_PROVIDERS_ERROR",e.UPDATE_PROVIDER_ORDERING="UPDATE_PROVIDER_ORDERING",e.SET_IS_ELIGIBLE="SET_IS_ELIGIBLE"}(Xi||(Xi={}));const _a=e=>e.isWooPayEligible,ga="wc/admin/payment-settings",fa=ga,Ra=(0,de.createReduxStore)(ga,{actions:G,selectors:M,resolvers:L,controls:Ee.controls,reducer:(e={providers:[],offlinePaymentGateways:[],suggestions:[],suggestionCategories:[],isFetching:!1,isWooPayEligible:!1,errors:{}},t)=>{if(t&&"type"in t)switch(t.type){case Xi.GET_PAYMENT_PROVIDERS_REQUEST:return{...e,isFetching:!0};case Xi.GET_PAYMENT_PROVIDERS_SUCCESS:return{...e,isFetching:!1,providers:t.providers,offlinePaymentGateways:t.offlinePaymentGateways,suggestions:t.suggestions,suggestionCategories:t.suggestionCategories};case Xi.GET_PAYMENT_PROVIDERS_ERROR:return{...e,isFetching:!1,errors:{...e.errors,getPaymentGatewaySuggestions:t.error}};case Xi.UPDATE_PROVIDER_ORDERING:return{...e};case Xi.SET_IS_ELIGIBLE:return{...e,isWooPayEligible:t.isEligible}}return e}});var ma;function Oa(){return{type:ma.GET_SHIPPING_METHODS_REQUEST}}function ha(e){return{type:ma.GET_SHIPPING_METHODS_SUCCESS,shippingMethods:e}}function Ia(e){return{type:ma.GET_SHIPPING_METHODS_ERROR,error:e}}function*Aa(e=!1){let t=Fe+"/shipping-partner-suggestions";e&&(t+="?force_default_suggestions=true"),yield Oa();try{const e=yield(0,Ee.apiFetch)({path:t,method:"GET"});yield ha(e)}catch(e){yield Ia(e)}}(0,de.register)(Ra),function(e){e.GET_SHIPPING_METHODS_REQUEST="GET_SHIPPING_METHODS_REQUEST",e.GET_SHIPPING_METHODS_SUCCESS="GET_SHIPPING_METHODS_SUCCESS",e.GET_SHIPPING_METHODS_ERROR="GET_SHIPPING_METHODS_ERROR"}(ma||(ma={}));const Pa=e=>e.shippingMethods||[];function Ca(e){return e.isUpdating||!1}const Ua="wc/shipping-methods",va=(0,de.createReduxStore)(Ua,{reducer:(e={shippingMethods:[],isUpdating:!1,errors:{}},t)=>{if(t&&"type"in t)switch(t.type){case ma.GET_SHIPPING_METHODS_REQUEST:return{...e,isUpdating:!0};case ma.GET_SHIPPING_METHODS_SUCCESS:return{...e,shippingMethods:t.shippingMethods,isUpdating:!1};case ma.GET_SHIPPING_METHODS_ERROR:return{...e,isUpdating:!1,errors:{...e.errors,getShippingMethods:t.error}}}return e},selectors:x,resolvers:q,controls:Ee.controls,actions:F});(0,de.register)(va);const Na="wc/admin/products",wa="/wc/v3/products",Da=/%(?:postname|pagename)%/,ba=`${wa}/suggested-products`;function ka(e){return me("product",e)}function Ga(e){const{_fields:t,page:r,per_page:o,...n}=e;return ka(n)}function La(e={}){if(!Object.keys(e).length)return"default";const t={...e};return e.categories?.sort(),e.tags?.sort(),e.attributes?.sort(),JSON.stringify(t)}const Ma=(e,t,r=void 0)=>e.data[t]||r,Fa=Ur(((e,t,r=void 0)=>{const o=ka(t),n=e.products[o]?e.products[o].data:void 0;if(!n)return r;if(t&&void 0!==t._fields){const r=t._fields;return n.map((t=>r.reduce(((r,o)=>({...r,[o]:e.data[t][o]})),{})))}return n.map((t=>e.data[t]))}),((e,t)=>{const r=ka(t),o=e.products[r]?e.products[r].data:void 0;return[e.products[r],...(o||[]).map((t=>e.data[t]))]})),qa=(e,t,r=void 0)=>{const o=Ga(t);return e.productsCount.hasOwnProperty(o)?e.productsCount[o]:r},xa=(e,t)=>{const r=ka(t);return e.errors[r]},ja=(e,t)=>{const r=ka(t);return e.errors[r]},Qa=(e,t,r)=>{const o=ka(r);return e.errors[`update/${t}/${o}`]},Va=(e,t)=>e.errors[`delete/${t}`],$a=(e,t,r)=>void 0!==r&&"createProduct"!==t?e.pending[t]?.[r]||!1:"createProduct"===t&&e.pending[t]||!1,Ka=Ur(((e,t)=>{const r=e.data[t];if(r&&r.permalink_template){const e=r.slug||r.generated_slug,[t,o]=r.permalink_template.split(Da);return{prefix:t,postName:e,suffix:o}}return null}),((e,t)=>[e.data[t]])),Ya=Ur(((e,t)=>{const r=e.data[t];return r?.related_ids&&Fa(e,{include:r.related_ids})||[]}),((e,t)=>[e.data[t]]));function Ha(e,t){const r=La(t);return e.suggestedProducts[r]?e.suggestedProducts[r].items:[]}var Wa;!function(e){e.CREATE_PRODUCT_START="CREATE_PRODUCT_START",e.CREATE_PRODUCT_ERROR="CREATE_PRODUCT_ERROR",e.CREATE_PRODUCT_SUCCESS="CREATE_PRODUCT_SUCCESS",e.GET_PRODUCT_SUCCESS="GET_PRODUCT_SUCCESS",e.GET_PRODUCT_ERROR="GET_PRODUCT_ERROR",e.GET_PRODUCTS_SUCCESS="GET_PRODUCTS_SUCCESS",e.GET_PRODUCTS_ERROR="GET_PRODUCTS_ERROR",e.GET_PRODUCTS_TOTAL_COUNT_SUCCESS="GET_PRODUCTS_TOTAL_COUNT_SUCCESS",e.GET_PRODUCTS_TOTAL_COUNT_ERROR="GET_PRODUCTS_TOTAL_COUNT_ERROR",e.UPDATE_PRODUCT_START="UPDATE_PRODUCT_START",e.UPDATE_PRODUCT_ERROR="UPDATE_PRODUCT_ERROR",e.UPDATE_PRODUCT_SUCCESS="UPDATE_PRODUCT_SUCCESS",e.DELETE_PRODUCT_START="DELETE_PRODUCT_START",e.DELETE_PRODUCT_ERROR="DELETE_PRODUCT_ERROR",e.DELETE_PRODUCT_SUCCESS="DELETE_PRODUCT_SUCCESS",e.DUPLICATE_PRODUCT_START="DUPLICATE_PRODUCT_START",e.DUPLICATE_PRODUCT_ERROR="DUPLICATE_PRODUCT_ERROR",e.DUPLICATE_PRODUCT_SUCCESS="DUPLICATE_PRODUCT_SUCCESS",e.SET_SUGGESTED_PRODUCTS="SET_SUGGESTED_PRODUCTS"}(Wa||(Wa={}));const Ba=Wa;function Ja(e,t){return{type:Ba.GET_PRODUCT_SUCCESS,id:e,product:t}}function za(e,t){return{type:Ba.GET_PRODUCT_ERROR,productId:e,error:t}}function Za(e,t){return{type:Ba.CREATE_PRODUCT_ERROR,query:e,error:t}}function Xa(e,t){return{type:Ba.DUPLICATE_PRODUCT_ERROR,id:e,error:t}}function ec(e,t){return{type:Ba.UPDATE_PRODUCT_ERROR,id:e,error:t}}function tc(e,t,r){return{type:Ba.GET_PRODUCTS_SUCCESS,products:t,query:e,totalCount:r}}function rc(e,t){return{type:Ba.GET_PRODUCTS_ERROR,query:e,error:t}}function oc(e,t){return{type:Ba.GET_PRODUCTS_TOTAL_COUNT_SUCCESS,query:e,totalCount:t}}function nc(e,t){return{type:Ba.GET_PRODUCTS_TOTAL_COUNT_ERROR,query:e,error:t}}function*sc(e){yield{type:Ba.CREATE_PRODUCT_START};try{const t=yield(0,Ee.apiFetch)({path:wa,method:"POST",data:e});return yield function(e,t){return{type:Ba.CREATE_PRODUCT_SUCCESS,id:e,product:t}}(t.id,t),t}catch(t){throw yield Za(e,t),t}}function*ic(e,t){yield function(e){return{type:Ba.UPDATE_PRODUCT_START,id:e}}(e);try{const r=yield(0,Ee.apiFetch)({path:`${wa}/${e}`,method:"PUT",data:t});return yield function(e,t){return{type:Ba.UPDATE_PRODUCT_SUCCESS,id:e,product:t}}(r.id,r),r}catch(t){throw yield ec(e,t),t}}function*ac(e,t){yield function(e){return{type:Ba.DUPLICATE_PRODUCT_START,id:e}}(e);try{const r=yield(0,Ee.apiFetch)({path:`${wa}/${e}/duplicate`,method:"POST",data:t});return yield function(e,t){return{type:Ba.DUPLICATE_PRODUCT_SUCCESS,id:e,product:t}}(r.id,r),r}catch(t){throw yield Xa(e,t),t}}function cc(e){return{type:Ba.DELETE_PRODUCT_START,id:e}}function uc(e,t,r){return{type:Ba.DELETE_PRODUCT_SUCCESS,id:e,product:t,force:r}}function lc(e,t){return{type:Ba.DELETE_PRODUCT_ERROR,id:e,error:t}}function*dc(e,t=!1){yield cc(e);try{const r=t?`${wa}/${e}?force=true`:`${wa}/${e}`,o=yield(0,Ee.apiFetch)({path:r,method:"DELETE"});return yield uc(o.id,o,t),o}catch(t){throw yield lc(e,t),t}}function Ec(e,t){return{type:Ba.SET_SUGGESTED_PRODUCTS,key:e,items:t}}const pc=de.controls&&de.controls.dispatch?de.controls.dispatch:Ee.dispatch,Sc=de.controls&&de.controls.resolveSelect?de.controls.resolveSelect:Ee.select;function*Tc(e){const t={...e};t&&t._fields&&!t._fields.includes("id")&&(t._fields=["id",...t._fields]);try{const{items:r,totalCount:o}=yield he(wa,t);return yield oc(e,o),yield tc(e,r,o),r}catch(t){throw yield rc(e,t),t}}function*yc(e){try{const t=yield(0,Ee.apiFetch)({path:(0,Se.addQueryArgs)(`${wa}/${e}`,{context:"edit"}),method:"GET"});return yield Ja(e,t),yield pc(Na,"finishResolution","getPermalinkParts",[e]),t}catch(t){throw yield za(e,t),t}}function*_c(e){try{const t=(yield Sc(Na,"getProduct",e)).related_ids;return t?.length?yield Sc(Na,"getProducts",{include:t}):[]}catch(e){throw e}}function*gc(e){try{const t={...e,page:1,per_page:1},{totalCount:r}=yield he(wa,t);return yield oc(e,r),r}catch(t){throw yield nc(e,t),t}}function*fc(e){yield Sc(Na,"getProduct",[e])}const Rc=e=>async({dispatch:t})=>{const r=La(e),o=await ye()({path:(0,Se.addQueryArgs)(ba,e)});t.setSuggestedProductAction(r,o)},mc=(0,de.createReduxStore)(Na,{reducer:(e={products:{},productsCount:{},errors:{},data:{},pending:{},suggestedProducts:{}},t)=>{if(t&&"type"in t)switch(t.type){case Ba.CREATE_PRODUCT_START:return{...e,pending:{createProduct:!0}};case Ba.UPDATE_PRODUCT_START:return{...e,pending:{updateProduct:{...e.pending.updateProduct||{},[t.id]:!0}}};case Ba.DUPLICATE_PRODUCT_START:return{...e,pending:{duplicateProduct:{...e.pending.duplicateProduct||{},[t.id]:!0}}};case Ba.CREATE_PRODUCT_SUCCESS:case Ba.GET_PRODUCT_SUCCESS:case Ba.UPDATE_PRODUCT_SUCCESS:case Ba.DUPLICATE_PRODUCT_SUCCESS:const r=e.data||{};return{...e,data:{...r,[t.id]:{...r[t.id]||{},...t.product}},pending:{createProduct:!1,duplicateProduct:{...e.pending.duplicateProduct||{},[t.id]:!1},updateProduct:{...e.pending.updateProduct||{},[t.id]:!1}}};case Ba.GET_PRODUCTS_SUCCESS:const o=[],n=t.products.reduce(((t,r)=>(o.push(r.id),t[r.id]={...e.data[r.id]||{},...r},t)),{}),s=ka(t.query);return{...e,products:{...e.products,[s]:{data:o}},data:{...e.data,...n}};case Ba.GET_PRODUCTS_TOTAL_COUNT_SUCCESS:const i=Ga(t.query);return{...e,productsCount:{...e.productsCount,[i]:t.totalCount}};case Ba.GET_PRODUCT_ERROR:return{...e,errors:{...e.errors,[t.productId]:t.error}};case Ba.GET_PRODUCTS_ERROR:case Ba.GET_PRODUCTS_TOTAL_COUNT_ERROR:case Ba.CREATE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[ka(t.query)]:t.error},pending:{createProduct:!1}};case Ba.UPDATE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[`update/${t.id}`]:t.error}};case Ba.DUPLICATE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[`duplicate/${t.id}`]:t.error}};case Ba.DELETE_PRODUCT_START:return{...e,pending:{deleteProduct:{...e.pending.deleteProduct||{},[t.id]:!0}}};case Ba.DELETE_PRODUCT_ERROR:return{...e,errors:{...e.errors,[`delete/${t.id}`]:t.error},pending:{deleteProduct:{...e.pending.deleteProduct||{},[t.id]:!1}}};case Ba.DELETE_PRODUCT_SUCCESS:const a=e.data||{};return{...e,data:{...a,[t.id]:{...a[t.id]||{},...t.product,status:t.force?"deleted":"trash"}},pending:{deleteProduct:{...e.pending.deleteProduct||{},[t.id]:!1}}};case Ba.SET_SUGGESTED_PRODUCTS:return{...e,suggestedProducts:{...e.suggestedProducts,[t.key]:{items:t.items||[]}}};default:return e}return e},actions:Q,controls:ge,selectors:j,resolvers:V});(0,de.register)(mc);const Oc=Na,hc="wc/admin/orders",Ic="/wc/v3/orders";function Ac(e){return me("order",e)}function Pc(e){const{_fields:t,page:r,per_page:o,...n}=e;return Ac(n)}const Cc=Ur(((e,t,r=void 0)=>{const o=Ac(t),n=e.orders[o]?e.orders[o].data:void 0;if(!n)return r;if(t&&void 0!==t._fields){const r=t._fields;return n.map((t=>r.reduce(((r,o)=>({...r,[o]:e.data[t][o]})),{})))}return n.map((t=>e.data[t]))}),((e,t)=>{const r=Ac(t),o=e.orders[r]?e.orders[r].data:[];return[e.orders[r],...o.map((t=>e.data[t]))]})),Uc=(e,t,r=void 0)=>{const o=Pc(t);return e.ordersCount.hasOwnProperty(o)?e.ordersCount[o]:r},vc=(e,t)=>{const r=Ac(t);return e.errors[r]};var Nc;!function(e){e.GET_ORDER_SUCCESS="GET_ORDER_SUCCESS",e.GET_ORDER_ERROR="GET_ORDER_ERROR",e.GET_ORDERS_SUCCESS="GET_ORDERS_SUCCESS",e.GET_ORDERS_ERROR="GET_ORDERS_ERROR",e.GET_ORDERS_TOTAL_COUNT_SUCCESS="GET_ORDERS_TOTAL_COUNT_SUCCESS",e.GET_ORDERS_TOTAL_COUNT_ERROR="GET_ORDERS_TOTAL_COUNT_ERROR"}(Nc||(Nc={}));const wc=Nc;function Dc(e,t){return{type:wc.GET_ORDER_SUCCESS,id:e,order:t}}function bc(e,t){return{type:wc.GET_ORDER_ERROR,query:e,error:t}}function kc(e,t,r){return{type:wc.GET_ORDERS_SUCCESS,orders:t,query:e,totalCount:r}}function Gc(e,t){return{type:wc.GET_ORDERS_ERROR,query:e,error:t}}function Lc(e,t){return{type:wc.GET_ORDERS_TOTAL_COUNT_SUCCESS,query:e,totalCount:t}}function Mc(e,t){return{type:wc.GET_ORDERS_TOTAL_COUNT_ERROR,query:e,error:t}}function*Fc(e){const t={...e};t&&t._fields&&!t._fields.includes("id")&&(t._fields=["id",...t._fields]);try{const{items:r,totalCount:o}=yield he(Ic,t);return yield Lc(e,o),yield kc(e,r,o),r}catch(t){return yield Gc(e,t),t}}function*qc(e){try{const t={...e,page:1,per_page:1},{totalCount:r}=yield he(Ic,t);return yield Lc(e,r),r}catch(t){return yield Mc(e,t),t}}const xc=(0,de.createReduxStore)(hc,{reducer:(e={orders:{},ordersCount:{},errors:{},data:{}},t)=>{if(t&&"type"in t)switch(t.type){case wc.GET_ORDER_SUCCESS:const r=e.data||{};return{...e,data:{...r,[t.id]:{...r[t.id]||{},...t.order}}};case wc.GET_ORDERS_SUCCESS:const o=[],n=t.orders.reduce(((t,r)=>(o.push(r.id),t[r.id]={...e.data[r.id]||{},...r},t)),{}),s=Ac(t.query);return{...e,orders:{...e.orders,[s]:{data:o}},data:{...e.data,...n}};case wc.GET_ORDERS_TOTAL_COUNT_SUCCESS:const i=Pc(t.query);return{...e,ordersCount:{...e.ordersCount,[i]:t.totalCount}};case wc.GET_ORDER_ERROR:case wc.GET_ORDERS_ERROR:case wc.GET_ORDERS_TOTAL_COUNT_ERROR:return{...e,errors:{...e.errors,[Ac(t.query)]:t.error}};default:return e}return e},actions:K,controls:ge,selectors:$,resolvers:Y});(0,de.register)(xc);const jc=hc,Qc="wc/admin/products/attributes";var Vc;!function(e){e.CREATE_ITEM="CREATE_ITEM",e.DELETE_ITEM="DELETE_ITEM",e.GET_ITEM="GET_ITEM",e.GET_ITEMS="GET_ITEMS",e.GET_ITEMS_TOTAL_COUNT="GET_ITEMS_TOTAL_COUNT",e.UPDATE_ITEM="UPDATE_ITEM"}(Vc||(Vc={}));const $c=Vc,Kc=(e,t,r)=>{let o=e;if(o.match(/{(.*?)}/g)?.forEach(((e,t)=>{o=o.replace(e,r[t].toString())})),new RegExp(/{|}/).test(o.toString()))throw new Error("Not all URL parameters were replaced");return(0,Se.addQueryArgs)(o,t)},Yc=(e,t=[])=>{const r="string"==typeof e||"number"==typeof e?e:e.id;return t.length?t.join("/")+"/"+r:r},Hc=(e,t=[],r={})=>{const o=[],n={},s=t.length>0;return e.forEach((e=>{const i=s?Yc(e.id,t):e.id;o.push(i),n[i]={...r[i]||{},...e}})),{objItems:n,ids:o}},Wc=(e,t=[])=>"string"==typeof e||"number"==typeof e?{id:e,key:e}:{id:e.id,key:Yc(e,t)},Bc=(e,t,r=[])=>(...o)=>(r.forEach(((e,t)=>{void 0===o[t+1]&&(o[t+1]=e)})),e(...o,t)),Jc=e=>{const t=[];return e.match(/{(.*?)}/g)?.forEach((e=>{const r=e.substr(1,e.length-2);t.push(r)})),t},zc=(e,t)=>{if("object"!=typeof t)return[];const r=[];return Jc(e).forEach((e=>{t.hasOwnProperty(e)&&r.push(t[e])})),r},Zc=(e,t)=>{const[r,...o]=e;if(!r||!((e,t)=>{if("string"==typeof e||"number"==typeof e)return!0;const r=["id",...Jc(t)];return!(!e||"object"!=typeof e||!e.hasOwnProperty("id")||JSON.stringify(r.sort())!==JSON.stringify(Object.keys(e).sort()))})(r,t))return e;const n=zc(t,r),{key:s}=Wc(r,n);return[s,...o]},Xc=(e,t)=>{const r={...e};return Jc(t).forEach((e=>{delete r[e]})),r},eu=me,tu=(e,t)=>{switch(e){case`create${t}`:return $c.CREATE_ITEM;case`delete${t}`:return $c.DELETE_ITEM;case`update${t}`:return $c.UPDATE_ITEM}return e},ru=(e,t)=>{const r=eu($c.CREATE_ITEM,t);return e.errors[r]},ou=(e,t,r)=>{const o=zc(r,t),{key:n}=Wc(t,o),s=eu($c.DELETE_ITEM,n);return e.errors[s]},nu=(e,t,r)=>{const o=zc(r,t),{key:n}=Wc(t,o);return e.data[n]},su=(e,t,r)=>{const o=zc(r,t),{key:n}=Wc(t,o),s=eu($c.GET_ITEM,n);return e.errors[s]},iu=Ur(((e,t)=>{const r=eu($c.GET_ITEMS,t||{}),o=e.items[r]?e.items[r].data:void 0;if(!o)return null;if(t&&void 0!==t._fields){const r=t._fields;return o.map((t=>r.reduce(((r,o)=>({...r,[o]:e.data[t][o]})),{})))}return o.map((t=>e.data[t])).filter((e=>void 0!==e))}),((e,t)=>{const r=eu($c.GET_ITEMS,t||{}),o=e.items[r]?e.items[r].data:void 0;return[e.items[r],...(o||[]).map((t=>e.data[t]))]})),au=(e,t,r=void 0)=>{const o=Oe($c.GET_ITEMS,t||{});return e.itemsCount.hasOwnProperty(o)?e.itemsCount[o]:r},cu=(e,t)=>{const r=eu($c.GET_ITEMS,t||{});return e.errors[r]},uu=(e,t,r)=>{const{key:o}=Wc(t,r),n=eu($c.UPDATE_ITEM,o);return e.errors[n]},lu={};var du;!function(e){e.CREATE_ITEM_ERROR="CREATE_ITEM_ERROR",e.CREATE_ITEM_REQUEST="CREATE_ITEM_REQUEST",e.CREATE_ITEM_SUCCESS="CREATE_ITEM_SUCCESS",e.DELETE_ITEM_ERROR="DELETE_ITEM_ERROR",e.DELETE_ITEM_REQUEST="DELETE_ITEM_REQUEST",e.DELETE_ITEM_SUCCESS="DELETE_ITEM_SUCCESS",e.GET_ITEM_ERROR="GET_ITEM_ERROR",e.GET_ITEM_SUCCESS="GET_ITEM_SUCCESS",e.GET_ITEMS_ERROR="GET_ITEMS_ERROR",e.GET_ITEMS_SUCCESS="GET_ITEMS_SUCCESS",e.UPDATE_ITEM_ERROR="UPDATE_ITEM_ERROR",e.UPDATE_ITEM_REQUEST="UPDATE_ITEM_REQUEST",e.UPDATE_ITEM_SUCCESS="UPDATE_ITEM_SUCCESS",e.GET_ITEMS_TOTAL_COUNT_SUCCESS="GET_ITEMS_TOTAL_COUNT_SUCCESS",e.GET_ITEMS_TOTAL_COUNT_ERROR="GET_ITEMS_TOTAL_COUNT_ERROR"}(du||(du={}));const Eu=du;function pu(e,t){return{type:Eu.GET_ITEMS_ERROR,query:e,error:t,errorType:$c.GET_ITEMS}}function Su(e,t,r){return{type:Eu.GET_ITEMS_SUCCESS,items:t,query:e,urlParameters:r}}function Tu(e,t){return{type:Eu.GET_ITEMS_TOTAL_COUNT_SUCCESS,query:e,totalCount:t}}function yu(e,t){return{type:Eu.GET_ITEMS_TOTAL_COUNT_ERROR,query:e,error:t,errorType:$c.GET_ITEMS_TOTAL_COUNT}}const _u=e=>(t={items:{},data:{},itemsCount:{},errors:{},requesting:{}},r)=>{const o=t.data||{};if(r&&"type"in r)switch(r.type){case du.CREATE_ITEM_ERROR:const e=eu(r.errorType,r.query||{});return{...t,errors:{...t.errors,[e]:r.error},requesting:{...t.requesting,[e]:!1}};case du.GET_ITEMS_TOTAL_COUNT_ERROR:case du.GET_ITEMS_ERROR:return{...t,errors:{...t.errors,[eu(r.errorType,r.query||{})]:r.error}};case du.GET_ITEMS_TOTAL_COUNT_SUCCESS:return{...t,itemsCount:{...t.itemsCount,[Oe($c.GET_ITEMS,r.query||{})]:r.totalCount}};case du.CREATE_ITEM_SUCCESS:{const{options:e={}}=r,{objItems:n,ids:s}=Hc([r.item],e.optimisticUrlParameters,o),i={...o,...n},a=eu($c.CREATE_ITEM,s[0],r.query),c=eu($c.GET_ITEMS,e.optimisticQueryUpdate),u=Oe($c.GET_ITEMS,e?.optimisticQueryUpdate||{});let l=t.items,d=[...l[c]?.data||[],...s],E=t.itemsCount;if(e?.optimisticQueryUpdate){if(e.optimisticQueryUpdate?.order_by){const t=e.optimisticQueryUpdate?.order_by;let r=Object.values(function(e,t){return t.reduce(((t,r)=>(e[r]&&(t[r]=e[r]),t)),{})}(i,d));r=r.sort(((e,r)=>String(e[t]).toLowerCase().localeCompare(String(r[t]).toLowerCase())));const{ids:o}=Hc(r,e.optimisticUrlParameters);d=o}l={...l,[c]:{data:d}},E={...t.itemsCount,[u]:d.length}}return{...t,items:l,itemsCount:E,data:i,requesting:{...t.requesting,[a]:!1}}}case du.GET_ITEM_SUCCESS:return{...t,data:{...o,[r.key]:{...o[r.key]||{},...r.item}}};case du.UPDATE_ITEM_SUCCESS:const n=eu($c.UPDATE_ITEM,r.key,r.query);return{...t,data:{...o,[r.key]:{...o[r.key]||{},...r.item}},requesting:{...t.requesting,[n]:!1}};case du.DELETE_ITEM_SUCCESS:const s=eu($c.DELETE_ITEM,r.key,r.force),i=Object.keys(t.data).reduce(((e,o)=>o!==r.key.toString()?(e[o]=t.data[o],e):(r.force||(e[o]=r.item),e)),{});return{...t,data:i,requesting:{...t.requesting,[s]:!1}};case du.DELETE_ITEM_ERROR:const a=eu(r.errorType,r.key,r.force);return{...t,errors:{...t.errors,[a]:r.error},requesting:{...t.requesting,[a]:!1}};case du.GET_ITEM_ERROR:return{...t,errors:{...t.errors,[eu(r.errorType,r.key)]:r.error}};case du.UPDATE_ITEM_ERROR:const c=eu(r.errorType,r.key,r.query);return{...t,errors:{...t.errors,[c]:r.error},requesting:{...t.requesting,[c]:!1}};case du.GET_ITEMS_SUCCESS:const{objItems:u,ids:l}=Hc(r.items,r.urlParameters,o),d=eu($c.GET_ITEMS,r.query||{});return{...t,items:{...t.items,[d]:{data:l}},data:{...t.data,...u}};case du.CREATE_ITEM_REQUEST:return{...t,requesting:{...t.requesting,[eu($c.CREATE_ITEM,r.query)]:!0}};case du.DELETE_ITEM_REQUEST:return{...t,requesting:{...t.requesting,[eu($c.DELETE_ITEM,r.key,r.force)]:!0}};case du.UPDATE_ITEM_REQUEST:return{...t,requesting:{...t.requesting,[eu($c.UPDATE_ITEM,r.key,r.query)]:!0}}}return e?e(t,r):t},gu=({storeName:e,resourceName:t,namespace:r,pluralResourceName:o,storeConfig:n})=>{const s=(({namespace:e,resourceName:t})=>({[`create${t}`]:function*(t,r){yield function(e){return{type:Eu.CREATE_ITEM_REQUEST,query:e}}(t);const o=zc(e,t);try{const n=yield(0,Ee.apiFetch)({path:Kc(e,Xc(t,e),o),method:"POST"}),{key:s}=Wc(n.id,o);return yield function(e,t,r,o){return{type:Eu.CREATE_ITEM_SUCCESS,key:e,item:t,query:r,options:o}}(s,n,t,r),n}catch(e){throw yield function(e,t){return{type:Eu.CREATE_ITEM_ERROR,query:e,error:t,errorType:$c.CREATE_ITEM}}(t,e),e}},[`delete${t}`]:function*(t,r=!0){const o=zc(e,t),{id:n,key:s}=Wc(t,o);yield function(e,t){return{type:Eu.DELETE_ITEM_REQUEST,key:e,force:t}}(s,r);try{const t=yield(0,Ee.apiFetch)({path:Kc(`${e}/${n}`,{force:r},o),method:"DELETE"});return yield function(e,t,r){return{type:Eu.DELETE_ITEM_SUCCESS,key:e,force:t,item:r}}(s,r,t),t}catch(e){throw yield function(e,t,r){return{type:Eu.DELETE_ITEM_ERROR,key:e,error:t,errorType:$c.DELETE_ITEM,force:r}}(s,e,r),e}},[`update${t}`]:function*(t,r){const o=zc(e,t),{id:n,key:s}=Wc(t,o);yield function(e,t){return{type:Eu.UPDATE_ITEM_REQUEST,key:e,query:t}}(s,r);try{const t=yield(0,Ee.apiFetch)({path:Kc(`${e}/${n}`,{},o),method:"PUT",data:r});return yield function(e,t,r){return{type:Eu.UPDATE_ITEM_SUCCESS,key:e,item:t,query:r}}(s,t,r),t}catch(e){throw yield function(e,t,r){return{type:Eu.UPDATE_ITEM_ERROR,key:e,error:t,errorType:$c.UPDATE_ITEM,query:r}}(s,e,r),e}}}))({resourceName:t,namespace:r}),i=(({storeName:e,resourceName:t,pluralResourceName:r,namespace:o})=>({[`get${t}`]:function*(e){const t=zc(o,e),{id:r,key:n}=Wc(e,t);try{const e=yield(0,Ee.apiFetch)({path:Kc(`${o}/${r}`,{},t),method:"GET"});return yield function(e,t){return{type:Eu.GET_ITEM_SUCCESS,key:e,item:t}}(n,e),e}catch(e){throw yield function(e,t){return{type:Eu.GET_ITEM_ERROR,key:e,error:t,errorType:$c.GET_ITEM}}(n,e),e}},[`get${r}`]:function*(n){const s=zc(o,n||{}),i=Xc(n||{},o);yield de.controls.dispatch(e,"startResolution",`get${r}TotalCount`,[n]),i&&i._fields&&!i._fields.includes("id")&&(i._fields=["id",...i._fields]);try{const a=Kc(o,n||{},s),{items:c,totalCount:u}=yield he(a,i);yield Tu(n,u),yield de.controls.dispatch(e,"finishResolution",`get${r}TotalCount`,[n]),yield Su(n,c,s);for(const r of c)r.id&&(yield de.controls.dispatch(e,"finishResolution",`get${t}`,[r.id]));return c}catch(e){throw yield yu(n,e),yield pu(n,e),e}},[`get${r}TotalCount`]:function*(t){if(yield de.controls.select(e,"hasStartedResolution",`get${r}`,[t]))return;const n={...t||{},page:1,per_page:1},s=zc(o,n),i=Xc(n,o);i&&i._fields&&!i._fields.includes("id")&&(i._fields=["id",...i._fields]);try{const e=Kc(o,{},s),{totalCount:r}=yield he(e,n);return yield Tu(t,r),r}catch(e){return yield yu(t,e),e}}}))({storeName:e,resourceName:t,pluralResourceName:o,namespace:r}),a=(({resourceName:e,pluralResourceName:t,namespace:r})=>({[`get${e}`]:Bc(nu,r),[`get${e}Error`]:Bc(su,r),[`get${t}`]:Bc(iu,r,[lu]),[`get${t}TotalCount`]:Bc(au,r,[lu,void 0]),[`get${t}Error`]:Bc(cu,r),[`get${e}CreateError`]:Bc(ru,r),[`get${e}DeleteError`]:Bc(ou,r),[`get${e}UpdateError`]:Bc(uu,r),hasFinishedRequest:(t,o,n=[])=>{const s=Zc(n,r),i=tu(o,e),a=eu(i,...s);if(o)return t.requesting.hasOwnProperty(a)&&!t.requesting[a]},isRequesting:(t,o,n=[])=>{const s=Zc(n,r),i=tu(o,e),a=eu(i,...s);return t.requesting[a]}}))({resourceName:t,pluralResourceName:o,namespace:r}),{reducer:c,actions:u={},selectors:l={},resolvers:d={},controls:E={}}=n||{},p=c?_u(c):_u(),S=(0,de.createReduxStore)(e,{reducer:p,actions:{...s,...u},selectors:{...a,...l},resolvers:{...i,...d},controls:{...ge,...E}});return(0,de.register)(S),S},fu=gu({storeName:Qc,resourceName:"ProductAttribute",pluralResourceName:"ProductAttributes",namespace:"/wc/v3/products/attributes"}),Ru=Qc,mu="experimental/wc/admin/products/shipping-classes",Ou=gu({storeName:mu,resourceName:"ProductShippingClass",pluralResourceName:"ProductShippingClasses",namespace:"/wc/v3/products/shipping_classes"}),hu=mu,Iu="experimental/wc/admin/shipping/zones",Au=gu({storeName:Iu,resourceName:"ShippingZone",pluralResourceName:"ShippingZones",namespace:"/wc/v3/shipping/zones"}),Pu=Iu,Cu="wc/admin/products/tags",Uu=gu({storeName:Cu,resourceName:"ProductTag",pluralResourceName:"ProductTags",namespace:"/wc/v3/products/tags"}),vu=Cu,Nu="experimental/wc/admin/products/categories",wu=gu({storeName:Nu,resourceName:"ProductCategory",pluralResourceName:"ProductCategories",namespace:"/wc/v3/products/categories"}),Du=Nu,bu="wc/admin/products/attributes/terms",ku=gu({storeName:bu,resourceName:"ProductAttributeTerm",pluralResourceName:"ProductAttributeTerms",namespace:"/wc/v3/products/attributes/{attribute_id}/terms"}),Gu=bu,Lu="wc/admin/products/variations",Mu="/wc/v3/products/{product_id}/variations";var Fu;!function(e){e.GENERATE_VARIATIONS_REQUEST="GENERATE_VARIATIONS_REQUEST",e.GENERATE_VARIATIONS_SUCCESS="GENERATE_VARIATIONS_SUCCESS",e.GENERATE_VARIATIONS_ERROR="GENERATE_VARIATIONS_ERROR",e.BATCH_UPDATE_VARIATIONS_ERROR="BATCH_UPDATE_VARIATIONS_ERROR"}(Fu||(Fu={}));const qu=Fu;var xu;!function(e){e.GENERATE_VARIATIONS="GENERATE_VARIATIONS"}(xu||(xu={}));const ju=xu;function Qu(e,t){return{type:qu.GENERATE_VARIATIONS_ERROR,key:e,error:t,errorType:ju.GENERATE_VARIATIONS}}function Vu(e){return{type:qu.GENERATE_VARIATIONS_REQUEST,key:e}}function $u(e){return{type:qu.GENERATE_VARIATIONS_SUCCESS,key:e}}const Ku=function*(e,t,r,o=!0){const n=zc(Mu,e),{key:s}=Wc(e,n);if(yield Vu(s),o)try{yield de.controls.dispatch("core","saveEntityRecord","postType","product",{id:n[0],...t})}catch(e){throw yield Qu(s,e),e}try{const e=yield(0,Ee.apiFetch)({path:Kc(`${Mu}/generate`,{},n),method:"POST",data:r});return yield $u(s),e}catch(e){throw yield Qu(s,e),e}};function Yu(e,t){return{type:qu.BATCH_UPDATE_VARIATIONS_ERROR,key:e,error:t,errorType:"BATCH_UPDATE_VARIATIONS"}}function*Hu(e,t){const r=zc(Mu,e);try{return yield(0,Ee.apiFetch)({path:Kc(`${Mu}/batch`,{},r),method:"POST",data:t})}catch(t){const{key:o}=Wc(e,r);throw yield Yu(o,t),t}}const Wu=(e,t)=>{const r=zc(Mu,t),{key:o}=Wc(t,r),n=eu(ju.GENERATE_VARIATIONS,o);return e.requesting[n]},Bu=(e,t)=>{const r=zc(Mu,t),{key:o}=Wc(t,r),n=eu(ju.GENERATE_VARIATIONS,o);return e.errors[n]},Ju=gu({storeName:Lu,resourceName:"ProductVariation",pluralResourceName:"ProductVariations",namespace:Mu,storeConfig:{reducer:(e={items:{},data:{},itemsCount:{},errors:{},requesting:{}},t)=>{if(t&&"type"in t)switch(t.type){case Fu.GENERATE_VARIATIONS_REQUEST:return{...e,requesting:{...e.requesting,[eu(ju.GENERATE_VARIATIONS,t.key)]:!0}};case Fu.GENERATE_VARIATIONS_SUCCESS:return{...e,requesting:{...e.requesting,[eu(ju.GENERATE_VARIATIONS,t.key)]:!1},errors:{...e.errors,[eu(ju.GENERATE_VARIATIONS,t.key)]:void 0}};case Fu.GENERATE_VARIATIONS_ERROR:return{...e,errors:{...e.errors,[eu(t.errorType,t.key)]:t.error},requesting:{...e.requesting,[eu(ju.GENERATE_VARIATIONS,t.key)]:!1}};default:return e}return e},actions:H,selectors:W}}),zu=Lu,Zu="experimental/wc/admin/tax-classes",Xu="/wc/v3/taxes/classes";function*el(e){const t=zc(Xu,e||{}),r=Xc(e||{},Xu);try{const o=Kc(Xu,e||{},t),{items:n}=yield he(o,r);return yield Tu(e,n.length),yield Su(e,n.map((e=>{var t;return{...e,id:null!==(t=e.id)&&void 0!==t?t:e.slug}})),t),n}catch(t){throw yield yu(e,t),yield pu(e,t),t}}const tl=gu({storeName:Zu,resourceName:"TaxClass",pluralResourceName:"TaxClasses",namespace:Xu,storeConfig:{resolvers:B}}),rl=Zu;var ol;!function(e){e.OfflinePmsGroup="offline_pms_group",e.OfflinePm="offline_pm",e.Suggestion="suggestion",e.Gateway="gateway"}(ol||(ol={}));const nl="experimental/wc/admin/product-form",sl=e=>e.fields,il=(e,t)=>e.fields.find((e=>e.id===t)),al=e=>{const{errors:t,...r}=e;return r};var cl;!function(e){e.GET_FIELDS_ERROR="GET_FIELDS_ERROR",e.GET_FIELDS_SUCCESS="GET_FIELDS_SUCCESS",e.GET_PRODUCT_FORM_ERROR="GET_PRODUCT_FORM_ERROR",e.GET_PRODUCT_FORM_SUCCESS="GET_PRODUCT_FORM_SUCCESS"}(cl||(cl={}));const ul=cl;function ll(e){return{type:ul.GET_FIELDS_SUCCESS,fields:e}}function dl(e){return{type:ul.GET_FIELDS_ERROR,error:e}}function El(e){return{type:ul.GET_PRODUCT_FORM_SUCCESS,fields:e.fields,sections:e.sections,subsections:e.subsections,tabs:e.tabs}}function pl(e){return{type:ul.GET_PRODUCT_FORM_ERROR,error:e}}function*Sl(){try{const e=Fe+"/product-form/fields";return ll(yield(0,Ee.apiFetch)({path:e,method:"GET"}))}catch(e){return dl(e)}}function*Tl(){try{const e=Fe+"/product-form";return El(yield(0,Ee.apiFetch)({path:e,method:"GET"}))}catch(e){return pl(e)}}const yl=(0,de.createReduxStore)(nl,{reducer:(e={errors:{},fields:[],sections:[],subsections:[],tabs:[]},t)=>{switch(t.type){case ul.GET_FIELDS_SUCCESS:e={...e,fields:t.fields};break;case ul.GET_FIELDS_ERROR:e={...e,errors:{...e.errors,fields:t.error}};break;case ul.GET_PRODUCT_FORM_SUCCESS:e={...e,fields:t.fields,sections:t.sections,subsections:t.subsections,tabs:t.tabs};break;case ul.GET_PRODUCT_FORM_ERROR:e={...e,errors:{...e.errors,fields:t.error,sections:t.error,subsections:t.error}}}return e},actions:z,controls:Ee.controls,selectors:J,resolvers:Z});(0,de.register)(yl);const _l=nl,gl={steps:[],context:{},isFetching:!1,errors:{}},fl=e=>e,Rl=e=>e.isFetching,ml=e=>e.errors.getOnboardingData;function Ol(){return{type:"GET_WOOPAYMENTS_ONBOARDING_DATA_REQUEST"}}function hl(e){return{type:"GET_WOOPAYMENTS_ONBOARDING_DATA_SUCCESS",steps:e.steps,context:e.context}}function Il(e){return{type:"GET_WOOPAYMENTS_ONBOARDING_DATA_ERROR",error:e}}function*Al(){yield{type:"GET_WOOPAYMENTS_ONBOARDING_DATA_REQUEST"};try{const e=yield(0,Ee.apiFetch)({path:`${Fe}/settings/payments/woopayments/onboarding`});yield hl(e)}catch(e){yield Il(e)}}const Pl="wc/admin/woopayments-onboarding",Cl=Pl,Ul=(0,de.createReduxStore)(Pl,{reducer:(e=gl,t)=>{switch(t.type){case"GET_WOOPAYMENTS_ONBOARDING_DATA_REQUEST":return{...e,isFetching:!0};case"GET_WOOPAYMENTS_ONBOARDING_DATA_SUCCESS":return{...e,steps:t.steps,context:t.context,isFetching:!1};case"GET_WOOPAYMENTS_ONBOARDING_DATA_ERROR":return{...e,errors:{...e.errors,getOnboardingData:t.error},isFetching:!1};default:return e}},actions:ee,controls:Ee.controls,selectors:X,resolvers:te});(0,de.register)(Ul);const vl="wc/admin/import",Nl=e=>{const{activeImport:t,lastImportStartTimestamp:r}=e;return{activeImport:t,lastImportStartTimestamp:r}},wl=e=>{const{period:t,skipPrevious:r}=e;return{period:t,skipPrevious:r}},Dl=(e,t)=>{const r=JSON.stringify(t);return e.importStatus[r]||{}},bl=(e,t)=>{const{importTotals:r,lastImportStartTimestamp:o}=e;return{...r[JSON.stringify(t)],lastImportStartTimestamp:o}},kl=(e,t)=>{const r=JSON.stringify(t);return e.errors[r]||!1},Gl={SET_IMPORT_DATE:"SET_IMPORT_DATE",SET_IMPORT_ERROR:"SET_IMPORT_ERROR",SET_IMPORT_PERIOD:"SET_IMPORT_PERIOD",SET_IMPORT_STARTED:"SET_IMPORT_STARTED",SET_IMPORT_STATUS:"SET_IMPORT_STATUS",SET_IMPORT_TOTALS:"SET_IMPORT_TOTALS",SET_SKIP_IMPORTED:"SET_SKIP_IMPORTED"};function Ll(e){return{type:Gl.SET_IMPORT_STARTED,activeImport:e}}function Ml(e,t){return t?{type:Gl.SET_IMPORT_DATE,date:e}:{type:Gl.SET_IMPORT_PERIOD,date:e}}function Fl(e){return{type:Gl.SET_SKIP_IMPORTED,skipPrevious:e}}function ql(e,t){return{type:Gl.SET_IMPORT_STATUS,importStatus:t,query:e}}function xl(e,t){return{type:Gl.SET_IMPORT_TOTALS,importTotals:t,query:e}}function jl(e,t){return{type:Gl.SET_IMPORT_ERROR,error:t,query:e}}function*Ql(e,t=!1){yield Ll(t);try{return yield(0,Ee.apiFetch)({path:e,method:"POST"})}catch(t){throw yield jl(e,t),t}}function*Vl(e){try{const t=(0,Se.addQueryArgs)(`${Me}/reports/import/status`,"object"==typeof e?(0,Ge.omit)(e,["timestamp"]):{}),r=yield(0,Ee.apiFetch)({path:t});yield ql(e,r)}catch(t){yield jl(e,t)}}function*$l(e){try{const t=(0,Se.addQueryArgs)(`${Me}/reports/import/totals`,e),r=yield(0,Ee.apiFetch)({path:t});yield xl(e,r)}catch(t){yield jl(e,t)}}const Kl=window.moment;var Yl=r.n(Kl);const Hl=(0,de.createReduxStore)(vl,{reducer:(e={activeImport:!1,importStatus:{},importTotals:{},errors:{},lastImportStartTimestamp:0,period:{date:Yl()().format((0,ke.__)("MM/DD/YYYY","woocommerce")),label:"all"},skipPrevious:!0},t)=>{switch(t.type){case Gl.SET_IMPORT_STARTED:const{activeImport:r}=t;e={...e,activeImport:r,lastImportStartTimestamp:r?Date.now():e.lastImportStartTimestamp};break;case Gl.SET_IMPORT_PERIOD:e={...e,period:{...e.period,label:t.date},activeImport:!1};break;case Gl.SET_IMPORT_DATE:e={...e,period:{date:t.date,label:"custom"},activeImport:!1};break;case Gl.SET_SKIP_IMPORTED:e={...e,skipPrevious:t.skipPrevious,activeImport:!1};break;case Gl.SET_IMPORT_STATUS:const{query:o,importStatus:n}=t;e={...e,importStatus:{...e.importStatus,[JSON.stringify(o)]:n},errors:{...e.errors,[JSON.stringify(o)]:!1}};break;case Gl.SET_IMPORT_TOTALS:e={...e,importTotals:{...e.importTotals,[JSON.stringify(t.query)]:t.importTotals}};break;case Gl.SET_IMPORT_ERROR:e={...e,errors:{...e.errors,[JSON.stringify(t.query)]:t.error}}}return e},actions:oe,controls:Ee.controls,selectors:re,resolvers:ne});(0,de.register)(Hl);const Wl=vl,Bl=e=>e.groups,Jl=(e,t)=>e.groups.find((e=>e.id===t)),zl={},Zl=Ur(((e,t,r={includeEdits:!1})=>{const o=e.settings[t];if(!o)return zl;if(!1===r.includeEdits)return o;const n=e.edits[t];return n?Object.keys(o).reduce(((e,t)=>{const r=o[t];return e[t]=t in n?{...r,value:n[t]}:r,e}),{}):o}),((e,t,r={includeEdits:!1})=>[e.settings[t],e.edits[t],r.includeEdits])),Xl=Ur(((e,t,r,o={includeEdits:!1})=>{const n=e.settings[t];if(!n)return;const s=n[r];if(!s)return;if(!1===o.includeEdits)return s;const i=e.edits[t];return i&&r in i?{...s,value:i[r]}:s}),((e,t,r,o={includeEdits:!1})=>[e.settings[t]?.[r],e.edits[t]?.[r],o.includeEdits])),ed=(e,t,r,o={includeEdits:!1})=>{if(!1!==o.includeEdits){const o=e.edits[t];if(o&&r in o)return o[r]}const n=e.settings[t];if(n)return n[r]?.value},td=(e,t,r)=>{const o=e.edits[t];return!!o&&r in o},rd=Ur(((e,t)=>{const r=e.edits[t];return r?Object.keys(r):[]}),((e,t)=>[e.edits[t]])),od=(e,t)=>!!e.isSaving.groups?.[t],nd=(e,t,r)=>{const o=e.isSaving.settings?.[t];return!!o&&!!o[r]},sd=(e,t)=>e.errors[t],id=(e,t,r)=>{const o=e.errors[t];if(o)return o[r]},ad=(e,t)=>{const r=e.edits[t];return!!r&&Object.keys(r).length>0};var cd=r(93057);const ud="RECEIVE_GROUPS",ld="RECEIVE_SETTINGS",dd="EDIT_SETTING",Ed="EDIT_SETTINGS",pd="SET_SAVING",Sd="SET_ERROR",Td="REVERT_EDITED_SETTING",yd="REVERT_EDITED_SETTINGS_GROUP",_d=e=>({type:ud,groups:e}),gd=(e,t)=>({type:ld,groupId:e,settings:t}),fd=(e,t,r)=>async({resolveSelect:o,dispatch:n})=>{await o.getSettingValue(e,t),n(((e,t,r)=>({type:dd,groupId:e,settingId:t,value:r}))(e,t,r))},Rd=e=>"string"==typeof e.id&&e.id.length>0&&void 0!==e.value,md=(e,t)=>async({resolveSelect:r,dispatch:o})=>{await r.getSettings(e);const n=Array.isArray(t)?t:Object.entries(t).map((([e,t])=>({id:e,value:t})));if(!n.every(Rd))throw new Error("Invalid setting edit payload");o(((e,t)=>({type:Ed,groupId:e,updates:t}))(e,n))},Od=(e,t,r)=>({type:pd,groupId:e,settingId:t,isSaving:r}),hd=(e,t,r)=>({type:Sd,groupId:e,settingId:t,error:r}),Id=(e,t)=>({type:Td,groupId:e,settingId:t}),Ad=e=>({type:yd,groupId:e}),Pd=async(e,t,r,o)=>{const n=await o.__unstableAcquireStoreLock(Qd,["settings",e,t],{exclusive:!0});o(Od(e,t,!0));try{const n=await ye()({path:`${Me}/settings/${e}/${t}`,method:"PUT",data:{value:r}});return o(gd(e,[n])),n}catch(r){throw o(hd(e,t,r)),r}finally{o(Od(e,t,!1)),o.__unstableReleaseStoreLock(n)}},Cd=async(e,t,r)=>{const o=await r.__unstableAcquireStoreLock(Qd,["settings",e],{exclusive:!0});r(Od(e,null,!0));try{const o=await ye()({path:`${Me}/settings/${e}/batch`,method:"POST",data:{update:t}}),n=[],s=[];if(o.update.forEach((t=>{"error"in t&&t.error&&"object"==typeof t.error?(s.push({id:t.id,error:t.error}),r(hd(e,t.id,t.error))):n.push(t)})),n.length>0&&r(gd(e,n)),s.length>0){const e=new Error("Failed to update some settings");throw e.settingErrors=s,e}return o}catch(t){throw t instanceof Error&&"settingErrors"in t||r(hd(e,null,t)),t}finally{r(Od(e,null,!1)),r.__unstableReleaseStoreLock(o)}},Ud=(e,t)=>async({dispatch:r})=>{const o=Array.isArray(t)?t:Object.entries(t).map((([e,t])=>({id:e,value:t})));return Cd(e,o,r)},vd=(e,t,r)=>async({dispatch:o})=>Pd(e,t,r,o),Nd=e=>async({select:t,dispatch:r})=>{const o=t.getEditedSettingIds(e).map((r=>({id:r,value:t.getSettingValue(e,r,{includeEdits:!0})})));if(0!==o.length)return Cd(e,o,r)},wd=(e,t)=>async({select:r,dispatch:o})=>{if(!r.getEditedSettingIds(e).includes(t))return;const n=r.getSettingValue(e,t,{includeEdits:!0});return Pd(e,t,n,o)},Dd=(0,cd.A)(),bd=Dd.__unstableAcquireStoreLock,kd=Dd.__unstableReleaseStoreLock,Gd=()=>async({dispatch:e})=>{try{const t=await ye()({path:"/wc/v3/settings"});return e(_d(t)),t}catch(e){throw e}},Ld=e=>async({dispatch:t})=>{const r=await t.__unstableAcquireStoreLock(Qd,["settings",e],{exclusive:!1});try{const r=await ye()({path:`${Me}/settings/${e}`});return t(gd(e,r)),r}catch(r){throw t(hd(e,null,r instanceof Error?r:new Error(String(r)))),r}finally{t.__unstableReleaseStoreLock(r)}},Md=(e,t)=>async({dispatch:r})=>{const o=await r.__unstableAcquireStoreLock(Qd,["settings",e,t],{exclusive:!1});try{const o=await ye()({path:`${Me}/settings/${e}/${t}`});return r(gd(e,[o])),o}catch(o){throw r(hd(e,t,o instanceof Error?o:new Error(String(o)))),o}finally{r.__unstableReleaseStoreLock(o)}},Fd=(e,t)=>async r=>{const o=await Md(e,t)(r);return o?.value},qd={groups:[],settings:{},edits:{},isSaving:{groups:{},settings:{}},errors:{}},xd=(e,t)=>(e[t]||(e[t]={}),e[t]),jd=(e,t)=>{const r=e[t];r&&0===Object.keys(r).length&&delete e[t]},Qd="wc/admin/settings-options",Vd=(0,de.createReduxStore)(Qd,{reducer:(e=qd,t)=>{switch(t.type){case ud:return{...e,groups:t.groups};case ld:{const r={...e.settings},o={...e.edits},n={...e.errors},s=xd(r,t.groupId);return t.settings.forEach((e=>{s[e.id]=e,[o,n].forEach((r=>{if(r[t.groupId]){const o=r[t.groupId];o&&e.id in o&&delete o[e.id]}}))})),[o,n].forEach((e=>{jd(e,t.groupId)})),{...e,settings:r,edits:o,errors:n}}case dd:{const r={...e.edits},o=e.settings[t.groupId]||{},n=o[t.settingId]?.value;if(t.value!==n)xd(r,t.groupId)[t.settingId]=t.value;else if(void 0!==r[t.groupId]?.[t.settingId]){const e=r[t.groupId];e&&(delete e[t.settingId],jd(r,t.groupId))}return{...e,edits:r}}case Ed:{const r={...e.edits},o=e.settings[t.groupId]||{},n=xd(r,t.groupId);return t.updates.forEach((e=>{const t=o[e.id]?.value;e.value!==t?n[e.id]=e.value:delete n[e.id]})),jd(r,t.groupId),{...e,edits:r}}case pd:{const r={...e.isSaving};return null===t.settingId?r.groups[t.groupId]=t.isSaving:xd(r.settings,t.groupId)[t.settingId]=t.isSaving,{...e,isSaving:r}}case Sd:{const r={...e.errors},o=xd(r,t.groupId);return null===t.settingId?null===t.error?delete r[t.groupId]:o.all=t.error:null===t.error?(delete o[t.settingId],jd(r,t.groupId)):o[t.settingId]=t.error,{...e,errors:r}}case Td:{const r={...e.edits},o={...e.errors};if(r[t.groupId]){const e=r[t.groupId];e&&(delete e[t.settingId],jd(r,t.groupId))}if(o[t.groupId]){const e=o[t.groupId];e&&(delete e[t.settingId],jd(o,t.groupId))}return{...e,edits:r,errors:o}}case yd:{const r={...e.edits},o={...e.errors};return delete r[t.groupId],delete o[t.groupId],{...e,edits:r,errors:o}}default:return e}},actions:ie,controls:Ee.controls,selectors:se,resolvers:ae});(0,de.register)(Vd);const $d=window.wp.compose,Kd=window.wp.element,Yd=(e,t)=>(0,$d.createHigherOrderComponent)((r=>o=>{const n=(0,Kd.useRef)(t),{startResolution:s,finishResolution:i,updateSettingsForGroup:a,clearIsDirty:c}=(0,de.useDispatch)(ct),{isResolvingGroup:u,hasFinishedResolutionGroup:l}=(0,de.useSelect)((t=>{const{isResolving:r,hasFinishedResolution:o}=t(ct);return{isResolvingGroup:r("getSettings",[e]),hasFinishedResolutionGroup:o("getSettings",[e])}}),[]);return(0,Kd.useEffect)((()=>{n.current&&(u||l||(s("getSettings",[e]),a(e,n.current),c(e),i("getSettings",[e])))}),[u,l,i,a,s,c]),(0,Kd.createElement)(r,{...o})}),"withSettingsHydration"),Hd=e=>{let t=!1;return(0,$d.createHigherOrderComponent)((r=>o=>{const n=(0,Kd.useRef)(e),{isResolvingGroup:s,hasFinishedResolutionGroup:i}=(0,de.useSelect)((e=>{const{isResolving:t,hasFinishedResolution:r}=e(Sn);return{isResolvingGroup:t("getProfileItems",[]),hasFinishedResolutionGroup:r("getProfileItems",[])}}),[]),{startResolution:a,finishResolution:c,setProfileItems:u}=(0,de.useDispatch)(Sn);return(0,Kd.useEffect)((()=>{if(!n.current)return;const{profileItems:e}=n.current;e&&(!e||t||s||i||(a("getProfileItems",[]),u(e,!0),c("getProfileItems",[]),t=!0))}),[c,u,a,s,i]),(0,Kd.createElement)(r,{...o})}),"withOnboardingHydration")},Wd=e=>(0,$d.createHigherOrderComponent)((t=>r=>{const o=(0,de.useSelect)((t=>{if(!e)return;const{isResolving:r,hasFinishedResolution:o}=t(le.store);return!r("getCurrentUser",[])&&!o("getCurrentUser",[])}),[]),{startResolution:n,finishResolution:s,receiveCurrentUser:i}=(0,de.useDispatch)(le.store);return o&&(n("getCurrentUser",[]),i(e),s("getCurrentUser",[])),(0,Kd.createElement)(t,{...r})}),"withCurrentUserHydration"),Bd=e=>(0,$d.createHigherOrderComponent)((t=>r=>{Wr()("withNavigationHydration",{});const o=(0,de.useSelect)((t=>{if(!e)return;const{isResolving:r,hasFinishedResolution:o}=t(xs);return!r("getMenuItems")&&!o("getMenuItems")}),[]),{startResolution:n,finishResolution:s,setMenuItems:i}=(0,de.useDispatch)(xs);return(0,Kd.useEffect)((()=>{o&&(n("getMenuItems",[]),i(e.menuItems),s("getMenuItems",[]))}),[o]),(0,Kd.createElement)(t,{...r})}),"withNavigationHydration"),Jd=e=>(0,$d.createHigherOrderComponent)((t=>r=>{const o=(0,de.useSelect)((t=>{if(!e)return;const{isResolving:r,hasFinishedResolution:o}=t(lt);return!r("getActivePlugins",[])&&!o("getActivePlugins",[])}),[]),{startResolution:n,finishResolution:s,updateActivePlugins:i,updateInstalledPlugins:a,updateIsJetpackConnected:c}=(0,de.useDispatch)(lt);return(0,Kd.useEffect)((()=>{o&&(n("getActivePlugins",[]),n("getInstalledPlugins",[]),n("isJetpackConnected",[]),i(e.activePlugins,!0),a(e.installedPlugins,!0),c(!(!e.jetpackStatus||!e.jetpackStatus.isActive)),s("getActivePlugins",[]),s("getInstalledPlugins",[]),s("isJetpackConnected",[]))}),[o]),(0,Kd.createElement)(t,{...r})}),"withPluginsHydration"),zd=e=>{const t=(0,de.useSelect)((t=>{const{isResolving:r,hasFinishedResolution:o}=t(lr);return e?Object.fromEntries(Object.keys(e).map((e=>[e,!r("getOption",[e])&&!o("getOption",[e])]))):{}}),[]),{startResolution:r,finishResolution:o,receiveOptions:n}=(0,de.useDispatch)(lr);(0,Kd.useEffect)((()=>{Object.entries(t).forEach((([t,s])=>{s&&(r("getOption",[t]),n({[t]:e[t]}),o("getOption",[t]))}))}),[t])},Zd=e=>(0,$d.createHigherOrderComponent)((t=>r=>(zd(e),(0,Kd.createElement)(t,{...r}))),"withOptionsHydration"),Xd=(e,t=[])=>{const{requestedSettings:r,settingsError:o,isRequesting:n,isDirty:s}=(0,de.useSelect)((r=>{const{getLastSettingsErrorForGroup:o,getSettingsForGroup:n,getIsDirty:s,isUpdateSettingsRequesting:i}=r(ct);return{requestedSettings:n(e,t),settingsError:Boolean(o(e)),isRequesting:i(e),isDirty:s(e,t)}}),[e,...t.sort()]),{persistSettingsForGroup:i,updateAndPersistSettingsForGroup:a,updateSettingsForGroup:c}=(0,de.useDispatch)(ct),u=(0,Kd.useCallback)(((t,r)=>{c(e,{[t]:r})}),[e]);return{settingsError:o,isRequesting:n,isDirty:s,...r,persistSettings:(0,Kd.useCallback)((()=>{i(e)}),[e]),updateAndPersistSettings:(0,Kd.useCallback)(((t,r)=>{a(e,{[t]:r})}),[e]),updateSettings:u}},eE=e=>{const t=e.woocommerce_meta||{};return(0,Ge.mapValues)(t,(e=>{if(!e||0===e.length)return"";try{return JSON.parse(e)}catch(t){return e}}))},tE=()=>{const e=(0,de.useDispatch)(le.store),{addEntities:t,receiveCurrentUser:r,saveEntityRecord:o}=e;let{saveUser:n}=e;const s=(0,de.useSelect)((e=>{const{getCurrentUser:t,getEntity:r,getEntityRecord:o,getLastEntitySaveError:n,hasStartedResolution:s,hasFinishedResolution:i}=e(le.store);return{isRequesting:s("getCurrentUser",[])&&!i("getCurrentUser",[]),user:t(),getCurrentUser:t,getEntity:r,getEntityRecord:o,getLastEntitySaveError:n}}),[]),i=s.user?eE(s.user):{};return{isRequesting:s.isRequesting,...i,updateUserPreferences:e=>{"function"!=typeof n&&(n=async e=>(Boolean(s.getEntity("root","user"))||await t([{name:"user",kind:"root",baseURL:"/wp/v2/users",plural:"users"}]),await o("root","user",e),s.getEntityRecord("root","user",e.id)));const i=s.getCurrentUser();return async function(e,t,r,o,n){const s=(0,Ge.mapValues)(n,(e=>"string"==typeof e?e:JSON.stringify(e)));if(0===Object.keys(s).length)return{error:new Error("Invalid woocommerce_meta data for update."),updatedUser:void 0};e({...t,woocommerce_meta:{...t.woocommerce_meta,...s}});const i=await r({id:t.id,woocommerce_meta:s});return void 0===i?{error:o("root","user",t.id),updatedUser:i}:{updatedUser:{...i,woocommerce_meta:eE(i)}}}(r,i,n,s.getLastEntitySaveError,e)}}},rE=()=>{const e=(0,de.useSelect)((e=>{const{getCurrentUser:t,hasStartedResolution:r,hasFinishedResolution:o}=e(le.store);return{isRequesting:r("getCurrentUser",[])&&!o("getCurrentUser",[]),user:t(),getCurrentUser:t}}),[]);return{currentUserCan:t=>!(!e.user||!e.user.is_super_admin)||!(!e.user||!e.user.capabilities[t]),user:e.user,isRequesting:e.isRequesting}},oE=e=>e.filter((e=>!e.isDismissed)),nE=window.wc.date;function sE(e){const t="leaderboards",{per_page:r,persisted_query:o,query:n,select:s,filterQuery:i}=e,{getItems:a,getItemsError:c,isResolving:u}=s(vi),l={isRequesting:!1,isError:!1,rows:[]},d=(0,nE.getCurrentDates)(n,e.defaultDateRange),E={...i,after:(0,nE.appendTimestamp)(d.primary.after,"start"),before:(0,nE.appendTimestamp)(d.primary.before,"end"),per_page:r,persisted_query:JSON.stringify(o)},p=a(t,E);if(u("getItems",[t,E]))return{...l,isRequesting:!0};if(c(t,E))return{...l,isError:!0};const S=p.get(e.id);return{...l,rows:S?.rows}}function iE(e){const{endpoint:t,query:r,limitBy:o,filters:n=[],advancedFilters:s={}}=e;return r.search?(o||[t]).reduce(((e,t)=>(e[t]=r[t],e)),{}):n.map((e=>function(e,t,r){const o=r[e.param];if(!o)return{};if("advanced"===o){const e=(0,Ks.getActiveFiltersFromQuery)(r,t.filters);if(0===e.length)return{};const o=(0,Ks.getQueryFromActiveFilters)(e.map((e=>function(e,t){const r=e.filters[t.key];if("Date"!==(0,Ge.get)(r,["input","component"]))return t;const{rule:o,value:n}=t,s={after:"start",before:"end"};if(Array.isArray(n)){const[e,r]=n;return Object.assign({},t,{value:[(0,nE.appendTimestamp)(Yl()(e),s.after),(0,nE.appendTimestamp)(Yl()(r),s.before)]})}return Object.assign({},t,{value:(0,nE.appendTimestamp)(Yl()(n),s[o])})}(t,e))),{},t.filters);return{match:r.match||"all",...o}}const n=(0,Ge.find)((0,Ks.flattenFilters)(e.filters),{value:o});if(!n)return{};if(n.settings&&n.settings.param){const{param:e}=n.settings;return r[e]?{[e]:r[e]}:{}}return{[e.param]:o}}(e,s,r))).reduce(((e,t)=>Object.assign(e,t)),{})}const aE=["stock","customers"];function cE(e){const{endpoint:t,dataType:r,query:o,fields:n,defaultDateRange:s}=e,i=(0,nE.getCurrentDates)(o,s),a=(0,nE.getIntervalForQuery)(o,s),c=iE(e),u=i[r].before;return(0,Ge.includes)(aE,t)?{...c,fields:n}:{order:"asc",interval:a,per_page:xe,after:(0,nE.appendTimestamp)(i[r].after,"start"),before:(0,nE.appendTimestamp)(u,"end"),segmentby:o.segmentby,fields:n,...c}}function uE(e){const{endpoint:t,select:r}=e,{getReportStats:o,getReportStatsError:n,isResolving:s}=r(Ts),i={isRequesting:!1,isError:!1,totals:{primary:null,secondary:null}},a=cE({...e,dataType:"primary"}),c=o(t,a);if(s("getReportStats",[t,a]))return{...i,isRequesting:!0};if(n(t,a))return{...i,isError:!0};const u=c&&c.data&&c.data.totals||null,l=cE({...e,dataType:"secondary"}),d=o(t,l);if(s("getReportStats",[t,l]))return{...i,isRequesting:!0};if(n(t,l))return{...i,isError:!0};const E=d&&d.data&&d.data.totals||null;return{...i,totals:{primary:u,secondary:E}}}const lE={requesting:{isEmpty:!1,isError:!1,isRequesting:!0,data:{totals:{},intervals:[]}},error:{isEmpty:!1,isError:!0,isRequesting:!1,data:{totals:{},intervals:[]}},empty:{isEmpty:!0,isError:!1,isRequesting:!1,data:{totals:{},intervals:[]}}},dE=[],EE=(0,Ge.memoize)(((e,t,r)=>({isEmpty:!1,isError:!1,isRequesting:!1,data:{totals:t,intervals:r}})),((e,t,r)=>[e,t.length,r.length].join(":")));function pE(e){const{endpoint:t}=e;let r=e.selector;e.select&&!e.selector&&(Wr()("option.select",{version:"1.7.0",hint:"You can pass the report selectors through option.selector now."}),r=e.select(Ts));const{getReportStats:o,getReportStatsError:n,isResolving:s}=r,i=cE(e),a=o(t,i);if(s("getReportStats",[t,i]))return lE.requesting;if(n(t,i))return lE.error;if(function(e,t){return!(e&&e.data&&e.data.totals&&!(0,Ge.isNull)(e.data.totals)&&((0,Ge.includes)(aE,t)||e.data.intervals&&0!==e.data.intervals.length))}(a,t))return lE.empty;const c=a&&a.data&&a.data.totals||null;let u=a&&a.data&&a.data.intervals||dE;if(a.totalResults>xe){let e=!0,r=!1;const c=[],l=Math.ceil(a.totalResults/xe);let d=1;for(let a=2;a<=l;a++){const u={...i,page:a},E=o(t,u);if(!s("getReportStats",[t,u])){if(n(t,u)){r=!0,e=!1;break}if(c.push(E),d++,d===l){e=!1;break}}}if(e)return lE.requesting;if(r)return lE.error;(0,Ge.forEach)(c,(function(e){e.data&&e.data.intervals&&Array.isArray(e.data.intervals)&&(u=u.concat(e.data.intervals))}))}return EE(me(t,i),c,u)}function SE(e,t){switch(e){case"currency":return t;case"percent":return".0%";case"number":default:return",";case"average":return",.2r"}}function TE(e){const{query:t,tableQuery:r={}}=e,o=iE(e),n=(0,nE.getCurrentDates)(t,e.defaultDateRange),s=(0,Ge.includes)(aE,e.endpoint);return{orderby:t.orderby||"date",order:t.order||"desc",after:s?void 0:(0,nE.appendTimestamp)(n.primary.after,"start"),before:s?void 0:(0,nE.appendTimestamp)(n.primary.before,"end"),page:t.paged||"1",per_page:t.per_page||He.pageSize,...o,...r}}function yE(e){const{endpoint:t}=e;let r=e.selector;e.select&&!e.selector&&(Wr()("option.select",{version:"1.7.0",hint:"You can pass the report selectors through option.selector now."}),r=e.select(Ts));const{getReportItems:o,getReportItemsError:n,hasFinishedResolution:s}=r,i=TE(e),a={query:i,isRequesting:!1,isError:!1,items:{data:[],totalResults:0}},c=o(t,i);return s("getReportItems",[t,i])?n(t,i)?{...a,isError:!0}:{...a,items:c}:{...a,isRequesting:!0}}const _E="wc/admin/export";var gE=r(16961),fE=r.n(gE);const RE=e=>fE()(me("export",e)),mE=(e,t,r)=>Boolean(e.requesting[t]&&e.requesting[t][RE(r)]),OE=(e,t,r)=>e.exportIds[t]&&e.exportIds[t][RE(r)],hE=(e,t,r)=>e.errors[t]&&e.errors[t][RE(r)],IE={START_EXPORT:"START_EXPORT",SET_EXPORT_ID:"SET_EXPORT_ID",SET_ERROR:"SET_ERROR",SET_IS_REQUESTING:"SET_IS_REQUESTING"};function AE(e,t,r){return{type:IE.SET_EXPORT_ID,exportType:e,exportArgs:t,exportId:r}}function PE(e,t,r){return{type:IE.SET_IS_REQUESTING,selector:e,selectorArgs:t,isRequesting:r}}function CE(e,t,r){return{type:IE.SET_ERROR,selector:e,selectorArgs:t,error:r}}function*UE(e,t){yield PE("startExport",{type:e,args:t},!0);try{const r=yield _e({path:`${Me}/reports/${e}/export`,method:"POST",data:{report_args:t,email:!0}});yield PE("startExport",{type:e,args:t},!1);const{export_id:o,message:n}=r.data;if(!o)throw new Error(n);return yield AE(e,t,o),r.data}catch(r){throw r instanceof Error?yield CE("startExport",{type:e,args:t},r.message):console.error(`Unexpected Error: ${JSON.stringify(r)}`),yield PE("startExport",{type:e,args:t},!1),r}}(0,de.registerStore)(_E,{reducer:(e={errors:{},requesting:{},exportMeta:{},exportIds:{}},t)=>{switch(t.type){case IE.SET_IS_REQUESTING:return{...e,requesting:{...e.requesting,[t.selector]:{...e.requesting[t.selector],[RE(t.selectorArgs)]:t.isRequesting}}};case IE.SET_EXPORT_ID:const{exportType:r,exportArgs:o,exportId:n}=t;return{...e,exportMeta:{...e.exportMeta,[n]:{exportType:r,exportArgs:o}},exportIds:{...e.exportIds,[r]:{...e.exportIds[r],[RE({type:r,args:o})]:n}}};case IE.SET_ERROR:return{...e,errors:{...e.errors,[t.selector]:{...e.errors[t.selector],[RE(t.selectorArgs)]:t.error}}};default:return e}},actions:ue,controls:ge,selectors:ce});const vE=_E,NE=["average_rating","backordered","backorders_allowed","date_created","date_created_gmt","date_modified","date_modified_gmt","generated_slug","id","on_sale","permalink","permalink_template","price","price_html","purchasable","rating_count","related_ids","shipping_class_id","shipping_required","shipping_taxable","total_sales","variations"]})(),(window.wc=window.wc||{}).data=o})();