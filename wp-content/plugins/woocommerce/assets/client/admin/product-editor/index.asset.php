<?php return array('dependencies' => array('moment', 'react', 'react-dom', 'wc-admin-layout', 'wc-block-templates', 'wc-components', 'wc-currency', 'wc-customer-effort-score', 'wc-experimental', 'wc-navigation', 'wc-number', 'wc-settings', 'wc-store-data', 'wc-tracks', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-plugins', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-router', 'wp-url', 'wp-viewport', 'wp-warning'), 'version' => '0169567f9ca849151f74');
