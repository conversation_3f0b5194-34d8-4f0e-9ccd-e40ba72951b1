{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-taxonomy-field", "title": "Taxonomy", "category": "widgets", "description": "A block that displays a taxonomy field, allowing searching, selection, and creation of new items", "keywords": ["taxonomy"], "textdomain": "default", "attributes": {"slug": {"type": "string", "role": "content"}, "property": {"type": "string", "role": "content"}, "label": {"type": "string", "role": "content"}, "createTitle": {"type": "string", "role": "content"}, "dialogNameHelpText": {"type": "string", "role": "content"}, "parentTaxonomyText": {"type": "string", "role": "content"}, "help": {"type": "string", "role": "content"}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "editorStyle": "file:./editor.css", "usesContext": ["postType", "isInSelectedTab"]}