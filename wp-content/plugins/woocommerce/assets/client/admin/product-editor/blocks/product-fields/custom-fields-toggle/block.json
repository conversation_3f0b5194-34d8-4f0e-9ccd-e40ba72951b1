{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-custom-fields-toggle-field", "title": "Product custom fields toggle control", "category": "woocommerce", "description": "The product custom fields toggle.", "keywords": ["products", "custom", "fields"], "textdomain": "default", "attributes": {"label": {"type": "string", "role": "content"}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": true, "inserter": false, "lock": false, "__experimentalToolbar": false}, "editorStyle": "file:./editor.css"}