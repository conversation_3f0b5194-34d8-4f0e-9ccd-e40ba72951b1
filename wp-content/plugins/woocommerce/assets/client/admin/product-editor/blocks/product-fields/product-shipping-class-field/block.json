{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-shipping-class-field", "title": "Product shipping class field", "category": "woocommerce", "description": "The product shipping class field.", "keywords": ["products", "shipping", "class"], "textdomain": "default", "attributes": {"title": {"type": "string", "role": "content"}, "disabled": {"type": "boolean", "default": false}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "usesContext": ["postType", "isInSelectedTab"]}