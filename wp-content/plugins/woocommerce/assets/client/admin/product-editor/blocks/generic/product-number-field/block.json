{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-number-field", "title": "Product number control", "category": "woocommerce", "description": "A reusable number field for the product editor.", "keywords": ["products", "number", "input"], "textdomain": "default", "attributes": {"label": {"type": "string", "role": "content"}, "property": {"type": "string"}, "suffix": {"type": "string"}, "help": {"type": "string"}, "placeholder": {"type": "string"}, "min": {"type": "number"}, "max": {"type": "number"}, "tooltip": {"type": "string"}, "required": {"type": "boolean", "default": false}, "step": {"type": "number", "default": 1}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "editorStyle": "file:./editor.css"}