{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-select-field", "title": "Product select field", "category": "woocommerce", "description": "A select field for use in the product editor.", "keywords": ["products", "select"], "textdomain": "default", "attributes": {"label": {"type": "string", "role": "content"}, "property": {"type": "string"}, "help": {"type": "string"}, "tooltip": {"type": "string"}, "disabled": {"type": "boolean"}, "multiple": {"type": "boolean", "default": false}, "options": {"type": "array", "items": {"type": "object", "properties": {"label": {"type": "string"}, "value": {"type": "string"}, "disabled": {"type": "boolean", "default": false}}}, "default": []}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "usesContext": ["postType"]}