{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-variations-options-field", "title": "Product variations options", "category": "woocommerce", "description": "The product variations options.", "keywords": ["products", "variations"], "textdomain": "default", "attributes": {"description": {"type": "string", "role": "content"}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}, "usesContext": ["postType", "isInSelectedTab"], "editorStyle": "file:./editor.css"}