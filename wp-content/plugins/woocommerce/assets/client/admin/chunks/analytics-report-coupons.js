"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[8286],{55737:(e,t,r)=>{r.d(t,{A:()=>b});var o=r(27723),a=r(86087),s=r(29491),n=r(38443),i=r(47143),c=r(66087),l=r(98846),u=r(40314),m=r(77374),d=r(94111),p=r(96476);function y(e,t,r={}){if(!e||0===e.length)return null;const o=e.slice(0),a=o.pop();if(a.showFilters(t,r)){const e=(0,p.flattenFilters)(a.filters),r=t[a.param]||a.defaultValue||"all";return(0,c.find)(e,{value:r})}return y(o,t,r)}function g(e){return t=>(0,n.format)(e,t)}function h(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,r=e.data.intervals[e.data.intervals.length-1].date_end;if((0,m.containsLeapYear)(t,r))return!0}return!1}var _=r(39793);class f extends a.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,c.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map((function(e){const r={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const o=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;r[e.segment_id]={label:o,value:e.subtotals[t.key]||0}}})),{date:(0,n.format)("Y-m-d\\TH:i:s",e.date_start),...r}}))}getTimeChartData(){const{query:e,primaryData:t,secondaryData:r,selectedChart:o,defaultDateRange:a}=this.props,s=(0,m.getIntervalForQuery)(e,a),{primary:i,secondary:c}=(0,m.getCurrentDates)(e,a);return function(e,t,r,o,a,s,i){const c=h(e),l=h(t),u=[...e.data.intervals],d=[...t.data.intervals],p=[];for(let e=0;e<u.length;e++){const t=u[e],y=(0,n.format)("Y-m-d\\TH:i:s",t.date_start),g=`${r.label} (${r.range})`,h=t.date_start,_=t.subtotals[s]||0,f=d[e],b=`${o.label} (${o.range})`;let C=(0,m.getPreviousDate)(t.date_start,r.after,o.after,a,i).format("YYYY-MM-DD HH:mm:ss"),v=f&&f.subtotals[s]||0;if("day"===i&&c&&!l&&d?.[e]){const r=new Date(t.date_start),o=new Date(d[e].date_start);(0,m.isLeapYear)(r.getFullYear())&&1===r.getMonth()&&29===r.getDate()&&2===o.getMonth()&&1===o.getDate()&&(C="-",v=0,d.splice(e,0,d[e]))}p.push({date:y,primary:{label:g,labelDate:h,value:_},secondary:{label:b,labelDate:C,value:v}})}return p}(t,r,i,c,e.compare,o.key,s)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,c.get)(e,["data","totals",r.key],null),secondary:(0,c.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,a){const{emptySearchResults:s,filterParam:n,interactiveLegend:i,itemsLabel:c,legendPosition:d,path:p,query:y,selectedChart:h,showHeaderControls:f,primaryData:b,defaultDateRange:C}=this.props,v=(0,m.getIntervalForQuery)(y,C),w=(0,m.getAllowedIntervalsForQuery)(y,C),D=(0,m.getDateFormatsForInterval)(v,b.data.intervals.length,{type:"php"}),x=s?(0,o.__)("No data for the current search","woocommerce"):(0,o.__)("No data for the selected date range","woocommerce"),{formatAmount:A,getCurrencyConfig:F}=this.context;return(0,_.jsx)(l.Chart,{allowedIntervals:w,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:x,filterParam:n,interactiveLegend:i,interval:v,isRequesting:t,itemsLabel:c,legendPosition:d,legendTotals:a,mode:e,path:p,query:y,screenReaderFormat:g(D.screenReaderFormat),showHeaderControls:f,title:h.label,tooltipLabelFormat:g(D.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&h.label||null,tooltipValueFormat:(0,u.getTooltipValueFormat)(h.type,A),chartType:(0,m.getChartTypeForQuery)(y),valueType:h.type,xFormat:g(D.xFormat),x2Format:g(D.x2Format),currency:F()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,_.jsx)(l.AnalyticsError,{});const r=e||t.isRequesting,o=this.getItemChartData();return this.renderChart("item-comparison",r,o)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:r}=this.props;if(!t||t.isError||r.isError)return(0,_.jsx)(l.AnalyticsError,{});const o=e||t.isRequesting||r.isRequesting,a=this.getTimeChartData(),s=this.getTimeChartTotals();return this.renderChart("time-comparison",o,a,s)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}f.contextType=d.CurrencyContext,f.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const b=(0,s.compose)((0,i.withSelect)(((e,t)=>{const{charts:r,endpoint:o,filters:a,isRequesting:s,limitProperties:n,query:i,advancedFilters:l}=t,m=n||[o],d=y(a,i),p=(0,c.get)(d,["settings","param"]),g=t.mode||function(e,t){if(e&&t){const r=(0,c.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,c.get)(e,["chartMode"])}return null}(d,i)||"time-comparison",{woocommerce_default_date_range:h}=e(u.settingsStore).getSetting("wc_admin","wcAdminSettings"),_={mode:g,filterParam:p,defaultDateRange:h};if(s)return _;const f=m.some((e=>i[e]&&i[e].length));if(i.search&&!f)return{..._,emptySearchResults:!0};const b=e(u.reportsStore),C=r&&r.map((e=>e.key)),v=(0,u.getReportChartData)({endpoint:o,dataType:"primary",query:i,selector:b,limitBy:m,filters:a,advancedFilters:l,defaultDateRange:h,fields:C});if("item-comparison"===g)return{..._,primaryData:v};const w=(0,u.getReportChartData)({endpoint:o,dataType:"secondary",query:i,selector:b,limitBy:m,filters:a,advancedFilters:l,defaultDateRange:h,fields:C});return{..._,primaryData:v,secondaryData:w}})))(f)},68224:(e,t,r)=>{r.d(t,{A:()=>h});var o=r(27723),a=r(86087),s=r(29491),n=r(47143),i=r(96476),c=r(98846),l=r(43577),u=r(40314),m=r(77374),d=r(83306),p=r(94111),y=r(39793);class g extends a.Component{formatVal(e,t){const{formatAmount:r,getCurrencyConfig:o}=this.context;return"currency"===t?r(e):(0,l.formatValue)(o(),t,e)}getValues(e,t){const{emptySearchResults:r,summaryData:o}=this.props,{totals:a}=o,s=a.primary?a.primary[e]:0,n=a.secondary?a.secondary[e]:0,i=r?0:s,c=r?0:n;return{delta:(0,l.calculateDelta)(i,c),prevValue:this.formatVal(c,t),value:this.formatVal(i,t)}}render(){const{charts:e,query:t,selectedChart:r,summaryData:a,endpoint:s,report:n,defaultDateRange:l}=this.props,{isError:u,isRequesting:p}=a;if(u)return(0,y.jsx)(c.AnalyticsError,{});if(p)return(0,y.jsx)(c.SummaryListPlaceholder,{numberOfItems:e.length});const{compare:g}=(0,m.getDateParamsFromQuery)(t,l);return(0,y.jsx)(c.SummaryList,{children:({onToggle:t})=>e.map((e=>{const{key:a,order:l,orderby:u,label:m,type:p,isReverseTrend:h,labelTooltipText:_}=e,f={chart:a};u&&(f.orderby=u),l&&(f.order=l);const b=(0,i.getNewPath)(f),C=r.key===a,{delta:v,prevValue:w,value:D}=this.getValues(a,p);return(0,y.jsx)(c.SummaryNumber,{delta:v,href:b,label:m,reverseTrend:h,prevLabel:"previous_period"===g?(0,o.__)("Previous period:","woocommerce"):(0,o.__)("Previous year:","woocommerce"),prevValue:w,selected:C,value:D,labelTooltipText:_,onLinkClickCallback:()=>{t&&t(),(0,d.recordEvent)("analytics_chart_tab_click",{report:n||s,key:a})}},a)}))})}}g.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},g.contextType=p.CurrencyContext;const h=(0,s.compose)((0,n.withSelect)(((e,t)=>{const{charts:r,endpoint:o,limitProperties:a,query:s,filters:n,advancedFilters:i}=t,c=a||[o],l=c.some((e=>s[e]&&s[e].length));if(s.search&&!l)return{emptySearchResults:!0};const m=r&&r.map((e=>e.key)),{woocommerce_default_date_range:d}=e(u.settingsStore).getSetting("wc_admin","wcAdminSettings");return{summaryData:(0,u.getSummaryNumbers)({endpoint:o,query:s,select:e,limitBy:c,filters:n,advancedFilters:i,defaultDateRange:d,fields:m}),defaultDateRange:d}})))(g)},620:(e,t,r)=>{r.d(t,{Qc:()=>u,eg:()=>l,uW:()=>d});var o=r(27723),a=r(52619),s=r(47143),n=r(27752),i=r(33958);const{addCesSurveyForAnalytics:c}=(0,s.dispatch)(n.STORE_KEY),l=(0,a.applyFilters)("woocommerce_admin_coupons_report_charts",[{key:"orders_count",label:(0,o.__)("Discounted orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"},{key:"amount",label:(0,o.__)("Amount","woocommerce"),order:"desc",orderby:"amount",type:"currency"}]),u=(0,a.applyFilters)("woocommerce_admin_coupon_report_advanced_filters",{filters:{},title:(0,o._x)("Coupons match <select/> filters","A sentence describing filters for Coupons. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),m=[{label:(0,o.__)("All coupons","woocommerce"),value:"all"},{label:(0,o.__)("Single coupon","woocommerce"),value:"select_coupon",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_coupon",chartMode:"item-comparison",path:["select_coupon"],settings:{type:"coupons",param:"coupons",getLabels:i.U4,labels:{placeholder:(0,o.__)("Type to search for a coupon","woocommerce"),button:(0,o.__)("Single Coupon","woocommerce")}}}]},{label:(0,o.__)("Comparison","woocommerce"),value:"compare-coupons",settings:{type:"coupons",param:"coupons",getLabels:i.U4,labels:{title:(0,o.__)("Compare Coupon Codes","woocommerce"),update:(0,o.__)("Compare","woocommerce"),helpText:(0,o.__)("Check at least two coupon codes below to compare","woocommerce")},onClick:c}}];Object.keys(u.filters).length&&m.push({label:(0,o.__)("Advanced filters","woocommerce"),value:"advanced"});const d=(0,a.applyFilters)("woocommerce_admin_coupons_report_filters",[{label:(0,o.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:m}])},35301:(e,t,r)=>{r.r(t),r.d(t,{default:()=>w});var o=r(86087),a=r(27723),s=r(620),n=r(66087),i=r(98846),c=r(96476),l=r(43577),u=r(77374),m=r(94111),d=r(97605),p=r(56109),y=r(39793);class g extends o.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,a.__)("Coupon code","woocommerce"),key:"code",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,a.__)("Orders","woocommerce"),key:"orders_count",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,a.__)("Amount discounted","woocommerce"),key:"amount",isSortable:!0,isNumeric:!0},{label:(0,a.__)("Created","woocommerce"),key:"created"},{label:(0,a.__)("Expires","woocommerce"),key:"expires"},{label:(0,a.__)("Type","woocommerce"),key:"type"}]}getRowsContent(e){const{query:t}=this.props,r=(0,c.getPersistedQuery)(t),o=(0,p.Qk)("dateFormat",u.defaultTableDateFormat),{formatAmount:s,formatDecimal:m,getCurrencyConfig:d}=this.context;return(0,n.map)(e,(e=>{const{amount:t,coupon_id:n,orders_count:u}=e,p=e.extended_info||{},{code:g,date_created:h,date_expires:_,discount_type:f}=p,b=n>0?(0,c.getNewPath)(r,"/analytics/coupons",{filter:"single_coupon",coupons:n}):null,C=null===b?g:(0,y.jsx)(i.Link,{href:b,type:"wc-admin",children:g}),v=n>0?(0,c.getNewPath)(r,"/analytics/orders",{filter:"advanced",coupon_includes:n}):null;return[{display:C,value:g},{display:null===v?u:(0,y.jsx)(i.Link,{href:v,type:"wc-admin",children:(0,l.formatValue)(d(),"number",u)}),value:u},{display:s(t),value:m(t)},{display:h?(0,y.jsx)(i.Date,{date:h,visibleFormat:o}):(0,a.__)("N/A","woocommerce"),value:h},{display:_?(0,y.jsx)(i.Date,{date:_,visibleFormat:o}):(0,a.__)("N/A","woocommerce"),value:_},{display:this.getCouponType(f),value:f}]}))}getSummary(e){const{coupons_count:t=0,orders_count:r=0,amount:o=0}=e,{formatAmount:s,getCurrencyConfig:n}=this.context,i=n();return[{label:(0,a._n)("Coupon","Coupons",t,"woocommerce"),value:(0,l.formatValue)(i,"number",t)},{label:(0,a._n)("Order","Orders",r,"woocommerce"),value:(0,l.formatValue)(i,"number",r)},{label:(0,a.__)("Amount discounted","woocommerce"),value:s(o)}]}getCouponType(e){return{percent:(0,a.__)("Percentage","woocommerce"),fixed_cart:(0,a.__)("Fixed cart","woocommerce"),fixed_product:(0,a.__)("Fixed product","woocommerce")}[e]||(0,a.__)("N/A","woocommerce")}render(){const{advancedFilters:e,filters:t,isRequesting:r,query:o}=this.props;return(0,y.jsx)(d.A,{compareBy:"coupons",endpoint:"coupons",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["coupons_count","orders_count","amount"],isRequesting:r,itemIdField:"coupon_id",query:o,searchBy:"coupons",tableQuery:{orderby:o.orderby||"orders_count",order:o.order||"desc",extended_info:!0},title:(0,a.__)("Coupons","woocommerce"),columnPrefsKey:"coupons_report_columns",filters:t,advancedFilters:e})}}g.contextType=m.CurrencyContext;const h=g;var _=r(95272),f=r(55737),b=r(68224),C=r(88711);class v extends o.Component{getChartMeta(){const{query:e}=this.props,t="compare-coupons"===e.filter&&e.coupons&&e.coupons.split(",").length>1?"item-comparison":"time-comparison";return{itemsLabel:(0,a.__)("%d coupons","woocommerce"),mode:t}}render(){const{isRequesting:e,query:t,path:r}=this.props,{mode:a,itemsLabel:n}=this.getChartMeta(),i={...t};return"item-comparison"===a&&(i.segmentby="coupon"),(0,y.jsxs)(o.Fragment,{children:[(0,y.jsx)(C.A,{query:t,path:r,filters:s.uW,advancedFilters:s.Qc,report:"coupons"}),(0,y.jsx)(b.A,{charts:s.eg,endpoint:"coupons",query:i,selectedChart:(0,_.A)(t.chart,s.eg),filters:s.uW,advancedFilters:s.Qc}),(0,y.jsx)(f.A,{charts:s.eg,filters:s.uW,advancedFilters:s.Qc,mode:a,endpoint:"coupons",path:r,query:i,isRequesting:e,itemsLabel:n,selectedChart:(0,_.A)(t.chart,s.eg)}),(0,y.jsx)(h,{isRequesting:e,query:t,filters:s.uW,advancedFilters:s.Qc})]})}}const w=v},32639:(e,t,r)=>{r.d(t,{H:()=>a});var o=r(27723);function a(e){return[e.country,e.state,e.name||(0,o.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},33958:(e,t,r)=>{r.d(t,{Dn:()=>d,U4:()=>y,aG:()=>p,b8:()=>b,jx:()=>_,p0:()=>h,wd:()=>g,xP:()=>f});var o=r(27723),a=r(93832),s=r(1455),n=r.n(s),i=r(66087),c=r(96476),l=r(40314),u=r(32639),m=r(56109);function d(e,t=i.identity){return function(r="",o){const s="function"==typeof e?e(o):e,i=(0,c.getIdsFromQuery)(r);if(i.length<1)return Promise.resolve([]);const l={include:i.join(","),per_page:i.length};return n()({path:(0,a.addQueryArgs)(s,l)}).then((e=>e.map(t)))}}d(l.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const p=d(l.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),y=d(l.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),g=d(l.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),h=d(l.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),_=d(l.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,u.H)(e)})));function f({attributes:e,name:t}){const r=(0,m.Qk)("variationTitleAttributesSeparator"," - ");if(t&&t.indexOf(r)>-1)return t;const a=(e||[]).map((({name:e,option:t})=>(t||(e=e.charAt(0).toUpperCase()+e.slice(1),t=(0,o.sprintf)((0,o.__)("Any %s","woocommerce"),e)),t))).join(", ");return a?t+r+a:t}const b=d((({products:e})=>e?l.NAMESPACE+`/products/${e}/variations`:l.NAMESPACE+"/variations"),(e=>({key:e.id,label:f(e)})))},95272:(e,t,r)=>{r.d(t,{A:()=>a});var o=r(66087);function a(e,t=[]){return(0,o.find)(t,{key:e})||t[0]}}}]);