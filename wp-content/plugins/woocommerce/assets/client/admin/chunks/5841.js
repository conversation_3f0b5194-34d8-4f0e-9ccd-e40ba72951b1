"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5841],{57506:(e,s,t)=>{t.r(s),t.d(s,{Products:()=>L});var o=t(27723),i=t(85816),c=t(14908),n=t(92279),r=t(86087),a=t(56427),l=t(15703),d=t(24148),p=t(56537),u=t(90700),m=t(83306),k=t(52619),_=t(40314),w=t(47143),g=t(56109),h=t(64321),x=t(51614),A=t(48958),C=t(16527),j=t(71506),b=t(10952),y=t(85259),P=t(30288),f=t(3246),v=t(39793);const S=({isExpanded:e,onClick:s})=>(0,v.jsxs)(a.<PERSON>,{className:"woocommerce-task-products__button-view-less-product-types",onClick:s,children:[e?(0,o.__)("View less product types","woocommerce"):(0,o.__)("View more product types","woocommerce"),(0,v.jsx)(d.A,{icon:e?p.A:u.A})]}),L=()=>{const[e,s]=(0,r.useState)(!1),[t,i]=(0,r.useState)(!1),{installedPlugins:n,isRequestingPlugins:a}=(0,w.useSelect)((e=>{const{getInstalledPlugins:s,isPluginsRequesting:t}=e(_.pluginsStore);return{isRequestingPlugins:t("installPlugins"),installedPlugins:s()}}),[]),d=(0,h.p)((()=>{const e=(0,g.Qk)("onboarding");return e?.profile&&e?.profile.product_types||["physical"]})()),{productTypes:p,isRequesting:u}=(0,x.A)((0,h.h)(),d),{recordCompletionTime:L}=(0,y.A)("products"),T=(0,r.useMemo)((()=>p.map((e=>({...e,onClick:()=>{e.onClick(),L()}})))),[L,p]),{loadSampleProduct:N,isLoadingSampleProducts:O}=(0,j.A)({redirectUrlAfterSuccess:(0,l.getAdminLink)("edit.php?post_type=product&wc_onboarding_active_task=products")}),E=(0,r.useMemo)((()=>{const s=T.filter((e=>d.includes(e.key)));return e&&T.forEach((e=>!s.includes(e)&&s.push(e))),(0,k.applyFilters)(P.j1,s)}),[d,e,T]),q=(0,r.useMemo)((()=>{const e=[{...P.p3,onClick:()=>{P.p3.onClick(),L()}}];return!window.wcAdminFeatures?.printful||a||n.includes("printful-shipping-for-woocommerce")||e.push(P.tF),e}),[L,a,n]);return(0,v.jsxs)("div",{className:"woocommerce-task-products",children:[(0,v.jsx)(c.Text,{variant:"title",as:"h2",className:"woocommerce-task-products__title",children:(0,o.__)("What product do you want to add?","woocommerce")}),(0,v.jsxs)("div",{className:"woocommerce-product-content",children:[(0,v.jsx)(A.A,{items:E,onClickLoadSampleProduct:()=>i(!0),showOtherOptions:e,isTaskListItemClicked:u}),(0,v.jsx)(S,{isExpanded:e,onClick:()=>{e||(0,m.recordEvent)("tasklist_view_more_product_types_click"),s(!e)}}),(0,v.jsx)(A.A,{items:q,showOtherOptions:!1,isTaskListItemClicked:u}),(0,v.jsx)(f.d,{textProps:{className:"woocommerce-products-marketplace-link"},message:(0,o.__)("Visit the {{Link}}Official WooCommerce Marketplace{{/Link}} to enhance your store with additional options such as Subscriptions, Gift Cards, and more.","woocommerce"),eventName:"tasklist_add_product_visit_marketplace_click",targetUrl:(0,l.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=merchandising")})]}),O?(0,v.jsx)(C.A,{}):t&&(0,v.jsx)(b.A,{onCancel:()=>{i(!1),(0,m.recordEvent)("tasklist_cancel_load_sample_products_click")},onImport:()=>{i(!1),N()}})]})},T=()=>(0,v.jsx)(i.WooOnboardingTask,{id:"products",children:(0,v.jsx)(L,{})});(0,n.registerPlugin)("wc-admin-onboarding-task-products",{scope:"woocommerce-tasks",render:()=>(0,v.jsx)(T,{})})}}]);