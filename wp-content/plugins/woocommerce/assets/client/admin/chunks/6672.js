/*! For license information please see 6672.js.LICENSE.txt */
(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6672],{44412:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.<PERSON>,{d:"M4 20h8v-1.5H4V20zM18.9 3.5c-.6-.6-1.5-.6-2.1 0l-7.2 7.2c-.4-.1-.7 0-1.1.1-.5.2-1.5.7-1.9 2.2-.4 1.7-.8 2.2-1.1 2.7-.1.1-.2.3-.3.4l-.6 1.1H6c2 0 3.4-.4 4.7-1.4.8-.6 1.2-1.4 1.3-2.3 0-.3 0-.5-.1-.7L19 5.7c.5-.6.5-1.6-.1-2.2zM9.7 14.7c-.7.5-1.5.8-2.4 1 .2-.5.5-1.2.8-2.3.2-.6.4-1 .8-1.1.5-.1 1 .1 1.3.3.2.2.3.5.2.8 0 .3-.1.9-.7 1.3z"})})},46445:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},48214:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},97897:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M12 3.3c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8s-4-8.8-8.8-8.8zm6.5 5.5h-2.6C15.4 7.3 14.8 6 14 5c2 .6 3.6 2 4.5 3.8zm.7 3.2c0 .6-.1 1.2-.2 1.8h-2.9c.1-.6.1-1.2.1-1.8s-.1-1.2-.1-1.8H19c.2.6.2 1.2.2 1.8zM12 18.7c-1-.7-1.8-1.9-2.3-3.5h4.6c-.5 1.6-1.3 2.9-2.3 3.5zm-2.6-4.9c-.1-.6-.1-1.1-.1-1.8 0-.6.1-1.2.1-1.8h5.2c.1.6.1 1.1.1 1.8s-.1 1.2-.1 1.8H9.4zM4.8 12c0-.6.1-1.2.2-1.8h2.9c-.1.6-.1 1.2-.1 1.8 0 .6.1 1.2.1 1.8H5c-.2-.6-.2-1.2-.2-1.8zM12 5.3c1 .7 1.8 1.9 2.3 3.5H9.7c.5-1.6 1.3-2.9 2.3-3.5zM10 5c-.8 1-1.4 2.3-1.8 3.8H5.5C6.4 7 8 5.6 10 5zM5.5 15.3h2.6c.4 1.5 1 2.8 1.8 3.7-1.8-.6-3.5-2-4.4-3.7zM14 19c.8-1 1.4-2.2 1.8-3.7h2.6C17.6 17 16 18.4 14 19z"})})},31613:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M12 4L4 7.9V20h16V7.9L12 4zm6.5 14.5H14V13h-4v5.5H5.5V8.8L12 5.7l6.5 3.1v9.7z"})})},73290:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})})},94302:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{fillRule:"evenodd",d:"M6.863 13.644L5 13.25h-.5a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5H5L18 6.5h2V16h-2l-3.854-.815.026.008a3.75 3.75 0 01-7.31-1.549zm1.477.313a2.251 2.251 0 004.356.921l-4.356-.921zm-2.84-3.28L18.157 8h.343v6.5h-.343L5.5 11.823v-1.146z",clipRule:"evenodd"})})},35166:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{fillRule:"evenodd",d:"M5.5 9.5v-2h13v2h-13zm0 3v4h13v-4h-13zM4 7a1 1 0 011-1h14a1 1 0 011 1v10a1 1 0 01-1 1H5a1 1 0 01-1-1V7z",clipRule:"evenodd"})})},37455:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"m19 7-3-3-8.5 8.5-1 4 4-1L19 7Zm-7 11.5H5V20h7v-1.5Z"})})},20273:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"})})},24652:(t,e,r)=>{"use strict";r.d(e,{A:()=>o});var n=r(5573),s=r(39793);const o=(0,s.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(n.Path,{d:"M3 6.75C3 5.784 3.784 5 4.75 5H15V7.313l.05.027 5.056 2.73.394.212v3.468a1.75 1.75 0 01-1.75 1.75h-.012a2.5 2.5 0 11-4.975 0H9.737a2.5 2.5 0 11-4.975 0H3V6.75zM13.5 14V6.5H4.75a.25.25 0 00-.25.25V14h.965a2.493 2.493 0 011.785-.75c.7 0 1.332.287 1.785.75H13.5zm4.535 0h.715a.25.25 0 00.25-.25v-2.573l-4-2.16v4.568a2.487 2.487 0 011.25-.335c.7 0 1.332.287 1.785.75zM6.282 15.5a1.002 1.002 0 00.968 1.25 1 1 0 10-.968-1.25zm9 0a1 1 0 101.937.498 1 1 0 00-1.938-.498z"})})},17697:(t,e)=>{var r;!function(){"use strict";var n={}.hasOwnProperty;function s(){for(var t=[],e=0;e<arguments.length;e++){var r=arguments[e];if(r){var o=typeof r;if("string"===o||"number"===o)t.push(r);else if(Array.isArray(r)){if(r.length){var i=s.apply(null,r);i&&t.push(i)}}else if("object"===o){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){t.push(r.toString());continue}for(var l in r)n.call(r,l)&&r[l]&&t.push(l)}}}return t.join(" ")}t.exports?(s.default=s,t.exports=s):void 0===(r=function(){return s}.apply(e,[]))||(t.exports=r)}()},73572:(t,e,r)=>{"use strict";e.A=function(t){var e=t.size,r=void 0===e?24:e,n=t.onClick,l=(t.icon,t.className),a=function(t,e){if(null==t)return{};var r,n,s=function(t,e){if(null==t)return{};var r,n,s={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],0<=e.indexOf(r)||(s[r]=t[r]);return s}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],0<=e.indexOf(r)||Object.prototype.propertyIsEnumerable.call(t,r)&&(s[r]=t[r])}return s}(t,o),h=["gridicon","gridicons-star-outline",l,!!function(t){return 0==t%18}(r)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return s.default.createElement("svg",i({className:h,height:r,width:r,onClick:n},a,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),s.default.createElement("g",null,s.default.createElement("path",{d:"M12 6.308l1.176 3.167.347.936.997.041 3.374.139-2.647 2.092-.784.62.27.962.911 3.249-2.814-1.871-.83-.553-.83.552-2.814 1.871.911-3.249.27-.962-.784-.62-2.648-2.092 3.374-.139.997-.041.347-.936L12 6.308M12 2L9.418 8.953 2 9.257l5.822 4.602L5.82 21 12 16.891 18.18 21l-2.002-7.141L22 9.257l-7.418-.305L12 2z"})))};var n,s=(n=r(51609))&&n.__esModule?n:{default:n},o=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(t){for(var e,r=1;r<arguments.length;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},i.apply(this,arguments)}},81739:(t,e,r)=>{"use strict";e.A=function(t){var e=t.size,r=void 0===e?24:e,n=t.onClick,l=(t.icon,t.className),a=function(t,e){if(null==t)return{};var r,n,s=function(t,e){if(null==t)return{};var r,n,s={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],0<=e.indexOf(r)||(s[r]=t[r]);return s}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],0<=e.indexOf(r)||Object.prototype.propertyIsEnumerable.call(t,r)&&(s[r]=t[r])}return s}(t,o),h=["gridicon","gridicons-star",l,!!function(t){return 0==t%18}(r)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return s.default.createElement("svg",i({className:h,height:r,width:r,onClick:n},a,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),s.default.createElement("g",null,s.default.createElement("path",{d:"M12 2l2.582 6.953L22 9.257l-5.822 4.602L18.18 21 12 16.891 5.82 21l2.002-7.141L2 9.257l7.418-.304z"})))};var n,s=(n=r(51609))&&n.__esModule?n:{default:n},o=["size","onClick","icon","className"];function i(){return i=Object.assign?Object.assign.bind():function(t){for(var e,r=1;r<arguments.length;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},i.apply(this,arguments)}},42851:(t,e,r)=>{"use strict";r.d(e,{hp:()=>A});var n,s=r(51609),o=r.n(s),i=Object.defineProperty,l=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,c=(t,e,r)=>e in t?i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,u=(t,e)=>{for(var r in e||(e={}))a.call(e,r)&&c(t,r,e[r]);if(l)for(var r of l(e))h.call(e,r)&&c(t,r,e[r]);return t},d=(t,e)=>{var r={};for(var n in t)a.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&l)for(var n of l(t))e.indexOf(n)<0&&h.call(t,n)&&(r[n]=t[n]);return r};(t=>{const e=class{constructor(t,r,n,s){if(this.version=t,this.errorCorrectionLevel=r,this.modules=[],this.isFunction=[],t<e.MIN_VERSION||t>e.MAX_VERSION)throw new RangeError("Version value out of range");if(s<-1||s>7)throw new RangeError("Mask value out of range");this.size=4*t+17;let i=[];for(let t=0;t<this.size;t++)i.push(!1);for(let t=0;t<this.size;t++)this.modules.push(i.slice()),this.isFunction.push(i.slice());this.drawFunctionPatterns();const l=this.addEccAndInterleave(n);if(this.drawCodewords(l),-1==s){let t=1e9;for(let e=0;e<8;e++){this.applyMask(e),this.drawFormatBits(e);const r=this.getPenaltyScore();r<t&&(s=e,t=r),this.applyMask(e)}}o(0<=s&&s<=7),this.mask=s,this.applyMask(s),this.drawFormatBits(s),this.isFunction=[]}static encodeText(r,n){const s=t.QrSegment.makeSegments(r);return e.encodeSegments(s,n)}static encodeBinary(r,n){const s=t.QrSegment.makeBytes(r);return e.encodeSegments([s],n)}static encodeSegments(t,r,s=1,i=40,a=-1,h=!0){if(!(e.MIN_VERSION<=s&&s<=i&&i<=e.MAX_VERSION)||a<-1||a>7)throw new RangeError("Invalid value");let c,u;for(c=s;;c++){const n=8*e.getNumDataCodewords(c,r),s=l.getTotalBits(t,c);if(s<=n){u=s;break}if(c>=i)throw new RangeError("Data too long")}for(const t of[e.Ecc.MEDIUM,e.Ecc.QUARTILE,e.Ecc.HIGH])h&&u<=8*e.getNumDataCodewords(c,t)&&(r=t);let d=[];for(const e of t){n(e.mode.modeBits,4,d),n(e.numChars,e.mode.numCharCountBits(c),d);for(const t of e.getData())d.push(t)}o(d.length==u);const f=8*e.getNumDataCodewords(c,r);o(d.length<=f),n(0,Math.min(4,f-d.length),d),n(0,(8-d.length%8)%8,d),o(d.length%8==0);for(let t=236;d.length<f;t^=253)n(t,8,d);let g=[];for(;8*g.length<d.length;)g.push(0);return d.forEach(((t,e)=>g[e>>>3]|=t<<7-(7&e))),new e(c,r,g,a)}getModule(t,e){return 0<=t&&t<this.size&&0<=e&&e<this.size&&this.modules[e][t]}getModules(){return this.modules}drawFunctionPatterns(){for(let t=0;t<this.size;t++)this.setFunctionModule(6,t,t%2==0),this.setFunctionModule(t,6,t%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);const t=this.getAlignmentPatternPositions(),e=t.length;for(let r=0;r<e;r++)for(let n=0;n<e;n++)0==r&&0==n||0==r&&n==e-1||r==e-1&&0==n||this.drawAlignmentPattern(t[r],t[n]);this.drawFormatBits(0),this.drawVersion()}drawFormatBits(t){const e=this.errorCorrectionLevel.formatBits<<3|t;let r=e;for(let t=0;t<10;t++)r=r<<1^1335*(r>>>9);const n=21522^(e<<10|r);o(n>>>15==0);for(let t=0;t<=5;t++)this.setFunctionModule(8,t,s(n,t));this.setFunctionModule(8,7,s(n,6)),this.setFunctionModule(8,8,s(n,7)),this.setFunctionModule(7,8,s(n,8));for(let t=9;t<15;t++)this.setFunctionModule(14-t,8,s(n,t));for(let t=0;t<8;t++)this.setFunctionModule(this.size-1-t,8,s(n,t));for(let t=8;t<15;t++)this.setFunctionModule(8,this.size-15+t,s(n,t));this.setFunctionModule(8,this.size-8,!0)}drawVersion(){if(this.version<7)return;let t=this.version;for(let e=0;e<12;e++)t=t<<1^7973*(t>>>11);const e=this.version<<12|t;o(e>>>18==0);for(let t=0;t<18;t++){const r=s(e,t),n=this.size-11+t%3,o=Math.floor(t/3);this.setFunctionModule(n,o,r),this.setFunctionModule(o,n,r)}}drawFinderPattern(t,e){for(let r=-4;r<=4;r++)for(let n=-4;n<=4;n++){const s=Math.max(Math.abs(n),Math.abs(r)),o=t+n,i=e+r;0<=o&&o<this.size&&0<=i&&i<this.size&&this.setFunctionModule(o,i,2!=s&&4!=s)}}drawAlignmentPattern(t,e){for(let r=-2;r<=2;r++)for(let n=-2;n<=2;n++)this.setFunctionModule(t+n,e+r,1!=Math.max(Math.abs(n),Math.abs(r)))}setFunctionModule(t,e,r){this.modules[e][t]=r,this.isFunction[e][t]=!0}addEccAndInterleave(t){const r=this.version,n=this.errorCorrectionLevel;if(t.length!=e.getNumDataCodewords(r,n))throw new RangeError("Invalid argument");const s=e.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][r],i=e.ECC_CODEWORDS_PER_BLOCK[n.ordinal][r],l=Math.floor(e.getNumRawDataModules(r)/8),a=s-l%s,h=Math.floor(l/s);let c=[];const u=e.reedSolomonComputeDivisor(i);for(let r=0,n=0;r<s;r++){let s=t.slice(n,n+h-i+(r<a?0:1));n+=s.length;const o=e.reedSolomonComputeRemainder(s,u);r<a&&s.push(0),c.push(s.concat(o))}let d=[];for(let t=0;t<c[0].length;t++)c.forEach(((e,r)=>{(t!=h-i||r>=a)&&d.push(e[t])}));return o(d.length==l),d}drawCodewords(t){if(t.length!=Math.floor(e.getNumRawDataModules(this.version)/8))throw new RangeError("Invalid argument");let r=0;for(let e=this.size-1;e>=1;e-=2){6==e&&(e=5);for(let n=0;n<this.size;n++)for(let o=0;o<2;o++){const i=e-o,l=e+1&2?n:this.size-1-n;!this.isFunction[l][i]&&r<8*t.length&&(this.modules[l][i]=s(t[r>>>3],7-(7&r)),r++)}}o(r==8*t.length)}applyMask(t){if(t<0||t>7)throw new RangeError("Mask value out of range");for(let e=0;e<this.size;e++)for(let r=0;r<this.size;r++){let n;switch(t){case 0:n=(r+e)%2==0;break;case 1:n=e%2==0;break;case 2:n=r%3==0;break;case 3:n=(r+e)%3==0;break;case 4:n=(Math.floor(r/3)+Math.floor(e/2))%2==0;break;case 5:n=r*e%2+r*e%3==0;break;case 6:n=(r*e%2+r*e%3)%2==0;break;case 7:n=((r+e)%2+r*e%3)%2==0;break;default:throw new Error("Unreachable")}!this.isFunction[e][r]&&n&&(this.modules[e][r]=!this.modules[e][r])}}getPenaltyScore(){let t=0;for(let r=0;r<this.size;r++){let n=!1,s=0,o=[0,0,0,0,0,0,0];for(let i=0;i<this.size;i++)this.modules[r][i]==n?(s++,5==s?t+=e.PENALTY_N1:s>5&&t++):(this.finderPenaltyAddHistory(s,o),n||(t+=this.finderPenaltyCountPatterns(o)*e.PENALTY_N3),n=this.modules[r][i],s=1);t+=this.finderPenaltyTerminateAndCount(n,s,o)*e.PENALTY_N3}for(let r=0;r<this.size;r++){let n=!1,s=0,o=[0,0,0,0,0,0,0];for(let i=0;i<this.size;i++)this.modules[i][r]==n?(s++,5==s?t+=e.PENALTY_N1:s>5&&t++):(this.finderPenaltyAddHistory(s,o),n||(t+=this.finderPenaltyCountPatterns(o)*e.PENALTY_N3),n=this.modules[i][r],s=1);t+=this.finderPenaltyTerminateAndCount(n,s,o)*e.PENALTY_N3}for(let r=0;r<this.size-1;r++)for(let n=0;n<this.size-1;n++){const s=this.modules[r][n];s==this.modules[r][n+1]&&s==this.modules[r+1][n]&&s==this.modules[r+1][n+1]&&(t+=e.PENALTY_N2)}let r=0;for(const t of this.modules)r=t.reduce(((t,e)=>t+(e?1:0)),r);const n=this.size*this.size,s=Math.ceil(Math.abs(20*r-10*n)/n)-1;return o(0<=s&&s<=9),t+=s*e.PENALTY_N4,o(0<=t&&t<=2568888),t}getAlignmentPatternPositions(){if(1==this.version)return[];{const t=Math.floor(this.version/7)+2,e=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*t-2));let r=[6];for(let n=this.size-7;r.length<t;n-=e)r.splice(1,0,n);return r}}static getNumRawDataModules(t){if(t<e.MIN_VERSION||t>e.MAX_VERSION)throw new RangeError("Version number out of range");let r=(16*t+128)*t+64;if(t>=2){const e=Math.floor(t/7)+2;r-=(25*e-10)*e-55,t>=7&&(r-=36)}return o(208<=r&&r<=29648),r}static getNumDataCodewords(t,r){return Math.floor(e.getNumRawDataModules(t)/8)-e.ECC_CODEWORDS_PER_BLOCK[r.ordinal][t]*e.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][t]}static reedSolomonComputeDivisor(t){if(t<1||t>255)throw new RangeError("Degree out of range");let r=[];for(let e=0;e<t-1;e++)r.push(0);r.push(1);let n=1;for(let s=0;s<t;s++){for(let t=0;t<r.length;t++)r[t]=e.reedSolomonMultiply(r[t],n),t+1<r.length&&(r[t]^=r[t+1]);n=e.reedSolomonMultiply(n,2)}return r}static reedSolomonComputeRemainder(t,r){let n=r.map((t=>0));for(const s of t){const t=s^n.shift();n.push(0),r.forEach(((r,s)=>n[s]^=e.reedSolomonMultiply(r,t)))}return n}static reedSolomonMultiply(t,e){if(t>>>8!=0||e>>>8!=0)throw new RangeError("Byte out of range");let r=0;for(let n=7;n>=0;n--)r=r<<1^285*(r>>>7),r^=(e>>>n&1)*t;return o(r>>>8==0),r}finderPenaltyCountPatterns(t){const e=t[1];o(e<=3*this.size);const r=e>0&&t[2]==e&&t[3]==3*e&&t[4]==e&&t[5]==e;return(r&&t[0]>=4*e&&t[6]>=e?1:0)+(r&&t[6]>=4*e&&t[0]>=e?1:0)}finderPenaltyTerminateAndCount(t,e,r){return t&&(this.finderPenaltyAddHistory(e,r),e=0),e+=this.size,this.finderPenaltyAddHistory(e,r),this.finderPenaltyCountPatterns(r)}finderPenaltyAddHistory(t,e){0==e[0]&&(t+=this.size),e.pop(),e.unshift(t)}};let r=e;function n(t,e,r){if(e<0||e>31||t>>>e!=0)throw new RangeError("Value out of range");for(let n=e-1;n>=0;n--)r.push(t>>>n&1)}function s(t,e){return!!(t>>>e&1)}function o(t){if(!t)throw new Error("Assertion error")}r.MIN_VERSION=1,r.MAX_VERSION=40,r.PENALTY_N1=3,r.PENALTY_N2=3,r.PENALTY_N3=40,r.PENALTY_N4=10,r.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],r.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],t.QrCode=r;const i=class{constructor(t,e,r){if(this.mode=t,this.numChars=e,this.bitData=r,e<0)throw new RangeError("Invalid argument");this.bitData=r.slice()}static makeBytes(t){let e=[];for(const r of t)n(r,8,e);return new i(i.Mode.BYTE,t.length,e)}static makeNumeric(t){if(!i.isNumeric(t))throw new RangeError("String contains non-numeric characters");let e=[];for(let r=0;r<t.length;){const s=Math.min(t.length-r,3);n(parseInt(t.substr(r,s),10),3*s+1,e),r+=s}return new i(i.Mode.NUMERIC,t.length,e)}static makeAlphanumeric(t){if(!i.isAlphanumeric(t))throw new RangeError("String contains unencodable characters in alphanumeric mode");let e,r=[];for(e=0;e+2<=t.length;e+=2){let s=45*i.ALPHANUMERIC_CHARSET.indexOf(t.charAt(e));s+=i.ALPHANUMERIC_CHARSET.indexOf(t.charAt(e+1)),n(s,11,r)}return e<t.length&&n(i.ALPHANUMERIC_CHARSET.indexOf(t.charAt(e)),6,r),new i(i.Mode.ALPHANUMERIC,t.length,r)}static makeSegments(t){return""==t?[]:i.isNumeric(t)?[i.makeNumeric(t)]:i.isAlphanumeric(t)?[i.makeAlphanumeric(t)]:[i.makeBytes(i.toUtf8ByteArray(t))]}static makeEci(t){let e=[];if(t<0)throw new RangeError("ECI assignment value out of range");if(t<128)n(t,8,e);else if(t<16384)n(2,2,e),n(t,14,e);else{if(!(t<1e6))throw new RangeError("ECI assignment value out of range");n(6,3,e),n(t,21,e)}return new i(i.Mode.ECI,0,e)}static isNumeric(t){return i.NUMERIC_REGEX.test(t)}static isAlphanumeric(t){return i.ALPHANUMERIC_REGEX.test(t)}getData(){return this.bitData.slice()}static getTotalBits(t,e){let r=0;for(const n of t){const t=n.mode.numCharCountBits(e);if(n.numChars>=1<<t)return 1/0;r+=4+t+n.bitData.length}return r}static toUtf8ByteArray(t){t=encodeURI(t);let e=[];for(let r=0;r<t.length;r++)"%"!=t.charAt(r)?e.push(t.charCodeAt(r)):(e.push(parseInt(t.substr(r+1,2),16)),r+=2);return e}};let l=i;l.NUMERIC_REGEX=/^[0-9]*$/,l.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,l.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:",t.QrSegment=l})(n||(n={})),(t=>{let e;(t=>{const e=class{constructor(t,e){this.ordinal=t,this.formatBits=e}};let r=e;r.LOW=new e(0,1),r.MEDIUM=new e(1,0),r.QUARTILE=new e(2,3),r.HIGH=new e(3,2),t.Ecc=r})(e=t.QrCode||(t.QrCode={}))})(n||(n={})),(t=>{let e;(t=>{const e=class{constructor(t,e){this.modeBits=t,this.numBitsCharCount=e}numCharCountBits(t){return this.numBitsCharCount[Math.floor((t+7)/17)]}};let r=e;r.NUMERIC=new e(1,[10,12,14]),r.ALPHANUMERIC=new e(2,[9,11,13]),r.BYTE=new e(4,[8,16,16]),r.KANJI=new e(8,[8,10,12]),r.ECI=new e(7,[0,0,0]),t.Mode=r})(e=t.QrSegment||(t.QrSegment={}))})(n||(n={}));var f=n,g={L:f.QrCode.Ecc.LOW,M:f.QrCode.Ecc.MEDIUM,Q:f.QrCode.Ecc.QUARTILE,H:f.QrCode.Ecc.HIGH},m=128,w="L",v="#FFFFFF",p="#000000",M=!1,E=4,C=.1;function A(t){const e=t,{value:r,size:n=m,level:s=w,bgColor:i=v,fgColor:l=p,includeMargin:a=M,imageSettings:h}=e,c=d(e,["value","size","level","bgColor","fgColor","includeMargin","imageSettings"]);let A=f.QrCode.encodeText(r,g[s]).getModules();const R=a?E:0,y=A.length+2*R,x=function(t,e,r,n){if(null==n)return null;const s=r?E:0,o=t.length+2*s,i=Math.floor(e*C),l=o/e,a=(n.width||i)*l,h=(n.height||i)*l,c=null==n.x?t.length/2-a/2:n.x*l,u=null==n.y?t.length/2-h/2:n.y*l;let d=null;if(n.excavate){let t=Math.floor(c),e=Math.floor(u);d={x:t,y:e,w:Math.ceil(a+c-t),h:Math.ceil(h+u-e)}}return{x:c,y:u,h,w:a,excavation:d}}(A,n,a,h);let z=null;var P,N;null!=h&&null!=x&&(null!=x.excavation&&(P=A,N=x.excavation,A=P.slice().map(((t,e)=>e<N.y||e>=N.y+N.h?t:t.map(((t,e)=>(e<N.x||e>=N.x+N.w)&&t))))),z=o().createElement("image",{xlinkHref:h.src,height:x.h,width:x.w,x:x.x+R,y:x.y+R,preserveAspectRatio:"none"}));const b=function(t,e=0){const r=[];return t.forEach((function(t,n){let s=null;t.forEach((function(o,i){if(!o&&null!==s)return r.push(`M${s+e} ${n+e}h${i-s}v1H${s+e}z`),void(s=null);if(i!==t.length-1)o&&null===s&&(s=i);else{if(!o)return;null===s?r.push(`M${i+e},${n+e} h1v1H${i+e}z`):r.push(`M${s+e},${n+e} h${i+1-s}v1H${s+e}z`)}}))})),r.join("")}(A,R);return o().createElement("svg",u({height:n,width:n,viewBox:`0 0 ${y} ${y}`},c),o().createElement("path",{fill:i,d:`M0,0 h${y}v${y}H0z`,shapeRendering:"crispEdges"}),o().createElement("path",{fill:l,d:b,shapeRendering:"crispEdges"}),z)}!function(){try{(new Path2D).addPath(new Path2D)}catch(t){return!1}}()},84343:t=>{function e(t,e){if((t=t.replace(/\s+/g,""))===(e=e.replace(/\s+/g,"")))return 1;if(t.length<2||e.length<2)return 0;let r=new Map;for(let e=0;e<t.length-1;e++){const n=t.substring(e,e+2),s=r.has(n)?r.get(n)+1:1;r.set(n,s)}let n=0;for(let t=0;t<e.length-1;t++){const s=e.substring(t,t+2),o=r.has(s)?r.get(s):0;o>0&&(r.set(s,o-1),n++)}return 2*n/(t.length+e.length-2)}t.exports={compareTwoStrings:e,findBestMatch:function(t,r){if(!function(t,e){return"string"==typeof t&&!!Array.isArray(e)&&!!e.length&&!e.find((function(t){return"string"!=typeof t}))}(t,r))throw new Error("Bad arguments: First argument should be a string, second should be an array of strings");const n=[];let s=0;for(let o=0;o<r.length;o++){const i=r[o],l=e(t,i);n.push({target:i,rating:l}),l>n[s].rating&&(s=o)}return{ratings:n,bestMatch:n[s],bestMatchIndex:s}}}}}]);