"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6424],{55737:(e,t,r)=>{r.d(t,{A:()=>w});var a=r(27723),o=r(86087),s=r(29491),n=r(38443),l=r(47143),i=r(66087),c=r(98846),d=r(40314),m=r(77374),u=r(94111),p=r(96476);function _(e,t,r={}){if(!e||0===e.length)return null;const a=e.slice(0),o=a.pop();if(o.showFilters(t,r)){const e=(0,p.flattenFilters)(o.filters),r=t[o.param]||o.defaultValue||"all";return(0,i.find)(e,{value:r})}return _(a,t,r)}function y(e){return t=>(0,n.format)(e,t)}function h(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,r=e.data.intervals[e.data.intervals.length-1].date_end;if((0,m.containsLeapYear)(t,r))return!0}return!1}var g=r(39793);class f extends o.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,i.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map((function(e){const r={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const a=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;r[e.segment_id]={label:a,value:e.subtotals[t.key]||0}}})),{date:(0,n.format)("Y-m-d\\TH:i:s",e.date_start),...r}}))}getTimeChartData(){const{query:e,primaryData:t,secondaryData:r,selectedChart:a,defaultDateRange:o}=this.props,s=(0,m.getIntervalForQuery)(e,o),{primary:l,secondary:i}=(0,m.getCurrentDates)(e,o);return function(e,t,r,a,o,s,l){const i=h(e),c=h(t),d=[...e.data.intervals],u=[...t.data.intervals],p=[];for(let e=0;e<d.length;e++){const t=d[e],_=(0,n.format)("Y-m-d\\TH:i:s",t.date_start),y=`${r.label} (${r.range})`,h=t.date_start,g=t.subtotals[s]||0,f=u[e],w=`${a.label} (${a.range})`;let b=(0,m.getPreviousDate)(t.date_start,r.after,a.after,o,l).format("YYYY-MM-DD HH:mm:ss"),v=f&&f.subtotals[s]||0;if("day"===l&&i&&!c&&u?.[e]){const r=new Date(t.date_start),a=new Date(u[e].date_start);(0,m.isLeapYear)(r.getFullYear())&&1===r.getMonth()&&29===r.getDate()&&2===a.getMonth()&&1===a.getDate()&&(b="-",v=0,u.splice(e,0,u[e]))}p.push({date:_,primary:{label:y,labelDate:h,value:g},secondary:{label:w,labelDate:b,value:v}})}return p}(t,r,l,i,e.compare,a.key,s)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,i.get)(e,["data","totals",r.key],null),secondary:(0,i.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,o){const{emptySearchResults:s,filterParam:n,interactiveLegend:l,itemsLabel:i,legendPosition:u,path:p,query:_,selectedChart:h,showHeaderControls:f,primaryData:w,defaultDateRange:b}=this.props,v=(0,m.getIntervalForQuery)(_,b),C=(0,m.getAllowedIntervalsForQuery)(_,b),D=(0,m.getDateFormatsForInterval)(v,w.data.intervals.length,{type:"php"}),x=s?(0,a.__)("No data for the current search","woocommerce"):(0,a.__)("No data for the selected date range","woocommerce"),{formatAmount:S,getCurrencyConfig:A}=this.context;return(0,g.jsx)(c.Chart,{allowedIntervals:C,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:x,filterParam:n,interactiveLegend:l,interval:v,isRequesting:t,itemsLabel:i,legendPosition:u,legendTotals:o,mode:e,path:p,query:_,screenReaderFormat:y(D.screenReaderFormat),showHeaderControls:f,title:h.label,tooltipLabelFormat:y(D.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&h.label||null,tooltipValueFormat:(0,d.getTooltipValueFormat)(h.type,S),chartType:(0,m.getChartTypeForQuery)(_),valueType:h.type,xFormat:y(D.xFormat),x2Format:y(D.x2Format),currency:A()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,g.jsx)(c.AnalyticsError,{});const r=e||t.isRequesting,a=this.getItemChartData();return this.renderChart("item-comparison",r,a)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:r}=this.props;if(!t||t.isError||r.isError)return(0,g.jsx)(c.AnalyticsError,{});const a=e||t.isRequesting||r.isRequesting,o=this.getTimeChartData(),s=this.getTimeChartTotals();return this.renderChart("time-comparison",a,o,s)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}f.contextType=u.CurrencyContext,f.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const w=(0,s.compose)((0,l.withSelect)(((e,t)=>{const{charts:r,endpoint:a,filters:o,isRequesting:s,limitProperties:n,query:l,advancedFilters:c}=t,m=n||[a],u=_(o,l),p=(0,i.get)(u,["settings","param"]),y=t.mode||function(e,t){if(e&&t){const r=(0,i.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,i.get)(e,["chartMode"])}return null}(u,l)||"time-comparison",{woocommerce_default_date_range:h}=e(d.settingsStore).getSetting("wc_admin","wcAdminSettings"),g={mode:y,filterParam:p,defaultDateRange:h};if(s)return g;const f=m.some((e=>l[e]&&l[e].length));if(l.search&&!f)return{...g,emptySearchResults:!0};const w=e(d.reportsStore),b=r&&r.map((e=>e.key)),v=(0,d.getReportChartData)({endpoint:a,dataType:"primary",query:l,selector:w,limitBy:m,filters:o,advancedFilters:c,defaultDateRange:h,fields:b});if("item-comparison"===y)return{...g,primaryData:v};const C=(0,d.getReportChartData)({endpoint:a,dataType:"secondary",query:l,selector:w,limitBy:m,filters:o,advancedFilters:c,defaultDateRange:h,fields:b});return{...g,primaryData:v,secondaryData:C}})))(f)},68224:(e,t,r)=>{r.d(t,{A:()=>h});var a=r(27723),o=r(86087),s=r(29491),n=r(47143),l=r(96476),i=r(98846),c=r(43577),d=r(40314),m=r(77374),u=r(83306),p=r(94111),_=r(39793);class y extends o.Component{formatVal(e,t){const{formatAmount:r,getCurrencyConfig:a}=this.context;return"currency"===t?r(e):(0,c.formatValue)(a(),t,e)}getValues(e,t){const{emptySearchResults:r,summaryData:a}=this.props,{totals:o}=a,s=o.primary?o.primary[e]:0,n=o.secondary?o.secondary[e]:0,l=r?0:s,i=r?0:n;return{delta:(0,c.calculateDelta)(l,i),prevValue:this.formatVal(i,t),value:this.formatVal(l,t)}}render(){const{charts:e,query:t,selectedChart:r,summaryData:o,endpoint:s,report:n,defaultDateRange:c}=this.props,{isError:d,isRequesting:p}=o;if(d)return(0,_.jsx)(i.AnalyticsError,{});if(p)return(0,_.jsx)(i.SummaryListPlaceholder,{numberOfItems:e.length});const{compare:y}=(0,m.getDateParamsFromQuery)(t,c);return(0,_.jsx)(i.SummaryList,{children:({onToggle:t})=>e.map((e=>{const{key:o,order:c,orderby:d,label:m,type:p,isReverseTrend:h,labelTooltipText:g}=e,f={chart:o};d&&(f.orderby=d),c&&(f.order=c);const w=(0,l.getNewPath)(f),b=r.key===o,{delta:v,prevValue:C,value:D}=this.getValues(o,p);return(0,_.jsx)(i.SummaryNumber,{delta:v,href:w,label:m,reverseTrend:h,prevLabel:"previous_period"===y?(0,a.__)("Previous period:","woocommerce"):(0,a.__)("Previous year:","woocommerce"),prevValue:C,selected:b,value:D,labelTooltipText:g,onLinkClickCallback:()=>{t&&t(),(0,u.recordEvent)("analytics_chart_tab_click",{report:n||s,key:o})}},o)}))})}}y.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},y.contextType=p.CurrencyContext;const h=(0,s.compose)((0,n.withSelect)(((e,t)=>{const{charts:r,endpoint:a,limitProperties:o,query:s,filters:n,advancedFilters:l}=t,i=o||[a],c=i.some((e=>s[e]&&s[e].length));if(s.search&&!c)return{emptySearchResults:!0};const m=r&&r.map((e=>e.key)),{woocommerce_default_date_range:u}=e(d.settingsStore).getSetting("wc_admin","wcAdminSettings");return{summaryData:(0,d.getSummaryNumbers)({endpoint:a,query:s,select:e,limitBy:i,filters:n,advancedFilters:l,defaultDateRange:u,fields:m}),defaultDateRange:u}})))(y)},80170:(e,t,r)=>{r.d(t,{Qc:()=>i,eg:()=>n,uW:()=>l});var a=r(27723),o=r(52619),s=r(33958);const n=(0,o.applyFilters)("woocommerce_admin_downloads_report_charts",[{key:"download_count",label:(0,a.__)("Downloads","woocommerce"),type:"number"}]),l=(0,o.applyFilters)("woocommerce_admin_downloads_report_filters",[{label:(0,a.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,a.__)("All downloads","woocommerce"),value:"all"},{label:(0,a.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),i=(0,o.applyFilters)("woocommerce_admin_downloads_report_advanced_filters",{title:(0,a._x)("Downloads match <select/> filters","A sentence describing filters for Downloads. See screen shot for context: https://cloudup.com/ccxhyH2mEDg","woocommerce"),filters:{product:{labels:{add:(0,a.__)("Product","woocommerce"),placeholder:(0,a.__)("Search","woocommerce"),remove:(0,a.__)("Remove product filter","woocommerce"),rule:(0,a.__)("Select a product filter match","woocommerce"),title:(0,a.__)("<title>Product</title> <rule/> <filter/>","woocommerce"),filter:(0,a.__)("Select product","woocommerce")},rules:[{value:"includes",label:(0,a._x)("Includes","products","woocommerce")},{value:"excludes",label:(0,a._x)("Excludes","products","woocommerce")}],input:{component:"Search",type:"products",getLabels:s.p0}},customer:{labels:{add:(0,a.__)("Username","woocommerce"),placeholder:(0,a.__)("Search customer username","woocommerce"),remove:(0,a.__)("Remove customer username filter","woocommerce"),rule:(0,a.__)("Select a customer username filter match","woocommerce"),title:(0,a.__)("<title>Username</title> <rule/> <filter />","woocommerce"),filter:(0,a.__)("Select customer username","woocommerce")},rules:[{value:"includes",label:(0,a._x)("Includes","customer usernames","woocommerce")},{value:"excludes",label:(0,a._x)("Excludes","customer usernames","woocommerce")}],input:{component:"Search",type:"usernames",getLabels:s.wd}},order:{labels:{add:(0,a.__)("Order #","woocommerce"),placeholder:(0,a.__)("Search order number","woocommerce"),remove:(0,a.__)("Remove order number filter","woocommerce"),rule:(0,a.__)("Select an order number filter match","woocommerce"),title:(0,a.__)("<title>Order #</title> <rule/> <filter/>","woocommerce"),filter:(0,a.__)("Select order number","woocommerce")},rules:[{value:"includes",label:(0,a._x)("Includes","order numbers","woocommerce")},{value:"excludes",label:(0,a._x)("Excludes","order numbers","woocommerce")}],input:{component:"Search",type:"orders",getLabels:async e=>{const t=e.split(",");return await t.map((e=>({id:e,label:"#"+e})))}}},ip_address:{labels:{add:(0,a.__)("IP Address","woocommerce"),placeholder:(0,a.__)("Search IP address","woocommerce"),remove:(0,a.__)("Remove IP address filter","woocommerce"),rule:(0,a.__)("Select an IP address filter match","woocommerce"),title:(0,a.__)("<title>IP Address</title> <rule/> <filter/>","woocommerce"),filter:(0,a.__)("Select IP address","woocommerce")},rules:[{value:"includes",label:(0,a._x)("Includes","IP addresses","woocommerce")},{value:"excludes",label:(0,a._x)("Excludes","IP addresses","woocommerce")}],input:{component:"Search",type:"downloadIps",getLabels:async e=>{const t=e.split(",");return await t.map((e=>({id:e,label:e})))}}}}})},47939:(e,t,r)=>{r.r(t),r.d(t,{default:()=>A});var a=r(86087),o=r(80170),s=r(27723),n=r(47143),l=r(66087),i=r(76154),c=r.n(i),d=r(98846),m=r(96476),u=r(43577),p=r(15703),_=r(40314),y=r(77374),h=r(94111),g=r(97605),f=r(56109),w=r(39793);class b extends a.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,s.__)("Date","woocommerce"),key:"date",defaultSort:!0,required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,s.__)("Product title","woocommerce"),key:"product",isSortable:!0,required:!0},{label:(0,s.__)("File name","woocommerce"),key:"file_name"},{label:(0,s.__)("Order #","woocommerce"),screenReaderLabel:(0,s.__)("Order Number","woocommerce"),key:"order_number"},{label:(0,s.__)("Username","woocommerce"),key:"user_id"},{label:(0,s.__)("IP","woocommerce"),key:"ip_address"}]}getRowsContent(e){const{query:t}=this.props,r=(0,m.getPersistedQuery)(t),a=(0,f.Qk)("dateFormat",y.defaultTableDateFormat);return(0,l.map)(e,(e=>{const{_embedded:t,date:o,file_name:n,file_path:l,ip_address:i,order_id:c,order_number:u,product_id:_,username:y}=e,{code:h,name:g}=t.product[0];let f,b;if("woocommerce_rest_product_invalid_id"===h)f=(0,s.__)("(Deleted)","woocommerce"),b=(0,s.__)("(Deleted)","woocommerce");else{const e=(0,m.getNewPath)(r,"/analytics/products",{filter:"single_product",products:_});f=(0,w.jsx)(d.Link,{href:e,type:"wc-admin",children:g}),b=g}return[{display:(0,w.jsx)(d.Date,{date:o,visibleFormat:a}),value:o},{display:f,value:b},{display:(0,w.jsx)(d.Link,{href:l,type:"external",children:n}),value:n},{display:(0,w.jsx)(d.Link,{href:(0,p.getAdminLink)(`post.php?post=${c}&action=edit`),type:"wp-admin",children:u}),value:u},{display:y,value:y},{display:i,value:i}]}))}getSummary(e){const{download_count:t=0}=e,{query:r,defaultDateRange:a}=this.props,o=(0,y.getCurrentDates)(r,a),n=c()(o.primary.after),l=c()(o.primary.before).diff(n,"days")+1,i=this.context.getCurrencyConfig();return[{label:(0,s._n)("day","days",l,"woocommerce"),value:(0,u.formatValue)(i,"number",l)},{label:(0,s._n)("Download","Downloads",t,"woocommerce"),value:(0,u.formatValue)(i,"number",t)}]}render(){const{query:e,filters:t,advancedFilters:r}=this.props;return(0,w.jsx)(g.A,{endpoint:"downloads",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["download_count"],query:e,tableQuery:{_embed:!0},title:(0,s.__)("Downloads","woocommerce"),columnPrefsKey:"downloads_report_columns",filters:t,advancedFilters:r})}}b.contextType=h.CurrencyContext;const v=(0,n.withSelect)((e=>{const{woocommerce_default_date_range:t}=e(_.settingsStore).getSetting("wc_admin","wcAdminSettings");return{defaultDateRange:t}}))(b);var C=r(95272),D=r(55737),x=r(68224),S=r(88711);class A extends a.Component{render(){const{query:e,path:t}=this.props;return(0,w.jsxs)(a.Fragment,{children:[(0,w.jsx)(S.A,{query:e,path:t,filters:o.uW,advancedFilters:o.Qc,report:"downloads"}),(0,w.jsx)(x.A,{charts:o.eg,endpoint:"downloads",query:e,selectedChart:(0,C.A)(e.chart,o.eg),filters:o.uW,advancedFilters:o.Qc}),(0,w.jsx)(D.A,{charts:o.eg,endpoint:"downloads",path:t,query:e,selectedChart:(0,C.A)(e.chart,o.eg),filters:o.uW,advancedFilters:o.Qc}),(0,w.jsx)(v,{query:e,filters:o.uW,advancedFilters:o.Qc})]})}}},32639:(e,t,r)=>{r.d(t,{H:()=>o});var a=r(27723);function o(e){return[e.country,e.state,e.name||(0,a.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},33958:(e,t,r)=>{r.d(t,{Dn:()=>u,U4:()=>_,aG:()=>p,b8:()=>w,jx:()=>g,p0:()=>h,wd:()=>y,xP:()=>f});var a=r(27723),o=r(93832),s=r(1455),n=r.n(s),l=r(66087),i=r(96476),c=r(40314),d=r(32639),m=r(56109);function u(e,t=l.identity){return function(r="",a){const s="function"==typeof e?e(a):e,l=(0,i.getIdsFromQuery)(r);if(l.length<1)return Promise.resolve([]);const c={include:l.join(","),per_page:l.length};return n()({path:(0,o.addQueryArgs)(s,c)}).then((e=>e.map(t)))}}u(c.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const p=u(c.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),_=u(c.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),y=u(c.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),h=u(c.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),g=u(c.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,d.H)(e)})));function f({attributes:e,name:t}){const r=(0,m.Qk)("variationTitleAttributesSeparator"," - ");if(t&&t.indexOf(r)>-1)return t;const o=(e||[]).map((({name:e,option:t})=>(t||(e=e.charAt(0).toUpperCase()+e.slice(1),t=(0,a.sprintf)((0,a.__)("Any %s","woocommerce"),e)),t))).join(", ");return o?t+r+o:t}const w=u((({products:e})=>e?c.NAMESPACE+`/products/${e}/variations`:c.NAMESPACE+"/variations"),(e=>({key:e.id,label:f(e)})))},95272:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(66087);function o(e,t=[]){return(0,a.find)(t,{key:e})||t[0]}}}]);