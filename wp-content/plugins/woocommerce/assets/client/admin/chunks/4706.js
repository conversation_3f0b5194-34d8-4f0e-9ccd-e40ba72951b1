"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4706],{74706:(e,t,a)=>{a.r(t),a.d(t,{ProductsApp:()=>Re});var i=a(86087),s=a(71628),n=a(43656),r=a(35434);const{lock:l,unlock:o}=(0,r.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site");var c=a(97961),m=a(40314),u=a(51157),d=a(25434),p=a(43112),_=a(3582),g=a(27723),v=a(47143),f=a(17697),b=a.n(f),h=a(56427),y=a(77892),E=a(29706),w=a(98639),N=a(82022),k=a(27200),x=a(22150);const A="grid",S="table",T="list",P="is",C={[S]:{layout:{primaryField:"name",styles:{name:{maxWidth:300}}}},[A]:{layout:{mediaField:"featured-image",primaryField:"name"}},[T]:{layout:{primaryField:"name",mediaField:"featured-image"}}},L={type:S,search:"",filters:[],page:1,perPage:20,sort:{field:"date",direction:"desc"},fields:["name","sku","status","date"],layout:C[T].layout};function I({postType:e}){const t=(0,v.useSelect)((t=>{const{getPostType:a}=t(_.store),i=a(e);return i?.labels}),[e]);return(0,i.useMemo)((()=>[{title:t?.all_items||(0,g.__)("All items","woocommerce"),slug:"all",icon:y.A,view:{...L}},{title:(0,g.__)("Published","woocommerce"),slug:"published",icon:E.A,view:{...L,filters:[{field:"status",operator:P,value:"publish"}]}},{title:(0,g.__)("Scheduled","woocommerce"),slug:"future",icon:w.A,view:{...L,filters:[{field:"status",operator:P,value:"future"}]}},{title:(0,g.__)("Drafts","woocommerce"),slug:"drafts",icon:N.A,view:{...L,filters:[{field:"status",operator:P,value:"draft"}]}},{title:(0,g.__)("Private","woocommerce"),slug:"private",icon:k.A,view:{...L,filters:[{field:"status",operator:P,value:"private"}]}},{title:(0,g.__)("Trash","woocommerce"),slug:"trash",icon:x.A,view:{...L,type:S,layout:C[S].layout,filters:[{field:"status",operator:P,value:"trash"}]}}]),[t])}const F=[{value:"draft",label:(0,g.__)("Draft","woocommerce")},{value:"future",label:(0,g.__)("Scheduled","woocommerce")},{value:"private",label:(0,g.__)("Private","woocommerce")},{value:"publish",label:(0,g.__)("Published","woocommerce")},{value:"trash",label:(0,g.__)("Trash","woocommerce")}],R=[{id:"name",label:(0,g.__)("Name","woocommerce"),enableHiding:!1,type:"text",render:function({item:e}){return(0,i.createElement)(i.Fragment,null,e.name)}},{id:"sku",label:(0,g.__)("SKU","woocommerce"),enableHiding:!1,enableSorting:!1,render:({item:e})=>(0,i.createElement)(i.Fragment,null,e.sku)},{id:"date",label:(0,g.__)("Date","woocommerce"),render:({item:e})=>(0,i.createElement)("time",null,e.date_created)},{label:(0,g.__)("Status","woocommerce"),id:"status",getValue:({item:e})=>{var t;return null!==(t=F.find((({value:t})=>t===e.status))?.label)&&void 0!==t?t:e.status},elements:F,filterBy:{operators:[P]},enableSorting:!1}];var H=a(62978);const{useHistory:D,useLocation:B}=o(s.privateApis),U=({postType:e})=>{const t=D(),a=B();return(0,i.useMemo)((()=>({id:"edit-product",label:(0,g.__)("Edit","woocommerce"),isPrimary:!0,icon:H.A,supportsBulk:!0,isEligible:e=>"trash"!==e.status,callback(i){const s=i[0];t.push({...a.params,postId:s.id,postType:e,quickEdit:!0})}})),[t,a.params])},V=(0,i.createContext)(null);function M({children:e}){const[t,a]=(0,i.useState)(!1);return(0,i.createElement)(V.Provider,{value:{showNewNavigation:t,setShowNewNavigation:a}},e)}function q(){const e=(0,i.useContext)(V);if(e){const{showNewNavigation:t,setShowNewNavigation:a}=e;return[t,a]}return[!1,()=>{}]}const{NavigableRegion:z,usePostActions:j}=o(n.privateApis),{useHistory:W,useLocation:O}=o(s.privateApis),G=25,K=[],Q=(e,t)=>e.find((({slug:e})=>e===t))?.view;function J(e){return e.id.toString()}function X({subTitle:e,className:t,hideTitleFromUI:a=!1}){const[s,n]=q(),r=W(),l=O(),{postId:o,quickEdit:f=!1,postType:y="product",isCustom:E,activeView:w="all"}=l.params,[N,k]=(0,i.useState)([o]),[x,A]=function(e){const{params:{activeView:t="all",isCustom:a="false",layout:s}}=O(),n=W(),r=I({postType:e}),[l,o]=(0,i.useState)((()=>{var e;const a=null!==(e=Q(r,t))&&void 0!==e?e:{type:null!=s?s:T},i=null!=s?s:a.type;return{...a,type:i}})),c=(0,i.useCallback)((e=>{const{params:t}=n.getLocationWithParams();(e.type!==T||t?.layout)&&e.type!==t?.layout&&n.push({...t,layout:e.type}),o(e)}),[n]);return(0,i.useEffect)((()=>{o((e=>({...e,type:null!=s?s:T})))}),[s]),(0,i.useEffect)((()=>{const e=Q(r,t);if(e){const t=null!=s?s:e.type;o({...e,type:t})}}),[t,a,s,r]),[l,c,c]}(y),S=(0,i.useMemo)((()=>{const e={};x.filters?.forEach((t=>{"status"===t.field&&(e.status=Array.isArray(t.value)?t.value.join(","):t.value)}));const t="name"===x.sort?.field?"title":x.sort?.field;return{per_page:x.perPage,page:x.page,order:x.sort?.direction,orderby:t,search:x.search,...e}}),[x]),P=(0,i.useCallback)((e=>{k(e),r.push({...l.params,postId:e.join(",")})}),[r,l.params]),{records:L,totalCount:F,isLoading:H}=(0,v.useSelect)((e=>{const{getProducts:t,getProductsTotalCount:a,isResolving:i}=e(m.productsStore);return{records:t(S),totalCount:a(S),isLoading:i("getProducts",[S])}}),[S]),D=(0,i.useMemo)((()=>({totalItems:null!=F?F:0,totalPages:Math.ceil((null!=F?F:0)/(x.perPage||G))})),[F,x.perPage]),{labels:B,canCreateRecord:V}=(0,v.useSelect)((e=>{const{getPostType:t,canUser:a}=e(_.store),i=t(y);return{labels:i?.labels,canCreateRecord:a("create",{kind:"postType",name:y})}}),[y]),M=j({postType:y,context:"list"}),X=U({postType:y}),Y=(0,i.useMemo)((()=>[X,...M]),[M,X]),Z=b()("edit-site-page",t);return(0,i.createElement)(z,{className:Z,ariaLabel:(0,g.__)("Products","woocommerce")},(0,i.createElement)("div",{className:"edit-site-page-content"},!a&&(0,i.createElement)(h.__experimentalVStack,{className:"edit-site-page-header",as:"header",spacing:0},(0,i.createElement)(h.__experimentalHStack,{className:"edit-site-page-header__page-title"},(0,i.createElement)(h.__experimentalHeading,{as:"h2",level:3,weight:500,className:"edit-site-page-header__title",truncate:!0},(0,g.__)("Products","woocommerce")),(0,i.createElement)(h.FlexItem,{className:"edit-site-page-header__actions"},B?.add_new_item&&V&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)(h.Button,{variant:"primary",disabled:!0,__next40pxDefaultSize:!0},B.add_new_item)))),e&&(0,i.createElement)(h.__experimentalText,{variant:"muted",as:"p",className:"edit-site-page-header__sub-title"},e)),(0,i.createElement)(c.A,{key:w+E,paginationInfo:D,fields:R,data:L||K,isLoading:H,view:x,actions:Y,onChangeView:A,onChangeSelection:P,getItemId:J,selection:N,defaultLayouts:C,header:(0,i.createElement)(i.Fragment,null,(0,i.createElement)(h.Button,{size:"compact",icon:s?u.A:d.A,label:(0,g.__)("Toggle navigation","woocommerce"),onClick:()=>{n(!s)}}),(0,i.createElement)(h.Button,{size:"compact",isPressed:f,icon:p.A,label:(0,g.__)("Toggle details panel","woocommerce"),onClick:()=>{r.push({...l.params,quickEdit:!f||void 0})}}))})))}var Y=a(75288),Z=a(92428);const{NavigableRegion:$}=o(n.privateApis),ee={type:"panel",fields:["name","status"]};function te({subTitle:e,actions:t,className:a,hideTitleFromUI:s=!0,postType:n,postId:r=""}){const l=b()("edit-product-page",a,{"is-empty":!r}),o=(0,i.useMemo)((()=>r.split(",")),[r]),{initialEdits:c}=(0,v.useSelect)((e=>({initialEdits:1===o.length?e(m.productsStore).getProduct(Number.parseInt(o[0],10)):null})),[n,o]),[u,d]=(0,i.useState)({}),p=(0,i.useMemo)((()=>({...c,...u})),[c,u]),_=!(0,Y.q)(p,R,ee);return(0,i.createElement)($,{className:l,ariaLabel:(0,g.__)("Product Edit","woocommerce")},(0,i.createElement)("div",{className:"edit-product-content"},!s&&(0,i.createElement)(h.__experimentalVStack,{className:"edit-site-page-header",as:"header",spacing:0},(0,i.createElement)(h.__experimentalHStack,{className:"edit-site-page-header__page-title"},(0,i.createElement)(h.__experimentalHeading,{as:"h2",level:3,weight:500,className:"edit-site-page-header__title",truncate:!0},(0,g.__)("Product Edit","woocommerce")),(0,i.createElement)(h.FlexItem,{className:"edit-site-page-header__actions"},t)),e&&(0,i.createElement)(h.__experimentalText,{variant:"muted",as:"p",className:"edit-site-page-header__sub-title"},e)),!r&&(0,i.createElement)("p",null,(0,g.__)("Select a product to edit","woocommerce")),r&&(0,i.createElement)(h.__experimentalVStack,{spacing:4,as:"form",onSubmit:async e=>{e.preventDefault(),(0,Y.q)(p,R,ee)&&d({})}},(0,i.createElement)(Z.A,{data:p,fields:R,form:ee,onChange:d}),(0,i.createElement)(h.FlexItem,null,(0,i.createElement)(h.Button,{variant:"primary",type:"submit",accessibleWhenDisabled:!0,disabled:_,__next40pxDefaultSize:!0},(0,g.__)("Update","woocommerce"))))))}var ae=a(93832),ie=a(45197),se=a(5520),ne=a(29543),re=a(40738);const{useHistory:le}=o(s.privateApis);function oe({className:e,icon:t,withChevron:a=!1,suffix:s,uid:n,params:r,onClick:l,children:o,...c}){const m=le();return(0,i.createElement)(h.__experimentalItem,{className:b()("edit-site-sidebar-navigation-item",{"with-suffix":!a&&s},e),onClick:function(e){l?l(e):r&&(e.preventDefault(),m.push(r))},id:n,...c},(0,i.createElement)(h.__experimentalHStack,{justify:"flex-start"},t&&(0,i.createElement)(se.A,{style:{fill:"currentcolor"},icon:t,size:24}),(0,i.createElement)(h.FlexBlock,null,o),a&&(0,i.createElement)(se.A,{icon:(0,g.isRTL)()?ne.A:re.A,className:"edit-site-sidebar-navigation-item__drilldown-indicator",size:24}),!a&&s))}const{useHistory:ce,useLocation:me}=o(s.privateApis);function ue({title:e,slug:t,customViewId:a,type:s,icon:n,isActive:r,isCustom:l,suffix:o}){const{params:{postType:c,page:m}}=me(),u=n||ie.Ad.find((e=>e.type===s))?.icon;let d=l?a:t;"all"===d&&(d=void 0);const p=function(e,t,a=!1){const i=ce(),s=(0,ae.getQueryArgs)(window.location.href),n=(0,ae.removeQueryArgs)(window.location.href,...Object.keys(s));return{href:(0,ae.addQueryArgs)(n,e),onClick:function(s){s?.preventDefault(),a?i.replace(e,t):i.push(e,t)}}}({page:m,postType:c,layout:s,activeView:d,isCustom:l?"true":void 0});return(0,i.createElement)(h.__experimentalHStack,{justify:"flex-start",className:b()("edit-site-sidebar-dataviews-dataview-item",{"is-selected":r})},(0,i.createElement)(oe,{icon:u,...p,"aria-current":r?"true":void 0},e),o)}const{useLocation:de}=o(s.privateApis);function pe(){const{params:{postType:e="product",activeView:t="all",isCustom:a="false"}}=de(),s=I({postType:e});if(!e)return null;const n="true"===a;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(h.__experimentalItemGroup,null,s.map((e=>(0,i.createElement)(ue,{key:e.slug,slug:e.slug,title:e.title,icon:e.icon,type:e.view.type,isActive:!n&&e.slug===t,isCustom:!1})))))}var _e=a(31496),ge=a(89505);function ve(e){return(0,i.createElement)(h.Button,{...e,className:b()("edit-site-sidebar-button",e.className)})}const{useHistory:fe,useLocation:be}=o(s.privateApis);function he({isRoot:e,title:t,actions:a,meta:s,content:n,footer:r,description:l,backPath:c}){const{dashboardLink:m,dashboardLinkText:u}=(0,v.useSelect)((e=>{const{getSettings:t}=o(e("core/edit-site"));return{dashboardLink:t().__experimentalDashboardLink,dashboardLinkText:t().__experimentalDashboardLinkText}}),[]),d=be(),p=fe(),_=null!=c?c:d.state?.backPath,f=(0,g.isRTL)()?_e.A:ge.A;return(0,i.createElement)(i.Fragment,null,(0,i.createElement)(h.__experimentalVStack,{className:b()("edit-site-sidebar-navigation-screen__main",{"has-footer":!!r}),spacing:0,justify:"flex-start"},(0,i.createElement)(h.__experimentalHStack,{spacing:3,alignment:"flex-start",className:"edit-site-sidebar-navigation-screen__title-icon"},!e&&(0,i.createElement)(ve,{onClick:()=>{p.push(_)},icon:f,label:(0,g.__)("Back","woocommerce"),showTooltip:!1}),e&&(0,i.createElement)(ve,{icon:f,label:u||(0,g.__)("Go to the Dashboard","woocommerce"),href:m||"index.php"}),(0,i.createElement)(h.__experimentalHeading,{as:"h1",className:"edit-site-sidebar-navigation-screen__title",color:"#e0e0e0",level:1},t),a&&(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__actions"},a)),s&&(0,i.createElement)(i.Fragment,null,(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__meta"},s)),(0,i.createElement)("div",{className:"edit-site-sidebar-navigation-screen__content"},l&&(0,i.createElement)("p",{className:"edit-site-sidebar-navigation-screen__description"},l),n)),r&&(0,i.createElement)("footer",{className:"edit-site-sidebar-navigation-screen__footer"},r))}const{useLocation:ye}=o(s.privateApis);var Ee=a(29491);function we({children:e}){const t=(0,i.useRef)(null);return(0,i.createElement)("div",{ref:t,className:"edit-site-sidebar__screen-wrapper"},e)}function Ne({routeKey:e,children:t}){return(0,i.createElement)("div",{className:"edit-site-sidebar__content"},(0,i.createElement)(we,{key:e},t))}var ke=a(18537),xe=a(30267);const Ae=function({className:e}){const{isRequestingSite:t,siteIconUrl:a}=(0,v.useSelect)((e=>{const{getEntityRecord:t}=e(_.store),a=t("root","__unstableBase");return{isRequestingSite:!a,siteIconUrl:a?.site_icon_url}}),[]);if(t&&!a)return(0,i.createElement)("div",{className:"edit-site-site-icon__image"});const s=a?(0,i.createElement)("img",{className:"edit-site-site-icon__image",alt:(0,g.__)("Site Icon","woocommerce"),src:a}):(0,i.createElement)(h.Icon,{className:"edit-site-site-icon__icon",icon:xe.A,size:48});return(0,i.createElement)("div",{className:b()(e,"edit-site-site-icon")},s)},Se=(0,i.memo)((0,i.forwardRef)((({isTransparent:e},t)=>{const{dashboardLink:a,homeUrl:s,siteTitle:n}=(0,v.useSelect)((e=>{const{getSettings:t}=o(e("core/edit-site")),{getSite:a,getUnstableBase:i}=e(_.store),s=a(),n=i();return{dashboardLink:t().__experimentalDashboardLink||"index.php",homeUrl:n?.home,siteTitle:!s?.title&&s?.url?(0,ae.filterURLForDisplay)(s?.url):s?.title}}),[]);return(0,i.createElement)("div",{className:"edit-site-site-hub"},(0,i.createElement)(h.__experimentalHStack,{justify:"flex-start",spacing:"0"},(0,i.createElement)("div",{className:b()("edit-site-site-hub__view-mode-toggle-container",{"has-transparent-background":e})},(0,i.createElement)(h.Button,{ref:t,href:a,label:(0,g.__)("Go to the Dashboard","woocommerce"),className:"edit-site-layout__view-mode-toggle",style:{transform:"scale(0.5)",borderRadius:4}},(0,i.createElement)(Ae,{className:"edit-site-layout__view-mode-toggle-icon"}))),(0,i.createElement)(h.__experimentalHStack,null,(0,i.createElement)("div",{className:"edit-site-site-hub__title"},(0,i.createElement)(h.Button,{variant:"link",href:s,target:"_blank"},n&&(0,ke.decodeEntities)(n),(0,i.createElement)(h.VisuallyHidden,{as:"span"},(0,g.__)("(opens in a new tab)","woocommerce")))))))}))),{NavigableRegion:Te}=o(n.privateApis),Pe=.3;function Ce({route:e,showNewNavigation:t=!1}){const[a]=(0,Ee.useResizeObserver)(),s=(0,i.useRef)(null),r=(0,Ee.useViewportMatch)("medium","<"),l=(0,Ee.useReducedMotion)(),{key:o,areas:c,widths:m}=e;return(0,i.createElement)(i.Fragment,null,a,(0,i.createElement)("div",{className:"edit-site-layout"},(0,i.createElement)("div",{className:"edit-site-layout__content"},(!r||!c.mobile)&&t&&(0,i.createElement)(Te,{ariaLabel:(0,g.__)("Navigation","woocommerce"),className:"edit-site-layout__sidebar-region"},(0,i.createElement)(h.__unstableAnimatePresence,null,(0,i.createElement)(h.__unstableMotion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{type:"tween",duration:l||r?0:Pe,ease:"easeOut"},className:"edit-site-layout__sidebar"},(0,i.createElement)(Se,{ref:s,isTransparent:!1}),(0,i.createElement)(Ne,{routeKey:o},c.sidebar)))),(0,i.createElement)(n.EditorSnackbars,null),!r&&c.content&&(0,i.createElement)("div",{className:"edit-site-layout__area",style:{maxWidth:m?.content}},c.content),!r&&c.edit&&(0,i.createElement)("div",{className:"edit-site-layout__area",style:{maxWidth:m?.edit}},c.edit))))}const{RouterProvider:Le}=o(s.privateApis),{GlobalStylesProvider:Ie}=o(n.privateApis);function Fe(){const[e]=q();e?document.body.classList.add("is-fullscreen-mode"):document.body.classList.remove("is-fullscreen-mode");const t=function(){const{params:e={}}=ye(),{postType:t="product",layout:a="table",canvas:s,quickEdit:n,postId:r}=e;if(["product"].includes(t)){const e="list"===a||!a;return{key:"products-list",areas:{sidebar:(0,i.createElement)(he,{title:"Products",isRoot:!0,content:(0,i.createElement)(pe,null)}),content:(0,i.createElement)(X,null),preview:!1,mobile:(0,i.createElement)(X,{postType:t}),edit:n&&(0,i.createElement)(te,{postType:t,postId:r})},widths:{edit:n&&!e?380:void 0}}}return{key:"default",areas:{preview:!1,mobile:"edit"===s}}}();return(0,i.createElement)(Ce,{route:t,showNewNavigation:e})}function Re(){return(0,i.createElement)(M,null,(0,i.createElement)(Ie,null,(0,i.createElement)(n.UnsavedChangesWarning,null),(0,i.createElement)(Le,null,(0,i.createElement)(Fe,null))))}}}]);