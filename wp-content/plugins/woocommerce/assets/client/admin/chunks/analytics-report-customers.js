"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3256],{2592:(e,o,r)=>{r.r(o),r.d(o,{default:()=>x});var t=r(86087),l=r(27723),a=r(18537),c=r(52619),m=r(47143),s=r(40314),i=r(33958);const n=(0,c.applyFilters)("woocommerce_admin_customers_report_filters",[{label:(0,l.__)("Show","woocommerce"),staticParams:["paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,l.__)("All Customers","woocommerce"),value:"all"},{label:(0,l.__)("Single Customer","woocommerce"),value:"select_customer",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_customer",chartMode:"item-comparison",path:["select_customer"],settings:{type:"customers",param:"customers",getLabels:i.wd,labels:{placeholder:(0,l.__)("Type to search for a customer","woocommerce"),button:(0,l.__)("Single Customer","woocommerce")}}}]},{label:(0,l.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),u=(0,c.applyFilters)("woocommerce_admin_customers_report_advanced_filters",{title:(0,l._x)("Customers match <select/> filters","A sentence describing filters for Customers. See screen shot for context: https://cloudup.com/cCsm3GeXJbE","woocommerce"),filters:{name:{labels:{add:(0,l.__)("Name","woocommerce"),placeholder:(0,l.__)("Search","woocommerce"),remove:(0,l.__)("Remove customer name filter","woocommerce"),rule:(0,l.__)("Select a customer name filter match","woocommerce"),title:(0,l.__)("<title>Name</title> <rule/> <filter/>","woocommerce"),filter:(0,l.__)("Select customer name","woocommerce")},rules:[{value:"includes",label:(0,l._x)("Includes","customer names","woocommerce")},{value:"excludes",label:(0,l._x)("Excludes","customer names","woocommerce")}],input:{component:"Search",type:"customers",getLabels:(0,i.Dn)(s.NAMESPACE+"/customers",(e=>({id:e.id,label:e.name})))}},country:{labels:{add:(0,l.__)("Country / Region","woocommerce"),placeholder:(0,l.__)("Search","woocommerce"),remove:(0,l.__)("Remove country / region filter","woocommerce"),rule:(0,l.__)("Select a country / region filter match","woocommerce"),title:(0,l.__)("<title>Country / Region</title> <rule/> <filter/>","woocommerce"),filter:(0,l.__)("Select country / region","woocommerce")},rules:[{value:"includes",label:(0,l._x)("Includes","countries","woocommerce")},{value:"excludes",label:(0,l._x)("Excludes","countries","woocommerce")}],input:{component:"Search",type:"countries",getLabels:async e=>{const o=(await(0,m.resolveSelect)(s.COUNTRIES_STORE_NAME).getCountries()).map((e=>({key:e.code,label:(0,a.decodeEntities)(e.name)}))),r=e.split(",");return await o.filter((e=>r.includes(e.key)))}}},username:{labels:{add:(0,l.__)("Username","woocommerce"),placeholder:(0,l.__)("Search customer username","woocommerce"),remove:(0,l.__)("Remove customer username filter","woocommerce"),rule:(0,l.__)("Select a customer username filter match","woocommerce"),title:(0,l.__)("<title>Username</title> <rule/> <filter/>","woocommerce"),filter:(0,l.__)("Select customer username","woocommerce")},rules:[{value:"includes",label:(0,l._x)("Includes","customer usernames","woocommerce")},{value:"excludes",label:(0,l._x)("Excludes","customer usernames","woocommerce")}],input:{component:"Search",type:"usernames",getLabels:i.wd}},email:{labels:{add:(0,l.__)("Email","woocommerce"),placeholder:(0,l.__)("Search customer email","woocommerce"),remove:(0,l.__)("Remove customer email filter","woocommerce"),rule:(0,l.__)("Select a customer email filter match","woocommerce"),title:(0,l.__)("<title>Email</title> <rule/> <filter/>","woocommerce"),filter:(0,l.__)("Select customer email","woocommerce")},rules:[{value:"includes",label:(0,l._x)("Includes","customer emails","woocommerce")},{value:"excludes",label:(0,l._x)("Excludes","customer emails","woocommerce")}],input:{component:"Search",type:"emails",getLabels:(0,i.Dn)(s.NAMESPACE+"/customers",(e=>({id:e.id,label:e.email})))}},orders_count:{labels:{add:(0,l.__)("No. of Orders","woocommerce"),remove:(0,l.__)("Remove order filter","woocommerce"),rule:(0,l.__)("Select an order count filter match","woocommerce"),title:(0,l.__)("<title>No. of Orders</title> <rule/> <filter/>","woocommerce")},rules:[{value:"max",label:(0,l._x)("Less Than","number of orders","woocommerce")},{value:"min",label:(0,l._x)("More Than","number of orders","woocommerce")},{value:"between",label:(0,l._x)("Between","number of orders","woocommerce")}],input:{component:"Number"}},total_spend:{labels:{add:(0,l.__)("Total Spend","woocommerce"),remove:(0,l.__)("Remove total spend filter","woocommerce"),rule:(0,l.__)("Select a total spend filter match","woocommerce"),title:(0,l.__)("<title>Total Spend</title> <rule/> <filter/>","woocommerce")},rules:[{value:"max",label:(0,l._x)("Less Than","total spend by customer","woocommerce")},{value:"min",label:(0,l._x)("More Than","total spend by customer","woocommerce")},{value:"between",label:(0,l._x)("Between","total spend by customer","woocommerce")}],input:{component:"Currency"}},avg_order_value:{labels:{add:(0,l.__)("AOV","woocommerce"),remove:(0,l.__)("Remove average order value filter","woocommerce"),rule:(0,l.__)("Select an average order value filter match","woocommerce"),title:(0,l.__)("<title>AOV</title> <rule/> <filter/>","woocommerce")},rules:[{value:"max",label:(0,l._x)("Less Than","average order value of customer","woocommerce")},{value:"min",label:(0,l._x)("More Than","average order value of customer","woocommerce")},{value:"between",label:(0,l._x)("Between","average order value of customer","woocommerce")}],input:{component:"Currency"}},registered:{labels:{add:(0,l.__)("Registered","woocommerce"),remove:(0,l.__)("Remove registered filter","woocommerce"),rule:(0,l.__)("Select a registered filter match","woocommerce"),title:(0,l.__)("<title>Registered</title> <rule/> <filter/>","woocommerce"),filter:(0,l.__)("Select registered date","woocommerce")},rules:[{value:"before",label:(0,l._x)("Before","date","woocommerce")},{value:"after",label:(0,l._x)("After","date","woocommerce")},{value:"between",label:(0,l._x)("Between","date","woocommerce")}],input:{component:"Date"}},last_active:{labels:{add:(0,l.__)("Last active","woocommerce"),remove:(0,l.__)("Remove last active filter","woocommerce"),rule:(0,l.__)("Select a last active filter match","woocommerce"),title:(0,l.__)("<title>Last active</title> <rule/> <filter/>","woocommerce"),filter:(0,l.__)("Select registered date","woocommerce")},rules:[{value:"before",label:(0,l._x)("Before","date","woocommerce")},{value:"after",label:(0,l._x)("After","date","woocommerce")},{value:"between",label:(0,l._x)("Between","date","woocommerce")}],input:{component:"Date"}}}});var _=r(56427),d=r(98846),w=r(43577),v=r(15703),p=r(77374),b=r(94111),f=r(97605),y=r(56109),g=r(39793);const h=function({isRequesting:e,query:o,filters:r,advancedFilters:a}){const c=(0,t.useContext)(b.CurrencyContext),{countries:i,loadingCountries:n}=(0,m.useSelect)((e=>{const{getCountries:o,hasFinishedResolution:r}=e(s.COUNTRIES_STORE_NAME);return{countries:o(),loadingCountries:!r("getCountries")}}));return(0,g.jsx)(f.A,{endpoint:"customers",getHeadersContent:()=>[{label:(0,l.__)("Name","woocommerce"),key:"name",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,l.__)("Username","woocommerce"),key:"username",hiddenByDefault:!0},{label:(0,l.__)("Last active","woocommerce"),key:"date_last_active",defaultSort:!0,isSortable:!0},{label:(0,l.__)("Date registered","woocommerce"),key:"date_registered",isSortable:!0},{label:(0,l.__)("Email","woocommerce"),key:"email"},{label:(0,l.__)("Orders","woocommerce"),key:"orders_count",isSortable:!0,isNumeric:!0},{label:(0,l.__)("Total spend","woocommerce"),key:"total_spend",isSortable:!0,isNumeric:!0},{label:(0,l.__)("AOV","woocommerce"),screenReaderLabel:(0,l.__)("Average order value","woocommerce"),key:"avg_order_value",isNumeric:!0},{label:(0,l.__)("Country / Region","woocommerce"),key:"country",isSortable:!0},{label:(0,l.__)("City","woocommerce"),key:"city",hiddenByDefault:!0,isSortable:!0},{label:(0,l.__)("Region","woocommerce"),key:"state",hiddenByDefault:!0,isSortable:!0},{label:(0,l.__)("Postal code","woocommerce"),key:"postcode",hiddenByDefault:!0,isSortable:!0}],getRowsContent:e=>{const o=(0,y.Qk)("dateFormat",p.defaultTableDateFormat),{formatAmount:r,formatDecimal:a,getCurrencyConfig:m}=c;return e?.map((e=>{const{avg_order_value:c,date_last_active:s,date_registered:n,email:u,name:p,user_id:b,orders_count:f,username:y,total_spend:h,postcode:S,city:x,state:A,country:C}=e,E=void 0!==i[k=C]?i[k]:null;var k;const R=""!==p?.trim()?p:(0,g.jsx)(d.Pill,{children:(0,l.__)("Guest","woocommerce")}),N=b?(0,g.jsx)(d.Link,{href:(0,v.getAdminLink)("user-edit.php?user_id="+b),type:"wp-admin",children:p}):R,T=s?(0,g.jsx)(d.Date,{date:s,visibleFormat:o}):"—",F=n?(0,g.jsx)(d.Date,{date:n,visibleFormat:o}):"—",j=(0,g.jsxs)(t.Fragment,{children:[(0,g.jsx)(_.Tooltip,{text:E,children:(0,g.jsx)("span",{"aria-hidden":"true",children:C})}),(0,g.jsx)("span",{className:"screen-reader-text",children:E})]});return[{display:N,value:p},{display:y,value:y},{display:T,value:s},{display:F,value:n},{display:(0,g.jsx)("a",{href:"mailto:"+u,children:u}),value:u},{display:(0,w.formatValue)(m(),"number",f),value:f},{display:r(h),value:a(h)},{display:r(c),value:a(c)},{display:j,value:C},{display:x,value:x},{display:A,value:A},{display:S,value:S}]}))},getSummary:e=>{const{customers_count:o=0,avg_orders_count:r=0,avg_total_spend:t=0,avg_avg_order_value:a=0}=e,{formatAmount:m,getCurrencyConfig:s}=c,i=s();return[{label:(0,l._n)("customer","customers",o,"woocommerce"),value:(0,w.formatValue)(i,"number",o)},{label:(0,l._n)("Average order","Average orders",r,"woocommerce"),value:(0,w.formatValue)(i,"number",r)},{label:(0,l.__)("Average lifetime spend","woocommerce"),value:m(t)},{label:(0,l.__)("Average order value","woocommerce"),value:m(a)}]},summaryFields:["customers_count","avg_orders_count","avg_total_spend","avg_avg_order_value"],isRequesting:e||n,itemIdField:"id",query:o,labels:{placeholder:(0,l.__)("Search by customer name","woocommerce")},searchBy:"customers",title:(0,l.__)("Customers","woocommerce"),columnPrefsKey:"customers_report_columns",filters:r,advancedFilters:a})};var S=r(88711);class x extends t.Component{render(){const{isRequesting:e,query:o,path:r}=this.props,l={orderby:"date_last_active",order:"desc",...o};return(0,g.jsxs)(t.Fragment,{children:[(0,g.jsx)(S.A,{query:o,path:r,filters:n,showDatePicker:!1,advancedFilters:u,report:"customers"}),(0,g.jsx)(h,{isRequesting:e,query:l,filters:n,advancedFilters:u})]})}}},32639:(e,o,r)=>{r.d(o,{H:()=>l});var t=r(27723);function l(e){return[e.country,e.state,e.name||(0,t.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},33958:(e,o,r)=>{r.d(o,{Dn:()=>_,U4:()=>w,aG:()=>d,b8:()=>y,jx:()=>b,p0:()=>p,wd:()=>v,xP:()=>f});var t=r(27723),l=r(93832),a=r(1455),c=r.n(a),m=r(66087),s=r(96476),i=r(40314),n=r(32639),u=r(56109);function _(e,o=m.identity){return function(r="",t){const a="function"==typeof e?e(t):e,m=(0,s.getIdsFromQuery)(r);if(m.length<1)return Promise.resolve([]);const i={include:m.join(","),per_page:m.length};return c()({path:(0,l.addQueryArgs)(a,i)}).then((e=>e.map(o)))}}_(i.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const d=_(i.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),w=_(i.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),v=_(i.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),p=_(i.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),b=_(i.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,n.H)(e)})));function f({attributes:e,name:o}){const r=(0,u.Qk)("variationTitleAttributesSeparator"," - ");if(o&&o.indexOf(r)>-1)return o;const l=(e||[]).map((({name:e,option:o})=>(o||(e=e.charAt(0).toUpperCase()+e.slice(1),o=(0,t.sprintf)((0,t.__)("Any %s","woocommerce"),e)),o))).join(", ");return l?o+r+l:o}const y=_((({products:e})=>e?i.NAMESPACE+`/products/${e}/variations`:i.NAMESPACE+"/variations"),(e=>({key:e.id,label:f(e)})))}}]);