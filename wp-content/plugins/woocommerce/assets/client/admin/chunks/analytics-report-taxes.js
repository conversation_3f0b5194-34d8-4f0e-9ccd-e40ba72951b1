"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[8068],{55737:(e,t,r)=>{r.d(t,{A:()=>x});var a=r(27723),o=r(86087),s=r(29491),n=r(38443),i=r(47143),l=r(66087),c=r(98846),m=r(40314),d=r(77374),u=r(94111),p=r(96476);function y(e,t,r={}){if(!e||0===e.length)return null;const a=e.slice(0),o=a.pop();if(o.showFilters(t,r)){const e=(0,p.flattenFilters)(o.filters),r=t[o.param]||o.defaultValue||"all";return(0,l.find)(e,{value:r})}return y(a,t,r)}function g(e){return t=>(0,n.format)(e,t)}function h(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,r=e.data.intervals[e.data.intervals.length-1].date_end;if((0,d.containsLeapYear)(t,r))return!0}return!1}var _=r(39793);class f extends o.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,l.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map((function(e){const r={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const a=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;r[e.segment_id]={label:a,value:e.subtotals[t.key]||0}}})),{date:(0,n.format)("Y-m-d\\TH:i:s",e.date_start),...r}}))}getTimeChartData(){const{query:e,primaryData:t,secondaryData:r,selectedChart:a,defaultDateRange:o}=this.props,s=(0,d.getIntervalForQuery)(e,o),{primary:i,secondary:l}=(0,d.getCurrentDates)(e,o);return function(e,t,r,a,o,s,i){const l=h(e),c=h(t),m=[...e.data.intervals],u=[...t.data.intervals],p=[];for(let e=0;e<m.length;e++){const t=m[e],y=(0,n.format)("Y-m-d\\TH:i:s",t.date_start),g=`${r.label} (${r.range})`,h=t.date_start,_=t.subtotals[s]||0,f=u[e],x=`${a.label} (${a.range})`;let b=(0,d.getPreviousDate)(t.date_start,r.after,a.after,o,i).format("YYYY-MM-DD HH:mm:ss"),v=f&&f.subtotals[s]||0;if("day"===i&&l&&!c&&u?.[e]){const r=new Date(t.date_start),a=new Date(u[e].date_start);(0,d.isLeapYear)(r.getFullYear())&&1===r.getMonth()&&29===r.getDate()&&2===a.getMonth()&&1===a.getDate()&&(b="-",v=0,u.splice(e,0,u[e]))}p.push({date:y,primary:{label:g,labelDate:h,value:_},secondary:{label:x,labelDate:b,value:v}})}return p}(t,r,i,l,e.compare,a.key,s)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,l.get)(e,["data","totals",r.key],null),secondary:(0,l.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,o){const{emptySearchResults:s,filterParam:n,interactiveLegend:i,itemsLabel:l,legendPosition:u,path:p,query:y,selectedChart:h,showHeaderControls:f,primaryData:x,defaultDateRange:b}=this.props,v=(0,d.getIntervalForQuery)(y,b),C=(0,d.getAllowedIntervalsForQuery)(y,b),w=(0,d.getDateFormatsForInterval)(v,x.data.intervals.length,{type:"php"}),D=s?(0,a.__)("No data for the current search","woocommerce"):(0,a.__)("No data for the selected date range","woocommerce"),{formatAmount:S,getCurrencyConfig:A}=this.context;return(0,_.jsx)(c.Chart,{allowedIntervals:C,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:D,filterParam:n,interactiveLegend:i,interval:v,isRequesting:t,itemsLabel:l,legendPosition:u,legendTotals:o,mode:e,path:p,query:y,screenReaderFormat:g(w.screenReaderFormat),showHeaderControls:f,title:h.label,tooltipLabelFormat:g(w.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&h.label||null,tooltipValueFormat:(0,m.getTooltipValueFormat)(h.type,S),chartType:(0,d.getChartTypeForQuery)(y),valueType:h.type,xFormat:g(w.xFormat),x2Format:g(w.x2Format),currency:A()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,_.jsx)(c.AnalyticsError,{});const r=e||t.isRequesting,a=this.getItemChartData();return this.renderChart("item-comparison",r,a)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:r}=this.props;if(!t||t.isError||r.isError)return(0,_.jsx)(c.AnalyticsError,{});const a=e||t.isRequesting||r.isRequesting,o=this.getTimeChartData(),s=this.getTimeChartTotals();return this.renderChart("time-comparison",a,o,s)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}f.contextType=u.CurrencyContext,f.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const x=(0,s.compose)((0,i.withSelect)(((e,t)=>{const{charts:r,endpoint:a,filters:o,isRequesting:s,limitProperties:n,query:i,advancedFilters:c}=t,d=n||[a],u=y(o,i),p=(0,l.get)(u,["settings","param"]),g=t.mode||function(e,t){if(e&&t){const r=(0,l.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,l.get)(e,["chartMode"])}return null}(u,i)||"time-comparison",{woocommerce_default_date_range:h}=e(m.settingsStore).getSetting("wc_admin","wcAdminSettings"),_={mode:g,filterParam:p,defaultDateRange:h};if(s)return _;const f=d.some((e=>i[e]&&i[e].length));if(i.search&&!f)return{..._,emptySearchResults:!0};const x=e(m.reportsStore),b=r&&r.map((e=>e.key)),v=(0,m.getReportChartData)({endpoint:a,dataType:"primary",query:i,selector:x,limitBy:d,filters:o,advancedFilters:c,defaultDateRange:h,fields:b});if("item-comparison"===g)return{..._,primaryData:v};const C=(0,m.getReportChartData)({endpoint:a,dataType:"secondary",query:i,selector:x,limitBy:d,filters:o,advancedFilters:c,defaultDateRange:h,fields:b});return{..._,primaryData:v,secondaryData:C}})))(f)},68224:(e,t,r)=>{r.d(t,{A:()=>h});var a=r(27723),o=r(86087),s=r(29491),n=r(47143),i=r(96476),l=r(98846),c=r(43577),m=r(40314),d=r(77374),u=r(83306),p=r(94111),y=r(39793);class g extends o.Component{formatVal(e,t){const{formatAmount:r,getCurrencyConfig:a}=this.context;return"currency"===t?r(e):(0,c.formatValue)(a(),t,e)}getValues(e,t){const{emptySearchResults:r,summaryData:a}=this.props,{totals:o}=a,s=o.primary?o.primary[e]:0,n=o.secondary?o.secondary[e]:0,i=r?0:s,l=r?0:n;return{delta:(0,c.calculateDelta)(i,l),prevValue:this.formatVal(l,t),value:this.formatVal(i,t)}}render(){const{charts:e,query:t,selectedChart:r,summaryData:o,endpoint:s,report:n,defaultDateRange:c}=this.props,{isError:m,isRequesting:p}=o;if(m)return(0,y.jsx)(l.AnalyticsError,{});if(p)return(0,y.jsx)(l.SummaryListPlaceholder,{numberOfItems:e.length});const{compare:g}=(0,d.getDateParamsFromQuery)(t,c);return(0,y.jsx)(l.SummaryList,{children:({onToggle:t})=>e.map((e=>{const{key:o,order:c,orderby:m,label:d,type:p,isReverseTrend:h,labelTooltipText:_}=e,f={chart:o};m&&(f.orderby=m),c&&(f.order=c);const x=(0,i.getNewPath)(f),b=r.key===o,{delta:v,prevValue:C,value:w}=this.getValues(o,p);return(0,y.jsx)(l.SummaryNumber,{delta:v,href:x,label:d,reverseTrend:h,prevLabel:"previous_period"===g?(0,a.__)("Previous period:","woocommerce"):(0,a.__)("Previous year:","woocommerce"),prevValue:C,selected:b,value:w,labelTooltipText:_,onLinkClickCallback:()=>{t&&t(),(0,u.recordEvent)("analytics_chart_tab_click",{report:n||s,key:o})}},o)}))})}}g.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},g.contextType=p.CurrencyContext;const h=(0,s.compose)((0,n.withSelect)(((e,t)=>{const{charts:r,endpoint:a,limitProperties:o,query:s,filters:n,advancedFilters:i}=t,l=o||[a],c=l.some((e=>s[e]&&s[e].length));if(s.search&&!c)return{emptySearchResults:!0};const d=r&&r.map((e=>e.key)),{woocommerce_default_date_range:u}=e(m.settingsStore).getSetting("wc_admin","wcAdminSettings");return{summaryData:(0,m.getSummaryNumbers)({endpoint:a,query:s,select:e,limitBy:l,filters:n,advancedFilters:i,defaultDateRange:u,fields:d}),defaultDateRange:u}})))(g)},9622:(e,t,r)=>{r.d(t,{Qc:()=>u,eg:()=>d,uW:()=>y});var a=r(27723),o=r(52619),s=r(27752),n=r(40314),i=r(47143),l=r(33958),c=r(32639);const{addCesSurveyForAnalytics:m}=(0,i.dispatch)(s.STORE_KEY),d=(0,o.applyFilters)("woocommerce_admin_taxes_report_charts",[{key:"total_tax",label:(0,a.__)("Total tax","woocommerce"),order:"desc",orderby:"total_tax",type:"currency"},{key:"order_tax",label:(0,a.__)("Order tax","woocommerce"),order:"desc",orderby:"order_tax",type:"currency"},{key:"shipping_tax",label:(0,a.__)("Shipping tax","woocommerce"),order:"desc",orderby:"shipping_tax",type:"currency"},{key:"orders_count",label:(0,a.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),u=(0,o.applyFilters)("woocommerce_admin_taxes_report_advanced_filters",{filters:{},title:(0,a._x)("Taxes match <select/> filters","A sentence describing filters for Taxes. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),p=[{label:(0,a.__)("All taxes","woocommerce"),value:"all"},{label:(0,a.__)("Comparison","woocommerce"),value:"compare-taxes",chartMode:"item-comparison",settings:{type:"taxes",param:"taxes",getLabels:(0,l.Dn)(n.NAMESPACE+"/taxes",(e=>({id:e.id,key:e.id,label:(0,c.H)(e)}))),labels:{helpText:(0,a.__)("Check at least two tax codes below to compare","woocommerce"),placeholder:(0,a.__)("Search for tax codes to compare","woocommerce"),title:(0,a.__)("Compare Tax Codes","woocommerce"),update:(0,a.__)("Compare","woocommerce")},onClick:m}}];Object.keys(u.filters).length&&p.push({label:(0,a.__)("Advanced filters","woocommerce"),value:"advanced"});const y=(0,o.applyFilters)("woocommerce_admin_taxes_report_filters",[{label:(0,a.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:p}])},15415:(e,t,r)=>{r.r(t),r.d(t,{default:()=>v});var a=r(86087),o=r(27723),s=r(9622),n=r(95272),i=r(55737),l=r(68224),c=r(66087),m=r(98846),d=r(96476),u=r(43577),p=r(94111),y=r(32639),g=r(97605),h=r(39793);class _ extends a.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,o.__)("Tax code","woocommerce"),key:"tax_code",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,o.__)("Rate","woocommerce"),key:"rate",isSortable:!0,isNumeric:!0},{label:(0,o.__)("Total tax","woocommerce"),key:"total_tax",isSortable:!0},{label:(0,o.__)("Order tax","woocommerce"),key:"order_tax",isSortable:!0},{label:(0,o.__)("Shipping tax","woocommerce"),key:"shipping_tax",isSortable:!0},{label:(0,o.__)("Orders","woocommerce"),key:"orders_count",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0}]}getRowsContent(e){const{render:t,formatDecimal:r,getCurrencyConfig:a}=this.context;return(0,c.map)(e,(e=>{const{query:o}=this.props,{order_tax:s,orders_count:n,tax_rate:i,tax_rate_id:l,total_tax:c,shipping_tax:p}=e,g=(0,y.H)(e),_=(0,d.getPersistedQuery)(o),f=(0,d.getNewPath)(_,"/analytics/orders",{filter:"advanced",tax_rate_includes:l});return[{display:(0,h.jsx)(m.Link,{href:f,type:"wc-admin",children:g}),value:g},{display:i.toFixed(2)+"%",value:i},{display:t(c),value:r(c)},{display:t(s),value:r(s)},{display:t(p),value:r(p)},{display:(0,u.formatValue)(a(),"number",n),value:n}]}))}getSummary(e,t=0){const{tax_codes:r=0,total_tax:a=0,order_tax:s=0,shipping_tax:n=0,orders_count:i=0}=e,{formatAmount:l,getCurrencyConfig:c}=this.context,m=c();return[{label:(0,o._n)("tax","taxes",t,"woocommerce"),value:(0,u.formatValue)(m,"number",t)},{label:(0,o._n)("distinct code","distinct codes",r,"woocommerce"),value:(0,u.formatValue)(m,"number",r)},{label:(0,o.__)("total tax","woocommerce"),value:l(a)},{label:(0,o.__)("order tax","woocommerce"),value:l(s)},{label:(0,o.__)("shipping tax","woocommerce"),value:l(n)},{label:(0,o._n)("order","orders",i,"woocommerce"),value:(0,u.formatValue)(m,"number",i)}]}render(){const{advancedFilters:e,filters:t,isRequesting:r,query:a}=this.props;return(0,h.jsx)(g.A,{compareBy:"taxes",endpoint:"taxes",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["tax_codes","total_tax","order_tax","shipping_tax","orders_count"],isRequesting:r,itemIdField:"tax_rate_id",query:a,searchBy:"taxes",tableQuery:{orderby:a.orderby||"tax_rate_id"},title:(0,o.__)("Taxes","woocommerce"),columnPrefsKey:"taxes_report_columns",filters:t,advancedFilters:e})}}_.contextType=p.CurrencyContext;const f=_;var x=r(88711);class b extends a.Component{getChartMeta(){const{query:e}=this.props,t="compare-taxes"===e.filter?"item-comparison":"time-comparison";return{itemsLabel:(0,o.__)("%d taxes","woocommerce"),mode:t}}render(){const{isRequesting:e,query:t,path:r}=this.props,{mode:o,itemsLabel:c}=this.getChartMeta(),m={...t};return"item-comparison"===o&&(m.segmentby="tax_rate_id"),(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(x.A,{query:t,path:r,filters:s.uW,advancedFilters:s.Qc,report:"taxes"}),(0,h.jsx)(l.A,{charts:s.eg,endpoint:"taxes",query:m,selectedChart:(0,n.A)(t.chart,s.eg),filters:s.uW,advancedFilters:s.Qc}),(0,h.jsx)(i.A,{charts:s.eg,filters:s.uW,advancedFilters:s.Qc,mode:o,endpoint:"taxes",query:m,path:r,isRequesting:e,itemsLabel:c,selectedChart:(0,n.A)(t.chart,s.eg)}),(0,h.jsx)(f,{isRequesting:e,query:t,filters:s.uW,advancedFilters:s.Qc})]})}}const v=b},32639:(e,t,r)=>{r.d(t,{H:()=>o});var a=r(27723);function o(e){return[e.country,e.state,e.name||(0,a.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},33958:(e,t,r)=>{r.d(t,{Dn:()=>u,U4:()=>y,aG:()=>p,b8:()=>x,jx:()=>_,p0:()=>h,wd:()=>g,xP:()=>f});var a=r(27723),o=r(93832),s=r(1455),n=r.n(s),i=r(66087),l=r(96476),c=r(40314),m=r(32639),d=r(56109);function u(e,t=i.identity){return function(r="",a){const s="function"==typeof e?e(a):e,i=(0,l.getIdsFromQuery)(r);if(i.length<1)return Promise.resolve([]);const c={include:i.join(","),per_page:i.length};return n()({path:(0,o.addQueryArgs)(s,c)}).then((e=>e.map(t)))}}u(c.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const p=u(c.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),y=u(c.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),g=u(c.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),h=u(c.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),_=u(c.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,m.H)(e)})));function f({attributes:e,name:t}){const r=(0,d.Qk)("variationTitleAttributesSeparator"," - ");if(t&&t.indexOf(r)>-1)return t;const o=(e||[]).map((({name:e,option:t})=>(t||(e=e.charAt(0).toUpperCase()+e.slice(1),t=(0,a.sprintf)((0,a.__)("Any %s","woocommerce"),e)),t))).join(", ");return o?t+r+o:t}const x=u((({products:e})=>e?c.NAMESPACE+`/products/${e}/variations`:c.NAMESPACE+"/variations"),(e=>({key:e.id,label:f(e)})))},95272:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(66087);function o(e,t=[]){return(0,a.find)(t,{key:e})||t[0]}}}]);