"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3678],{61288:(e,t,o)=>{o.d(t,{A:()=>c});var s=o(86087),a=o(17697),n=o.n(a),r=o(56427);function c(e){return(0,s.createElement)(r.<PERSON>,{...e,className:n()("edit-site-sidebar-button",e.className)})}},59783:(e,t,o)=>{o.d(t,{A:()=>h});var s=o(86087),a=o(17697),n=o.n(a),r=o(56427),c=o(27723);const i=(0,s.forwardRef)((function({icon:e,size:t=24,...o},a){return(0,s.cloneElement)(e,{width:t,height:t,...o,ref:a})}));var l=o(51609),m=o(5573);const u=(0,l.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(m.Path,{d:"m13.1 16-3.4-4 3.4-4 1.1 1-2.6 3 2.6 3-1.1 1z"})),d=(0,l.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,l.createElement)(m.Path,{d:"M10.8622 8.04053L14.2805 12.0286L10.8622 16.0167L9.72327 15.0405L12.3049 12.0286L9.72327 9.01672L10.8622 8.04053Z"}));function h({className:e,icon:t,withChevron:o=!1,suffix:a,children:l,...m}){return(0,s.createElement)(r.__experimentalItem,{className:n()("edit-site-sidebar-navigation-item",{"with-suffix":!o&&a},e),...m},(0,s.createElement)(r.__experimentalHStack,{justify:"flex-start"},t&&(0,s.createElement)(i,{style:{fill:"currentcolor"},icon:t,size:24}),(0,s.createElement)(r.FlexBlock,null,l),o&&(0,s.createElement)(i,{icon:(0,c.isRTL)()?u:d,className:"edit-site-sidebar-navigation-item__drilldown-indicator",size:24}),!o&&a))}},23522:(e,t,o)=>{o.d(t,{A:()=>h});var s=o(86087),a=o(17697),n=o.n(a),r=o(47143),c=o(56427),i=o(27723),l=o(51609),m=o(5573);const u=(0,l.createElement)(m.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,l.createElement)(m.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"}));var d=o(3582);const h=function({className:e}){const{isRequestingSite:t,siteIconUrl:o}=(0,r.useSelect)((e=>{const{getEntityRecord:t}=e(d.store),o=t("root","__unstableBase",void 0);return{isRequestingSite:!o,siteIconUrl:o?.site_icon_url}}),[]);if(t&&!o)return(0,s.createElement)("div",{className:"edit-site-site-icon__image"});const a=o?(0,s.createElement)("img",{className:"edit-site-site-icon__image",alt:(0,i.__)("Site Icon"),src:o}):(0,s.createElement)(c.Icon,{className:"edit-site-site-icon__icon",size:"48px",icon:u});return(0,s.createElement)("div",{className:n()(e,"edit-site-site-icon")},a)}},44412:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{d:"M4 20h8v-1.5H4V20zM18.9 3.5c-.6-.6-1.5-.6-2.1 0l-7.2 7.2c-.4-.1-.7 0-1.1.1-.5.2-1.5.7-1.9 2.2-.4 1.7-.8 2.2-1.1 2.7-.1.1-.2.3-.3.4l-.6 1.1H6c2 0 3.4-.4 4.7-1.4.8-.6 1.2-1.4 1.3-2.3 0-.3 0-.5-.1-.7L19 5.7c.5-.6.5-1.6-.1-2.2zM9.7 14.7c-.7.5-1.5.8-2.4 1 .2-.5.5-1.2.8-2.3.2-.6.4-1 .8-1.1.5-.1 1 .1 1.3.3.2.2.3.5.2.8 0 .3-.1.9-.7 1.3z"})})},46445:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},45260:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},48214:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},35166:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{fillRule:"evenodd",d:"M5.5 9.5v-2h13v2h-13zm0 3v4h13v-4h-13zM4 7a1 1 0 011-1h14a1 1 0 011 1v10a1 1 0 01-1 1H5a1 1 0 01-1-1V7z",clipRule:"evenodd"})})},20273:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"})})},24652:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{d:"M3 6.75C3 5.784 3.784 5 4.75 5H15V7.313l.05.027 5.056 2.73.394.212v3.468a1.75 1.75 0 01-1.75 1.75h-.012a2.5 2.5 0 11-4.975 0H9.737a2.5 2.5 0 11-4.975 0H3V6.75zM13.5 14V6.5H4.75a.25.25 0 00-.25.25V14h.965a2.493 2.493 0 011.785-.75c.7 0 1.332.287 1.785.75H13.5zm4.535 0h.715a.25.25 0 00.25-.25v-2.573l-4-2.16v4.568a2.487 2.487 0 011.25-.335c.7 0 1.332.287 1.785.75zM6.282 15.5a1.002 1.002 0 00.968 1.25 1 1 0 10-.968-1.25zm9 0a1 1 0 101.937.498 1 1 0 00-1.938-.498z"})})},33484:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(5573),a=o(39793);const n=(0,a.jsx)(s.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,a.jsx)(s.Path,{d:"M4.75 4a.75.75 0 0 0-.75.75v7.826c0 .**********.53l6.72 6.716a2.313 2.313 0 0 0 3.276-.001l5.61-5.611-.531-.53.532.528a2.315 2.315 0 0 0 0-3.264L13.104 4.22a.75.75 0 0 0-.53-.22H4.75ZM19 12.576a.815.815 0 0 1-.236.574l-5.61 5.611a.814.814 0 0 1-1.153 0L5.5 12.264V5.5h6.763l6.5 6.502a.816.816 0 0 1 .237.574ZM8.75 9.75a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z"})})},5751:(e,t,o)=>{o.d(t,{A:()=>a});var s=o(39793);const a=()=>(0,s.jsxs)("svg",{width:"91",height:"24",viewBox:"0 0 91 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"wc-icon wc-icon__woo-logo new-branding",children:[(0,s.jsx)("path",{d:"M79.0537 0C72.2755 0 67.0874 5.10851 67.0874 12C67.0874 18.8915 72.2755 24 79.0537 24C85.832 24 91.0002 18.8915 91.0002 12C91.0002 5.10851 85.7923 0 79.0537 0ZM79.0537 16.6277C76.5094 16.6277 74.7602 14.6644 74.7602 12C74.7602 9.33555 76.4895 7.37228 79.0537 7.37228C81.6179 7.37228 83.3473 9.33555 83.3473 12C83.3473 14.6644 81.5981 16.6277 79.0537 16.6277Z",fill:"#873DFF"}),(0,s.jsx)("path",{d:"M53.7285 0C46.9503 0 41.7622 5.10851 41.7622 12C41.7622 18.8915 46.9701 24 53.7285 24C60.4869 24 65.675 18.8915 65.675 12C65.675 5.10851 60.4671 0 53.7285 0ZM53.7285 16.6277C51.1842 16.6277 49.435 14.6644 49.435 12C49.435 9.33555 51.1643 7.37228 53.7285 7.37228C56.2928 7.37228 58.0221 9.33555 58.0221 12C58.0221 14.6644 56.2928 16.6277 53.7285 16.6277Z",fill:"#873DFF"}),(0,s.jsx)("path",{d:"M11.688 24C14.3715 24 16.5183 22.6577 18.1483 19.5726L21.7461 12.7813V18.5509C21.7461 21.9365 23.9327 24 27.3317 24C29.9556 24 31.8837 22.798 33.792 19.5726L42.1207 5.44908C43.9494 2.36394 42.6574 0 38.6421 0C36.4953 0 35.1039 0.721201 33.8516 3.08514L28.107 13.9232V4.28714C28.107 1.40234 26.7553 0 24.2308 0C22.2629 0 20.6926 0.861435 19.4602 3.26544L14.0535 13.9032V4.38731C14.0535 1.30217 12.8012 0 9.74004 0H3.53822C1.19266 0 0 1.10184 0 3.14524C0 5.18864 1.23241 6.33054 3.53822 6.33054H6.08255V18.5309C6.10243 21.9365 8.3486 24 11.688 24Z",fill:"#873DFF"})]})},11495:(e,t,o)=>{o.r(t),o.d(t,{default:()=>we});var s=o(84437),a=o(51609),n=o.n(a),r=o(4921),c=o(24060),i=o(51513),l=o(53187),m=o(97233),u=o(71529),d=o(4316),h=o(96476),_=o(40314),p=o(47143),g=o(83306),w=o(1455),v=o.n(w),y=o(86087),x=o(27723),S=o(59783),k=o(56427),C=o(45260),E=o(46445),b=o(61288),j=o(39793);const f=({title:e,description:t,footer:o,children:s})=>{const a=(0,x.isRTL)()?C.A:E.A;return(0,j.jsxs)(j.Fragment,{children:[(0,j.jsxs)(k.__experimentalVStack,{className:(0,r.A)("woocommerce-edit-site-sidebar-navigation-screen__main",{"has-footer":!!o}),spacing:0,justify:"flex-start",children:[(0,j.jsxs)(k.__experimentalHStack,{spacing:4,alignment:"flex-start",className:"woocommerce-edit-site-sidebar-navigation-screen__title-icon",children:[(0,j.jsx)(b.A,{onClick:(c=e,n().isValidElement(c)&&"function"==typeof c.props.onClick?e.props.onClick:void 0),icon:a,label:(0,x.__)("Back","woocommerce"),showTooltip:!1}),(0,j.jsx)(k.__experimentalHeading,{className:"woocommerce-edit-site-sidebar-navigation-screen__title",level:1,as:"h1",children:e})]}),(0,j.jsxs)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen__content",children:[t&&(0,j.jsx)("p",{className:"woocommerce-edit-site-sidebar-navigation-screen__description",children:t}),s]})]}),o&&(0,j.jsx)("footer",{className:"woocommerce-edit-site-sidebar-navigation-screen__footer",children:o})]});var c};var L=o(72744),N=o(20273),T=o(24652),P=o(44412),A=o(35166),O=o(33484);const M=L.A,R={tax:N.A,shipping:T.A,"customize-store":P.A,payments:A.A,"woocommerce-payments":A.A,products:O.A};var H=o(64155),B=o(52619),I=o(85816),D=o(15703);const{getWithExpiry:U,setWithExpiry:W}=(0,I.createStorageUtils)("lys_recently_actioned_tasks",604800),z=({task:e,classNames:t})=>(0,j.jsx)(S.A,{className:(0,r.A)(e.id,"is-complete",t),icon:M,disabled:!0,children:e.title}),V=({task:e,classNames:t,onClick:o})=>(0,j.jsx)(S.A,{className:(0,r.A)(e.id,t),icon:R[e.id],withChevron:!0,onClick:o,children:e.title}),F=(e,t)=>{let o=(0,h.getHistory)().location;const s=(0,h.getHistory)().listen((({action:s,location:a})=>{if("POP"===s){const s=new URLSearchParams(o.search),n=new URLSearchParams(a.search);s.get(e)!==n.get(e)&&(o=a,t({type:"EXTERNAL_URL_UPDATE"}))}o=a}));return()=>{s()}},G=e=>{const t=(0,h.getQuery)(),o=Object.entries(e).reduce(((e,[o,s])=>(t[o]!==s&&(e[o]=s),e)),{});Object.keys(o).length>0&&(0,h.updateQueryString)(o)};let Y=null;const Q=async()=>{if(null!==Y)return Y;const e=await v()({path:"/wc-admin/launch-your-store/survey-completed"});return Y=e,Y},K=(0,l.Sx)((async()=>{const[e,t,o]=await Promise.all([Q(),(0,p.resolveSelect)(_.onboardingStore).getTaskListsByIds(["setup","extended"]),(0,p.resolveSelect)(_.pluginsStore).getActivePlugins()]);return{surveyCompleted:e,tasklists:t,activePlugins:o}})),$=(0,l.SP)((({sendBack:e})=>F("sidebar",e))),Z=async({url:e})=>{try{const t=await fetch(e,{method:"GET",credentials:"omit",cache:"no-store"});if(!t.ok)throw new Error(`Failed to fetch ${e}`);const o=await t.text();return!!(new DOMParser).parseFromString(o,"text/html").querySelector('meta[name="woo-coming-soon-page"]')}catch(t){throw new Error(`Error fetching ${e}: ${t}`)}},q=async()=>{const e=await(0,p.resolveSelect)(_.settingsStore).getSettings("wc_admin"),t=[];return e?.shopUrl&&t.push(Z({url:e.shopUrl})),e?.siteUrl&&t.push(Z({url:e.siteUrl})),(await Promise.all(t)).some((e=>e))},J=(0,m.mj)({types:{},actions:{showLaunchStoreSuccessPage:(0,u.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_LAUNCH_STORE_SUCCESS"}),showLaunchStorePendingCache:(0,u.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_LAUNCH_STORE_PENDING_CACHE"}),showLoadingPage:(0,u.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_LOADING"}),updateQueryParams:(e,t)=>{G(t)},taskClicked:({event:e})=>{"TASK_CLICKED"===e.type&&function(e){var t;const o=null!==(t=U())&&void 0!==t?t:[];W([...o,e.task.id]),window.sessionStorage.setItem("lysWaiting","yes");const{setWithExpiry:s}=(0,I.accessTaskReferralStorage)({taskId:e.task.id,referralLifetime:86400});s({referrer:"launch-your-store",returnUrl:(0,D.getAdminLink)("admin.php?page=wc-admin&path=/launch-your-store")}),(0,g.recordEvent)("launch_your_store_hub_task_clicked",{task:e.task.id}),e.task.actionUrl?(0,h.navigateTo)({url:e.task.actionUrl}):(0,h.navigateTo)({url:(0,h.getNewPath)({task:e.task.id},"/",{})})}(e)},openWcAdminUrl:({event:e})=>{"OPEN_WC_ADMIN_URL"===e.type&&(0,h.navigateTo)({url:e.url})},windowHistoryBack:()=>{window.history.back()},recordStoreLaunchAttempt:(0,u.a)({launchStoreAttemptTimestamp:({context:e})=>{const t=e.tasklist?.fullLysTaskList.length||0,o=e.tasklist?.tasks.filter((e=>!e.isComplete)).map((e=>e.id))||[],s=e.tasklist?.fullLysTaskList.filter((e=>e.isComplete)).map((e=>e.id))||[],a=s.filter((t=>e.tasklist?.recentlyActionedTasks.includes(t)));return(0,g.recordEvent)("launch_your_store_hub_store_launch_attempted",{tasks_total_count:t,tasks_completed:s,tasks_completed_count:s.length,tasks_completed_in_lys:a,tasks_completed_in_lys_count:a.length,incomplete_tasks:o,incomplete_tasks_count:o.length,delete_test_orders:e.removeTestOrders||!1}),performance.now()}}),recordStoreLaunchResults:({context:e},{success:t})=>{((e,t)=>{(0,g.recordEvent)("launch_your_store_hub_store_launch_results",{success:t,duration:(0,c.D8)(performance.now()-e)})})(e.launchStoreAttemptTimestamp||0,t)},recordStoreLaunchCachedContentDetected:()=>{(0,g.recordEvent)("launch_your_store_hub_store_launch_cached_content_detected")}},guards:{hasSidebarLocation:(e,{sidebar:t})=>{const{sidebar:o}=(0,h.getQuery)();return!!o&&o===t},hasWooPayments:({context:e})=>!!e.hasWooPayments,siteIsShowingCachedContent:({context:e})=>!!e.siteIsShowingCachedContent},actors:{sidebarQueryParamListener:$,getTasklist:(0,l.Sx)((async()=>{var e;const t=(0,B.applyFilters)("woocommerce_launch_your_store_tasklist_whitelist",["products","customize-store","woocommerce-payments","payments","shipping","tax"]),o=await(0,p.resolveSelect)(_.onboardingStore).getTaskListsByIds(["setup"]),s=null!==(e=U())&&void 0!==e?e:[],a=o[0].tasks.filter((e=>t.includes(e.id)&&(!e.isComplete||s.includes(e.id))));return{...o[0],tasks:a,recentlyActionedTasks:s,fullLysTaskList:o[0].tasks.filter((e=>t.includes(e.id)))}})),getTestOrderCount:(0,l.Sx)((async()=>(await v()({path:"/wc-admin/launch-your-store/woopayments/test-orders/count",method:"GET"})).count)),getSiteCachedStatus:(0,l.Sx)(q),updateLaunchStoreOptions:(0,l.Sx)((async()=>{const e=await(0,p.dispatch)(_.optionsStore).updateOptions({woocommerce_coming_soon:"no"});if(e.success)return e;throw new Error(JSON.stringify(e))})),deleteTestOrders:(0,l.Sx)((async({input:e})=>e.removeTestOrders?await v()({path:"/wc-admin/launch-your-store/woopayments/test-orders",method:"DELETE"}):null)),fetchCongratsData:K,getWooPaymentsStatus:(0,l.Sx)((async()=>{if(!1===window?.wcSettings?.admin?.plugins?.activePlugins.includes("woocommerce-payments"))return!1;const e=(await(0,p.resolveSelect)(_.paymentGatewaysStore).getPaymentGateways()).filter((e=>e.enabled));return 1===e.length&&"woocommerce_payments"===e[0].id}))}}).createMachine({id:"sidebar",initial:"navigate",context:({input:e})=>({externalUrl:null,testOrderCount:0,mainContentMachineRef:e.mainContentMachineRef}),invoke:{id:"sidebarQueryParamListener",src:"sidebarQueryParamListener"},states:{navigate:{always:[{guard:{type:"hasSidebarLocation",params:{sidebar:"hub"}},target:"launchYourStoreHub"},{guard:{type:"hasSidebarLocation",params:{sidebar:"launch-success"}},target:"storeLaunchSuccessful"},{target:"launchYourStoreHub"}]},launchYourStoreHub:{initial:"preLaunchYourStoreHub",states:{preLaunchYourStoreHub:{entry:[(0,d.P)("fetchCongratsData",{id:"prefetch-congrats-data "})],invoke:{src:"getTasklist",onDone:{actions:(0,u.a)({tasklist:({event:e})=>e.output}),target:"checkWooPayments"}}},checkWooPayments:{invoke:{src:"getWooPaymentsStatus",onDone:{actions:(0,u.a)({hasWooPayments:({event:e})=>e.output}),target:"maybeCountTestOrders"},onError:{target:"maybeCountTestOrders"}}},maybeCountTestOrders:{always:[{guard:"hasWooPayments",target:"countTestOrders"},{target:"launchYourStoreHub"}]},countTestOrders:{invoke:{src:"getTestOrderCount",onDone:{actions:(0,u.a)({testOrderCount:({event:e})=>e.output}),target:"launchYourStoreHub"},onError:{target:"launchYourStoreHub"}}},launchYourStoreHub:{id:"launchYourStoreHub",tags:"sidebar-visible",meta:{component:e=>{const{context:{tasklist:t,removeTestOrders:o,testOrderCount:s,launchStoreError:a}}=e,n=(0,j.jsx)(k.Button,{onClick:()=>{e.sendEventToSidebar({type:"POP_BROWSER_STACK"})},children:(0,x.__)("Launch Your Store","woocommerce")}),c=(0,x.__)("Ready to start selling? Before you launch your store, make sure you’ve completed these essential tasks. If you’d like to change your store visibility, go to WooCommerce | Settings | Site visibility.","woocommerce"),i=t&&!t.tasks.every((e=>e.isComplete)),[l,m]=(0,y.useState)(null==o||o),[u,d]=(0,y.useState)(!1),[h,_]=(0,y.useState)(!1),p=()=>{_(!0),e.sendEventToSidebar({type:"LAUNCH_STORE",removeTestOrders:l})};return(0,y.useEffect)((()=>{a?.message&&_(!1)}),[a?.message]),(0,j.jsxs)("div",{className:(0,r.A)("launch-store-sidebar__container",e.className),children:[(0,j.jsx)(k.__unstableMotion.div,{className:"woocommerce-edit-site-layout__header-container",animate:"view",children:(0,j.jsx)(H.b,{variants:{view:{x:0}},isTransparent:!1,className:"woocommerce-edit-site-layout__hub"})}),(0,j.jsxs)(f,{title:n,description:c,children:[(0,j.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-essential-tasks__group-header",children:(0,j.jsx)(k.__experimentalHeading,{level:2,children:(0,x.__)("Essential Tasks","woocommerce")})}),(0,j.jsxs)(k.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-essential-tasks__group",children:[t&&i&&t.tasks.map((t=>t.isComplete?(0,j.jsx)(z,{task:t},t.id):(0,j.jsx)(V,{task:t,onClick:()=>{e.sendEventToSidebar({type:"TASK_CLICKED",task:t})}},t.id))),t&&!i&&(0,j.jsx)(S.A,{className:"all-tasks-complete",icon:M,children:(0,x.__)("Fantastic job! Your store is ready to go — no pending tasks to complete.","woocommerce")})]}),s>0&&(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-test-data__group-header",children:(0,j.jsx)(k.__experimentalHeading,{level:2,children:(0,x.__)("Test data","woocommerce")})}),(0,j.jsxs)(k.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-remove-test-data__group",children:[(0,j.jsx)(k.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,x.sprintf)((0,x.__)("Remove %d test orders","woocommerce"),s),checked:l,onChange:m}),(0,j.jsx)("p",{children:(0,x.__)("Remove test orders and associated data, including analytics and transactions, once your store goes live. ","woocommerce")})]})]}),(0,j.jsxs)(k.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-launch-store-button__group",children:[a?.message&&!u&&(0,j.jsx)(k.Notice,{className:"launch-store-error-notice",isDismissible:!0,onRemove:()=>d(!0),status:"error",children:(0,y.createInterpolateElement)((0,x.__)("Oops! We encountered a problem while launching your store. <retryButton/>","woocommerce"),{retryButton:(0,j.jsx)(k.Button,{onClick:p,variant:"tertiary",children:(0,x.__)("Please try again","woocommerce")})})}),(0,j.jsx)(k.Button,{variant:"primary",onClick:p,children:h?(0,j.jsx)(k.Spinner,{}):(0,x.__)("Launch your store","woocommerce")})]})]})]})}},on:{LAUNCH_STORE:{target:"#storeLaunching"}}}}},storeLaunching:{id:"storeLaunching",initial:"launching",states:{launching:{entry:[(0,u.a)({launchStoreError:void 0}),"recordStoreLaunchAttempt"],invoke:[{src:"updateLaunchStoreOptions",onDone:{actions:[{type:"recordStoreLaunchResults",params:{success:!0}}],target:"checkingForCachedContent"},onError:{actions:[(0,u.a)({launchStoreError:({event:e})=>({message:JSON.stringify(e.error)})}),{type:"recordStoreLaunchResults",params:{success:!1}}],target:"#launchYourStoreHub"}},{src:"deleteTestOrders",input:({event:e})=>({removeTestOrders:e.removeTestOrders})}]},checkingForCachedContent:{invoke:[{src:"getSiteCachedStatus",onDone:{target:"#storeLaunchSuccessful",actions:(0,u.a)({siteIsShowingCachedContent:({event:e})=>e.output})},onError:{target:"#storeLaunchSuccessful"}}]}}},storeLaunchSuccessful:{id:"storeLaunchSuccessful",tags:"fullscreen",entry:[{type:"updateQueryParams",params:{sidebar:"launch-success",content:"launch-store-success"}},(0,u.b)((({check:e,enqueue:t})=>{if(e("siteIsShowingCachedContent"))return t({type:"showLaunchStorePendingCache"}),void t({type:"recordStoreLaunchCachedContentDetected"});t({type:"showLaunchStoreSuccessPage"})}))]}},on:{EXTERNAL_URL_UPDATE:{target:".navigate"},TASK_CLICKED:{actions:"taskClicked"},OPEN_WC_ADMIN_URL:{actions:"openWcAdminUrl"},POP_BROWSER_STACK:{actions:"windowHistoryBack"},OPEN_WC_ADMIN_URL_IN_CONTENT_AREA:{}}}),X=({children:e,className:t})=>(0,j.jsx)("div",{className:(0,r.A)("launch-your-store-layout__sidebar",t),children:e});var ee=o(98846);const te=()=>(0,j.jsx)("div",{className:"spinner-container",children:(0,j.jsx)(ee.Spinner,{})});var oe=o(29491),se=o(56109),ae=o(61208),ne=o(5751);const re={congratsScreen:({context:e})=>(v()({path:"/wc-admin/launch-your-store/update-survey-status",data:{status:"yes"},method:"POST"}).catch((()=>{})),{...e.congratsScreen,hasCompleteSurvey:!0})},ce=({activePlugins:e,allTasklists:t})=>{const o=(0,y.useMemo)((()=>(({activePlugins:e,allTasklists:t})=>{const o=[],s=(e,t)=>{o.length<3&&t&&o.push(e)},a=t.find((({id:e})=>"setup"===e))?.tasks?.reduce(((e,{id:t,isComplete:o})=>(e[t]=o||!1,e)),{}),n=t.find((({id:e})=>"extended"===e))?.tasks?.reduce(((e,{id:t,isComplete:o})=>(e[t]=o||!1,e)),{}),r=n?.marketing||!1,c=a?.payments||!1,i=n?.["get-mobile-app"]||!1,l=e.includes("mailchimp-for-woocommerce"),m={title:(0,x.__)("Promote your products","woocommerce"),description:(0,x.__)("Grow your customer base by promoting your products to millions of engaged shoppers.","woocommerce"),link:`${se.kY}admin.php?page=wc-admin&task=marketing`,linkText:(0,x.__)("Promote products","woocommerce"),trackEvent:"launch_you_store_congrats_marketing_click"},u={title:(0,x.__)("Provide more ways to pay","woocommerce"),description:(0,x.__)("Give your shoppers more ways to pay by adding additional payment methods to your store.","woocommerce"),link:`${se.kY}admin.php?page=wc-settings&tab=checkout`,linkText:(0,x.__)("Add payment methods","woocommerce"),trackEvent:"launch_you_store_congrats_payments_click"},d={title:(0,x.__)("Build customer relationships","woocommerce"),description:(0,x.__)("Keep your shoppers up to date with what’s new in your store and set up clever post-purchase automations.","woocommerce"),link:l?`${se.kY}admin.php?page=mailchimp-woocommerce`:"https://woo.com/products/mailchimp-for-woocommerce/?utm_source=launch_your_store&utm_medium=product",linkText:l?(0,x.__)("Manage Mailchimp","woocommerce"):(0,x.__)("Install Mailchimp","woocommerce"),trackEvent:"launch_you_store_congrats_mailchimp_click"},h={title:(0,x.__)("Power up your store","woocommerce"),description:(0,x.__)("Add extra features and functionality to your store with Woo extensions.","woocommerce"),link:`${se.kY}admin.php?page=wc-admin&path=%2Fextensions`,linkText:(0,x.__)("Add extensions","woocommerce"),trackEvent:"launch_you_store_congrats_extensions_click"},_={title:(0,x.__)("Manage your store on the go","woocommerce"),description:(0,x.__)("Manage your store anywhere with the free WooCommerce Mobile App.","woocommerce"),link:`${se.kY}admin.php?page=wc-admin&mobileAppModal=true`,linkText:(0,x.__)("Get the app","woocommerce"),trackEvent:"launch_you_store_congrats_mobile_app_click"},p={title:(0,x.__)("Help is on hand","woocommerce"),description:(0,x.__)("Detailed guides and our support team are always available if you’re feeling stuck or need some guidance.","woocommerce"),link:"https://woo.com/documentation/woocommerce/?utm_source=launch_your_store&utm_medium=product",linkText:(0,x.__)("Explore support resources","woocommerce"),trackEvent:"launch_you_store_congrats_external_documentation_click"};return s(m,!r),s(u,!c),s(h,!0),s(_,!i),s(d,!l),s(p,!0),s(u,!0),s(h,!0),s(p,!0),o})({activePlugins:e,allTasklists:t})),[e,t]);return(0,j.jsx)("div",{className:"woocommerce-launch-store__congrats-main-actions",children:o.map(((e,t)=>(0,j.jsx)("div",{className:"woocommerce-launch-store__congrats-action",children:(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-action__content",children:[(0,j.jsx)("h3",{children:e.title}),(0,j.jsx)("p",{children:e.description}),(0,j.jsx)(k.Button,{variant:"link",href:e.link,target:-1===e.link.indexOf(se.kY)?"_blank":"_self",onClick:()=>{(0,g.recordEvent)(e.trackEvent)},children:e.linkText})]})},t)))})};var ie=o(66087),le=o(48214),me=o(27752),ue=o(97687);const de=({hasCompleteSurvey:e,onSubmit:t})=>{const[o,s]=(0,y.useState)(null),[a,n]=(0,y.useState)(""),[r,c]=(0,y.useState)(!1),[i,l]=(0,y.useState)(!e),m=(0,ie.isInteger)(o);return(0,j.jsxs)(j.Fragment,{children:[i&&(0,j.jsx)("hr",{className:"separator"}),i&&(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-survey",children:[r?(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-thanks",children:[(0,j.jsxs)("p",{className:"thanks-copy",children:["🙌"," ",(0,x.__)("We appreciate your feedback!","woocommerce")]}),(0,j.jsx)(k.Button,{className:"close-button",label:(0,x.__)("Close","woocommerce"),icon:(0,j.jsx)(k.Icon,{icon:le.A,viewBox:"6 4 12 14"}),iconSize:14,onClick:()=>{c(!1),l(!1)}})]}):(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-section_1",children:[(0,j.jsx)("div",{className:"woocommerce-launch-store__congrats-survey__selection",children:(0,j.jsx)(me.CustomerFeedbackSimple,{label:(0,x.__)("How was the experience of launching your store?","woocommerce"),onSelect:s,selectedValue:o})}),m&&(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-survey__comment",children:[(0,j.jsx)("label",{className:"comment-label",htmlFor:"launch-your-store-comment",children:(0,y.createInterpolateElement)((0,x.__)("Why do you feel that way? <smallText>(optional)</smallText>","woocommerce"),{smallText:(0,j.jsx)("span",{className:"small-text"})})}),(0,j.jsx)(k.TextareaControl,{__nextHasNoMarginBottom:!0,id:"launch-your-store-comment","data-testid":"launch-your-store-comment",value:a,onChange:e=>{n(e)}}),(0,j.jsx)("span",{className:"privacy-text",children:(0,y.createInterpolateElement)((0,x.__)("Your feedback will be only be shared with WooCommerce and treated in accordance with our <privacyLink>privacy policy</privacyLink>.","woocommerce"),{privacyLink:(0,j.jsx)(ee.Link,{href:"https://automattic.com/privacy/",type:"external",target:"_blank",children:(0,j.jsx)(j.Fragment,{})})})})]})]}),m&&!r&&(0,j.jsx)("div",{className:"woocommerce-launch-store__congrats-section_2",children:(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-buttons",children:[(0,j.jsx)(k.Button,{className:"",variant:"tertiary",onClick:()=>{s(null)},children:(0,x.__)("Cancel","woocommerce")}),(0,j.jsx)(k.Button,{className:"",variant:"primary",onClick:()=>{(0,g.recordEvent)((0,ue.E)()?"launch_your_store_congrats_survey_click":"launch_your_store_on_core_congrats_survey_click"),t({action:"lys_experience",score:o,comments:a}),c(!0)},children:(0,x.__)("Send","woocommerce")})]})})]})]})},he=(0,l.SP)((({sendBack:e})=>F("content",e))),_e=(0,m.mj)({types:{},actions:{updateQueryParams:(e,t)=>{G(t)},assignSiteCachedStatus:(0,u.a)({siteIsShowingCachedContent:!0}),recordSurveyResults:({event:e})=>{(0,m.DT)(e,"COMPLETE_SURVEY"),(0,g.recordEvent)("launch_your_store_congrats_survey_complete",{action:e.payload.action,score:e.payload.score,comments:e.payload.comments})},recordBackToHomeClick:()=>{(0,g.recordEvent)("launch_your_store_congrats_back_to_home_click")},recordPreviewStoreClick:()=>{(0,g.recordEvent)("launch_your_store_congrats_preview_store_click")},navigateToPreview:()=>{const e=(0,D.getSetting)("homeUrl","");window.open(e,"_blank")},navigateToHome:()=>{const{invalidateResolutionForStoreSelector:e}=(0,p.dispatch)(_.onboardingStore);e("getTaskLists"),(0,h.navigateTo)({url:"/"})}},guards:{hasContentLocation:(e,{content:t})=>{const{content:o}=(0,h.getQuery)();return!!o&&o===t}},actors:{contentQueryParamListener:he,fetchCongratsData:K,getSiteCachedStatus:(0,l.Sx)(q)}}).createMachine({id:"mainContent",initial:"navigate",context:{congratsScreen:{hasLoadedCongratsData:!1,hasCompleteSurvey:!1,allTasklists:[],activePlugins:[]},siteIsShowingCachedContent:void 0},invoke:{id:"contentQueryParamListener",src:"contentQueryParamListener"},states:{navigate:{always:[{guard:{type:"hasContentLocation",params:{content:"site-preview"}}},{guard:{type:"hasContentLocation",params:{content:"launch-store-success"}},target:"launchStoreSuccess"},{target:"#sitePreview"}]},sitePreview:{id:"sitePreview",meta:{component:e=>{const t=(0,se.Qk)("siteUrl")+"?site-preview=1",[o,s]=(0,y.useState)(!0),a=(0,y.useRef)(null),[n,c]=(0,oe.useResizeObserver)(),[i,l]=(0,y.useState)(!1);return(0,y.useEffect)((()=>{const e=a.current?.contentWindow,t=()=>{s(!0)};return e&&e.addEventListener("beforeunload",t),()=>{e&&e.removeEventListener("beforeunload",t)}}),[a,s,o]),(0,j.jsxs)("div",{className:(0,r.A)("launch-store-site-preview-page__container",{"is-loading":o},e.className),children:[n,!!c.width&&(0,j.jsx)(k.__unstableMotion.div,{initial:!1,layout:"position",className:"launch-store-preview-layout__canvas",children:(0,j.jsxs)(ae.A,{isReady:!o,isHandleVisibleByDefault:!1,isFullWidth:!1,defaultSize:{width:c.width-24,height:c.height},isOversized:i,setIsOversized:l,innerContentStyle:{},children:[o&&(0,j.jsx)("div",{className:"launch-store-site-preview-site__loading-overlay",children:(0,j.jsx)(ee.Spinner,{})}),(0,j.jsx)("iframe",{ref:a,className:"launch-store-site__preview-site-iframe",src:t,title:"Preview",onLoad:()=>s(!1)})]})})]})}}},launchStoreSuccess:{id:"launchStoreSuccess",initial:"loading",states:{loading:{invoke:[{src:"fetchCongratsData",onDone:{actions:(0,u.a)({congratsScreen:({context:e,event:t})=>({...e.congratsScreen,hasLoadedCongratsData:!0,hasCompleteSurvey:"yes"===t.output.surveyCompleted,allTasklists:t.output.tasklists,activePlugins:t.output.activePlugins})})}},{src:"getSiteCachedStatus",onDone:{actions:(0,u.a)({siteIsShowingCachedContent:({event:e})=>e.output})},onError:{actions:(0,u.a)({siteIsShowingCachedContent:!1})}}],always:{guard:({context:e})=>e.congratsScreen.hasLoadedCongratsData&&void 0!==e.siteIsShowingCachedContent,target:"congrats"},meta:{component:te}},congrats:{entry:[{type:"updateQueryParams",params:{content:"launch-store-success"}}],meta:{component:({context:{congratsScreen:{activePlugins:e,allTasklists:t,hasCompleteSurvey:o},siteIsShowingCachedContent:s},sendEventToMainContent:a,className:n})=>{const i=(0,x.__)("Copy link","woocommerce"),l=(0,x.__)("Copied!","woocommerce"),m=(0,D.getSetting)("homeUrl",""),u=new URL(m);let d=u?.hostname;u?.port&&(d+=":"+u.port);const[h,_]=(0,y.useState)(i),p=(0,oe.useCopyToClipboard)(m,(()=>{_(l),setTimeout((()=>{_(i)}),2e3)}));return(0,c.xG)(["woocommerce-launch-your-store-success"]),(0,j.jsx)("div",{className:(0,r.A)("launch-store-success-page__container",n),children:(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats",children:[(0,j.jsx)(ee.ConfettiAnimation,{delay:1e3,colors:["#DFD1FB","#FB79D9","#FFA60E","#03D479","#AD86E9","#7F54B3","#3C2861"]}),(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-header-container",children:[(0,j.jsx)("span",{className:"woologo",children:(0,j.jsx)(ne.A,{})}),(0,j.jsxs)(k.Button,{onClick:()=>{a({type:"BACK_TO_HOME"})},className:"back-to-home-button",variant:"link",children:[(0,j.jsx)(k.Dashicon,{icon:"arrow-left-alt2"}),(0,j.jsx)("span",{children:(0,x.__)("Back to Home","woocommerce")})]})]}),(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-content",children:[(0,j.jsx)("h1",{className:"woocommerce-launch-store__congrats-heading",children:s?(0,x.__)("Congratulations! Your store will launch soon","woocommerce"):(0,x.__)("Congratulations! Your store is now live","woocommerce")}),(0,j.jsx)("h2",{className:"woocommerce-launch-store__congrats-subheading",children:s?(0,y.createInterpolateElement)((0,x.__)("It’ll be ready to view as soon as your <link></link> have updated. Please wait, or contact your web host to find out how to do this manually.","woocommerce"),{link:(0,j.jsx)("a",{href:"https://woocommerce.com/document/configuring-woocommerce-settings/coming-soon-mode/#server-caches",target:"_blank",rel:"noreferrer",children:(0,x.__)("server caches","woocommerce")})}):(0,x.__)("You’ve successfully launched your store and are ready to start selling! We can’t wait to see your business grow.","woocommerce")}),(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-midsection-container",children:[(0,j.jsxs)("div",{className:"woocommerce-launch-store__congrats-visit-store",children:[(0,j.jsx)("p",{className:"store-name",children:d}),(0,j.jsxs)("div",{className:"buttons-container",children:[(0,j.jsx)(k.Button,{className:"",variant:"secondary",ref:p,onClick:()=>{(0,g.recordEvent)("launch_your_store_congrats_copy_store_link_click")},children:h}),(0,j.jsx)(k.Button,{className:"",variant:"primary",onClick:()=>{a({type:"PREVIEW_STORE"})},children:(0,x.__)("Visit your store","woocommerce")})]})]}),(0,j.jsx)(de,{hasCompleteSurvey:o,onSubmit:e=>{a({type:"COMPLETE_SURVEY",payload:e})}})]}),(0,j.jsx)("h2",{className:"woocommerce-launch-store__congrats-main-actions-title",children:(0,x.__)("What’s next?","woocommerce")}),(0,j.jsx)(ce,{activePlugins:e,allTasklists:t})]})]})})}}}},on:{COMPLETE_SURVEY:{actions:[(0,u.a)(re),"recordSurveyResults"]},PREVIEW_STORE:{actions:["recordPreviewStoreClick","navigateToPreview"]},BACK_TO_HOME:{actions:["recordBackToHomeClick","navigateToHome"]}}},loading:{id:"loading",meta:{component:te}}},on:{EXTERNAL_URL_UPDATE:{target:".navigate"},SHOW_LAUNCH_STORE_SUCCESS:{target:"#launchStoreSuccess"},SHOW_LAUNCH_STORE_PENDING_CACHE:{actions:["assignSiteCachedStatus"],target:"#launchStoreSuccess"},SHOW_LOADING:{target:"#loading"}}}),pe=({children:e})=>(0,j.jsx)("div",{className:"launch-your-store-layout__content",children:e});var ge=o(89677);const we=()=>{(0,c.xG)(["woocommerce-launch-your-store"]),(0,a.useEffect)((()=>{window.sessionStorage.setItem("lysWaiting","no")}),[]);const{xstateV5Inspector:e}=(0,ge.D)("V5"),[t,o,n]=(0,s.zl)(_e,{inspect:e}),[l,m,u]=(0,s.zl)(J,{inspect:e,input:{mainContentMachineRef:n}}),d=!l.hasTag("fullscreen"),[h]=(0,i.$)(u),[_]=(0,i.$)(n);return(0,j.jsxs)("div",{className:"launch-your-store-layout__container",children:[(0,j.jsx)(X,{className:(0,r.A)({"is-sidebar-hidden":!d}),children:h&&(0,j.jsx)(h,{sendEventToSidebar:m,sendEventToMainContent:o,context:l.context})}),(0,j.jsx)(pe,{children:_&&(0,j.jsx)(_,{sendEventToSidebar:m,sendEventToMainContent:o,context:t.context})})]})}},51513:(e,t,o)=>{o.d(t,{$:()=>r});var s=o(84437),a=o(51609),n=o(42843);function r(e){const t=(0,s.d4)(e,(e=>{var t;return(0,n.Q)(null!==(t=e.getMeta())&&void 0!==t?t:void 0)})),[o,r]=(0,a.useState)(null);return(0,a.useEffect)((()=>{t?.component&&r((()=>t.component))}),[t?.component]),[o||null]}}}]);