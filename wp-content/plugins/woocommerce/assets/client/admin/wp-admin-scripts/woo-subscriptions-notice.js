(()=>{"use strict";const i=window.wc.tracks;window.jQuery(document).ready((function(){const n="#woo-subscription-expired-notice",o="#woo-subscription-expiring-notice";window.jQuery(n).length&&(0,i.recordEvent)("woo_subscription_expired_notice_in_settings_shown"),window.jQuery(o).length&&(0,i.recordEvent)("woo_subscription_expiring_notice_in_settings_shown");const t=function(i,n){const o={notice_id:i,dismiss_notice_nonce:n};window.wp.apiFetch({path:"/wc-admin/notice/dismiss",method:"POST",data:o})};window.jQuery(n).on("click","button.notice-dismiss",(function(){(0,i.recordEvent)("woo_subscription_expired_notice_in_settings_dismissed");const o=window.jQuery(n).data("dismissnonce");t("woo-subscription-expired-notice",o)})),window.jQuery(n).on("click","a",(function(){return(0,i.recordEvent)("woo_subscription_expired_notice_in_settings_clicked"),!0})),window.jQuery(o).on("click","button.notice-dismiss",(function(){(0,i.recordEvent)("woo_subscription_expiring_notice_in_settings_dismissed");const o=window.jQuery(n).data("dismissnonce");t("woo-subscription-expiring-notice",o)})),window.jQuery(o).on("click","a",(function(){return(0,i.recordEvent)("woo_subscription_expiring_notice_in_settings_clicked"),!0}))})),(window.wc=window.wc||{}).wooSubscriptionsNotice={}})();