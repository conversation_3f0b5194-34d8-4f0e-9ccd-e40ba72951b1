(()=>{"use strict";const n=window.wc.tracks;window.jQuery(document).ready((function(){const o=".woo-connect-notice",e="woo-connect-notice-settings-dismissed";window.jQuery(o).on("click","button.notice-dismiss",(function(){window.localStorage.setItem(e,(new Date).toString()),(0,n.recordEvent)("woo_connect_notice_in_settings_dismissed")})),window.jQuery("#woo-connect-notice-url").on("click",(function(){return(0,n.recordEvent)("woo_connect_notice_in_settings_clicked"),!0}));let t=!1;const c=window.localStorage.getItem(e),i=new Date(c||""),w=new Date;w.setMonth(w.getMonth()-1),c&&w.valueOf()<i.valueOf()&&(t=!0),t?window.jQuery(o).remove():(0,n.recordEvent)("woo_connect_notice_in_settings_shown")})),(window.wc=window.wc||{}).wooConnectNotice={}})();