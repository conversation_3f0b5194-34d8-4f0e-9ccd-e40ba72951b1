(()=>{"use strict";const e=window.wc.tracks,t=document.querySelector("#addtag #submit");function c(t){const c=t.target.parentElement.classList[0],n={edit:"edit",inline:"quick_edit",delete:"delete",view:"preview"};n[c]&&(0,e.recordEvent)("product_attributes_term_list_action_click",{selected_action:n[c]})}function n(){document.querySelectorAll(".row-actions span").forEach((e=>{e.removeEventListener("click",c),e.addEventListener("click",c)}))}n(),t?.addEventListener("click",(function(){const t=document.querySelector("#tag-name"),c=document.querySelector("#tag-slug");(0,e.recordEvent)("product_attributes_add_term",{page:"tags",name:t?.value,slug:c?.value}),setTimeout((()=>{n()}),1e3)})),(window.wc=window.wc||{}).addTermTracking={}})();