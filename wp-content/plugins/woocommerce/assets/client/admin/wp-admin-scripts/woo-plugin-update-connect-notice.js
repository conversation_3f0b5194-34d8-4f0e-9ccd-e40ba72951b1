(()=>{"use strict";var o={n:e=>{var n=e&&e.__esModule?()=>e.default:()=>e;return o.d(n,{a:n}),n},d:(e,n)=>{for(var c in n)o.o(n,c)&&!o.o(e,c)&&Object.defineProperty(e,c,{enumerable:!0,get:n[c]})},o:(o,e)=>Object.prototype.hasOwnProperty.call(o,e)};const e=window.wp.domReady;var n=o.n(e);const c=window.wc.tracks;n()((()=>{const o=document.querySelectorAll(".woocommerce-connect-your-store");o.length>0&&((0,c.recordEvent)("woo_connect_notice_in_plugins_shown"),o.forEach((o=>{o.addEventListener("click",(function(){(0,c.recordEvent)("woo_connect_notice_in_plugins_clicked")}))})))})),(window.wc=window.wc||{}).wooPluginUpdateConnectNotice={}})();