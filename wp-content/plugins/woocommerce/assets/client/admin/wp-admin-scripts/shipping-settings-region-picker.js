/*! For license information please see shipping-settings-region-picker.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,o,t)=>{var n=t(51609),r=Symbol.for("react.element"),i=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),l=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};o.jsx=function(e,o,t){var n,c={},a=null,p=null;for(n in void 0!==t&&(a=""+t),void 0!==o.key&&(a=""+o.key),void 0!==o.ref&&(p=o.ref),o)i.call(o,n)&&!s.hasOwnProperty(n)&&(c[n]=o[n]);if(e&&e.defaultProps)for(n in o=e.defaultProps)void 0===c[n]&&(c[n]=o[n]);return{$$typeof:r,type:e,key:a,ref:p,props:c,_owner:l.current}}},39793:(e,o,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React}},o={};const t=window.wp.element,n=window.wp.htmlEntities,r=window.wc.components,i=window.wp.i18n;var l=function t(n){var r=o[n];if(void 0!==r)return r.exports;var i=o[n]={exports:{}};return e[n](i,i.exports,t),i.exports}(39793);const s=({options:e,initialValues:o})=>{const[n,s]=(0,t.useState)(o);return(0,l.jsx)(r.TreeSelectControl,{value:n,onChange:e=>{document.body.dispatchEvent(new CustomEvent("wc_region_picker_update",{detail:e})),s(e)},options:e,placeholder:(0,i.__)("Start typing to filter zones","woocommerce"),selectAllLabel:(0,i.__)("Select all countries","woocommerce"),individuallySelectParent:!0,maxVisibleTags:5})},c=(e,o)=>Array.isArray(e)?e.map((e=>c(e,o))):(e.label&&(e.label=o(e.label)),e.children&&(e.children=c(e.children,o)),e);var a,p;const d=document.getElementById("wc-shipping-zone-region-picker-root"),w=null!==(a=c(window.shippingZoneMethodsLocalizeScript?.region_options,n.decodeEntities))&&void 0!==a?a:[],u=null!==(p=window.shippingZoneMethodsLocalizeScript?.locations)&&void 0!==p?p:[],_=()=>(0,l.jsx)("div",{children:(0,l.jsx)(s,{options:w,initialValues:u})});d&&(0,t.createRoot)(d).render((0,l.jsx)(_,{})),(window.wc=window.wc||{}).shippingSettingsRegionPicker={}})();