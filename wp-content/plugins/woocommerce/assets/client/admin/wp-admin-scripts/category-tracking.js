(()=>{"use strict";const e=window.wc.tracks,t=document.querySelector("#addtag #submit");function c(t){const c=t.target.parentElement.classList[0],n={edit:"edit",inline:"quick_edit",delete:"delete",view:"preview",make_default:"make_default"};n[c]&&(0,e.recordEvent)("product_category_manage",{option_selected:n[c]})}function n(){document.querySelectorAll(".row-actions span").forEach((e=>{e.removeEventListener("click",c),e.addEventListener("click",c)}))}n(),t?.addEventListener("click",(function(){setTimeout((()=>{n()}),1e3)})),(window.wc=window.wc||{}).categoryTracking={}})();