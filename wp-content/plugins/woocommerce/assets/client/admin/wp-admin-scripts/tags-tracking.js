(()=>{"use strict";const e=window.wc.tracks,t=document.querySelector("#addtag #submit");function c(t){const c=t.target.parentElement.classList[0],i={edit:"edit",inline:"quick_edit",delete:"delete",view:"preview"};i[c]&&(0,e.recordEvent)("product_tags_list_action_click",{selected_action:i[c]})}function i(){document.querySelectorAll(".row-actions span").forEach((e=>{e.removeEventListener("click",c),e.addEventListener("click",c)}))}i(),t?.addEventListener("click",(function(){(0,e.recordEvent)("product_tags_add",{page:"attributes"}),setTimeout((()=>{i()}),1e3)})),(window.wc=window.wc||{}).tagsTracking={}})();