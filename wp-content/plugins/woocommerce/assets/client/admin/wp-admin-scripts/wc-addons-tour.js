/*! For license information please see wc-addons-tour.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,o,t)=>{var r=t(51609),n=Symbol.for("react.element"),a=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),c=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};o.jsx=function(e,o,t){var r,i={},l=null,m=null;for(r in void 0!==t&&(l=""+t),void 0!==o.key&&(l=""+o.key),void 0!==o.ref&&(m=o.ref),o)a.call(o,r)&&!s.hasOwnProperty(r)&&(i[r]=o[r]);if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===i[r]&&(i[r]=o[r]);return{$$typeof:n,type:e,key:l,ref:m,props:i,_owner:c.current}}},39793:(e,o,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React}},o={};const t=window.wp.element,r=window.wc.components,n=window.wc.tracks,a=window.wp.data,c=window.wc.data,s=e=>{const o=(document.getElementById("wpadminbar")?.offsetHeight||0)+8;e.top<o?window.scrollBy(0,e.top-o):e.bottom>window.innerHeight&&window.scrollBy(0,e.bottom-window.innerHeight)},i=window.wp.i18n;var l=function t(r){var n=o[r];if(void 0!==n)return n.exports;var a=o[r]={exports:{}};return e[r](a,a.exports,t),a.exports}(39793);const m=()=>{const[e,o]=(0,t.useState)(!0),{updateOptions:m}=(0,a.useDispatch)(c.optionsStore),p=(()=>{const e=(0,t.createElement)("br");return[{referenceElements:{desktop:'#adminmenu a[href="admin.php?page=wc-admin&path=%2Fextensions"]'},focusElement:{desktop:'#adminmenu a[href="admin.php?page=wc-admin&path=%2Fextensions"]'},meta:{name:"wc-extensions-menu-item",heading:(0,i.__)("Welcome to the WooCommerce Marketplace","woocommerce"),descriptions:{desktop:(0,t.createInterpolateElement)((0,i.__)("Power up your store by adding extra functionality with extensions or integrate your store with other software and services.<br/><br/>Here you'll find hundreds of trusted solutions for your store — all reviewed and approved by the Woo team.<br/><br/>You can also browse the Woo Marketplace at WooCommerce.com.","woocommerce"),{br:e})}}},{referenceElements:{desktop:".woocommerce-marketplace__search"},focusElement:{desktop:".woocommerce-marketplace__search"},meta:{name:"wc-extensions-search",heading:(0,i.__)("Find exactly what you need","woocommerce"),descriptions:{desktop:(0,i.__)("Use the search box to find specific extensions or solutions.","woocommerce")}}},{referenceElements:{desktop:".woocommerce-marketplace__tab-browse"},focusElement:{desktop:".woocommerce-marketplace__tab-browse"},meta:{name:"wc-addons-categories",heading:(0,i.__)("Browse for new ideas","woocommerce"),descriptions:{desktop:(0,t.createInterpolateElement)((0,i.__)("Or if you're not sure exactly what you need, you can browse all available extensions by category.","woocommerce"),{br:e})}}},{referenceElements:{desktop:".woocommerce-marketplace__discover:first-child"},focusElement:{desktop:".woocommerce-marketplace__discover:first-child"},meta:{name:"wc-addons-featured",heading:(0,i.__)("Learn more about each product","woocommerce"),descriptions:{desktop:(0,t.createInterpolateElement)((0,i.__)("Scroll down to see all of the relevant extensions and solutions.<br/><br/>Click on any solution to learn more about its features, any installation requirements, and available documentation.","woocommerce"),{br:e})}}},{referenceElements:{desktop:".woocommerce-marketplace__header-meta"},focusElement:{desktop:".woocommerce-marketplace__header-meta"},meta:{name:"wc-addons-my-subscriptions",heading:(0,i.__)("Manage your purchases","woocommerce"),descriptions:{desktop:(0,t.createInterpolateElement)((0,i.__)("All of your Woo Marketplace purchases can be found here, or on WooCommerce.com.<br/><br/>Every purchase is backed by our <a1>30-day money-back guarantee</a1>, and includes <a2>email and live chat support</a2>.<br/><br/>That's it! You're now ready to power up your store.","woocommerce"),{a1:(0,t.createElement)("a",{href:"https://woocommerce.com/refund-policy/","aria-label":(0,i.__)("Refund policy","woocommerce")},(0,i.__)("30-day money-back guarantee","woocommerce")),a2:(0,t.createElement)("a",{href:"https://woocommerce.com/my-account/create-a-ticket/","aria-label":(0,i.__)("Contact support","woocommerce")},(0,i.__)("email and live chat support","woocommerce")),br:e})}}}]})();if((0,t.useEffect)((()=>{if("true"===new URLSearchParams(location.search).get("tutorial")){const e=(e=>{const t=document.querySelector(e);let r=t?.getBoundingClientRect().top;const a=setInterval((()=>{const e=t?.getBoundingClientRect().top;r===e&&((()=>{const e=p[0]?.meta?.name;o(!0),(0,n.recordEvent)("in_app_marketplace_tour_started",{step:e})})(),clearInterval(a)),r=e}),500);return a})(p[0].referenceElements?.desktop||"");return()=>clearInterval(e)}}),[]),(0,t.useEffect)((()=>{if(e){function o(){const e=document.querySelector(".tour-kit-frame__container");e&&s(e.getBoundingClientRect())}const t=setTimeout(o,500),r=((e,o)=>{const t=document.querySelector(".woocommerce-marketplace");let r=t?.offsetTop;return setInterval((()=>{const e=t?.offsetTop;r!==e&&o(),r=e}),150)})(0,o);return()=>{clearTimeout(t),clearInterval(r)}}}),[e]),!e)return null;const d=(({closeHandler:e,onNextStepHandler:o,autoScrollBlock:t,steps:r})=>{let n=null,a=null;const c="top-start";return{placement:c,options:{effects:{spotlight:{interactivity:{enabled:!0,rootElementSelector:".woocommerce-marketplace"}},autoScroll:{behavior:"auto",block:t}},popperModifiers:[{name:"offset",options:{offset:[20,20]}},{name:"flip",options:{allowedAutoPlacements:["right","bottom","top"],fallbackPlacements:["bottom-start","right"],flipVariations:!1,boundry:"clippingParents"}},{name:"inAppTourPopperModifications",enabled:!0,phase:"read",fn({state:e,instance:o}){if(a!==e.elements.reference){const t=e.elements.reference.closest("#adminmenu")?"right":c;e.placement!==t&&o.setOptions({placement:t})}const t=e.elements.popper.getBoundingClientRect(),r=e.elements.arrow?.getBoundingClientRect(),i=r?.height||0;a!==e.elements.reference&&0!==i&&n!==t.top&&(s(t),n=t.top,a=e.elements.reference)}}],callbacks:{onNextStep:o}},steps:r,closeHandler:e}})({closeHandler:(e,t)=>{o(!1),m({woocommerce_admin_dismissed_in_app_marketplace_tour:"yes"});const r=new URL(window.location.href);if(r.searchParams.delete("tutorial"),window.history.replaceState(null,"",r),p.length-1===t)(0,n.recordEvent)("in_app_marketplace_tour_completed");else{const o=e[t]?.meta?.name;(0,n.recordEvent)("in_app_marketplace_tour_dismissed",{step:o})}},onNextStepHandler:e=>{const o=p[e]?.meta?.name||"";(0,n.recordEvent)("in_app_marketplace_tour_step_viewed",{step:o})},autoScrollBlock:"center",steps:p});return(0,l.jsx)(r.TourKit,{config:d})},p=document.createElement("div");p.setAttribute("id","wc-addons-tour-root"),(0,t.createRoot)(document.body.appendChild(p)).render((0,l.jsx)(m,{})),(window.wc=window.wc||{}).wcAddonsTour={}})();