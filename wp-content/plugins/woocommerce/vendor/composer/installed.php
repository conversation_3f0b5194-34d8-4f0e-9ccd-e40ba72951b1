<?php return array(
    'root' => array(
        'name' => 'woocommerce/woocommerce',
        'pretty_version' => '9.9.4',
        'version' => '9.9.4.0',
        'reference' => null,
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'automattic/jetpack-a8c-mc-stats' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'd6bdf2f1d1941e0a22d17c6f3152097d8e0a30e6',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-a8c-mc-stats',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-admin-ui' => array(
            'pretty_version' => 'v0.5.1',
            'version' => '0.5.1.0',
            'reference' => 'a0894d34333451089add7b20f70e73b6509d6b6d',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-admin-ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-assets' => array(
            'pretty_version' => 'v4.0.1',
            'version' => '4.0.1.0',
            'reference' => 'ca1ebeceeeafb31876a234fa68ea3065b3eab2c3',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-assets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-autoloader' => array(
            'pretty_version' => 'v5.0.0',
            'version' => '5.0.0.0',
            'reference' => 'eb6331a5c50a03afd9896ce012e66858de9c49c5',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../automattic/jetpack-autoloader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-config' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'fc719eff5073634b0c62793b05be913ca634e192',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-connection' => array(
            'pretty_version' => 'v6.2.0',
            'version' => '6.2.0.0',
            'reference' => '52cd2ba7d845eb516d505959bd9a5e94d1bf4203',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-connection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-constants' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'd4b7820defcdb40c1add88d5ebd722e4ba80a873',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-constants',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-redirect' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '3.0.1.0',
            'reference' => '89732a3ba1c5eba8cfd948b7567823cd884102d5',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-redirect',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-roles' => array(
            'pretty_version' => 'v3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'fe5f2a45901ea14be00728119d097619615fb031',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-roles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-status' => array(
            'pretty_version' => 'v5.0.1',
            'version' => '5.0.1.0',
            'reference' => '769f55b6327187a85c14ed21943eea430f63220d',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-status',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v1.12.0',
            'version' => '1.12.0.0',
            'reference' => 'd20a64ed3c94748397ff5973488761b22f6d3f19',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maxmind-db/reader' => array(
            'pretty_version' => 'v1.11.1',
            'version' => '1.11.1.0',
            'reference' => '1e66f73ffcf25e17c7a910a1317e9720a95497c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maxmind-db/reader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opis/json-schema' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'reference' => '712827751c62b465daae6e725bf0cf5ffbf965e1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/json-schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opis/string' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '9ebf1a1f873f502f6859d11210b25a4bf5d141e7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opis/uri' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/uri',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pelago/emogrifier' => array(
            'pretty_version' => 'v6.0.0',
            'version' => '6.0.0.0',
            'reference' => 'aa72d5407efac118f3896bcb995a2cba793df0ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pelago/emogrifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'roundcube/plugin-installer' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.6.0',
            'version' => '8.6.0.0',
            'reference' => 'd2fb94a9641be84d79c7548c6d39bbebba6e9a70',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'shama/baton' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'soundasleep/html2text' => array(
            'pretty_version' => '2.1.0',
            'version' => '2.1.0.0',
            'reference' => '83502b6f8f1aaef8e2e238897199d64f284b4af3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../soundasleep/html2text',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v5.4.40',
            'version' => '5.4.40.0',
            'reference' => 'ea43887e9afd2029509662d4f95e8b5ef6fc9bbb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.30.0',
            'version' => '1.30.0.0',
            'reference' => '77fa7995ac1b21ab60769b7323d600a991a90433',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/action-scheduler' => array(
            'pretty_version' => '3.9.2',
            'version' => '3.9.2.0',
            'reference' => 'efbb7953f72a433086335b249292f280dd43ddfe',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../packages/action-scheduler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/blueprint' => array(
            'pretty_version' => '0.0.1',
            'version' => '0.0.1.0',
            'reference' => 'f996d43977196b9670d783b58c167243950876d3',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../packages/blueprint',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/email-editor' => array(
            'pretty_version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
            'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
            'reference' => '956f59488f8336599f920f70267a6147149cb032',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../packages/email-editor',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/woocommerce' => array(
            'pretty_version' => '9.9.4',
            'version' => '9.9.4.0',
            'reference' => null,
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
