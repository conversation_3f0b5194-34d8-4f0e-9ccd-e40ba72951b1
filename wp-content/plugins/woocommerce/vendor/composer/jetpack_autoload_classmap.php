<?php

// This file `jetpack_autoload_classmap.php` was auto generated by automattic/jetpack-autoloader.

$vendorDir = dirname(__DIR__);
$baseDir   = dirname($vendorDir);

return array(
	'Attribute' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Attribute.php'
	),
	'Autoloader' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader.php'
	),
	'Autoloader_Handler' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-handler.php'
	),
	'Autoloader_Locator' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-locator.php'
	),
	'Automattic\\Jetpack\\A8c_Mc_Stats' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php'
	),
	'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-admin-ui/src/class-admin-menu.php'
	),
	'Automattic\\Jetpack\\Assets' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-assets/src/class-assets.php'
	),
	'Automattic\\Jetpack\\Assets\\Script_Data' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-assets/src/class-script-data.php'
	),
	'Automattic\\Jetpack\\Assets\\Semver' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-assets/src/class-semver.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php'
	),
	'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php'
	),
	'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php'
	),
	'Automattic\\Jetpack\\Config' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-config/src/class-config.php'
	),
	'Automattic\\Jetpack\\Connection\\Authorize_Json_Api' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-authorize-json-api.php'
	),
	'Automattic\\Jetpack\\Connection\\Client' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-client.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Assets' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-connection-assets.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Notice' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-connection-notice.php'
	),
	'Automattic\\Jetpack\\Connection\\Error_Handler' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-error-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Initial_State' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-initial-state.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager_Interface' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/interface-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Nonce_Handler' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-nonce-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-package-version-tracker.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-plugin.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin_Storage' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-plugin-storage.php'
	),
	'Automattic\\Jetpack\\Connection\\REST_Connector' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-rest-connector.php'
	),
	'Automattic\\Jetpack\\Connection\\Rest_Authentication' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-rest-authentication.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/sso/class-sso.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Force_2FA' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/sso/class-force-2fa.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Helpers' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/sso/class-helpers.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\Notices' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/sso/class-notices.php'
	),
	'Automattic\\Jetpack\\Connection\\SSO\\User_Admin' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/sso/class-user-admin.php'
	),
	'Automattic\\Jetpack\\Connection\\Secrets' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-secrets.php'
	),
	'Automattic\\Jetpack\\Connection\\Server_Sandbox' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-server-sandbox.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-tokens.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens_Locks' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-tokens-locks.php'
	),
	'Automattic\\Jetpack\\Connection\\Urls' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-urls.php'
	),
	'Automattic\\Jetpack\\Connection\\Utils' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-webhooks.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-async-call.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-connector.php'
	),
	'Automattic\\Jetpack\\Constants' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-constants/src/class-constants.php'
	),
	'Automattic\\Jetpack\\CookieState' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-cookiestate.php'
	),
	'Automattic\\Jetpack\\Errors' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-errors.php'
	),
	'Automattic\\Jetpack\\Files' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-files.php'
	),
	'Automattic\\Jetpack\\Heartbeat' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-heartbeat.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\Exception' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-exception.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\UI' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-ui.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\URL_Secret' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-url-secret.php'
	),
	'Automattic\\Jetpack\\Identity_Crisis' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/identity-crisis/class-identity-crisis.php'
	),
	'Automattic\\Jetpack\\Modules' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-modules.php'
	),
	'Automattic\\Jetpack\\Partner' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-partner.php'
	),
	'Automattic\\Jetpack\\Partner_Coupon' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-partner-coupon.php'
	),
	'Automattic\\Jetpack\\Paths' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-paths.php'
	),
	'Automattic\\Jetpack\\Redirect' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-redirect/src/class-redirect.php'
	),
	'Automattic\\Jetpack\\Roles' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-roles/src/class-roles.php'
	),
	'Automattic\\Jetpack\\Status' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-status.php'
	),
	'Automattic\\Jetpack\\Status\\Cache' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-cache.php'
	),
	'Automattic\\Jetpack\\Status\\Host' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-host.php'
	),
	'Automattic\\Jetpack\\Status\\Visitor' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-visitor.php'
	),
	'Automattic\\Jetpack\\Terms_Of_Service' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-terms-of-service.php'
	),
	'Automattic\\Jetpack\\Tracking' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-tracking.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\BusinessDescription' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/BusinessDescription.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\Images' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/Images.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\Middleware' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/Middleware.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\Patterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/Patterns.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\Product' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/Product.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\StoreInfo' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/StoreInfo.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\AI\\StoreTitle' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/AI/StoreTitle.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Coupons' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Coupons.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\CustomAttributeTraits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/CustomAttributeTraits.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Customers' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Customers.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Data' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Data.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\DataCountries' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/DataCountries.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\DataDownloadIPs' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/DataDownloadIPs.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Experiments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Experiments.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Features' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Features.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\LaunchYourStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/LaunchYourStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Leaderboards' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Leaderboards.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Marketing' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Marketing.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\MarketingCampaignTypes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/MarketingCampaignTypes.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\MarketingCampaigns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/MarketingCampaigns.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\MarketingChannels' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/MarketingChannels.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\MarketingOverview' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/MarketingOverview.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\MarketingRecommendations' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/MarketingRecommendations.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\MobileAppMagicLink' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/MobileAppMagicLink.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\NoteActions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/NoteActions.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Notes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Notes.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Notice' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Notice.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingFreeExtensions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingFreeExtensions.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingPlugins' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingPlugins.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingProductTypes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingProductTypes.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingProducts.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingProfile' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingProfile.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingTasks' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingTasks.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\OnboardingThemes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/OnboardingThemes.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Options' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Options.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Orders' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Orders.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\PaymentGatewaySuggestions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/PaymentGatewaySuggestions.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Plugins' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Plugins.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductAttributeTerms' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductAttributeTerms.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductAttributes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductAttributes.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductCategories' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductCategories.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductForm' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductForm.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductReviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductReviews.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductVariations' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductVariations.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Products' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Products.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ProductsLowInStock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ProductsLowInStock.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Cache' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Cache.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Categories\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Categories/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Categories\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Categories/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Categories\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Categories/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Coupons\\Stats\\Segmenter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Coupons/Stats/Segmenter.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Customers/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Customers/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Customers/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Customers/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Customers/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Customers\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Customers/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\DataStoreInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/DataStoreInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Files\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/Files/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Downloads\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Downloads/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Export\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Export/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\ExportableInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/ExportableInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\ExportableTraits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/ExportableTraits.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\FilteredGetDataTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/FilteredGetDataTrait.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\GenericController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/GenericController.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\GenericQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/GenericQuery.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\GenericStatsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/GenericStatsController.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Import\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Import/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\OrderAwareControllerTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/OrderAwareControllerTrait.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Orders\\Stats\\Segmenter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Orders/Stats/Segmenter.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\ParameterException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/ParameterException.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\PerformanceIndicators\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/PerformanceIndicators/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Products\\Stats\\Segmenter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Products/Stats/Segmenter.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Revenue\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Revenue/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Revenue\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Revenue/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Segmenter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Segmenter.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\SqlQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/SqlQuery.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\StatsDataStoreTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/StatsDataStoreTrait.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Stock/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Stock/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Stock/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Stock\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Stock/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Taxes\\Stats\\Segmenter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Taxes/Stats/Segmenter.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\TimeInterval' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/TimeInterval.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/Stats/Controller.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/Stats/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\Query' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/Stats/Query.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Reports\\Variations\\Stats\\Segmenter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Reports/Variations/Stats/Segmenter.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\SettingOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/SettingOptions.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Settings' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Settings.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\ShippingPartnerSuggestions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/ShippingPartnerSuggestions.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Taxes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Taxes.php'
	),
	'Automattic\\WooCommerce\\Admin\\API\\Themes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/API/Themes.php'
	),
	'Automattic\\WooCommerce\\Admin\\BlockTemplates\\BlockContainerInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/BlockTemplates/BlockContainerInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\BlockTemplates\\BlockInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/BlockTemplates/BlockInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\BlockTemplates\\BlockTemplateInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/BlockTemplates/BlockTemplateInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\BlockTemplates\\ContainerInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/BlockTemplates/ContainerInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\Composer\\Package' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Composer/Package.php'
	),
	'Automattic\\WooCommerce\\Admin\\DataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/DataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\DateTimeProvider\\CurrentDateTimeProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/DateTimeProvider/CurrentDateTimeProvider.php'
	),
	'Automattic\\WooCommerce\\Admin\\DateTimeProvider\\DateTimeProviderInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/DateTimeProvider/DateTimeProviderInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\DeprecatedClassFacade' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/DeprecatedClassFacade.php'
	),
	'Automattic\\WooCommerce\\Admin\\FeaturePlugin' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/FeaturePlugin.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\AsyncProductEditorCategoryField\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/AsyncProductEditorCategoryField/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCCoreProfilerOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCCoreProfilerOptions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCPaymentGateways' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCPaymentGateways.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettings' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettings.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsAccount' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsAccount.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsAdvanced' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsAdvanced.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsEmails' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsEmails.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsGeneral' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsGeneral.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsIntegrations' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsIntegrations.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsProducts.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsShipping' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsShipping.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsSiteVisibility' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsSiteVisibility.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCSettingsTax' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCSettingsTax.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Exporters\\ExportWCTaskOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Exporters/ExportWCTaskOptions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\RestApi' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/RestApi.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Blueprint\\SettingOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Blueprint/SettingOptions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Features' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Features.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\LaunchYourStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/LaunchYourStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\DefaultMarketingRecommendations' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/MarketingRecommendations/DefaultMarketingRecommendations.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/MarketingRecommendations/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\MarketingRecommendationsDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/MarketingRecommendations/MarketingRecommendationsDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\MarketingRecommendations\\MiscRecommendationsDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/MarketingRecommendations/MiscRecommendationsDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Navigation\\RemovedDeprecated' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Navigation/RemovedDeprecated.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Onboarding' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Onboarding.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\DeprecatedExtendedTask' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/DeprecatedExtendedTask.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\DeprecatedOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/DeprecatedOptions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Task' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Task.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskList' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskList.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskListSection' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskListSection.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskLists' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskLists.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\TaskTraits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/TaskTraits.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\AdditionalPayments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/AdditionalPayments.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Appearance' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Appearance.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\CustomizeStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/CustomizeStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\ExperimentalShippingRecommendation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/ExperimentalShippingRecommendation.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\ExtendStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/ExtendStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\GetMobileApp' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/GetMobileApp.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\LaunchYourStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/LaunchYourStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Marketing' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Marketing.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Payments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Payments.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Products' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Products.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\ReviewShippingOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/ReviewShippingOptions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Shipping' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Shipping.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\StoreCreation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/StoreCreation.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\StoreDetails' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/StoreDetails.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\Tax' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/Tax.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\TourInAppMarketplace' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/TourInAppMarketplace.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\OnboardingTasks\\Tasks\\WooCommercePayments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/OnboardingTasks/Tasks/WooCommercePayments.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\DefaultPaymentGateways' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/DefaultPaymentGateways.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\EvaluateSuggestion' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/EvaluateSuggestion.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\PaymentGatewaySuggestionsDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/PaymentGatewaySuggestionsDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\PaymentGatewaySuggestions\\PaymentGatewaysController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/PaymentGatewaySuggestions/PaymentGatewaysController.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\BlockRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/BlockRegistry.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\BlockTemplateUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/BlockTemplateUtils.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductFormsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductFormsController.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplate.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\GroupInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/GroupInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\ProductFormTemplateInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/ProductFormTemplateInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\SectionInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/SectionInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\ProductTemplates\\SubsectionInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/ProductTemplates/SubsectionInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\RedirectionController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/RedirectionController.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductBlockEditor\\Tracks' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductBlockEditor/Tracks.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ProductDataViews\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ProductDataViews/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Settings\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Settings/Init.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\Settings\\Transformer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/Settings/Transformer.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ShippingPartnerSuggestions\\DefaultShippingPartners' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ShippingPartnerSuggestions/DefaultShippingPartners.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ShippingPartnerSuggestions\\ShippingPartnerSuggestions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ShippingPartnerSuggestions/ShippingPartnerSuggestions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\ShippingPartnerSuggestions\\ShippingPartnerSuggestionsDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/ShippingPartnerSuggestions/ShippingPartnerSuggestionsDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\Features\\TransientNotices' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Features/TransientNotices.php'
	),
	'Automattic\\WooCommerce\\Admin\\Loader' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Loader.php'
	),
	'Automattic\\WooCommerce\\Admin\\Marketing\\InstalledExtensions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Marketing/InstalledExtensions.php'
	),
	'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingCampaign' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Marketing/MarketingCampaign.php'
	),
	'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingCampaignType' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Marketing/MarketingCampaignType.php'
	),
	'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingChannelInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Marketing/MarketingChannelInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\Marketing\\MarketingChannels' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Marketing/MarketingChannels.php'
	),
	'Automattic\\WooCommerce\\Admin\\Marketing\\Price' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Marketing/Price.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\DataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DataStore.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\Note' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/Note.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\NoteTraits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/NoteTraits.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\Notes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/Notes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\NotesUnavailableException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/NotesUnavailableException.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Note' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Customize_Store_With_Blocks' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_EU_VAT_Number' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Edit_Products_On_The_Move' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Facebook_Marketing_Expert' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_First_Product' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Giving_Feedback_Notes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Install_JP_And_WCS_Plugins' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Launch_Checklist' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Migrate_From_Shopify' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Mobile_App' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_New_Sales_Record' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Onboarding_Email_Marketing' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Onboarding_Payments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Online_Clothing_Store' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Order_Milestones' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Performance_On_Mobile' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Personalize_Store' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Real_Time_Order_Alerts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Selling_Online_Courses' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Tracking_Opt_In' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_WooCommerce_Payments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_WooCommerce_Subscriptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Notes\\WC_Admin_Notes_Woo_Subscriptions_Notes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Notes/DeprecatedNotes.php'
	),
	'Automattic\\WooCommerce\\Admin\\Overrides\\Order' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Overrides/Order.php'
	),
	'Automattic\\WooCommerce\\Admin\\Overrides\\OrderRefund' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Overrides/OrderRefund.php'
	),
	'Automattic\\WooCommerce\\Admin\\Overrides\\OrderTraits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Overrides/OrderTraits.php'
	),
	'Automattic\\WooCommerce\\Admin\\Overrides\\ThemeUpgrader' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Overrides/ThemeUpgrader.php'
	),
	'Automattic\\WooCommerce\\Admin\\Overrides\\ThemeUpgraderSkin' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Overrides/ThemeUpgraderSkin.php'
	),
	'Automattic\\WooCommerce\\Admin\\PageController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PageController.php'
	),
	'Automattic\\WooCommerce\\Admin\\PluginsHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PluginsHelper.php'
	),
	'Automattic\\WooCommerce\\Admin\\PluginsInstallLoggers\\AsyncPluginsInstallLogger' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PluginsInstallLoggers/AsyncPluginsInstallLogger.php'
	),
	'Automattic\\WooCommerce\\Admin\\PluginsInstallLoggers\\PluginsInstallLogger' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PluginsInstallLoggers/PluginsInstallLogger.php'
	),
	'Automattic\\WooCommerce\\Admin\\PluginsInstaller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PluginsInstaller.php'
	),
	'Automattic\\WooCommerce\\Admin\\PluginsProvider\\PluginsProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PluginsProvider/PluginsProvider.php'
	),
	'Automattic\\WooCommerce\\Admin\\PluginsProvider\\PluginsProviderInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/PluginsProvider/PluginsProviderInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\RemoteInboxNotificationsDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteInboxNotifications/RemoteInboxNotificationsDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\RemoteInboxNotificationsEngine' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteInboxNotifications/RemoteInboxNotificationsEngine.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\RuleProcessorInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteInboxNotifications/RuleProcessorInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\SpecRunner' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteInboxNotifications/SpecRunner.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteInboxNotifications\\TransformerInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteInboxNotifications/TransformerInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\DataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/DataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RemoteSpecsEngine' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RemoteSpecsEngine.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\BaseLocationCountryRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/BaseLocationCountryRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\BaseLocationStateRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/BaseLocationStateRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\ComparisonOperation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/ComparisonOperation.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\ContextPluginsRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/ContextPluginsRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\EvaluateAndGetStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/EvaluateAndGetStatus.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\EvaluateOverrides' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/EvaluateOverrides.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\EvaluationLogger' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/EvaluationLogger.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\FailRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/FailRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\GetRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/GetRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\GetRuleProcessorForContext' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/GetRuleProcessorForContext.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\IsEcommerceRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/IsEcommerceRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\IsWooExpressRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/IsWooExpressRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\NotRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/NotRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\NoteStatusRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/NoteStatusRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OnboardingProfileRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OnboardingProfileRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OptionRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OptionRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OrRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OrRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OrderCountRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OrderCountRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\OrdersProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/OrdersProvider.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PassRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PassRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PluginVersionRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PluginVersionRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PluginsActivatedRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PluginsActivatedRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\ProductCountRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/ProductCountRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PublishAfterTimeRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PublishAfterTimeRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\PublishBeforeTimeRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/PublishBeforeTimeRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\RuleEvaluator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/RuleEvaluator.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\RuleProcessorInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/RuleProcessorInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\StoredStateRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/StoredStateRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\StoredStateSetupForProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/StoredStateSetupForProducts.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\TotalPaymentsVolumeProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/TotalPaymentsVolumeProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayColumn' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayColumn.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayFlatten' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayFlatten.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayKeys' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayKeys.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArraySearch' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArraySearch.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\ArrayValues' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/ArrayValues.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\Count' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/Count.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\DotNotation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/DotNotation.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\PrepareUrl' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/PrepareUrl.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\TransformerInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/TransformerInterface.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\Transformers\\TransformerService' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/Transformers/TransformerService.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\WCAdminActiveForProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/WCAdminActiveForProvider.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\WCAdminActiveForRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/WCAdminActiveForRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\RemoteSpecs\\RuleProcessors\\WooCommerceAdminUpdatedRuleProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/RemoteSpecs/RuleProcessors/WooCommerceAdminUpdatedRuleProcessor.php'
	),
	'Automattic\\WooCommerce\\Admin\\ReportCSVEmail' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/ReportCSVEmail.php'
	),
	'Automattic\\WooCommerce\\Admin\\ReportCSVExporter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/ReportCSVExporter.php'
	),
	'Automattic\\WooCommerce\\Admin\\ReportExporter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/ReportExporter.php'
	),
	'Automattic\\WooCommerce\\Admin\\ReportsSync' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/ReportsSync.php'
	),
	'Automattic\\WooCommerce\\Admin\\Schedulers\\SchedulerTraits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/Schedulers/SchedulerTraits.php'
	),
	'Automattic\\WooCommerce\\Admin\\WCAdminHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Admin/WCAdminHelper.php'
	),
	'Automattic\\WooCommerce\\Autoloader' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Autoloader.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AIContent\\ContentProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AIContent/ContentProcessor.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AIContent\\PatternsDictionary' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AIContent/PatternsDictionary.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AIContent\\PatternsHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AIContent/PatternsHelper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AIContent\\UpdatePatterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AIContent/UpdatePatterns.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AIContent\\UpdateProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AIContent/UpdateProducts.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AI\\Configuration' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AI/Configuration.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AI\\Connection' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AI/Connection.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Assets' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Assets.php'
	),
	'Automattic\\WooCommerce\\Blocks\\AssetsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/AssetsController.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Assets\\Api' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Assets/Api.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Assets\\AssetDataRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Assets/AssetDataRegistry.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockPatterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockPatterns.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTemplatesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTemplatesController.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTemplatesRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTemplatesRegistry.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypesController.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AbstractBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractDynamicBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AbstractDynamicBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractInnerBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AbstractInnerBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AbstractProductGrid' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AbstractProductGrid.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionGroup' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionGroup.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionHeader' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionHeader.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionItem' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionItem.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Accordion\\AccordionPanel' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Accordion/AccordionPanel.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ActiveFilters' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ActiveFilters.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartForm' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartForm.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\AddToCartWithOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/AddToCartWithOptions.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\GroupedProductSelector' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/GroupedProductSelector.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\GroupedProductSelectorItemCTA' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/GroupedProductSelectorItemCTA.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\GroupedProductSelectorItemTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/GroupedProductSelectorItemTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\QuantitySelector' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/QuantitySelector.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/Utils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelector' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelector.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelectorAttributeName' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelectorAttributeName.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelectorAttributeOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelectorAttributeOptions.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AddToCartWithOptions\\VariationSelectorItemTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AddToCartWithOptions/VariationSelectorItemTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AllProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AllProducts.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AllReviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AllReviews.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AtomicBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AtomicBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\AttributeFilter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/AttributeFilter.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\BlockifiedProductDetails' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/BlockifiedProductDetails.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Breadcrumbs' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Breadcrumbs.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Cart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Cart.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartAcceptedPaymentMethodsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartAcceptedPaymentMethodsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartCrossSellsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartCrossSellsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartCrossSellsProductsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartCrossSellsProductsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartExpressPaymentBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartExpressPaymentBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartItemsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartItemsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartLineItemsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartLineItemsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartLink' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartLink.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryCouponFormBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryCouponFormBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryDiscountBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryDiscountBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryFeeBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryFeeBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryHeadingBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryHeadingBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryShippingBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryShippingBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummarySubtotalBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummarySubtotalBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryTaxesBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryTaxesBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartOrderSummaryTotalsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartOrderSummaryTotalsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CartTotalsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CartTotalsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CatalogSorting' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CatalogSorting.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Checkout' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Checkout.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutActionsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutActionsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutAdditionalInformationBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutAdditionalInformationBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutBillingAddressBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutBillingAddressBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutContactInformationBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutContactInformationBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutExpressPaymentBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutExpressPaymentBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutFieldsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutFieldsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderNoteBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderNoteBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryCartItemsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryCartItemsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryCouponFormBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryCouponFormBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryDiscountBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryDiscountBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryFeeBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryFeeBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryShippingBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryShippingBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummarySubtotalBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummarySubtotalBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryTaxesBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryTaxesBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutOrderSummaryTotalsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutOrderSummaryTotalsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutPaymentBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutPaymentBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutPickupOptionsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutPickupOptionsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutShippingAddressBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutShippingAddressBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutShippingMethodBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutShippingMethodBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutShippingMethodsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutShippingMethodsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutTermsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutTermsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CheckoutTotalsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CheckoutTotalsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ClassicShortcode' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ClassicShortcode.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ClassicTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ClassicTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ComingSoon' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ComingSoon.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\CustomerAccount' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/CustomerAccount.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\EmptyCartBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/EmptyCartBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\EmptyMiniCartContentsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/EmptyMiniCartContentsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\EnableBlockJsonAssetsTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/EnableBlockJsonAssetsTrait.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FeaturedCategory' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/FeaturedCategory.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FeaturedItem' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/FeaturedItem.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FeaturedProduct' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/FeaturedProduct.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FilledCartBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/FilledCartBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FilledMiniCartContentsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/FilledMiniCartContentsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\FilterWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/FilterWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\HandpickedProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/HandpickedProducts.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCart.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartCartButtonBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartCartButtonBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartCheckoutButtonBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartCheckoutButtonBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartContents' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartContents.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartFooterBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartFooterBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartItemsBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartItemsBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartProductsTableBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartProductsTableBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartShoppingButtonBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartShoppingButtonBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartTitleBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartTitleBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartTitleItemsCounterBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartTitleItemsCounterBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\MiniCartTitleLabelBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/MiniCartTitleLabelBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AbstractOrderConfirmationBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AbstractOrderConfirmationBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AdditionalFields' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AdditionalFields.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AdditionalFieldsWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AdditionalFieldsWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\AdditionalInformation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/AdditionalInformation.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\BillingAddress' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/BillingAddress.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\BillingWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/BillingWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\CreateAccount' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/CreateAccount.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Downloads' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Downloads.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\DownloadsWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/DownloadsWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\ShippingAddress' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/ShippingAddress.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\ShippingWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/ShippingWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Status' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Status.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Summary' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Summary.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\Totals' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/Totals.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\OrderConfirmation\\TotalsWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/OrderConfirmation/TotalsWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\PageContentWrapper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/PageContentWrapper.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\PriceFilter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/PriceFilter.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProceedToCheckoutBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProceedToCheckoutBlock.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductAverageRating' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductAverageRating.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductBestSellers' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductBestSellers.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductButton' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductButton.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCategories' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCategories.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCategory' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCategory.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/Controller.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\HandlerRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/HandlerRegistry.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\NoResults' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/NoResults.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\QueryBuilder' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/QueryBuilder.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\Renderer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/Renderer.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductCollection\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductCollection/Utils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductDescription' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductDescription.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductDetails' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductDetails.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterActive' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterActive.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterAttribute' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterAttribute.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterCheckboxList' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterCheckboxList.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterChips' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterChips.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterClearButton' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterClearButton.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterPrice' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterPrice.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterPriceSlider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterPriceSlider.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterRating' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterRating.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterRemovableChips' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterRemovableChips.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilterStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilterStatus.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductFilters' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductFilters.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGallery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductGallery.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGalleryLargeImage' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductGalleryLargeImage.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGalleryLargeImageNextPrevious' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductGalleryLargeImageNextPrevious.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductGalleryThumbnails' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductGalleryThumbnails.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductImage' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductImage.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductImageGallery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductImageGallery.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductMeta' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductMeta.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductNew' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductNew.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductOnSale' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductOnSale.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductPrice' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductPrice.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductQuery.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductRating' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductRating.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductRatingCounter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductRatingCounter.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductRatingStars' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductRatingStars.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductResultsCount' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductResultsCount.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductReviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductReviews.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSKU' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductSKU.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSaleBadge' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductSaleBadge.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSearch' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductSearch.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSpecifications' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductSpecifications.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductStockIndicator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductStockIndicator.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductSummary' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductSummary.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTag' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductTag.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTitle' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductTitle.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductTopRated' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductTopRated.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ProductsByAttribute' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ProductsByAttribute.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\RatingFilter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/RatingFilter.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\RelatedProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/RelatedProducts.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ReviewsByCategory' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ReviewsByCategory.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\ReviewsByProduct' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/ReviewsByProduct.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewAuthorName' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewAuthorName.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewContent' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewContent.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewDate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewDate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewForm' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewForm.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewRating' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewRating.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviews.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPagination' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPagination.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPaginationNext' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPaginationNext.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPaginationNumbers' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPaginationNumbers.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsPaginationPrevious' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsPaginationPrevious.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\Reviews\\ProductReviewsTitle' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/Reviews/ProductReviewsTitle.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\SingleProduct' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/SingleProduct.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\StockFilter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/StockFilter.php'
	),
	'Automattic\\WooCommerce\\Blocks\\BlockTypes\\StoreNotices' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/BlockTypes/StoreNotices.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Bootstrap' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Bootstrap.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Package' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Package.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFields' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/CheckoutFields.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsAdmin' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsAdmin.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsFrontend' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsFrontend.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsSchema\\DocumentObject' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsSchema/DocumentObject.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CheckoutFieldsSchema\\Validation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/CheckoutFieldsSchema/Validation.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\CreateAccount' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/CreateAccount.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\DraftOrders' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/DraftOrders.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\Email\\CustomerNewAccount' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/Email/CustomerNewAccount.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\FeatureGating' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/FeatureGating.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\GoogleAnalytics' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/GoogleAnalytics.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\Hydration' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/Hydration.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Domain\\Services\\Notices' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Domain/Services/Notices.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Images\\Pexels' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Images/Pexels.php'
	),
	'Automattic\\WooCommerce\\Blocks\\InboxNotifications' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/InboxNotifications.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Installer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Installer.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Integrations\\IntegrationInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Integrations/IntegrationInterface.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Integrations\\IntegrationRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Integrations/IntegrationRegistry.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Library' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Library.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Options' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Options.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Package' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Package.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Patterns\\AIPatterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Patterns/AIPatterns.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Patterns\\PTKClient' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Patterns/PTKClient.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Patterns\\PTKPatternsStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Patterns/PTKPatternsStore.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Patterns\\PatternRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Patterns/PatternRegistry.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\Api' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/Api.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\AbstractPaymentMethodType' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/Integrations/AbstractPaymentMethodType.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\BankTransfer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/Integrations/BankTransfer.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\CashOnDelivery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/Integrations/CashOnDelivery.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\Cheque' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/Integrations/Cheque.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\Integrations\\PayPal' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/Integrations/PayPal.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\PaymentMethodRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/PaymentMethodRegistry.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Payments\\PaymentMethodTypeInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Payments/PaymentMethodTypeInterface.php'
	),
	'Automattic\\WooCommerce\\Blocks\\QueryFilters' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/QueryFilters.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Registry\\AbstractDependencyType' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Registry/AbstractDependencyType.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Registry\\Container' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Registry/Container.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Registry\\FactoryType' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Registry/FactoryType.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Registry\\SharedType' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Registry/SharedType.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Shipping\\PickupLocation' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Shipping/PickupLocation.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Shipping\\ShippingController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Shipping/ShippingController.php'
	),
	'Automattic\\WooCommerce\\Blocks\\TemplateOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/TemplateOptions.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractPageTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/AbstractPageTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/AbstractTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractTemplateCompatibility' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/AbstractTemplateCompatibility.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\AbstractTemplatePart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/AbstractTemplatePart.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ArchiveProductTemplatesCompatibility' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ArchiveProductTemplatesCompatibility.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\CartTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/CartTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\CheckoutHeaderTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/CheckoutHeaderTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\CheckoutTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/CheckoutTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ClassicTemplatesCompatibility' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ClassicTemplatesCompatibility.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ComingSoonSocialLinksTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ComingSoonSocialLinksTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ComingSoonTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ComingSoonTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ExternalProductAddToCartWithOptionsTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ExternalProductAddToCartWithOptionsTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\GroupedProductAddToCartWithOptionsTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/GroupedProductAddToCartWithOptionsTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\MiniCartTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/MiniCartTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\OrderConfirmationTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/OrderConfirmationTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ProductAttributeTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ProductAttributeTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ProductCatalogTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ProductCatalogTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ProductCategoryTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ProductCategoryTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ProductSearchResultsTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ProductSearchResultsTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\ProductTagTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/ProductTagTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\SimpleProductAddToCartWithOptionsTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/SimpleProductAddToCartWithOptionsTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\SingleProductTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/SingleProductTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\SingleProductTemplateCompatibility' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/SingleProductTemplateCompatibility.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Templates\\VariableProductAddToCartWithOptionsTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Templates/VariableProductAddToCartWithOptionsTemplate.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\BlockHooksTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/BlockHooksTrait.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\BlockTemplateUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/BlockTemplateUtils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\BlocksWpQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/BlocksWpQuery.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\CartCheckoutUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/CartCheckoutUtils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\MiniCartUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/MiniCartUtils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\ProductAvailabilityUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/ProductAvailabilityUtils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\ProductGalleryUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/ProductGalleryUtils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\StyleAttributesUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/StyleAttributesUtils.php'
	),
	'Automattic\\WooCommerce\\Blocks\\Utils\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Blocks/Utils/Utils.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\BuiltInExporters' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/BuiltInExporters.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\BuiltInStepProcessors' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/BuiltInStepProcessors.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ClassExtractor' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ClassExtractor.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Cli' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Cli.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Cli\\ExportCli' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Cli/ExportCli.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Cli\\ImportCli' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Cli/ImportCli.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ExportSchema' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ExportSchema.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Exporters\\ExportInstallPluginSteps' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Exporters/ExportInstallPluginSteps.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Exporters\\ExportInstallThemeSteps' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Exporters/ExportInstallThemeSteps.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Exporters\\HasAlias' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Exporters/HasAlias.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Exporters\\StepExporter' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Exporters/StepExporter.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ImportSchema' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ImportSchema.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ImportStep' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ImportStep.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportActivatePlugin' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Importers/ImportActivatePlugin.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportActivateTheme' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Importers/ImportActivateTheme.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportInstallPlugin' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Importers/ImportInstallPlugin.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportInstallTheme' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Importers/ImportInstallTheme.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportRunSql' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Importers/ImportRunSql.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Importers\\ImportSetSiteOptions' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Importers/ImportSetSiteOptions.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Logger' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Logger.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResourceStorages' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResourceStorages.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\LocalPluginResourceStorage' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResourceStorages/LocalPluginResourceStorage.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\LocalThemeResourceStorage' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResourceStorages/LocalThemeResourceStorage.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\OrgPluginResourceStorage' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResourceStorages/OrgPluginResourceStorage.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\OrgThemeResourceStorage' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResourceStorages/OrgThemeResourceStorage.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResourceStorages\\ResourceStorage' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResourceStorages/ResourceStorage.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResultFormatters\\CliResultFormatter' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResultFormatters/CliResultFormatter.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\ResultFormatters\\JsonResultFormatter' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/ResultFormatters/JsonResultFormatter.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Schemas\\JsonSchema' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Schemas/JsonSchema.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\StepProcessor' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/StepProcessor.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\StepProcessorResult' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/StepProcessorResult.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\ActivatePlugin' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/ActivatePlugin.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\ActivateTheme' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/ActivateTheme.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\InstallPlugin' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/InstallPlugin.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\InstallTheme' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/InstallTheme.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\RunSql' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/RunSql.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\SetSiteOptions' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/SetSiteOptions.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Steps\\Step' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Steps/Step.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\UsePluginHelpers' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/UsePluginHelpers.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\UsePubSub' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/UsePubSub.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\UseWPFunctions' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/UseWPFunctions.php'
	),
	'Automattic\\WooCommerce\\Blueprint\\Util' => array(
		'version' => '0.0.1.0',
		'path'    => $baseDir . '/packages/blueprint/src/Util.php'
	),
	'Automattic\\WooCommerce\\Caches\\OrderCache' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caches/OrderCache.php'
	),
	'Automattic\\WooCommerce\\Caches\\OrderCacheController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caches/OrderCacheController.php'
	),
	'Automattic\\WooCommerce\\Caches\\OrderCountCache' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caches/OrderCountCache.php'
	),
	'Automattic\\WooCommerce\\Caches\\OrderCountCacheService' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caches/OrderCountCacheService.php'
	),
	'Automattic\\WooCommerce\\Caching\\CacheEngine' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caching/CacheEngine.php'
	),
	'Automattic\\WooCommerce\\Caching\\CacheException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caching/CacheException.php'
	),
	'Automattic\\WooCommerce\\Caching\\CacheNameSpaceTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caching/CacheNameSpaceTrait.php'
	),
	'Automattic\\WooCommerce\\Caching\\ObjectCache' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caching/ObjectCache.php'
	),
	'Automattic\\WooCommerce\\Caching\\WPCacheEngine' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Caching/WPCacheEngine.php'
	),
	'Automattic\\WooCommerce\\Checkout\\Helpers\\ReserveStock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Checkout/Helpers/ReserveStock.php'
	),
	'Automattic\\WooCommerce\\Checkout\\Helpers\\ReserveStockException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Checkout/Helpers/ReserveStockException.php'
	),
	'Automattic\\WooCommerce\\Container' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Container.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\CLIRunner' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/CustomOrderTable/CLIRunner.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostMetaToOrderMetaMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostMetaToOrderMetaMigrator.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostToOrderAddressTableMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostToOrderAddressTableMigrator.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostToOrderOpTableMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostToOrderOpTableMigrator.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostToOrderTableMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostToOrderTableMigrator.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\CustomOrderTable\\PostsToOrdersMigrationController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/CustomOrderTable/PostsToOrdersMigrationController.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\MetaToCustomTableMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/MetaToCustomTableMigrator.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\MetaToMetaTableMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/MetaToMetaTableMigrator.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\MigrationHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/MigrationHelper.php'
	),
	'Automattic\\WooCommerce\\Database\\Migrations\\TableMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Database/Migrations/TableMigrator.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\AccessDeniedException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Bootstrap' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/class-bootstrap.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\ConflictException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Container' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/class-container.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Email_Css_Inliner' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/class-email-css-inliner.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Email_Editor_Container' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/class-email-editor-container.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Dependency_Check' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-dependency-check.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Email_Api_Controller' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-email-api-controller.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Email_Editor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-email-editor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Email_Styles_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-email-styles-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Patterns\\Abstract_Pattern' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Patterns/class-abstract-pattern.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Patterns\\Patterns' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Patterns/class-patterns.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\PersonalizationTags\\HTML_Tag_Processor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/PersonalizationTags/class-html-tag-processor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\PersonalizationTags\\Personalization_Tag' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/PersonalizationTags/class-personalization-tag.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\PersonalizationTags\\Personalization_Tags_Registry' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/PersonalizationTags/class-personalization-tags-registry.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Personalizer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-personalizer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Block_Renderer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-block-renderer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Blocks_Parser' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-blocks-parser.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Blocks_Registry' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-blocks-registry.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Content_Renderer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-content-renderer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Layout\\Flex_Layout_Renderer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Layout/class-flex-layout-renderer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Border_Style_Postprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/class-border-style-postprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Highlighting_Postprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/class-highlighting-postprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Postprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/interface-postprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Postprocessors\\Variables_Postprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Postprocessors/class-variables-postprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Blocks_Width_Preprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-blocks-width-preprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Cleanup_Preprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-cleanup-preprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Preprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/interface-preprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Quote_Preprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-quote-preprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Spacing_Preprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-spacing-preprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Preprocessors\\Typography_Preprocessor' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/Preprocessors/class-typography-preprocessor.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\ContentRenderer\\Process_Manager' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/ContentRenderer/class-process-manager.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\Css_Inliner' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/interface-css-inliner.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Renderer\\Renderer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Renderer/class-renderer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Send_Preview_Email' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-send-preview-email.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Settings_Controller' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-settings-controller.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Templates\\Template' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Templates/class-template.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Templates\\Templates' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Templates/class-templates.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Templates\\Templates_Registry' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/Templates/class-templates-registry.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\Theme_Controller' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-theme-controller.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Engine\\User_Theme' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Engine/class-user-theme.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Exception' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\HttpAwareException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Initializer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/class-initializer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Abstract_Block_Renderer' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-abstract-block-renderer.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Button' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-button.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Buttons' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-buttons.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Column' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-column.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Columns' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-columns.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Fallback' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-fallback.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Group' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-group.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Image' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-image.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\List_Block' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-list-block.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\List_Item' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-list-item.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Quote' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-quote.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Core\\Renderer\\Blocks\\Text' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Core/Renderer/Blocks/class-text.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Integrations\\Utils\\Dom_Document_Helper' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Integrations/Utils/class-dom-document-helper.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\InvalidStateException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\NewsletterProcessingException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\NotFoundException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Package' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/class-package.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\RuntimeException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\UnexpectedValueException' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/exceptions.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Builder' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/class-builder.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/class-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Any_Of_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-any-of-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Array_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-array-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Boolean_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-boolean-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Integer_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-integer-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Null_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-null-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Number_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-number-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\Object_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-object-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\One_Of_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-one-of-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Schema\\String_Schema' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/Schema/class-string-schema.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Validation_Exception' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/class-validation-exception.php'
	),
	'Automattic\\WooCommerce\\EmailEditor\\Validator\\Validator' => array(
		'version' => 'dev-add-a-basic-integration-of-the-block-email-editor-to-the-woo-core',
		'path'    => $baseDir . '/packages/email-editor/src/Validator/class-validator.php'
	),
	'Automattic\\WooCommerce\\Enums\\CatalogVisibility' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/CatalogVisibility.php'
	),
	'Automattic\\WooCommerce\\Enums\\OrderInternalStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/OrderInternalStatus.php'
	),
	'Automattic\\WooCommerce\\Enums\\OrderStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/OrderStatus.php'
	),
	'Automattic\\WooCommerce\\Enums\\ProductStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/ProductStatus.php'
	),
	'Automattic\\WooCommerce\\Enums\\ProductStockStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/ProductStockStatus.php'
	),
	'Automattic\\WooCommerce\\Enums\\ProductTaxStatus' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/ProductTaxStatus.php'
	),
	'Automattic\\WooCommerce\\Enums\\ProductType' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Enums/ProductType.php'
	),
	'Automattic\\WooCommerce\\Internal\\AddressProvider\\AddressProviderController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/AddressProvider/AddressProviderController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ActivityPanels' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ActivityPanels.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Analytics' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Analytics.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\AbstractBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/AbstractBlock.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\AbstractBlockTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/AbstractBlockTemplate.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\Block' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/Block.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockContainerTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockContainerTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockFormattedTemplateTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockFormattedTemplateTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockTemplate.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\BlockTemplates\\BlockTemplateLogger' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/BlockTemplates/BlockTemplateLogger.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\CategoryLookup' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/CategoryLookup.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Coupons' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Coupons.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\CouponsMovedTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/CouponsMovedTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\CustomerEffortScoreTracks' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/CustomerEffortScoreTracks.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\EmailImprovements\\EmailImprovements' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/EmailImprovements/EmailImprovements.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\EmailPreview\\EmailPreview' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/EmailPreview/EmailPreview.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\EmailPreview\\EmailPreviewRestController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/EmailPreview/EmailPreviewRestController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Events' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Events.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\FeaturePlugin' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/FeaturePlugin.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Homescreen' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Homescreen.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ImportExport\\CSVUploadHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ImportExport/CSVUploadHelper.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Loader' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Loader.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\File' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/FileV2/File.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\FileController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/FileV2/FileController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\FileExporter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/FileV2/FileExporter.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\FileListTable' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/FileV2/FileListTable.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\FileV2\\SearchListTable' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/FileV2/SearchListTable.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\LogHandlerFileV2' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/LogHandlerFileV2.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\PageController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/PageController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Logging\\Settings' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Logging/Settings.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Marketing' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Marketing.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Marketing\\MarketingSpecs' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Marketing/MarketingSpecs.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Marketplace' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Marketplace.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\MobileAppBanner' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/MobileAppBanner.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\CustomizeStoreWithBlocks' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/CustomizeStoreWithBlocks.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\CustomizingProductCatalog' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/CustomizingProductCatalog.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\EUVATNumber' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/EUVATNumber.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\EditProductsOnTheMove' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/EditProductsOnTheMove.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\EmailImprovements' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/EmailImprovements.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\FirstProduct' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/FirstProduct.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\GivingFeedbackNotes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/GivingFeedbackNotes.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\InstallJPAndWCSPlugins' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/InstallJPAndWCSPlugins.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\LaunchChecklist' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/LaunchChecklist.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MagentoMigration' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/MagentoMigration.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\ManageOrdersOnTheGo' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/ManageOrdersOnTheGo.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MarketingJetpack' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/MarketingJetpack.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MigrateFromShopify' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/MigrateFromShopify.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\MobileApp' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/MobileApp.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\NewSalesRecord' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/NewSalesRecord.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\OnboardingPayments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/OnboardingPayments.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\OnlineClothingStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/OnlineClothingStore.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\OrderMilestones' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/OrderMilestones.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PaymentsMoreInfoNeeded' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/PaymentsMoreInfoNeeded.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PaymentsRemindMeLater' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/PaymentsRemindMeLater.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PerformanceOnMobile' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/PerformanceOnMobile.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\PersonalizeStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/PersonalizeStore.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\RealTimeOrderAlerts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/RealTimeOrderAlerts.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\SellingOnlineCourses' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/SellingOnlineCourses.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\TrackingOptIn' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/TrackingOptIn.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\UnsecuredReportFiles' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/UnsecuredReportFiles.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\WooCommercePayments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/WooCommercePayments.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\WooCommerceSubscriptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/WooCommerceSubscriptions.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Notes\\WooSubscriptionsNotes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Notes/WooSubscriptionsNotes.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\Onboarding' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/Onboarding.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingFonts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingFonts.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingHelper.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingIndustries' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingIndustries.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingJetpack' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingJetpack.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingMailchimp' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingMailchimp.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingProducts' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingProducts.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingProfile' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingProfile.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingSetupWizard' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingSetupWizard.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Onboarding\\OnboardingSync' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Onboarding/OnboardingSync.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\COTRedirectionController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/COTRedirectionController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\Edit' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/Edit.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\EditLock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/EditLock.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\ListTable' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/ListTable.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\CustomMetaBox' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/CustomMetaBox.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\CustomerHistory' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/CustomerHistory.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\OrderAttribution' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/OrderAttribution.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\MetaBoxes\\TaxonomiesMetaBox' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/MetaBoxes/TaxonomiesMetaBox.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\PageController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/PageController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Orders\\PostsRedirectionController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Orders/PostsRedirectionController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Component' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/Component.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\ComponentTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/ComponentTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Field' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/Field.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\FormFactory' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/FormFactory.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Section' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/Section.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Subsection' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/Subsection.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductForm\\Tab' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductForm/Tab.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\Reviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductReviews/Reviews.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\ReviewsCommentsOverrides' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductReviews/ReviewsCommentsOverrides.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\ReviewsListTable' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductReviews/ReviewsListTable.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ProductReviews\\ReviewsUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ProductReviews/ReviewsUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\DefaultFreeExtensions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/DefaultFreeExtensions.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\EvaluateExtension' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/EvaluateExtension.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/Init.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\ProcessCoreProfilerPluginInstallOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/ProcessCoreProfilerPluginInstallOptions.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\RemoteFreeExtensions\\RemoteFreeExtensionsDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/RemoteFreeExtensions/RemoteFreeExtensionsDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\RemoteInboxNotifications' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/RemoteInboxNotifications.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\CustomersScheduler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Schedulers/CustomersScheduler.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\ImportInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Schedulers/ImportInterface.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\ImportScheduler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Schedulers/ImportScheduler.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\MailchimpScheduler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Schedulers/MailchimpScheduler.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Schedulers\\OrdersScheduler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Schedulers/OrdersScheduler.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Exceptions\\ApiArgumentException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/Exceptions/ApiArgumentException.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Exceptions\\ApiException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/Exceptions/ApiException.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\AmazonPay' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/AmazonPay.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\MercadoPago' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/MercadoPago.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\Mollie' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/Mollie.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\PayPal' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/PayPal.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\PaymentGateway' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/PaymentGateway.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\PseudoWCPaymentGateway' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/PseudoWCPaymentGateway.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\Stripe' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/Stripe.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WCCore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WCCore.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WooPayments.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsRestController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsRestController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsService' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsService.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Payments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/Payments.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentsController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\PaymentsRestController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/PaymentsRestController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Settings\\Utils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Settings/Utils.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ShippingLabelBanner' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ShippingLabelBanner.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\ShippingLabelBannerDisplayRules' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/ShippingLabelBannerDisplayRules.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\SiteHealth' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/SiteHealth.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\Incentives\\Incentive' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Suggestions/Incentives/Incentive.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\Incentives\\WooPayments' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Suggestions/Incentives/WooPayments.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\PaymentExtensionSuggestionIncentives' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Suggestions/PaymentExtensionSuggestionIncentives.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Suggestions\\PaymentExtensionSuggestions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Suggestions/PaymentExtensionSuggestions.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Survey' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Survey.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\SystemStatusReport' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/SystemStatusReport.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\Translations' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/Translations.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCAdminAssets' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCAdminAssets.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCAdminSharedSettings' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCAdminSharedSettings.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCAdminUser' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCAdminUser.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\DefaultPromotions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCPayPromotion/DefaultPromotions.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\Init' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCPayPromotion/Init.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\WCPayPromotionDataSourcePoller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCPayPromotion/WCPayPromotionDataSourcePoller.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WCPayPromotion\\WCPaymentGatewayPreInstallWCPayPromotion' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WCPayPromotion/WCPaymentGatewayPreInstallWCPayPromotion.php'
	),
	'Automattic\\WooCommerce\\Internal\\Admin\\WcPayWelcomePage' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Admin/WcPayWelcomePage.php'
	),
	'Automattic\\WooCommerce\\Internal\\AssignDefaultCategory' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/AssignDefaultCategory.php'
	),
	'Automattic\\WooCommerce\\Internal\\BatchProcessing\\BatchProcessingController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/BatchProcessing/BatchProcessingController.php'
	),
	'Automattic\\WooCommerce\\Internal\\BatchProcessing\\BatchProcessorInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/BatchProcessing/BatchProcessorInterface.php'
	),
	'Automattic\\WooCommerce\\Internal\\Brands' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Brands.php'
	),
	'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonAdminBarBadge' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ComingSoon/ComingSoonAdminBarBadge.php'
	),
	'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonCacheInvalidator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ComingSoon/ComingSoonCacheInvalidator.php'
	),
	'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ComingSoon/ComingSoonHelper.php'
	),
	'Automattic\\WooCommerce\\Internal\\ComingSoon\\ComingSoonRequestHandler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ComingSoon/ComingSoonRequestHandler.php'
	),
	'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CogsAwareRestControllerTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/CostOfGoodsSold/CogsAwareRestControllerTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CogsAwareTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/CostOfGoodsSold/CogsAwareTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CogsAwareUnitTestSuiteTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/CostOfGoodsSold/CogsAwareUnitTestSuiteTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\CostOfGoodsSold\\CostOfGoodsSoldController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/CostOfGoodsSold/CostOfGoodsSoldController.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\CustomMetaDataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/CustomMetaDataStore.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\CustomOrdersTableController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/CustomOrdersTableController.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\DataSynchronizer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/DataSynchronizer.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\LegacyDataCleanup' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/LegacyDataCleanup.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\LegacyDataHandler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/LegacyDataHandler.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableDataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableDataStore.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableDataStoreMeta' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableDataStoreMeta.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableFieldQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableFieldQuery.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableMetaQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableMetaQuery.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableQuery.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableRefundDataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableRefundDataStore.php'
	),
	'Automattic\\WooCommerce\\Internal\\DataStores\\Orders\\OrdersTableSearchQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DataStores/Orders/OrdersTableSearchQuery.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\AbstractServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/AbstractServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ContainerException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ContainerException.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\Definition' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/Definition.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ExtendedContainer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ExtendedContainer.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\RuntimeContainer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/RuntimeContainer.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AbstractInterfaceServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AbstractInterfaceServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AddressProviderServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AddressProviderServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AdminSettingsServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AdminSettingsServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AdminSuggestionsServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AdminSuggestionsServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\AssignDefaultCategoryServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/AssignDefaultCategoryServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\BatchProcessingServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/BatchProcessingServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\COTMigrationServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/COTMigrationServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ComingSoonServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ComingSoonServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\CostOfGoodsSoldServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/CostOfGoodsSoldServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\DownloadPermissionsAdjusterServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/DownloadPermissionsAdjusterServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\EmailEditorServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/EmailEditorServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\EmailPreviewServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/EmailPreviewServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\EnginesServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/EnginesServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\FeaturesServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/FeaturesServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ImportExportServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ImportExportServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\LayoutTemplatesServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/LayoutTemplatesServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\LoggingServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/LoggingServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\MarketingServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/MarketingServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\MarketplaceServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/MarketplaceServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ObjectCacheServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ObjectCacheServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OptionSanitizerServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OptionSanitizerServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrderAdminServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrderAdminServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrderAttributionServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrderAttributionServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrderMetaBoxServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrderMetaBoxServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrdersControllersServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrdersControllersServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\OrdersDataStoreServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/OrdersDataStoreServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductAttributesLookupServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductAttributesLookupServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductDownloadsServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductDownloadsServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductFiltersServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductFiltersServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductImageBySKUServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductImageBySKUServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProductReviewsServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProductReviewsServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\ProxiesServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/ProxiesServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\RestockRefundedItemsAdjusterServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/RestockRefundedItemsAdjusterServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\StatsServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/StatsServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DependencyManagement\\ServiceProviders\\UtilsClassesServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DependencyManagement/ServiceProviders/UtilsClassesServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\DownloadPermissionsAdjuster' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/DownloadPermissionsAdjuster.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\BlockEmailRenderer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/BlockEmailRenderer.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailApiController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/EmailApiController.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailPatterns\\PatternsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/EmailPatterns/PatternsController.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailPatterns\\WooEmailContentPattern' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/EmailPatterns/WooEmailContentPattern.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailTemplates\\TemplateApiController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/EmailTemplates/TemplateApiController.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailTemplates\\TemplatesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/EmailTemplates/TemplatesController.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\EmailTemplates\\WooEmailTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/EmailTemplates/WooEmailTemplate.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\Integration' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/Integration.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\Package' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/Package.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PageRenderer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PageRenderer.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTagManager' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PersonalizationTagManager.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\AbstractTagProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/AbstractTagProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\CustomerTagsProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/CustomerTagsProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\OrderTagsProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/OrderTagsProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\SiteTagsProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/SiteTagsProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\PersonalizationTags\\StoreTagsProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/PersonalizationTags/StoreTagsProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\Renderer\\Blocks\\WooContent' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/Renderer/Blocks/WooContent.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\TransactionalEmailPersonalizer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/TransactionalEmailPersonalizer.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailPostsGenerator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailPostsGenerator.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailPostsManager' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailPostsManager.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmails' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmails.php'
	),
	'Automattic\\WooCommerce\\Internal\\EmailEditor\\WooContentProcessor' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/EmailEditor/WooContentProcessor.php'
	),
	'Automattic\\WooCommerce\\Internal\\Email\\EmailColors' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Email/EmailColors.php'
	),
	'Automattic\\WooCommerce\\Internal\\Email\\EmailFont' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Email/EmailFont.php'
	),
	'Automattic\\WooCommerce\\Internal\\Email\\EmailStyleSync' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Email/EmailStyleSync.php'
	),
	'Automattic\\WooCommerce\\Internal\\Email\\OrderPriceFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Email/OrderPriceFormatter.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\FeaturesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/FeaturesController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\AbstractProductFormTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/AbstractProductFormTemplate.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\DownloadableProductTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/DownloadableProductTrait.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\Group' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/Group.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\ProductBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/ProductBlock.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\ProductVariationTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/ProductVariationTemplate.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\Section' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/Section.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\SimpleProductTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/SimpleProductTemplate.php'
	),
	'Automattic\\WooCommerce\\Internal\\Features\\ProductBlockEditor\\ProductTemplates\\Subsection' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Features/ProductBlockEditor/ProductTemplates/Subsection.php'
	),
	'Automattic\\WooCommerce\\Internal\\Font\\FontFace' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Font/FontFace.php'
	),
	'Automattic\\WooCommerce\\Internal\\Font\\FontFamily' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Font/FontFamily.php'
	),
	'Automattic\\WooCommerce\\Internal\\Integrations\\WPConsentAPI' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Integrations/WPConsentAPI.php'
	),
	'Automattic\\WooCommerce\\Internal\\Logging\\RemoteLogger' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Logging/RemoteLogger.php'
	),
	'Automattic\\WooCommerce\\Internal\\Logging\\SafeGlobalFunctionProxy' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Logging/SafeGlobalFunctionProxy.php'
	),
	'Automattic\\WooCommerce\\Internal\\McStats' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/McStats.php'
	),
	'Automattic\\WooCommerce\\Internal\\OrderCouponDataMigrator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/OrderCouponDataMigrator.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\CouponsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/CouponsController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\IppFunctions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/IppFunctions.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\MobileMessagingHandler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/MobileMessagingHandler.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\OrderActionsRestController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/OrderActionsRestController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\OrderAttributionBlocksController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/OrderAttributionBlocksController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\OrderAttributionController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/OrderAttributionController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\OrderStatusRestController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/OrderStatusRestController.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\PaymentInfo' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/PaymentInfo.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\PointOfSaleOrderUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/PointOfSaleOrderUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Orders\\TaxesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Orders/TaxesController.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\CLIRunner' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductAttributesLookup/CLIRunner.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\DataRegenerator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductAttributesLookup/DataRegenerator.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\Filterer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductAttributesLookup/Filterer.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductAttributesLookup\\LookupDataStore' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductAttributesLookup/LookupDataStore.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Admin\\SyncUI' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Admin/SyncUI.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Admin\\Table' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Admin/Table.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Admin\\UI' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Admin/UI.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\ApprovedDirectoriesException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/ApprovedDirectoriesException.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Register' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Register.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\StoredUrl' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/StoredUrl.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductDownloads\\ApprovedDirectories\\Synchronize' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductDownloads/ApprovedDirectories/Synchronize.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductFilters\\CacheController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductFilters/CacheController.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductFilters\\FilterData' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductFilters/FilterData.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductFilters\\FilterDataProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductFilters/FilterDataProvider.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductFilters\\Interfaces\\QueryClausesGenerator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductFilters/Interfaces/QueryClausesGenerator.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductFilters\\MainQueryController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductFilters/MainQueryController.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductFilters\\QueryClauses' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductFilters/QueryClauses.php'
	),
	'Automattic\\WooCommerce\\Internal\\ProductImage\\MatchImageBySKU' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ProductImage/MatchImageBySKU.php'
	),
	'Automattic\\WooCommerce\\Internal\\ReceiptRendering\\ReceiptRenderingEngine' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ReceiptRendering/ReceiptRenderingEngine.php'
	),
	'Automattic\\WooCommerce\\Internal\\ReceiptRendering\\ReceiptRenderingRestController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/ReceiptRendering/ReceiptRenderingRestController.php'
	),
	'Automattic\\WooCommerce\\Internal\\RegisterHooksInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/RegisterHooksInterface.php'
	),
	'Automattic\\WooCommerce\\Internal\\RestApiControllerBase' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/RestApiControllerBase.php'
	),
	'Automattic\\WooCommerce\\Internal\\RestApiParameterUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/RestApiParameterUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\RestockRefundedItemsAdjuster' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/RestockRefundedItemsAdjuster.php'
	),
	'Automattic\\WooCommerce\\Internal\\Settings\\OptionSanitizer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Settings/OptionSanitizer.php'
	),
	'Automattic\\WooCommerce\\Internal\\Traits\\AccessiblePrivateMethods' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Traits/AccessiblePrivateMethods.php'
	),
	'Automattic\\WooCommerce\\Internal\\Traits\\OrderAttributionMeta' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Traits/OrderAttributionMeta.php'
	),
	'Automattic\\WooCommerce\\Internal\\Traits\\ScriptDebug' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Traits/ScriptDebug.php'
	),
	'Automattic\\WooCommerce\\Internal\\TransientFiles\\TransientFilesEngine' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/TransientFiles/TransientFilesEngine.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\ArrayUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/ArrayUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\BlocksUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/BlocksUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\COTMigrationUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/COTMigrationUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\DatabaseUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/DatabaseUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\FilesystemUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/FilesystemUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\HtmlSanitizer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/HtmlSanitizer.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\LegacyRestApiStub' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/LegacyRestApiStub.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\PluginInstaller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/PluginInstaller.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\Types' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/Types.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\URL' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/URL.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\URLException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/URLException.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\Users' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/Users.php'
	),
	'Automattic\\WooCommerce\\Internal\\Utilities\\WebhookUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/Utilities/WebhookUtil.php'
	),
	'Automattic\\WooCommerce\\Internal\\WCCom\\ConnectionHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Internal/WCCom/ConnectionHelper.php'
	),
	'Automattic\\WooCommerce\\LayoutTemplates\\LayoutTemplateRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/LayoutTemplates/LayoutTemplateRegistry.php'
	),
	'Automattic\\WooCommerce\\Packages' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Packages.php'
	),
	'Automattic\\WooCommerce\\Proxies\\ActionsProxy' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Proxies/ActionsProxy.php'
	),
	'Automattic\\WooCommerce\\Proxies\\LegacyProxy' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Proxies/LegacyProxy.php'
	),
	'Automattic\\WooCommerce\\RestApi\\Package' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Package.php'
	),
	'Automattic\\WooCommerce\\RestApi\\Server' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Server.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\AdminNotesHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/AdminNotesHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\CouponHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/CouponHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\CustomerHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/CustomerHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\OrderHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/OrderHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\ProductHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/ProductHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\QueueHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/QueueHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\SettingsHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/SettingsHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\UnitTests\\Helpers\\ShippingHelper' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/legacy/unit-tests/rest-api/Helpers/ShippingHelper.php'
	),
	'Automattic\\WooCommerce\\RestApi\\Utilities\\ImageAttachment' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Utilities/ImageAttachment.php'
	),
	'Automattic\\WooCommerce\\RestApi\\Utilities\\SingletonTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Utilities/SingletonTrait.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Authentication' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Authentication.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\InvalidCartException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/InvalidCartException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\InvalidStockLevelsInCartException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/InvalidStockLevelsInCartException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\NotPurchasableException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/NotPurchasableException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\OutOfStockException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/OutOfStockException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\PartialOutOfStockException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/PartialOutOfStockException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\RouteException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/RouteException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\StockAvailabilityException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/StockAvailabilityException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Exceptions\\TooManyInCartException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Exceptions/TooManyInCartException.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Formatters' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Formatters.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Formatters\\CurrencyFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Formatters/CurrencyFormatter.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Formatters\\DefaultFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Formatters/DefaultFormatter.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Formatters\\FormatterInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Formatters/FormatterInterface.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Formatters\\HtmlFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Formatters/HtmlFormatter.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Formatters\\MoneyFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Formatters/MoneyFormatter.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Legacy' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Legacy.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Payments\\PaymentContext' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Payments/PaymentContext.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Payments\\PaymentResult' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Payments/PaymentResult.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\RoutesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/RoutesController.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\RouteInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/RouteInterface.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AI\\Middleware' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/AI/Middleware.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AI\\Products' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/AI/Products.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AbstractCartRoute' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/AbstractCartRoute.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AbstractRoute' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/AbstractRoute.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\AbstractTermsRoute' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/AbstractTermsRoute.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Batch' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/Batch.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Cart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/Cart.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartAddItem' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartAddItem.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartApplyCoupon' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartApplyCoupon.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartCoupons' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartCoupons.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartCouponsByCode' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartCouponsByCode.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartExtensions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartExtensions.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartItems' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartItems.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartItemsByKey' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartItemsByKey.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartRemoveCoupon' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartRemoveCoupon.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartRemoveItem' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartRemoveItem.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartSelectShippingRate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartSelectShippingRate.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartUpdateCustomer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartUpdateCustomer.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CartUpdateItem' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CartUpdateItem.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Checkout' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/Checkout.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\CheckoutOrder' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/CheckoutOrder.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Order' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/Order.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Patterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/Patterns.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductAttributeTerms' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductAttributeTerms.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductAttributes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductAttributes.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductAttributesById' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductAttributesById.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductBrands' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductBrands.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductBrandsById' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductBrandsById.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductCategories' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductCategories.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductCategoriesById' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductCategoriesById.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductCollectionData' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductCollectionData.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductReviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductReviews.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductTags' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductTags.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\Products' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/Products.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductsById' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductsById.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Routes\\V1\\ProductsBySlug' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Routes/V1/ProductsBySlug.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\SchemaController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/SchemaController.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\ExtendSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/ExtendSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\AI\\ProductsSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/AI/ProductsSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\AbstractAddressSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/AbstractAddressSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\AbstractSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/AbstractSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\BatchSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/BatchSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\BillingAddressSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/BillingAddressSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartCouponSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CartCouponSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartExtensionsSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CartExtensionsSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartFeeSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CartFeeSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartItemSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CartItemSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CartSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CartShippingRateSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CartShippingRateSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CheckoutOrderSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CheckoutOrderSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\CheckoutSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/CheckoutSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ErrorSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ErrorSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ImageAttachmentSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ImageAttachmentSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ItemSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ItemSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderCouponSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/OrderCouponSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderFeeSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/OrderFeeSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderItemSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/OrderItemSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\OrderSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/OrderSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\PatternsSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/PatternsSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductAttributeSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ProductAttributeSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductBrandSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ProductBrandSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductCategorySchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ProductCategorySchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductCollectionDataSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ProductCollectionDataSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductReviewSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ProductReviewSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ProductSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ProductSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\ShippingAddressSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/ShippingAddressSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Schemas\\V1\\TermSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Schemas/V1/TermSchema.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\SessionHandler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/SessionHandler.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\StoreApi' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/StoreApi.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\ArrayUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/ArrayUtils.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\CartController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/CartController.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\CheckoutTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/CheckoutTrait.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\DraftOrderTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/DraftOrderTrait.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\JsonWebToken' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/JsonWebToken.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\LocalPickupUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/LocalPickupUtils.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\NoticeHandler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/NoticeHandler.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\OrderAuthorizationTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/OrderAuthorizationTrait.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\OrderController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/OrderController.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\Pagination' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/Pagination.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\PaymentUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/PaymentUtils.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\ProductItemTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/ProductItemTrait.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\ProductQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/ProductQuery.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\ProductQueryFilters' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/ProductQueryFilters.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\QuantityLimits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/QuantityLimits.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\RateLimits' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/RateLimits.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\SanitizationUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/SanitizationUtils.php'
	),
	'Automattic\\WooCommerce\\StoreApi\\Utilities\\ValidationUtils' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/StoreApi/Utilities/ValidationUtils.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\CodeHacking\\CodeHacker' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/CodeHacking/CodeHacker.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\CodeHacking\\Hacks\\BypassFinalsHack' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/CodeHacking/Hacks/BypassFinalsHack.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\CodeHacking\\Hacks\\CodeHack' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/CodeHacking/Hacks/CodeHack.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\CodeHacking\\Hacks\\FunctionsMockerHack' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/CodeHacking/Hacks/FunctionsMockerHack.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\CodeHacking\\Hacks\\StaticMockerHack' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/CodeHacking/Hacks/StaticMockerHack.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\DependencyManagement\\MockableLegacyProxy' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/DependencyManagement/MockableLegacyProxy.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\DynamicDecorator' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/DynamicDecorator.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\FakeQueue' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/FakeQueue.php'
	),
	'Automattic\\WooCommerce\\Testing\\Tools\\TestingContainer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/Tools/TestingContainer.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\LaunchYourStoreTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/LaunchYourStoreTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\MarketingCampaignTypesTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/MarketingCampaignTypesTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\MarketingCampaignsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/MarketingCampaignsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\MarketingChannelsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/MarketingChannelsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\MarketingRecommendationsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/MarketingRecommendationsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\OnboardingPluginsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/OnboardingPluginsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\OnboardingProfileTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/OnboardingProfileTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\API\\PaymentGatewaySuggestionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/API/PaymentGatewaySuggestionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\Analytics\\FeatureEnabledTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/Analytics/FeatureEnabledTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\Blueprint\\InitTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/Blueprint/InitTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\Blueprint\\RestApiTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/Blueprint/RestApiTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\Blueprint\\Stubs\\DummyExporter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/Blueprint/Stubs/DummyExporter.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\OnboardingTasks\\Tasks\\ExperimentalShippingRecommendationTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/OnboardingTasks/Tasks/ExperimentalShippingRecommendationTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\Settings\\TransformerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/Settings/TransformerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Features\\ShippingPartnerSuggestions\\DefaultShippingPartnersTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Features/ShippingPartnerSuggestions/DefaultShippingPartnersTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Marketing\\MarketingCampaignTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Marketing/MarketingCampaignTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\Marketing\\MarketingChannelsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/Marketing/MarketingChannelsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\ProductBlockEditor\\BlockRegistryTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/ProductBlockEditor/BlockRegistryTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\ProductBlockEditor\\ProductTemplates\\CustomProductFormTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/ProductBlockEditor/ProductTemplates/CustomProductFormTemplate.php'
	),
	'Automattic\\WooCommerce\\Tests\\Admin\\ProductBlockEditor\\ProductTemplates\\CustomProductFormTemplateTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Admin/ProductBlockEditor/ProductTemplates/CustomProductFormTemplateTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\AssetsController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/AssetsController.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Assets\\AssetDataRegistry' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Assets/AssetDataRegistry.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockPatterns\\BlockPatterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockPatterns/BlockPatterns.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypesController' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypesController.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\AddToCartWithOptions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/AddToCartWithOptions.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\BlockHooksTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/BlockHooksTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\Cart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/Cart.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\CatalogSorting' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/CatalogSorting.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\Checkout' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/Checkout.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\MiniCart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/MiniCart.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\OrderConfirmation\\Totals' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/OrderConfirmation/Totals.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\ProductCollection' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/ProductCollection.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\BlockTypes\\ProductQuery' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/BlockTypes/ProductQuery.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Bootstrap\\MainFile' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Bootstrap/MainFile.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Domain\\Package' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Domain/Package.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Domain\\Services\\CheckoutFieldsFrontendTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Domain/Services/CheckoutFieldsFrontendTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Domain\\Services\\CheckoutFieldsSchema\\DocumentObjectTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Domain/Services/CheckoutFieldsSchema/DocumentObjectTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Domain\\Services\\CheckoutFieldsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Domain/Services/CheckoutFieldsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Domain\\Services\\DeleteDraftOrders' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Domain/Services/DeleteDraftOrders.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Domain\\Services\\Hydration' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Domain/Services/Hydration.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Helpers\\FixtureData' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Helpers/FixtureData.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Helpers\\TestValidateSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Helpers/TestValidateSchema.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Helpers\\ValidateSchema' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Helpers/ValidateSchema.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\AddToCartWithOptionsGroupedProductSelectorItemCTAMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/AddToCartWithOptionsGroupedProductSelectorItemCTAMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\AddToCartWithOptionsGroupedProductSelectorItemTemplateMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/AddToCartWithOptionsGroupedProductSelectorItemTemplateMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\AddToCartWithOptionsGroupedProductSelectorMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/AddToCartWithOptionsGroupedProductSelectorMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\AddToCartWithOptionsMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/AddToCartWithOptionsMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\AddToCartWithOptionsQuantitySelectorMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/AddToCartWithOptionsQuantitySelectorMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\AssetDataRegistryMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/AssetDataRegistryMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\BlockHooksTestBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/BlockHooksTestBlock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\CartCheckoutUtilsMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/CartCheckoutUtilsMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\CheckoutMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/CheckoutMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\MockTestDependency' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/MockTestDependency.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\OrderConfirmation\\TotalsMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/OrderConfirmation/TotalsMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\ProductCollectionMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/ProductCollectionMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Mocks\\ProductQueryMock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Mocks/ProductQueryMock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Patterns\\PTKClientTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Patterns/PTKClientTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Patterns\\PTKPatternsStoreTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Patterns/PTKPatternsStoreTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Patterns\\PatternRegistryTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Patterns/PatternRegistryTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Registry\\Container' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Registry/Container.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Shipping\\ShippingControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Shipping/ShippingControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\ControllerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/ControllerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\ExtendSchemaTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/ExtendSchemaTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Formatters\\TestCurrencyFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Formatters/TestCurrencyFormatter.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Formatters\\TestFormatters' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Formatters/TestFormatters.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Formatters\\TestHtmlFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Formatters/TestHtmlFormatter.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Formatters\\TestMoneyFormatter' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Formatters/TestMoneyFormatter.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\MockSessionHandler' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/MockSessionHandler.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\RateLimitsTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/RateLimitsTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\AdditionalFields' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/AdditionalFields.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\Batch' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/Batch.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\Cart' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/Cart.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\CartCoupons' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/CartCoupons.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\CartExtensions' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/CartExtensions.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\CartItems' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/CartItems.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\Checkout' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/Checkout.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\ControllerTestCase' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/ControllerTestCase.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\Patterns' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/Patterns.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\ProductAttributeTerms' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/ProductAttributeTerms.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\ProductAttributes' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/ProductAttributes.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\ProductBrands' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/ProductBrands.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\ProductCollectionData' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/ProductCollectionData.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\ProductReviews' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/ProductReviews.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Routes\\Products' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Routes/Products.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Utilities\\CartControllerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Utilities/CartControllerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Utilities\\NoticeHandlerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Utilities/NoticeHandlerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Utilities\\OrderControllerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Utilities/OrderControllerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Utilities\\ProductQueryFiltersTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Utilities/ProductQueryFiltersTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\StoreApi\\Utilities\\SanitizationUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/StoreApi/Utilities/SanitizationUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Templates\\SingleProductTemplateCompatibilityTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Templates/SingleProductTemplateCompatibilityTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Templates\\SingleProductTemplateTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Templates/SingleProductTemplateTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Utils\\BlockTemplateUtilsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Utils/BlockTemplateUtilsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Utils\\CartCheckoutUtilsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Utils/CartCheckoutUtilsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Utils\\MiniCartUtilsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Utils/MiniCartUtilsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Utils\\ProductGalleryUtilsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Utils/ProductGalleryUtilsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Blocks\\Utils\\WC_Product_Custom' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Blocks/Utils/WC_Product_Custom.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\CacheExceptionTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/CacheExceptionTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\InvalidObjectCacheClass' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/InvalidObjectCacheClass.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\ObjectCacheTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/ObjectCacheTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\OrderCacheTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/OrderCacheTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\OrderCountCacheServiceTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/OrderCountCacheServiceTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\OrderCountCacheTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/OrderCountCacheTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Caching\\WPCacheEngineTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Caching/WPCacheEngineTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Database\\BlockHooksVersionTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Database/BlockHooksVersionTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Database\\Migrations\\CustomOrderTable\\PostsToOrdersMigrationControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Database/Migrations/CustomOrderTable/PostsToOrdersMigrationControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Database\\StoreNoticeOnThemeSwitch' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Database/StoreNoticeOnThemeSwitch.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\AddressProvider\\AddressProviderControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/AddressProvider/AddressProviderControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\BlockTemplateTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/BlockTemplateTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\BlockTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/BlockTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\CustomBlock' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/CustomBlock.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\CustomBlockInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/CustomBlockInterface.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\CustomBlockTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/CustomBlockTemplate.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\CustomBlockTemplateTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/CustomBlockTemplateTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\BlockTemplates\\CustomBlockTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/BlockTemplates/CustomBlockTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\EmailPreview\\EmailPreviewRestControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/EmailPreview/EmailPreviewRestControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\EmailPreview\\EmailPreviewTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/EmailPreview/EmailPreviewTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\LoaderTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/LoaderTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Logging\\FileV2\\FileControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Logging/FileV2/FileControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Logging\\FileV2\\FileTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Logging/FileV2/FileTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Logging\\LogHandlerFileV2Test' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Logging/LogHandlerFileV2Test.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Logging\\SettingsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Logging/SettingsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\MarketingRecommendations\\InitTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/MarketingRecommendations/InitTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Orders\\COTRedirectionControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Orders/COTRedirectionControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Orders\\EditLockTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Orders/EditLockTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Orders\\ListTableTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Orders/ListTableTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Orders\\MetaBoxes\\OrderAttributionTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Orders/MetaBoxes/OrderAttributionTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Orders\\PageControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Orders/PageControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\ProductReviews\\ReviewsCommentsOverridesTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/ProductReviews/ReviewsCommentsOverridesTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\ProductReviews\\ReviewsListTableTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/ProductReviews/ReviewsListTableTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\ProductReviews\\ReviewsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/ProductReviews/ReviewsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\ProductReviews\\ReviewsUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/ProductReviews/ReviewsUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\RemoteFreeExtensions\\DefaultFreeExtensionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/RemoteFreeExtensions/DefaultFreeExtensionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\RemoteFreeExtensions\\EvaluateOverridesTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/RemoteFreeExtensions/EvaluateOverridesTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\RemoteFreeExtensions\\InitTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/RemoteFreeExtensions/InitTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\RemoteFreeExtensions\\ProcessCoreProfilerPluginInstallOptionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/RemoteFreeExtensions/ProcessCoreProfilerPluginInstallOptionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\Mocks\\FakePaymentGateway' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/Mocks/FakePaymentGateway.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProvidersTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProvidersTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProviders\\PaymentGatewayTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProviders/PaymentGatewayTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProviders\\WCCoreTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProviders/WCCoreTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProviders\\WooPaymentsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProviders/WooPaymentsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsRestControllerIntegrationTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsRestControllerIntegrationTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsRestControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsRestControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentProviders\\WooPayments\\WooPaymentsServiceTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentProviders/WooPayments/WooPaymentsServiceTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentsRestControllerIntegrationTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentsRestControllerIntegrationTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentsRestControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentsRestControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\PaymentsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/PaymentsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Settings\\UtilsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Settings/UtilsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\ShippingPartnerSuggestions\\ShippingPartnerSuggestionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/ShippingPartnerSuggestions/ShippingPartnerSuggestionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Suggestions\\Incentives\\IncentiveTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Suggestions/Incentives/IncentiveTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Suggestions\\Incentives\\WooPaymentsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Suggestions/Incentives/WooPaymentsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Suggestions\\Mocks\\FakeIncentive' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Suggestions/Mocks/FakeIncentive.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Suggestions\\PaymentExtensionSuggestionIncentivesTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Suggestions/PaymentExtensionSuggestionIncentivesTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\Suggestions\\PaymentExtensionSuggestionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/Suggestions/PaymentExtensionSuggestionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\WCPayPromotion\\DefaultPromotionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/WCPayPromotion/DefaultPromotionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Admin\\WCPayPromotion\\InitTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Admin/WCPayPromotion/InitTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\AssignDefaultCategoryTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/AssignDefaultCategoryTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\BatchProcessing\\BatchProcessingControllerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/BatchProcessing/BatchProcessingControllerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ComingSoon\\ComingSoonCacheInvalidatorTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ComingSoon/ComingSoonCacheInvalidatorTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ComingSoon\\ComingSoonHelperTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ComingSoon/ComingSoonHelperTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ComingSoon\\ComingSoonRequestHandlerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ComingSoon/ComingSoonRequestHandlerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\DataSynchronizerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/DataSynchronizerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\LegacyDataCleanupTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/LegacyDataCleanupTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\LegacyDataHandlerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/LegacyDataHandlerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\OrdersTableDataStoreRestOrdersControllerTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/OrdersTableDataStoreRestOrdersControllerTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\OrdersTableDataStoreTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/OrdersTableDataStoreTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\OrdersTableQueryTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/OrdersTableQueryTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DataStores\\Orders\\OrdersTableRefundDataStoreTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DataStores/Orders/OrdersTableRefundDataStoreTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\AbstractInterfaceServiceProviderTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/AbstractInterfaceServiceProviderTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\AbstractServiceProviderTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/AbstractServiceProviderTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\AnotherClassInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/AnotherClassInterface.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassInterface.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassThatHasReferenceArgumentsInInit' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassThatHasReferenceArgumentsInInit.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassThatThrowsOnInit' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassThatThrowsOnInit.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithDependencies' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithDependencies.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithInjectionMethodArgumentWithoutTypeHint' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithInjectionMethodArgumentWithoutTypeHint.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithInterface.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithMultipleInterfaces' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithMultipleInterfaces.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithNestedDependencies' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithNestedDependencies.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithNoInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithNoInterface.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithNonFinalInjectionMethod' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithNonFinalInjectionMethod.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithPrivateInjectionMethod' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithPrivateInjectionMethod.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithRecursiveDependencies1' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithRecursiveDependencies1.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithRecursiveDependencies2' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithRecursiveDependencies2.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithRecursiveDependencies3' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithRecursiveDependencies3.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithScalarInjectionMethodArgument' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithScalarInjectionMethodArgument.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithStaticInjectionMethod' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithStaticInjectionMethod.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithStoreApiDependency' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithStoreApiDependency.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\ClassWithUntypedInjectionMethodArgument' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithUntypedInjectionMethodArgument.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\DependencyClass' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/DependencyClass.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\DependencyClassWithInnerDependency' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/DependencyClassWithInnerDependency.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\DerivedDependencyClass' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/DerivedDependencyClass.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\InnerDependencyClass' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/InnerDependencyClass.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleClasses\\SomeTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/SomeTrait.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ClassA' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ClassA.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ClassAWithInterface1' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ClassAWithInterface1.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ClassAWithInterface2' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ClassAWithInterface2.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ClassB' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ClassB.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ClassBWithInterface1' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ClassBWithInterface1.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ClassBWithInterface2' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ClassBWithInterface2.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ProviderA' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ProviderA.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\ProviderB' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/ProviderB.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExampleProviders\\TheInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleProviders/TheInterface.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\ExtendedContainerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExtendedContainerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\RuntimeContainerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/RuntimeContainerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DependencyManagement\\TestingContainerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/TestingContainerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\DownloadPermissionsAdjusterTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DownloadPermissionsAdjusterTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\EmailEditor\\BlockEmailRendererTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/EmailEditor/BlockEmailRendererTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\EmailEditor\\EmailTemplates\\TemplateApiControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/EmailEditor/EmailTemplates/TemplateApiControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailPostsGeneratorTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailPostsGeneratorTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailPostsManagerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailPostsManagerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\EmailEditor\\WCTransactionalEmails\\WCTransactionalEmailsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/EmailEditor/WCTransactionalEmails/WCTransactionalEmailsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\EmailEditor\\WooContentProcessorTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/EmailEditor/WooContentProcessorTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Email\\EmailStyleSyncTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Email/EmailStyleSyncTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Email\\OrderPriceFormatterTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Email/OrderPriceFormatterTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Features\\FeaturesControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Features/FeaturesControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Integration\\WPConsentAPITest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Integration/WPConsentAPITest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Logging\\RemoteLoggerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Logging/RemoteLoggerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Logging\\RemoteLoggerWithEnvironmentOverride' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Logging/RemoteLoggerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\McStatsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/McStatsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\IppFunctionsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/IppFunctionsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\MobileMessagingHandlerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/MobileMessagingHandlerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\OrderActionsRestControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/OrderActionsRestControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\OrderAttributionControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/OrderAttributionControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\OrderStatusRestControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/OrderStatusRestControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\PaymentInfoTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/PaymentInfoTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Orders\\PointOfSaleOrderUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Orders/PointOfSaleOrderUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductAttributesLookup\\DataRegeneratorTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductAttributesLookup/DataRegeneratorTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductAttributesLookup\\FiltererTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductAttributesLookup/FiltererTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductAttributesLookup\\LookupDataStoreTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductAttributesLookup/LookupDataStoreTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductDownloads\\ApprovedDirectories\\RegisterTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductDownloads/ApprovedDirectories/RegisterTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductDownloads\\ApprovedDirectories\\SynchronizeTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductDownloads/ApprovedDirectories/SynchronizeTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductFilters\\AbstractProductFiltersTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductFilters/AbstractProductFiltersTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductFilters\\FilterDataTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductFilters/FilterDataTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ProductFilters\\QueryClausesTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ProductFilters/QueryClausesTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\ReceiptRendering\\ReceiptRenderingEngineTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/ReceiptRendering/ReceiptRenderingEngineTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\RestApiParameterUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/RestApiParameterUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Telemetry\\TelemetryControllerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Telemetry/TelemetryControllerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Traits\\AccessiblePrivateMethodsTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Traits/AccessiblePrivateMethodsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Traits\\BaseClass' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Traits/AccessiblePrivateMethodsTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\TransientFiles\\TransientFilesEngineTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/TransientFiles/TransientFilesEngineTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\COTMigrationUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/COTMigrationUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\DatabaseUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/DatabaseUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\FilesystemUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/FilesystemUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\HtmlSanitizerTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/HtmlSanitizerTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\TypesTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/TypesTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\URLTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/URLTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\Utilities\\UsersTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/Utilities/UsersTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Internal\\WCCom\\ConnectionHelperTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/WCCom/ConnectionHelperTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\LayoutTemplates\\LayoutTemplateRegistryTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/LayoutTemplates/LayoutTemplateRegistryTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\LayoutTemplates\\TestLayoutTemplate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/LayoutTemplates/TestLayoutTemplate.php'
	),
	'Automattic\\WooCommerce\\Tests\\Proxies\\ClassThatDependsOnLegacyCodeTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/ClassThatDependsOnLegacyCodeTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Proxies\\DynamicDecoratorTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/DynamicDecoratorTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Proxies\\ExampleClasses\\ClassThatDependsOnLegacyCode' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/ExampleClasses/ClassThatDependsOnLegacyCode.php'
	),
	'Automattic\\WooCommerce\\Tests\\Proxies\\ExampleClasses\\ClassWithReplaceableMembers' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/ExampleClasses/ClassWithReplaceableMembers.php'
	),
	'Automattic\\WooCommerce\\Tests\\Proxies\\LegacyProxyTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/LegacyProxyTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Proxies\\MockableLegacyProxyTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/MockableLegacyProxyTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Utilities\\ArrayUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Utilities/ArrayUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Utilities\\DiscountsUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Utilities/DiscountsUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Utilities\\I18nUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Utilities/I18nUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Utilities\\NumberUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Utilities/NumberUtilTest.php'
	),
	'Automattic\\WooCommerce\\Tests\\Utilities\\PluginUtilTests' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Utilities/PluginUtilTests.php'
	),
	'Automattic\\WooCommerce\\Tests\\Utilities\\StringUtilTest' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Utilities/StringUtilTest.php'
	),
	'Automattic\\WooCommerce\\Utilities\\ArrayUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/ArrayUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\DiscountsUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/DiscountsUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\FeaturesUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/FeaturesUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\I18nUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/I18nUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\LoggingUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/LoggingUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\NumberUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/NumberUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\OrderUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/OrderUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\PluginUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/PluginUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\RestApiUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/RestApiUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\ShippingUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/ShippingUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\StringUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/StringUtil.php'
	),
	'Automattic\\WooCommerce\\Utilities\\TimeUtil' => array(
		'version' => '*******',
		'path'    => $baseDir . '/src/Utilities/TimeUtil.php'
	),
	'Automattic\\WooCommerce\\Vendor\\Detection\\MobileDetect' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/Detection/MobileDetect.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ArgumentResolverInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/ArgumentResolverInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ArgumentResolverTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/ArgumentResolverTrait.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ClassName' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/ClassName.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ClassNameInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/ClassNameInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\ClassNameWithOptionalValue' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/ClassNameWithOptionalValue.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\RawArgument' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/RawArgument.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Argument\\RawArgumentInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Argument/RawArgumentInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Container' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Container.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ContainerAwareInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ContainerAwareInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ContainerAwareTrait' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ContainerAwareTrait.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\Definition' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Definition/Definition.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\DefinitionAggregate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Definition/DefinitionAggregate.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\DefinitionAggregateInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Definition/DefinitionAggregateInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Definition\\DefinitionInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Definition/DefinitionInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Exception\\ContainerException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Exception/ContainerException.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Exception\\NotFoundException' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Exception/NotFoundException.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\Inflector' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Inflector/Inflector.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\InflectorAggregate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Inflector/InflectorAggregate.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\InflectorAggregateInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Inflector/InflectorAggregateInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\Inflector\\InflectorInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/Inflector/InflectorInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ReflectionContainer' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ReflectionContainer.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\AbstractServiceProvider' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ServiceProvider/AbstractServiceProvider.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\BootableServiceProviderInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ServiceProvider/BootableServiceProviderInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\ServiceProviderAggregate' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ServiceProvider/ServiceProviderAggregate.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\ServiceProviderAggregateInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ServiceProvider/ServiceProviderAggregateInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\League\\Container\\ServiceProvider\\ServiceProviderInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/League/Container/ServiceProvider/ServiceProviderInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\Psr\\Container\\ContainerExceptionInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/Psr/Container/ContainerExceptionInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\Psr\\Container\\ContainerInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/Psr/Container/ContainerInterface.php'
	),
	'Automattic\\WooCommerce\\Vendor\\Psr\\Container\\NotFoundExceptionInterface' => array(
		'version' => '*******',
		'path'    => $baseDir . '/lib/packages/Psr/Container/NotFoundExceptionInterface.php'
	),
	'ClassWithLoadMethod' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithLoadMethod.php'
	),
	'ClassWithSingleton' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Internal/DependencyManagement/ExampleClasses/ClassWithSingleton.php'
	),
	'Composer\\Installers\\AglInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php'
	),
	'Composer\\Installers\\AimeosInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php'
	),
	'Composer\\Installers\\AnnotateCmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php'
	),
	'Composer\\Installers\\AsgardInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php'
	),
	'Composer\\Installers\\AttogramInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php'
	),
	'Composer\\Installers\\BaseInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php'
	),
	'Composer\\Installers\\BitrixInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php'
	),
	'Composer\\Installers\\BonefishInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php'
	),
	'Composer\\Installers\\CakePHPInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php'
	),
	'Composer\\Installers\\ChefInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php'
	),
	'Composer\\Installers\\CiviCrmInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php'
	),
	'Composer\\Installers\\ClanCatsFrameworkInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php'
	),
	'Composer\\Installers\\CockpitInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php'
	),
	'Composer\\Installers\\CodeIgniterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php'
	),
	'Composer\\Installers\\Concrete5Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php'
	),
	'Composer\\Installers\\CraftInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php'
	),
	'Composer\\Installers\\CroogoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php'
	),
	'Composer\\Installers\\DecibelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php'
	),
	'Composer\\Installers\\DframeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php'
	),
	'Composer\\Installers\\DokuWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php'
	),
	'Composer\\Installers\\DolibarrInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php'
	),
	'Composer\\Installers\\DrupalInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php'
	),
	'Composer\\Installers\\ElggInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php'
	),
	'Composer\\Installers\\EliasisInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php'
	),
	'Composer\\Installers\\ExpressionEngineInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php'
	),
	'Composer\\Installers\\EzPlatformInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php'
	),
	'Composer\\Installers\\FuelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php'
	),
	'Composer\\Installers\\FuelphpInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php'
	),
	'Composer\\Installers\\GravInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php'
	),
	'Composer\\Installers\\HuradInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php'
	),
	'Composer\\Installers\\ImageCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php'
	),
	'Composer\\Installers\\Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php'
	),
	'Composer\\Installers\\ItopInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php'
	),
	'Composer\\Installers\\JoomlaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php'
	),
	'Composer\\Installers\\KanboardInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php'
	),
	'Composer\\Installers\\KirbyInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php'
	),
	'Composer\\Installers\\KnownInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php'
	),
	'Composer\\Installers\\KodiCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php'
	),
	'Composer\\Installers\\KohanaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php'
	),
	'Composer\\Installers\\LanManagementSystemInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php'
	),
	'Composer\\Installers\\LaravelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php'
	),
	'Composer\\Installers\\LavaLiteInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php'
	),
	'Composer\\Installers\\LithiumInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php'
	),
	'Composer\\Installers\\MODULEWorkInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php'
	),
	'Composer\\Installers\\MODXEvoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php'
	),
	'Composer\\Installers\\MagentoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php'
	),
	'Composer\\Installers\\MajimaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php'
	),
	'Composer\\Installers\\MakoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php'
	),
	'Composer\\Installers\\MantisBTInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php'
	),
	'Composer\\Installers\\MauticInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php'
	),
	'Composer\\Installers\\MayaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php'
	),
	'Composer\\Installers\\MediaWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php'
	),
	'Composer\\Installers\\MiaoxingInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MiaoxingInstaller.php'
	),
	'Composer\\Installers\\MicroweberInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php'
	),
	'Composer\\Installers\\ModxInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php'
	),
	'Composer\\Installers\\MoodleInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php'
	),
	'Composer\\Installers\\OctoberInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php'
	),
	'Composer\\Installers\\OntoWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php'
	),
	'Composer\\Installers\\OsclassInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php'
	),
	'Composer\\Installers\\OxidInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php'
	),
	'Composer\\Installers\\PPIInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php'
	),
	'Composer\\Installers\\PantheonInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PantheonInstaller.php'
	),
	'Composer\\Installers\\PhiftyInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php'
	),
	'Composer\\Installers\\PhpBBInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php'
	),
	'Composer\\Installers\\PimcoreInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php'
	),
	'Composer\\Installers\\PiwikInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php'
	),
	'Composer\\Installers\\PlentymarketsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php'
	),
	'Composer\\Installers\\Plugin' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php'
	),
	'Composer\\Installers\\PortoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php'
	),
	'Composer\\Installers\\PrestashopInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php'
	),
	'Composer\\Installers\\ProcessWireInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php'
	),
	'Composer\\Installers\\PuppetInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php'
	),
	'Composer\\Installers\\PxcmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php'
	),
	'Composer\\Installers\\RadPHPInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php'
	),
	'Composer\\Installers\\ReIndexInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php'
	),
	'Composer\\Installers\\Redaxo5Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php'
	),
	'Composer\\Installers\\RedaxoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php'
	),
	'Composer\\Installers\\RoundcubeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php'
	),
	'Composer\\Installers\\SMFInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php'
	),
	'Composer\\Installers\\ShopwareInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php'
	),
	'Composer\\Installers\\SilverStripeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php'
	),
	'Composer\\Installers\\SiteDirectInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php'
	),
	'Composer\\Installers\\StarbugInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php'
	),
	'Composer\\Installers\\SyDESInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php'
	),
	'Composer\\Installers\\SyliusInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php'
	),
	'Composer\\Installers\\Symfony1Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php'
	),
	'Composer\\Installers\\TYPO3CmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php'
	),
	'Composer\\Installers\\TYPO3FlowInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php'
	),
	'Composer\\Installers\\TaoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php'
	),
	'Composer\\Installers\\TastyIgniterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TastyIgniterInstaller.php'
	),
	'Composer\\Installers\\TheliaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php'
	),
	'Composer\\Installers\\TuskInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php'
	),
	'Composer\\Installers\\UserFrostingInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php'
	),
	'Composer\\Installers\\VanillaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php'
	),
	'Composer\\Installers\\VgmcpInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php'
	),
	'Composer\\Installers\\WHMCSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php'
	),
	'Composer\\Installers\\WinterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WinterInstaller.php'
	),
	'Composer\\Installers\\WolfCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php'
	),
	'Composer\\Installers\\WordPressInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php'
	),
	'Composer\\Installers\\YawikInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php'
	),
	'Composer\\Installers\\ZendInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php'
	),
	'Composer\\Installers\\ZikulaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php'
	),
	'Container' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-container.php'
	),
	'Foo\\Bar\\ClassWithNonWooNamespace' => array(
		'version' => '*******',
		'path'    => $baseDir . '/tests/php/src/Proxies/ExampleClasses/ClassWithNonWooNamespace.php'
	),
	'Hook_Manager' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-hook-manager.php'
	),
	'Jetpack_IXR_Client' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php'
	),
	'Jetpack_IXR_ClientMulticall' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php'
	),
	'Jetpack_Options' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-options.php'
	),
	'Jetpack_Signature' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-signature.php'
	),
	'Jetpack_Tracks_Client' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php'
	),
	'Jetpack_Tracks_Event' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php'
	),
	'Jetpack_XMLRPC_Server' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php'
	),
	'Latest_Autoloader_Guard' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-latest-autoloader-guard.php'
	),
	'Manifest_Reader' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-manifest-reader.php'
	),
	'MaxMind\\Db\\Reader' => array(
		'version' => '1.11.1.0',
		'path'    => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader.php'
	),
	'MaxMind\\Db\\Reader\\Decoder' => array(
		'version' => '1.11.1.0',
		'path'    => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Decoder.php'
	),
	'MaxMind\\Db\\Reader\\InvalidDatabaseException' => array(
		'version' => '1.11.1.0',
		'path'    => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/InvalidDatabaseException.php'
	),
	'MaxMind\\Db\\Reader\\Metadata' => array(
		'version' => '1.11.1.0',
		'path'    => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Metadata.php'
	),
	'MaxMind\\Db\\Reader\\Util' => array(
		'version' => '1.11.1.0',
		'path'    => $vendorDir . '/maxmind-db/reader/src/MaxMind/Db/Reader/Util.php'
	),
	'Opis\\JsonSchema\\CompliantValidator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/CompliantValidator.php'
	),
	'Opis\\JsonSchema\\ContentEncoding' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/ContentEncoding.php'
	),
	'Opis\\JsonSchema\\ContentMediaType' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/ContentMediaType.php'
	),
	'Opis\\JsonSchema\\Errors\\CustomError' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Errors/CustomError.php'
	),
	'Opis\\JsonSchema\\Errors\\ErrorContainer' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Errors/ErrorContainer.php'
	),
	'Opis\\JsonSchema\\Errors\\ErrorFormatter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Errors/ErrorFormatter.php'
	),
	'Opis\\JsonSchema\\Errors\\ValidationError' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Errors/ValidationError.php'
	),
	'Opis\\JsonSchema\\Exceptions\\DuplicateSchemaIdException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/DuplicateSchemaIdException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\InvalidKeywordException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/InvalidKeywordException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\InvalidPragmaException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/InvalidPragmaException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\ParseException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/ParseException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\SchemaException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/SchemaException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\UnresolvedContentEncodingException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedContentEncodingException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\UnresolvedContentMediaTypeException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedContentMediaTypeException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\UnresolvedException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\UnresolvedFilterException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedFilterException.php'
	),
	'Opis\\JsonSchema\\Exceptions\\UnresolvedReferenceException' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Exceptions/UnresolvedReferenceException.php'
	),
	'Opis\\JsonSchema\\Filter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filter.php'
	),
	'Opis\\JsonSchema\\Filters\\CommonFilters' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/CommonFilters.php'
	),
	'Opis\\JsonSchema\\Filters\\DataExistsFilter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/DataExistsFilter.php'
	),
	'Opis\\JsonSchema\\Filters\\DateTimeFilters' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/DateTimeFilters.php'
	),
	'Opis\\JsonSchema\\Filters\\FilterExistsFilter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/FilterExistsFilter.php'
	),
	'Opis\\JsonSchema\\Filters\\FormatExistsFilter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/FormatExistsFilter.php'
	),
	'Opis\\JsonSchema\\Filters\\GlobalVarExistsFilter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/GlobalVarExistsFilter.php'
	),
	'Opis\\JsonSchema\\Filters\\SchemaExistsFilter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/SchemaExistsFilter.php'
	),
	'Opis\\JsonSchema\\Filters\\SlotExistsFilter' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Filters/SlotExistsFilter.php'
	),
	'Opis\\JsonSchema\\Format' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Format.php'
	),
	'Opis\\JsonSchema\\Formats\\DateTimeFormats' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Formats/DateTimeFormats.php'
	),
	'Opis\\JsonSchema\\Formats\\IriFormats' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Formats/IriFormats.php'
	),
	'Opis\\JsonSchema\\Formats\\MiscFormats' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Formats/MiscFormats.php'
	),
	'Opis\\JsonSchema\\Formats\\UriFormats' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Formats/UriFormats.php'
	),
	'Opis\\JsonSchema\\Helper' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Helper.php'
	),
	'Opis\\JsonSchema\\Info\\DataInfo' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Info/DataInfo.php'
	),
	'Opis\\JsonSchema\\Info\\SchemaInfo' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Info/SchemaInfo.php'
	),
	'Opis\\JsonSchema\\JsonPointer' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/JsonPointer.php'
	),
	'Opis\\JsonSchema\\Keyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keyword.php'
	),
	'Opis\\JsonSchema\\KeywordValidator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/KeywordValidator.php'
	),
	'Opis\\JsonSchema\\KeywordValidators\\AbstractKeywordValidator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/KeywordValidators/AbstractKeywordValidator.php'
	),
	'Opis\\JsonSchema\\KeywordValidators\\CallbackKeywordValidator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/KeywordValidators/CallbackKeywordValidator.php'
	),
	'Opis\\JsonSchema\\KeywordValidators\\PragmaKeywordValidator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/KeywordValidators/PragmaKeywordValidator.php'
	),
	'Opis\\JsonSchema\\Keywords\\AbstractRefKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/AbstractRefKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\AdditionalItemsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/AdditionalItemsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\AdditionalPropertiesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/AdditionalPropertiesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\AllOfKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/AllOfKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\AnyOfKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/AnyOfKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ConstDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ConstDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ConstKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ConstKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ContainsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ContainsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ContentEncodingKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ContentEncodingKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ContentMediaTypeKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ContentMediaTypeKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ContentSchemaKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ContentSchemaKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\DefaultKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/DefaultKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\DependenciesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/DependenciesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\DependentRequiredKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/DependentRequiredKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\DependentSchemasKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/DependentSchemasKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\EnumDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/EnumDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\EnumKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/EnumKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ErrorTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ErrorTrait.php'
	),
	'Opis\\JsonSchema\\Keywords\\ExclusiveMaximumDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMaximumDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ExclusiveMaximumKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMaximumKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ExclusiveMinimumDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMinimumDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ExclusiveMinimumKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ExclusiveMinimumKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\FiltersKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/FiltersKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\FormatDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/FormatDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\FormatKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/FormatKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\IfThenElseKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/IfThenElseKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\ItemsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/ItemsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\IterableDataValidationTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/IterableDataValidationTrait.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaxItemsDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaxItemsDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaxItemsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaxItemsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaxLengthDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaxLengthDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaxLengthKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaxLengthKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaxPropertiesDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaxPropertiesDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaxPropertiesKeywords' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaxPropertiesKeywords.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaximumDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaximumDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MaximumKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MaximumKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinItemsDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinItemsDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinItemsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinItemsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinLengthDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinLengthDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinLengthKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinLengthKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinPropertiesDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinPropertiesDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinPropertiesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinPropertiesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinimumDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinimumDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MinimumKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MinimumKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MultipleOfDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MultipleOfDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\MultipleOfKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/MultipleOfKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\NotKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/NotKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\OfTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/OfTrait.php'
	),
	'Opis\\JsonSchema\\Keywords\\OneOfKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/OneOfKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\PatternDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/PatternDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\PatternKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/PatternKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\PatternPropertiesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/PatternPropertiesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\PointerRefKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/PointerRefKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\PropertiesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/PropertiesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\PropertyNamesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/PropertyNamesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\RecursiveRefKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/RecursiveRefKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\RequiredDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/RequiredDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\RequiredKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/RequiredKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\SlotsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/SlotsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\TemplateRefKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/TemplateRefKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\TypeKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/TypeKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\URIRefKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/URIRefKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\UnevaluatedItemsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/UnevaluatedItemsKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\UnevaluatedPropertiesKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/UnevaluatedPropertiesKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\UniqueItemsDataKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/UniqueItemsDataKeyword.php'
	),
	'Opis\\JsonSchema\\Keywords\\UniqueItemsKeyword' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Keywords/UniqueItemsKeyword.php'
	),
	'Opis\\JsonSchema\\Parsers\\DataKeywordTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/DataKeywordTrait.php'
	),
	'Opis\\JsonSchema\\Parsers\\DefaultVocabulary' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/DefaultVocabulary.php'
	),
	'Opis\\JsonSchema\\Parsers\\Draft' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Draft.php'
	),
	'Opis\\JsonSchema\\Parsers\\DraftOptionTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/DraftOptionTrait.php'
	),
	'Opis\\JsonSchema\\Parsers\\Drafts\\Draft06' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft06.php'
	),
	'Opis\\JsonSchema\\Parsers\\Drafts\\Draft07' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft07.php'
	),
	'Opis\\JsonSchema\\Parsers\\Drafts\\Draft201909' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft201909.php'
	),
	'Opis\\JsonSchema\\Parsers\\Drafts\\Draft202012' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Drafts/Draft202012.php'
	),
	'Opis\\JsonSchema\\Parsers\\KeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/KeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\KeywordParserTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/KeywordParserTrait.php'
	),
	'Opis\\JsonSchema\\Parsers\\KeywordValidatorParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/KeywordValidatorParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\KeywordValidators\\PragmaKeywordValidatorParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/KeywordValidators/PragmaKeywordValidatorParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\AdditionalItemsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AdditionalItemsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\AdditionalPropertiesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AdditionalPropertiesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\AllOfKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AllOfKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\AnyOfKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/AnyOfKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ConstKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ConstKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ContainsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContainsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ContentEncodingKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContentEncodingKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ContentMediaTypeKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContentMediaTypeKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ContentSchemaKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ContentSchemaKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\DefaultKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DefaultKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\DependenciesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DependenciesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\DependentRequiredKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DependentRequiredKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\DependentSchemasKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/DependentSchemasKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\EnumKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/EnumKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ExclusiveMaximumKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ExclusiveMaximumKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ExclusiveMinimumKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ExclusiveMinimumKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\FiltersKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/FiltersKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\FormatKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/FormatKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\IfThenElseKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/IfThenElseKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\ItemsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/ItemsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MaxItemsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaxItemsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MaxLengthKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaxLengthKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MaxPropertiesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaxPropertiesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MaximumKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MaximumKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MinItemsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinItemsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MinLengthKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinLengthKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MinPropertiesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinPropertiesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MinimumKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MinimumKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\MultipleOfKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/MultipleOfKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\NotKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/NotKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\OneOfKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/OneOfKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\PatternKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PatternKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\PatternPropertiesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PatternPropertiesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\PropertiesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PropertiesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\PropertyNamesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/PropertyNamesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\RefKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/RefKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\RequiredKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/RequiredKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\SlotsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/SlotsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\TypeKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/TypeKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\UnevaluatedItemsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/UnevaluatedItemsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\UnevaluatedPropertiesKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/UnevaluatedPropertiesKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Keywords\\UniqueItemsKeywordParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Keywords/UniqueItemsKeywordParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\PragmaParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/PragmaParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Pragmas\\CastPragmaParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/CastPragmaParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Pragmas\\GlobalsPragmaParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/GlobalsPragmaParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Pragmas\\MaxErrorsPragmaParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/MaxErrorsPragmaParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\Pragmas\\SlotsPragmaParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Pragmas/SlotsPragmaParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\ResolverTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/ResolverTrait.php'
	),
	'Opis\\JsonSchema\\Parsers\\SchemaParser' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/SchemaParser.php'
	),
	'Opis\\JsonSchema\\Parsers\\VariablesTrait' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/VariablesTrait.php'
	),
	'Opis\\JsonSchema\\Parsers\\Vocabulary' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Parsers/Vocabulary.php'
	),
	'Opis\\JsonSchema\\Pragma' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Pragma.php'
	),
	'Opis\\JsonSchema\\Pragmas\\CastPragma' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Pragmas/CastPragma.php'
	),
	'Opis\\JsonSchema\\Pragmas\\GlobalsPragma' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Pragmas/GlobalsPragma.php'
	),
	'Opis\\JsonSchema\\Pragmas\\MaxErrorsPragma' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Pragmas/MaxErrorsPragma.php'
	),
	'Opis\\JsonSchema\\Pragmas\\SlotsPragma' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Pragmas/SlotsPragma.php'
	),
	'Opis\\JsonSchema\\Resolvers\\ContentEncodingResolver' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Resolvers/ContentEncodingResolver.php'
	),
	'Opis\\JsonSchema\\Resolvers\\ContentMediaTypeResolver' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Resolvers/ContentMediaTypeResolver.php'
	),
	'Opis\\JsonSchema\\Resolvers\\FilterResolver' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Resolvers/FilterResolver.php'
	),
	'Opis\\JsonSchema\\Resolvers\\FormatResolver' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Resolvers/FormatResolver.php'
	),
	'Opis\\JsonSchema\\Resolvers\\SchemaResolver' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Resolvers/SchemaResolver.php'
	),
	'Opis\\JsonSchema\\Schema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schema.php'
	),
	'Opis\\JsonSchema\\SchemaLoader' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/SchemaLoader.php'
	),
	'Opis\\JsonSchema\\SchemaValidator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/SchemaValidator.php'
	),
	'Opis\\JsonSchema\\Schemas\\AbstractSchema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schemas/AbstractSchema.php'
	),
	'Opis\\JsonSchema\\Schemas\\BooleanSchema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schemas/BooleanSchema.php'
	),
	'Opis\\JsonSchema\\Schemas\\EmptySchema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schemas/EmptySchema.php'
	),
	'Opis\\JsonSchema\\Schemas\\ExceptionSchema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schemas/ExceptionSchema.php'
	),
	'Opis\\JsonSchema\\Schemas\\LazySchema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schemas/LazySchema.php'
	),
	'Opis\\JsonSchema\\Schemas\\ObjectSchema' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Schemas/ObjectSchema.php'
	),
	'Opis\\JsonSchema\\Uri' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Uri.php'
	),
	'Opis\\JsonSchema\\ValidationContext' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/ValidationContext.php'
	),
	'Opis\\JsonSchema\\ValidationResult' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/ValidationResult.php'
	),
	'Opis\\JsonSchema\\Validator' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Validator.php'
	),
	'Opis\\JsonSchema\\Variables' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Variables.php'
	),
	'Opis\\JsonSchema\\Variables\\RefVariablesContainer' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Variables/RefVariablesContainer.php'
	),
	'Opis\\JsonSchema\\Variables\\VariablesContainer' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/opis/json-schema/src/Variables/VariablesContainer.php'
	),
	'Opis\\String\\Exception\\InvalidCodePointException' => array(
		'version' => '2.0.1.0',
		'path'    => $vendorDir . '/opis/string/src/Exception/InvalidCodePointException.php'
	),
	'Opis\\String\\Exception\\InvalidStringException' => array(
		'version' => '2.0.1.0',
		'path'    => $vendorDir . '/opis/string/src/Exception/InvalidStringException.php'
	),
	'Opis\\String\\Exception\\UnicodeException' => array(
		'version' => '2.0.1.0',
		'path'    => $vendorDir . '/opis/string/src/Exception/UnicodeException.php'
	),
	'Opis\\String\\UnicodeString' => array(
		'version' => '2.0.1.0',
		'path'    => $vendorDir . '/opis/string/src/UnicodeString.php'
	),
	'Opis\\Uri\\Punycode' => array(
		'version' => '1.1.0.0',
		'path'    => $vendorDir . '/opis/uri/src/Punycode.php'
	),
	'Opis\\Uri\\PunycodeException' => array(
		'version' => '1.1.0.0',
		'path'    => $vendorDir . '/opis/uri/src/PunycodeException.php'
	),
	'Opis\\Uri\\Uri' => array(
		'version' => '1.1.0.0',
		'path'    => $vendorDir . '/opis/uri/src/Uri.php'
	),
	'Opis\\Uri\\UriTemplate' => array(
		'version' => '1.1.0.0',
		'path'    => $vendorDir . '/opis/uri/src/UriTemplate.php'
	),
	'PHP_Autoloader' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-php-autoloader.php'
	),
	'Path_Processor' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-path-processor.php'
	),
	'Pelago\\Emogrifier\\Caching\\SimpleStringCache' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/Caching/SimpleStringCache.php'
	),
	'Pelago\\Emogrifier\\CssInliner' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/CssInliner.php'
	),
	'Pelago\\Emogrifier\\Css\\CssDocument' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/Css/CssDocument.php'
	),
	'Pelago\\Emogrifier\\Css\\StyleRule' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/Css/StyleRule.php'
	),
	'Pelago\\Emogrifier\\HtmlProcessor\\AbstractHtmlProcessor' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/AbstractHtmlProcessor.php'
	),
	'Pelago\\Emogrifier\\HtmlProcessor\\CssToAttributeConverter' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/CssToAttributeConverter.php'
	),
	'Pelago\\Emogrifier\\HtmlProcessor\\HtmlNormalizer' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/HtmlNormalizer.php'
	),
	'Pelago\\Emogrifier\\HtmlProcessor\\HtmlPruner' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/HtmlProcessor/HtmlPruner.php'
	),
	'Pelago\\Emogrifier\\Utilities\\ArrayIntersector' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/Utilities/ArrayIntersector.php'
	),
	'Pelago\\Emogrifier\\Utilities\\CssConcatenator' => array(
		'version' => '6.0.0.0',
		'path'    => $vendorDir . '/pelago/emogrifier/src/Utilities/CssConcatenator.php'
	),
	'PhpToken' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/PhpToken.php'
	),
	'Plugin_Locator' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugin-locator.php'
	),
	'Plugins_Handler' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugins-handler.php'
	),
	'Sabberworm\\CSS\\CSSList\\AtRuleBlockList' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/AtRuleBlockList.php'
	),
	'Sabberworm\\CSS\\CSSList\\CSSBlockList' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSBlockList.php'
	),
	'Sabberworm\\CSS\\CSSList\\CSSList' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSList.php'
	),
	'Sabberworm\\CSS\\CSSList\\Document' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/Document.php'
	),
	'Sabberworm\\CSS\\CSSList\\KeyFrame' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/KeyFrame.php'
	),
	'Sabberworm\\CSS\\Comment\\Comment' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Comment.php'
	),
	'Sabberworm\\CSS\\Comment\\Commentable' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Commentable.php'
	),
	'Sabberworm\\CSS\\OutputFormat' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormat.php'
	),
	'Sabberworm\\CSS\\OutputFormatter' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormatter.php'
	),
	'Sabberworm\\CSS\\Parser' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parser.php'
	),
	'Sabberworm\\CSS\\Parsing\\Anchor' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/Anchor.php'
	),
	'Sabberworm\\CSS\\Parsing\\OutputException' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/OutputException.php'
	),
	'Sabberworm\\CSS\\Parsing\\ParserState' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/ParserState.php'
	),
	'Sabberworm\\CSS\\Parsing\\SourceException' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/SourceException.php'
	),
	'Sabberworm\\CSS\\Parsing\\UnexpectedEOFException' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedEOFException.php'
	),
	'Sabberworm\\CSS\\Parsing\\UnexpectedTokenException' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedTokenException.php'
	),
	'Sabberworm\\CSS\\Property\\AtRule' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Property/AtRule.php'
	),
	'Sabberworm\\CSS\\Property\\CSSNamespace' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Property/CSSNamespace.php'
	),
	'Sabberworm\\CSS\\Property\\Charset' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Property/Charset.php'
	),
	'Sabberworm\\CSS\\Property\\Import' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Property/Import.php'
	),
	'Sabberworm\\CSS\\Property\\KeyframeSelector' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Property/KeyframeSelector.php'
	),
	'Sabberworm\\CSS\\Property\\Selector' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Property/Selector.php'
	),
	'Sabberworm\\CSS\\Renderable' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Renderable.php'
	),
	'Sabberworm\\CSS\\RuleSet\\AtRuleSet' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/AtRuleSet.php'
	),
	'Sabberworm\\CSS\\RuleSet\\DeclarationBlock' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/DeclarationBlock.php'
	),
	'Sabberworm\\CSS\\RuleSet\\RuleSet' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/RuleSet.php'
	),
	'Sabberworm\\CSS\\Rule\\Rule' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Rule/Rule.php'
	),
	'Sabberworm\\CSS\\Settings' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Settings.php'
	),
	'Sabberworm\\CSS\\Value\\CSSFunction' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSFunction.php'
	),
	'Sabberworm\\CSS\\Value\\CSSString' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSString.php'
	),
	'Sabberworm\\CSS\\Value\\CalcFunction' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcFunction.php'
	),
	'Sabberworm\\CSS\\Value\\CalcRuleValueList' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcRuleValueList.php'
	),
	'Sabberworm\\CSS\\Value\\Color' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/Color.php'
	),
	'Sabberworm\\CSS\\Value\\LineName' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/LineName.php'
	),
	'Sabberworm\\CSS\\Value\\PrimitiveValue' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/PrimitiveValue.php'
	),
	'Sabberworm\\CSS\\Value\\RuleValueList' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/RuleValueList.php'
	),
	'Sabberworm\\CSS\\Value\\Size' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/Size.php'
	),
	'Sabberworm\\CSS\\Value\\URL' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/URL.php'
	),
	'Sabberworm\\CSS\\Value\\Value' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/Value.php'
	),
	'Sabberworm\\CSS\\Value\\ValueList' => array(
		'version' => '8.6.0.0',
		'path'    => $vendorDir . '/sabberworm/php-css-parser/src/Value/ValueList.php'
	),
	'Shutdown_Handler' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-shutdown-handler.php'
	),
	'Soundasleep\\Html2Text' => array(
		'version' => '2.1.0.0',
		'path'    => $vendorDir . '/soundasleep/html2text/src/Html2Text.php'
	),
	'Soundasleep\\Html2TextException' => array(
		'version' => '2.1.0.0',
		'path'    => $vendorDir . '/soundasleep/html2text/src/Html2TextException.php'
	),
	'Stringable' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/Stringable.php'
	),
	'Symfony\\Component\\CssSelector\\CssSelectorConverter' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/CssSelectorConverter.php'
	),
	'Symfony\\Component\\CssSelector\\Exception\\ExceptionInterface' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Exception/ExceptionInterface.php'
	),
	'Symfony\\Component\\CssSelector\\Exception\\ExpressionErrorException' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Exception/ExpressionErrorException.php'
	),
	'Symfony\\Component\\CssSelector\\Exception\\InternalErrorException' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Exception/InternalErrorException.php'
	),
	'Symfony\\Component\\CssSelector\\Exception\\ParseException' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Exception/ParseException.php'
	),
	'Symfony\\Component\\CssSelector\\Exception\\SyntaxErrorException' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Exception/SyntaxErrorException.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\AbstractNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/AbstractNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\AttributeNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/AttributeNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\ClassNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/ClassNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\CombinedSelectorNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/CombinedSelectorNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\ElementNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/ElementNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\FunctionNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/FunctionNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\HashNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/HashNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\NegationNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/NegationNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\NodeInterface' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/NodeInterface.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\PseudoNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/PseudoNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\SelectorNode' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/SelectorNode.php'
	),
	'Symfony\\Component\\CssSelector\\Node\\Specificity' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Node/Specificity.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\CommentHandler' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/CommentHandler.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\HandlerInterface' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/HandlerInterface.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\HashHandler' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/HashHandler.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\IdentifierHandler' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/IdentifierHandler.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\NumberHandler' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/NumberHandler.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\StringHandler' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/StringHandler.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Handler\\WhitespaceHandler' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Handler/WhitespaceHandler.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Parser' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Parser.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\ParserInterface' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/ParserInterface.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Reader' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Reader.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ClassParser' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Shortcut/ClassParser.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\ElementParser' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Shortcut/ElementParser.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\EmptyStringParser' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Shortcut/EmptyStringParser.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Shortcut\\HashParser' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Shortcut/HashParser.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Token' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Token.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\TokenStream' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/TokenStream.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\Tokenizer' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/Tokenizer.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerEscaping' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/TokenizerEscaping.php'
	),
	'Symfony\\Component\\CssSelector\\Parser\\Tokenizer\\TokenizerPatterns' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/Parser/Tokenizer/TokenizerPatterns.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\AbstractExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/AbstractExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\AttributeMatchingExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/AttributeMatchingExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\CombinationExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/CombinationExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\ExtensionInterface' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/ExtensionInterface.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\FunctionExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/FunctionExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\HtmlExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/HtmlExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\NodeExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/NodeExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Extension\\PseudoClassExtension' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Extension/PseudoClassExtension.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\Translator' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/Translator.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\TranslatorInterface' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/TranslatorInterface.php'
	),
	'Symfony\\Component\\CssSelector\\XPath\\XPathExpr' => array(
		'version' => '5.4.40.0',
		'path'    => $vendorDir . '/symfony/css-selector/XPath/XPathExpr.php'
	),
	'Symfony\\Polyfill\\Php80\\Php80' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/Php80.php'
	),
	'Symfony\\Polyfill\\Php80\\PhpToken' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/PhpToken.php'
	),
	'UnhandledMatchError' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php'
	),
	'ValueError' => array(
		'version' => '1.30.0.0',
		'path'    => $vendorDir . '/symfony/polyfill-php80/Resources/stubs/ValueError.php'
	),
	'Version_Loader' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-loader.php'
	),
	'Version_Selector' => array(
		'version' => '5.0.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-selector.php'
	),
	'WC_REST_CRUD_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-crud-controller.php'
	),
	'WC_REST_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-controller.php'
	),
	'WC_REST_Coupons_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-coupons-controller.php'
	),
	'WC_REST_Coupons_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-coupons-v1-controller.php'
	),
	'WC_REST_Coupons_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-coupons-v2-controller.php'
	),
	'WC_REST_Customer_Downloads_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-customer-downloads-controller.php'
	),
	'WC_REST_Customer_Downloads_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-customer-downloads-v1-controller.php'
	),
	'WC_REST_Customer_Downloads_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-customer-downloads-v2-controller.php'
	),
	'WC_REST_Customers_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-customers-controller.php'
	),
	'WC_REST_Customers_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-customers-v1-controller.php'
	),
	'WC_REST_Customers_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-customers-v2-controller.php'
	),
	'WC_REST_Data_Continents_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-continents-controller.php'
	),
	'WC_REST_Data_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-controller.php'
	),
	'WC_REST_Data_Countries_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-countries-controller.php'
	),
	'WC_REST_Data_Currencies_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-data-currencies-controller.php'
	),
	'WC_REST_Layout_Templates_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-layout-templates-controller.php'
	),
	'WC_REST_Network_Orders_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-network-orders-controller.php'
	),
	'WC_REST_Network_Orders_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-network-orders-v2-controller.php'
	),
	'WC_REST_Order_Notes_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-order-notes-controller.php'
	),
	'WC_REST_Order_Notes_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-order-notes-v1-controller.php'
	),
	'WC_REST_Order_Notes_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-order-notes-v2-controller.php'
	),
	'WC_REST_Order_Refunds_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-order-refunds-controller.php'
	),
	'WC_REST_Order_Refunds_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-order-refunds-v1-controller.php'
	),
	'WC_REST_Order_Refunds_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-order-refunds-v2-controller.php'
	),
	'WC_REST_Orders_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-orders-controller.php'
	),
	'WC_REST_Orders_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-orders-v1-controller.php'
	),
	'WC_REST_Orders_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-orders-v2-controller.php'
	),
	'WC_REST_Payment_Gateways_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-payment-gateways-controller.php'
	),
	'WC_REST_Payment_Gateways_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-payment-gateways-v2-controller.php'
	),
	'WC_REST_Posts_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-posts-controller.php'
	),
	'WC_REST_Product_Attribute_Terms_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-attribute-terms-controller.php'
	),
	'WC_REST_Product_Attribute_Terms_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-attribute-terms-v1-controller.php'
	),
	'WC_REST_Product_Attribute_Terms_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-attribute-terms-v2-controller.php'
	),
	'WC_REST_Product_Attributes_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-attributes-controller.php'
	),
	'WC_REST_Product_Attributes_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-attributes-v1-controller.php'
	),
	'WC_REST_Product_Attributes_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-attributes-v2-controller.php'
	),
	'WC_REST_Product_Brands_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-brands-controller.php'
	),
	'WC_REST_Product_Brands_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-brands-v2-controller.php'
	),
	'WC_REST_Product_Categories_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-categories-controller.php'
	),
	'WC_REST_Product_Categories_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-categories-v1-controller.php'
	),
	'WC_REST_Product_Categories_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-categories-v2-controller.php'
	),
	'WC_REST_Product_Custom_Fields_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-custom-fields-controller.php'
	),
	'WC_REST_Product_Reviews_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-reviews-controller.php'
	),
	'WC_REST_Product_Reviews_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-reviews-v1-controller.php'
	),
	'WC_REST_Product_Reviews_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-reviews-v2-controller.php'
	),
	'WC_REST_Product_Shipping_Classes_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-shipping-classes-controller.php'
	),
	'WC_REST_Product_Shipping_Classes_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-shipping-classes-v1-controller.php'
	),
	'WC_REST_Product_Shipping_Classes_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-shipping-classes-v2-controller.php'
	),
	'WC_REST_Product_Tags_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-tags-controller.php'
	),
	'WC_REST_Product_Tags_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-product-tags-v1-controller.php'
	),
	'WC_REST_Product_Tags_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-tags-v2-controller.php'
	),
	'WC_REST_Product_Variations_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-product-variations-controller.php'
	),
	'WC_REST_Product_Variations_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-product-variations-v2-controller.php'
	),
	'WC_REST_Products_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-products-controller.php'
	),
	'WC_REST_Products_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-products-v1-controller.php'
	),
	'WC_REST_Products_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-products-v2-controller.php'
	),
	'WC_REST_Refunds_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-refunds-controller.php'
	),
	'WC_REST_Report_Coupons_Totals_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-coupons-totals-controller.php'
	),
	'WC_REST_Report_Customers_Totals_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-customers-totals-controller.php'
	),
	'WC_REST_Report_Orders_Totals_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-orders-totals-controller.php'
	),
	'WC_REST_Report_Products_Totals_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-products-totals-controller.php'
	),
	'WC_REST_Report_Reviews_Totals_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-reviews-totals-controller.php'
	),
	'WC_REST_Report_Sales_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-sales-controller.php'
	),
	'WC_REST_Report_Sales_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-report-sales-v1-controller.php'
	),
	'WC_REST_Report_Sales_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-report-sales-v2-controller.php'
	),
	'WC_REST_Report_Top_Sellers_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-report-top-sellers-controller.php'
	),
	'WC_REST_Report_Top_Sellers_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-report-top-sellers-v1-controller.php'
	),
	'WC_REST_Report_Top_Sellers_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-report-top-sellers-v2-controller.php'
	),
	'WC_REST_Reports_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-reports-controller.php'
	),
	'WC_REST_Reports_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-reports-v1-controller.php'
	),
	'WC_REST_Reports_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-reports-v2-controller.php'
	),
	'WC_REST_Setting_Options_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-setting-options-controller.php'
	),
	'WC_REST_Setting_Options_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-setting-options-v2-controller.php'
	),
	'WC_REST_Settings_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-settings-controller.php'
	),
	'WC_REST_Settings_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-settings-v2-controller.php'
	),
	'WC_REST_Shipping_Methods_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-methods-controller.php'
	),
	'WC_REST_Shipping_Methods_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-methods-v2-controller.php'
	),
	'WC_REST_Shipping_Zone_Locations_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zone-locations-controller.php'
	),
	'WC_REST_Shipping_Zone_Locations_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-zone-locations-v2-controller.php'
	),
	'WC_REST_Shipping_Zone_Methods_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zone-methods-controller.php'
	),
	'WC_REST_Shipping_Zone_Methods_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-zone-methods-v2-controller.php'
	),
	'WC_REST_Shipping_Zones_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zones-controller.php'
	),
	'WC_REST_Shipping_Zones_Controller_Base' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-shipping-zones-controller-base.php'
	),
	'WC_REST_Shipping_Zones_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-shipping-zones-v2-controller.php'
	),
	'WC_REST_System_Status_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-system-status-controller.php'
	),
	'WC_REST_System_Status_Tools_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-system-status-tools-controller.php'
	),
	'WC_REST_System_Status_Tools_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-system-status-tools-v2-controller.php'
	),
	'WC_REST_System_Status_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-system-status-v2-controller.php'
	),
	'WC_REST_Tax_Classes_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-tax-classes-controller.php'
	),
	'WC_REST_Tax_Classes_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-tax-classes-v1-controller.php'
	),
	'WC_REST_Tax_Classes_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-tax-classes-v2-controller.php'
	),
	'WC_REST_Taxes_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-taxes-controller.php'
	),
	'WC_REST_Taxes_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-taxes-v1-controller.php'
	),
	'WC_REST_Taxes_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-taxes-v2-controller.php'
	),
	'WC_REST_Telemetry_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Telemetry/class-wc-rest-telemetry-controller.php'
	),
	'WC_REST_Terms_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-terms-controller.php'
	),
	'WC_REST_Webhook_Deliveries_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-webhook-deliveries-v1-controller.php'
	),
	'WC_REST_Webhook_Deliveries_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-webhook-deliveries-v2-controller.php'
	),
	'WC_REST_Webhooks_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version3/class-wc-rest-webhooks-controller.php'
	),
	'WC_REST_Webhooks_V1_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version1/class-wc-rest-webhooks-v1-controller.php'
	),
	'WC_REST_Webhooks_V2_Controller' => array(
		'version' => '*******',
		'path'    => $baseDir . '/includes/rest-api/Controllers/Version2/class-wc-rest-webhooks-v2-controller.php'
	),
);
