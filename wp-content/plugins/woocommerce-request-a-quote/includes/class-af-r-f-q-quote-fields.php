<?php
/**
 * Addify Quotes fields
 *
 * The Addify Quotes fields class stores quote fields data and validate it.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * AF_R_F_Q_Quote_Fields class.
 */
class AF_R_F_Q_Quote_Fields {

	/**
	 * Contains an array of quote items.
	 *
	 * @var array
	 */
	public $quote_fields = array();

	/**
	 * Supported field types with their configurations.
	 *
	 * @var array
	 */
	private $field_types = array();

	/**
	 * Field validation rules.
	 *
	 * @var array
	 */
	private $validation_rules = array();

	/**
	 * Constructor for the Add_To_Quote class. Loads quote contents.
	 */
	public function __construct() {
		$this->init_field_types();
		$this->init_validation_rules();
		$this->quote_fields = $this->afrfq_get_fields_enabled();
	}

	/**
	 * Initialize supported field types with their configurations.
	 */
	private function init_field_types() {
		$this->field_types = array(
			'text' => array(
				'label' => __( 'Text', 'addify_rfq' ),
				'description' => __( 'Single line text input', 'addify_rfq' ),
				'supports' => array( 'placeholder', 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required', 'min_length', 'max_length', 'pattern' ),
			),
			'textarea' => array(
				'label' => __( 'Textarea', 'addify_rfq' ),
				'description' => __( 'Multi-line text input', 'addify_rfq' ),
				'supports' => array( 'placeholder', 'default_value', 'validation', 'width', 'rows' ),
				'validation_types' => array( 'required', 'min_length', 'max_length' ),
			),
			'email' => array(
				'label' => __( 'Email', 'addify_rfq' ),
				'description' => __( 'Email address input with validation', 'addify_rfq' ),
				'supports' => array( 'placeholder', 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required', 'email_format' ),
			),
			'phone' => array(
				'label' => __( 'Phone', 'addify_rfq' ),
				'description' => __( 'Phone number input with formatting', 'addify_rfq' ),
				'supports' => array( 'placeholder', 'default_value', 'validation', 'width', 'format' ),
				'validation_types' => array( 'required', 'phone_format' ),
			),
			'number' => array(
				'label' => __( 'Number', 'addify_rfq' ),
				'description' => __( 'Numeric input with validation', 'addify_rfq' ),
				'supports' => array( 'placeholder', 'default_value', 'validation', 'width', 'min', 'max', 'step' ),
				'validation_types' => array( 'required', 'min_value', 'max_value', 'numeric' ),
			),
			'url' => array(
				'label' => __( 'URL', 'addify_rfq' ),
				'description' => __( 'Website URL input with validation', 'addify_rfq' ),
				'supports' => array( 'placeholder', 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required', 'url_format' ),
			),
			'date' => array(
				'label' => __( 'Date', 'addify_rfq' ),
				'description' => __( 'Date picker input', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width', 'min_date', 'max_date' ),
				'validation_types' => array( 'required', 'date_format', 'date_range' ),
			),
			'time' => array(
				'label' => __( 'Time', 'addify_rfq' ),
				'description' => __( 'Time picker input', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required', 'time_format' ),
			),
			'datetime' => array(
				'label' => __( 'DateTime', 'addify_rfq' ),
				'description' => __( 'Date and time picker input', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width', 'min_datetime', 'max_datetime' ),
				'validation_types' => array( 'required', 'datetime_format', 'datetime_range' ),
			),
			'select' => array(
				'label' => __( 'Select (Dropdown)', 'addify_rfq' ),
				'description' => __( 'Single selection dropdown', 'addify_rfq' ),
				'supports' => array( 'options', 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required' ),
			),
			'multiselect' => array(
				'label' => __( 'Multi Select', 'addify_rfq' ),
				'description' => __( 'Multiple selection dropdown', 'addify_rfq' ),
				'supports' => array( 'options', 'default_value', 'validation', 'width', 'max_selections' ),
				'validation_types' => array( 'required', 'min_selections', 'max_selections' ),
			),
			'radio' => array(
				'label' => __( 'Radio Buttons', 'addify_rfq' ),
				'description' => __( 'Single selection radio buttons', 'addify_rfq' ),
				'supports' => array( 'options', 'default_value', 'validation', 'width', 'layout' ),
				'validation_types' => array( 'required' ),
			),
			'checkbox' => array(
				'label' => __( 'Checkboxes', 'addify_rfq' ),
				'description' => __( 'Multiple selection checkboxes', 'addify_rfq' ),
				'supports' => array( 'options', 'default_value', 'validation', 'width', 'layout', 'max_selections' ),
				'validation_types' => array( 'required', 'min_selections', 'max_selections' ),
			),
			'file' => array(
				'label' => __( 'File Upload', 'addify_rfq' ),
				'description' => __( 'File upload with security validation', 'addify_rfq' ),
				'supports' => array( 'validation', 'width', 'file_types', 'max_size', 'multiple' ),
				'validation_types' => array( 'required', 'file_type', 'file_size', 'file_count' ),
			),
			'range' => array(
				'label' => __( 'Range Slider', 'addify_rfq' ),
				'description' => __( 'Range slider for numeric values', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width', 'min', 'max', 'step' ),
				'validation_types' => array( 'required', 'min_value', 'max_value' ),
			),
			'color' => array(
				'label' => __( 'Color Picker', 'addify_rfq' ),
				'description' => __( 'Color selection input', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required', 'color_format' ),
			),
			'terms_cond' => array(
				'label' => __( 'Terms & Conditions', 'addify_rfq' ),
				'description' => __( 'Terms and conditions checkbox', 'addify_rfq' ),
				'supports' => array( 'validation', 'width', 'terms_text' ),
				'validation_types' => array( 'required' ),
			),
			'rating' => array(
				'label' => __( 'Star Rating', 'addify_rfq' ),
				'description' => __( 'Star rating input (1-5 stars)', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width', 'max_rating' ),
				'validation_types' => array( 'required', 'rating_range' ),
			),
			'slider' => array(
				'label' => __( 'Slider', 'addify_rfq' ),
				'description' => __( 'Visual slider for numeric input', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width', 'min', 'max', 'step' ),
				'validation_types' => array( 'required', 'min_value', 'max_value' ),
			),
			'toggle' => array(
				'label' => __( 'Toggle Switch', 'addify_rfq' ),
				'description' => __( 'Modern toggle switch for yes/no values', 'addify_rfq' ),
				'supports' => array( 'default_value', 'validation', 'width' ),
				'validation_types' => array( 'required' ),
			),
			'signature' => array(
				'label' => __( 'Digital Signature', 'addify_rfq' ),
				'description' => __( 'Digital signature pad for capturing signatures', 'addify_rfq' ),
				'supports' => array( 'validation', 'width', 'signature_options' ),
				'validation_types' => array( 'required', 'signature_format' ),
			),
		);

		/**
		 * Filter to allow custom field types.
		 *
		 * @param array $field_types The field types array.
		 */
		$this->field_types = apply_filters( 'afrfq_field_types', $this->field_types );

		// Initialize conditional logic rules
		$this->init_conditional_logic();
	}

	/**
	 * Initialize conditional logic system.
	 */
	private function init_conditional_logic() {
		$this->conditional_operators = array(
			'equals' => array(
				'label' => __( 'Equals', 'addify_rfq' ),
				'callback' => array( $this, 'condition_equals' ),
			),
			'not_equals' => array(
				'label' => __( 'Not Equals', 'addify_rfq' ),
				'callback' => array( $this, 'condition_not_equals' ),
			),
			'contains' => array(
				'label' => __( 'Contains', 'addify_rfq' ),
				'callback' => array( $this, 'condition_contains' ),
			),
			'not_contains' => array(
				'label' => __( 'Does Not Contain', 'addify_rfq' ),
				'callback' => array( $this, 'condition_not_contains' ),
			),
			'greater_than' => array(
				'label' => __( 'Greater Than', 'addify_rfq' ),
				'callback' => array( $this, 'condition_greater_than' ),
			),
			'less_than' => array(
				'label' => __( 'Less Than', 'addify_rfq' ),
				'callback' => array( $this, 'condition_less_than' ),
			),
			'is_empty' => array(
				'label' => __( 'Is Empty', 'addify_rfq' ),
				'callback' => array( $this, 'condition_is_empty' ),
			),
			'is_not_empty' => array(
				'label' => __( 'Is Not Empty', 'addify_rfq' ),
				'callback' => array( $this, 'condition_is_not_empty' ),
			),
		);

		$this->conditional_triggers = array(
			'field_value' => array(
				'label' => __( 'Field Value', 'addify_rfq' ),
				'callback' => array( $this, 'trigger_field_value' ),
			),
			'user_role' => array(
				'label' => __( 'User Role', 'addify_rfq' ),
				'callback' => array( $this, 'trigger_user_role' ),
			),
			'product_type' => array(
				'label' => __( 'Product Type', 'addify_rfq' ),
				'callback' => array( $this, 'trigger_product_type' ),
			),
			'product_category' => array(
				'label' => __( 'Product Category', 'addify_rfq' ),
				'callback' => array( $this, 'trigger_product_category' ),
			),
			'cart_total' => array(
				'label' => __( 'Cart Total', 'addify_rfq' ),
				'callback' => array( $this, 'trigger_cart_total' ),
			),
		);

		/**
		 * Filter to allow custom conditional operators.
		 *
		 * @param array $operators The conditional operators array.
		 */
		$this->conditional_operators = apply_filters( 'afrfq_conditional_operators', $this->conditional_operators );

		/**
		 * Filter to allow custom conditional triggers.
		 *
		 * @param array $triggers The conditional triggers array.
		 */
		$this->conditional_triggers = apply_filters( 'afrfq_conditional_triggers', $this->conditional_triggers );
	}

	/**
	 * Initialize validation rules.
	 */
	private function init_validation_rules() {
		$this->validation_rules = array(
			'required' => array(
				'message' => __( 'This field is required.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_required' ),
			),
			'email_format' => array(
				'message' => __( 'Please enter a valid email address.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_email' ),
			),
			'phone_format' => array(
				'message' => __( 'Please enter a valid phone number.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_phone' ),
			),
			'url_format' => array(
				'message' => __( 'Please enter a valid URL.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_url' ),
			),
			'numeric' => array(
				'message' => __( 'Please enter a valid number.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_numeric' ),
			),
			'min_length' => array(
				'message' => __( 'This field must be at least %d characters long.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_min_length' ),
			),
			'max_length' => array(
				'message' => __( 'This field must not exceed %d characters.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_max_length' ),
			),
			'min_value' => array(
				'message' => __( 'Value must be at least %s.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_min_value' ),
			),
			'max_value' => array(
				'message' => __( 'Value must not exceed %s.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_max_value' ),
			),
			'pattern' => array(
				'message' => __( 'Please enter a value in the correct format.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_pattern' ),
			),
			'file_type' => array(
				'message' => __( 'File type not allowed.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_file_type' ),
			),
			'file_size' => array(
				'message' => __( 'File size exceeds the maximum allowed size.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_file_size' ),
			),
			'color_format' => array(
				'message' => __( 'Please enter a valid color value.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_color' ),
			),
			'rating_range' => array(
				'message' => __( 'Please select a valid rating.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_rating' ),
			),
			'signature_format' => array(
				'message' => __( 'Please provide a valid signature.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_signature' ),
			),
			'custom_regex' => array(
				'message' => __( 'Please enter a value in the correct format.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_custom_regex' ),
			),
			'date_format' => array(
				'message' => __( 'Please enter a valid date.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_date' ),
			),
			'time_format' => array(
				'message' => __( 'Please enter a valid time.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_time' ),
			),
			'numeric_range' => array(
				'message' => __( 'Please enter a number within the allowed range.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_numeric_range' ),
			),
			'text_length' => array(
				'message' => __( 'Text length is outside the allowed range.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_text_length' ),
			),
			'unique_value' => array(
				'message' => __( 'This value has already been used.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_unique_value' ),
			),
			'conditional_required' => array(
				'message' => __( 'This field is required based on your other selections.', 'addify_rfq' ),
				'callback' => array( $this, 'validate_conditional_required' ),
			),
		);

		/**
		 * Filter to allow custom validation rules.
		 *
		 * @param array $validation_rules The validation rules array.
		 */
		$this->validation_rules = apply_filters( 'afrfq_validation_rules', $this->validation_rules );
	}

	/**
	 * Get supported field types.
	 *
	 * @return array
	 */
	public function get_field_types() {
		return $this->field_types;
	}

	/**
	 * Get field type configuration.
	 *
	 * @param string $type Field type.
	 * @return array|false
	 */
	public function get_field_type_config( $type ) {
		return isset( $this->field_types[ $type ] ) ? $this->field_types[ $type ] : false;
	}

	/**
	 * Validation Methods
	 */

	/**
	 * Validate required field.
	 *
	 * @param mixed $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_required( $value, $field_config ) {
		if ( is_array( $value ) ) {
			return ! empty( array_filter( $value ) );
		}
		return ! empty( trim( $value ) );
	}

	/**
	 * Validate email format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_email( $value, $field_config ) {
		return empty( $value ) || is_email( $value );
	}

	/**
	 * Validate phone format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_phone( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}
		// Remove all non-digit characters except +
		$clean_phone = preg_replace( '/[^\d+]/', '', $value );
		// Check if it's a valid phone number (10-15 digits, optionally starting with +)
		return preg_match( '/^\+?[1-9]\d{9,14}$/', $clean_phone );
	}

	/**
	 * Validate URL format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_url( $value, $field_config ) {
		return empty( $value ) || filter_var( $value, FILTER_VALIDATE_URL ) !== false;
	}

	/**
	 * Validate numeric value.
	 *
	 * @param mixed $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_numeric( $value, $field_config ) {
		return empty( $value ) || is_numeric( $value );
	}

	/**
	 * Validate minimum length.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_min_length( $value, $field_config ) {
		$min_length = isset( $field_config['min_length'] ) ? (int) $field_config['min_length'] : 0;
		return empty( $value ) || strlen( $value ) >= $min_length;
	}

	/**
	 * Validate maximum length.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_max_length( $value, $field_config ) {
		$max_length = isset( $field_config['max_length'] ) ? (int) $field_config['max_length'] : PHP_INT_MAX;
		return empty( $value ) || strlen( $value ) <= $max_length;
	}

	/**
	 * Validate minimum value.
	 *
	 * @param mixed $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_min_value( $value, $field_config ) {
		if ( empty( $value ) || ! is_numeric( $value ) ) {
			return true;
		}
		$min_value = isset( $field_config['min_value'] ) ? (float) $field_config['min_value'] : PHP_FLOAT_MIN;
		return (float) $value >= $min_value;
	}

	/**
	 * Validate maximum value.
	 *
	 * @param mixed $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_max_value( $value, $field_config ) {
		if ( empty( $value ) || ! is_numeric( $value ) ) {
			return true;
		}
		$max_value = isset( $field_config['max_value'] ) ? (float) $field_config['max_value'] : PHP_FLOAT_MAX;
		return (float) $value <= $max_value;
	}

	/**
	 * Validate pattern.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_pattern( $value, $field_config ) {
		if ( empty( $value ) || empty( $field_config['pattern'] ) ) {
			return true;
		}
		return preg_match( '/' . $field_config['pattern'] . '/', $value );
	}

	/**
	 * Validate file type.
	 *
	 * @param array $file File data.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_file_type( $file, $field_config ) {
		if ( empty( $file['name'] ) ) {
			return true;
		}

		// Support both 'file_types' and 'allowed_types' for backward compatibility
		$allowed_types = '';
		if ( isset( $field_config['file_types'] ) ) {
			$allowed_types = $field_config['file_types'];
		} elseif ( isset( $field_config['allowed_types'] ) ) {
			$allowed_types = $field_config['allowed_types'];
		}

		if ( empty( $allowed_types ) ) {
			return true;
		}

		$file_extension = strtolower( pathinfo( $file['name'], PATHINFO_EXTENSION ) );
		$allowed_extensions = array_map( 'trim', explode( ',', strtolower( $allowed_types ) ) );

		return in_array( $file_extension, $allowed_extensions, true );
	}

	/**
	 * Validate file size.
	 *
	 * @param array $file File data.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_file_size( $file, $field_config ) {
		if ( empty( $file['size'] ) ) {
			return true;
		}

		$max_size = isset( $field_config['max_size'] ) ? (int) $field_config['max_size'] : 10000000; // 10MB default
		return $file['size'] <= $max_size;
	}

	/**
	 * Validate color format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_color( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}
		// Validate hex color format
		return preg_match( '/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $value );
	}

	/**
	 * Validate rating value.
	 *
	 * @param mixed $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_rating( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}
		$max_rating = isset( $field_config['max_rating'] ) ? (int) $field_config['max_rating'] : 5;
		return is_numeric( $value ) && $value >= 1 && $value <= $max_rating;
	}

	/**
	 * Validate signature format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_signature( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}
		// Validate base64 image data
		return preg_match( '/^data:image\/(png|jpeg|jpg);base64,/', $value );
	}

	/**
	 * Conditional Logic Methods
	 */

	/**
	 * Check if field should be visible based on conditional logic.
	 *
	 * @param int $field_id Field ID.
	 * @param array $form_data Current form data.
	 * @return bool
	 */
	public function should_field_be_visible( $field_id, $form_data = array() ) {
		$conditional_logic = get_post_meta( $field_id, 'afrfq_conditional_logic', true );

		if ( empty( $conditional_logic ) || ! is_array( $conditional_logic ) ) {
			return true; // No conditions, always visible
		}

		$logic_type = isset( $conditional_logic['logic_type'] ) ? $conditional_logic['logic_type'] : 'and';
		$rules = isset( $conditional_logic['rules'] ) ? $conditional_logic['rules'] : array();

		if ( empty( $rules ) ) {
			return true;
		}

		$results = array();
		foreach ( $rules as $rule ) {
			$results[] = $this->evaluate_condition( $rule, $form_data );
		}

		// Apply logic type (AND/OR)
		if ( 'or' === $logic_type ) {
			return in_array( true, $results, true );
		} else {
			return ! in_array( false, $results, true );
		}
	}

	/**
	 * Evaluate a single condition.
	 *
	 * @param array $rule Condition rule.
	 * @param array $form_data Current form data.
	 * @return bool
	 */
	private function evaluate_condition( $rule, $form_data ) {
		$trigger = isset( $rule['trigger'] ) ? $rule['trigger'] : '';
		$operator = isset( $rule['operator'] ) ? $rule['operator'] : '';
		$value = isset( $rule['value'] ) ? $rule['value'] : '';

		if ( empty( $trigger ) || empty( $operator ) ) {
			return true;
		}

		// Get trigger value
		$trigger_value = $this->get_trigger_value( $trigger, $rule, $form_data );

		// Apply operator
		return $this->apply_operator( $operator, $trigger_value, $value );
	}

	/**
	 * Get value for a trigger.
	 *
	 * @param string $trigger Trigger type.
	 * @param array $rule Complete rule array.
	 * @param array $form_data Current form data.
	 * @return mixed
	 */
	private function get_trigger_value( $trigger, $rule, $form_data ) {
		if ( isset( $this->conditional_triggers[ $trigger ] ) ) {
			return call_user_func( $this->conditional_triggers[ $trigger ]['callback'], $rule, $form_data );
		}
		return '';
	}

	/**
	 * Apply operator to compare values.
	 *
	 * @param string $operator Operator type.
	 * @param mixed $trigger_value Actual value.
	 * @param mixed $compare_value Value to compare against.
	 * @return bool
	 */
	private function apply_operator( $operator, $trigger_value, $compare_value ) {
		if ( isset( $this->conditional_operators[ $operator ] ) ) {
			return call_user_func( $this->conditional_operators[ $operator ]['callback'], $trigger_value, $compare_value );
		}
		return true;
	}

	/**
	 * Conditional Operators
	 */

	public function condition_equals( $value1, $value2 ) {
		return (string) $value1 === (string) $value2;
	}

	public function condition_not_equals( $value1, $value2 ) {
		return (string) $value1 !== (string) $value2;
	}

	public function condition_contains( $value1, $value2 ) {
		return false !== strpos( (string) $value1, (string) $value2 );
	}

	public function condition_not_contains( $value1, $value2 ) {
		return false === strpos( (string) $value1, (string) $value2 );
	}

	public function condition_greater_than( $value1, $value2 ) {
		return is_numeric( $value1 ) && is_numeric( $value2 ) && (float) $value1 > (float) $value2;
	}

	public function condition_less_than( $value1, $value2 ) {
		return is_numeric( $value1 ) && is_numeric( $value2 ) && (float) $value1 < (float) $value2;
	}

	public function condition_is_empty( $value1, $value2 ) {
		return empty( $value1 );
	}

	public function condition_is_not_empty( $value1, $value2 ) {
		return ! empty( $value1 );
	}

	/**
	 * Conditional Triggers
	 */

	public function trigger_field_value( $rule, $form_data ) {
		$field_name = isset( $rule['field'] ) ? $rule['field'] : '';
		return isset( $form_data[ $field_name ] ) ? $form_data[ $field_name ] : '';
	}

	public function trigger_user_role( $rule, $form_data ) {
		$user = wp_get_current_user();
		return ! empty( $user->roles ) ? $user->roles[0] : 'guest';
	}

	public function trigger_product_type( $rule, $form_data ) {
		global $product;
		if ( $product && is_a( $product, 'WC_Product' ) ) {
			return $product->get_type();
		}
		return '';
	}

	public function trigger_product_category( $rule, $form_data ) {
		global $product;
		if ( $product && is_a( $product, 'WC_Product' ) ) {
			$categories = wp_get_post_terms( $product->get_id(), 'product_cat', array( 'fields' => 'slugs' ) );
			return ! empty( $categories ) ? $categories[0] : '';
		}
		return '';
	}

	public function trigger_cart_total( $rule, $form_data ) {
		if ( function_exists( 'WC' ) && WC()->cart ) {
			return WC()->cart->get_total( 'raw' );
		}
		return 0;
	}

	/**
	 * Enhanced Validation Methods
	 */

	/**
	 * Validate custom regex pattern.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_custom_regex( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}

		$pattern = isset( $field_config['pattern'] ) ? $field_config['pattern'] : '';
		if ( empty( $pattern ) ) {
			return true;
		}

		// Ensure pattern is properly formatted
		if ( substr( $pattern, 0, 1 ) !== '/' ) {
			$pattern = '/' . $pattern . '/';
		}

		return @preg_match( $pattern, $value ) === 1;
	}

	/**
	 * Validate date format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_date( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}

		$date_format = isset( $field_config['date_format'] ) ? $field_config['date_format'] : 'Y-m-d';
		$date = DateTime::createFromFormat( $date_format, $value );

		return $date && $date->format( $date_format ) === $value;
	}

	/**
	 * Validate time format.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_time( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}

		$time_format = isset( $field_config['time_format'] ) ? $field_config['time_format'] : 'H:i';
		$time = DateTime::createFromFormat( $time_format, $value );

		return $time && $time->format( $time_format ) === $value;
	}

	/**
	 * Validate numeric range.
	 *
	 * @param mixed $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_numeric_range( $value, $field_config ) {
		if ( empty( $value ) || ! is_numeric( $value ) ) {
			return empty( $value ); // Allow empty values, fail non-numeric
		}

		$min = isset( $field_config['min_value'] ) ? (float) $field_config['min_value'] : null;
		$max = isset( $field_config['max_value'] ) ? (float) $field_config['max_value'] : null;
		$value = (float) $value;

		if ( null !== $min && $value < $min ) {
			return false;
		}

		if ( null !== $max && $value > $max ) {
			return false;
		}

		return true;
	}

	/**
	 * Validate text length.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_text_length( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}

		$min_length = isset( $field_config['min_length'] ) ? (int) $field_config['min_length'] : 0;
		$max_length = isset( $field_config['max_length'] ) ? (int) $field_config['max_length'] : 0;
		$length = mb_strlen( $value );

		if ( $min_length > 0 && $length < $min_length ) {
			return false;
		}

		if ( $max_length > 0 && $length > $max_length ) {
			return false;
		}

		return true;
	}



	/**
	 * Validate unique value.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_unique_value( $value, $field_config ) {
		if ( empty( $value ) ) {
			return true;
		}

		$field_name = isset( $field_config['field_name'] ) ? $field_config['field_name'] : '';
		if ( empty( $field_name ) ) {
			return true;
		}

		// Check if value already exists in quotes
		global $wpdb;
		$existing = $wpdb->get_var( $wpdb->prepare(
			"SELECT COUNT(*) FROM {$wpdb->postmeta} WHERE meta_key = %s AND meta_value = %s",
			$field_name,
			$value
		) );

		return $existing == 0;
	}

	/**
	 * Validate conditional required field.
	 *
	 * @param string $value Field value.
	 * @param array $field_config Field configuration.
	 * @return bool
	 */
	public function validate_conditional_required( $value, $field_config ) {
		// If field has a value, it's valid
		if ( ! empty( $value ) ) {
			return true;
		}

		// Check if field should be required based on conditions
		$field_id = isset( $field_config['field_id'] ) ? $field_config['field_id'] : 0;
		$form_data = isset( $field_config['form_data'] ) ? $field_config['form_data'] : array();

		if ( empty( $field_id ) ) {
			return true;
		}

		// Get conditional logic for this field
		$conditional_logic = get_post_meta( $field_id, 'afrfq_conditional_logic', true );
		if ( empty( $conditional_logic ) ) {
			return true;
		}

		// If field is visible based on conditions, it's required
		return ! $this->should_field_be_visible( $field_id, $form_data );
	}

	/**
	 * Get all fields those are enabled.
	 *
	 * @param object $field_id returns feild id.
	 *
	 * @param object $user_id returns user id.
	 */
	public function get_field_default_value( $field_id, $user_id ) {

		$field_meta_key = get_post_meta( (int) $field_id, 'afrfq_field_value', true );

		if ( empty( $field_meta_key ) ) {
			return '';
		}

		if ( empty( intval( $user_id ) ) ) {
			return '';
		}

		$user = get_user_by( 'id', $user_id );

		if ( in_array( $field_meta_key, array( 'email', 'user_login', 'user_nicename', 'display_name' ), true ) ) {
			switch ( $field_meta_key ) {
				case 'email':
					return $user->user_email;
				case 'user_login':
					return $user->user_login;
				case 'user_nicename':
					return $user->user_nicename;
				case 'display_name':
					return $user->display_name;
				default:
					return '';
			}
		}

		$data = (string) get_user_meta( (int) $user_id, $field_meta_key, true );

		if ( 'billing_country' === $field_meta_key || 'shipping_country' === $field_meta_key ) {

			if ( isset( WC()->countries->countries[ $data ] ) ) {
				return strtolower( WC()->countries->countries[ $data ] );
			}
		}

		if ( 'billing_state' === $field_meta_key || 'shipping_state' === $field_meta_key ) {

			if ( isset( WC()->countries->states[ $data ] ) ) {
				return strtolower( WC()->countries->states[ $data ] );
			}
		}

		return $data;
	}

	/**
	 * Get all fields those are enabled.
	 */
	public function afrfq_get_fields_enabled() {

		$args = array(
			'post_type'        => 'addify_rfq_fields',
			'post_status'      => 'publish',
			'posts_per_page'   => -1,
			'suppress_filters' => false,
			'meta_query'       => array(
				array(
					'key'     => 'afrfq_field_enable',
					'value'   => 'enable',
					'compare' => '=',
				),
			),
			'orderby'          => 'menu_order',
			'order'            => 'ASC',
		);

		$the_query = new WP_Query( $args );

		return $the_query->get_posts();
	}

	/**
	 * Get all fields those are enabled.
	 *
	 * @param object $field_name returns feild name.
	 */
	public function afrfq_get_field_by_field_name( $field_name ) {

		$args = array(
			'post_type'        => 'addify_rfq_fields',
			'post_status'      => 'publish',
			'posts_per_page'   => -1,
			'suppress_filters' => false,
			'meta_query'       => array(
				array(
					'key'     => 'afrfq_field_name',
					'value'   => $field_name,
					'compare' => '=',
				),
			),
			'orderby'          => 'menu_order',
			'order'            => 'ASC',
			'fields'           => 'ids',
		);

		$the_query = new WP_Query( $args );

		if ( $the_query->have_posts() ) {

			return current( $the_query->get_posts() );
		}
		return false;
	}

	/**
	 * Get all fields those are enabled.
	 *
	 * @param object $field_name returns feild name.
	 *
	 * @param object $field_id returns feild id.
	 */
	public function afrfq_validate_field_name( $field_name, $field_id ) {

		$args = array(
			'post_type'        => 'addify_rfq_fields',
			'post_status'      => 'publish',
			'suppress_filters' => false,
			'posts_per_page'   => -1,
			'meta_query'       => array(
				array(
					'key'     => 'afrfq_field_name',
					'value'   => $field_name,
					'compare' => '=',
				),
			),
			'orderby'          => 'menu_order',
			'order'            => 'ASC',
			'fields'           => 'ids',
		);

		$the_query = new WP_Query( $args );

		if ( $the_query->have_posts() ) {

			$fields_ids = $the_query->get_posts();

			if ( 1 > count( $fields_ids ) ) {
				return false;
			} elseif ( 1 === count( $fields_ids ) && current( $fields_ids ) !== $field_id ) {
				return false;
			} else {
				return true;
			}
		}
		return true;
	}

	public function afrfq_get_shipping_data( $quote_id ) {

		$fields = $this->afrfq_get_fields_enabled();

		$quote_user = get_post_meta( $quote_id, '_customer_user', true );

		if ( ! empty( intval( $quote_user ) ) ) {
			$customer = new WC_Customer( $quote_user );
		} else {
			$customer = new WC_Customer();
		}

		$shipping_address = (array) $customer->get_shipping();

		$shipping_keys = array(
			'shipping_first_name',
			'shipping_last_name',
			'shipping_company',
			'shipping_email',
			'shipping_phone',
			'shipping_address_1',
			'shipping_address_2',
			'shipping_city',
			'shipping_state',
			'shipping_postcode',
			'shipping_country',
		);

		foreach ( $fields as $key => $field ) {

			$field_id = $field->ID;

			$default          = get_post_meta( (int) $field_id, 'afrfq_field_value', true );
			$afrfq_field_name = get_post_meta( (int) $field_id, 'afrfq_field_name', true );
			$value            = get_post_meta( (int) $quote_id, $afrfq_field_name, true );
			if ( empty( $default ) ) {
				continue;
			}

			if ( ! in_array( $default, $shipping_keys, true ) ) {
				continue;
			}

			$data_key = str_replace( 'shipping_', '', $default );

			if ( 'shipping_country' === $default ) {

				foreach ( WC()->countries->countries as $code => $country ) {
					if ( strtolower( $country ) === $value ) {
						$shipping_address[ $data_key ] = $code;
						break;
					}
				}
				continue;

			} elseif ( 'shipping_state' === $default ) {

				foreach ( WC()->countries->states as $code => $country ) {
					if ( strtolower( $country ) === $value ) {
						$shipping_address[ $data_key ] = $code;
						break;
					}
				}
				continue;

			} else {

				$shipping_address[ $data_key ] = $value;
			}
		}

		return $shipping_address;
	}

	public function afrfq_get_billing_data( $quote_id ) {

		$fields = $this->afrfq_get_fields_enabled();

		$quote_user = get_post_meta( $quote_id, '_customer_user', true );

		if ( ! empty( intval( $quote_user ) ) ) {
			$customer = new WC_Customer( $quote_user );
		} else {
			$customer = new WC_Customer();
		}

		$billing_address = (array) $customer->get_billing();

		$billing_keys = array(
			'billing_first_name',
			'billing_last_name',
			'billing_company',
			'billing_email',
			'billing_phone',
			'billing_address_1',
			'billing_address_2',
			'billing_city',
			'billing_state',
			'billing_postcode',
			'billing_country',
		);

		foreach ( $fields as $key => $field ) {

			$field_id = $field->ID;

			$default          = get_post_meta( (int) $field_id, 'afrfq_field_value', true );
			$afrfq_field_name = get_post_meta( (int) $field_id, 'afrfq_field_name', true );
			$value            = get_post_meta( (int) $quote_id, $afrfq_field_name, true );
			if ( empty( $default ) ) {
				continue;
			}

			if ( ! in_array( $default, $billing_keys, true ) ) {
				continue;
			}

			$data_key = str_replace( 'billing_', '', $default );

			if ( 'billing_country' === $default ) {

				foreach ( WC()->countries->countries as $code => $country ) {
					if ( strtolower( $country ) === $value ) {
						$billing_address[ $data_key ] = $code;
						break;
					}
				}
				continue;

			} elseif ( 'billing_state' === $default ) {

				foreach ( WC()->countries->states as $code => $country ) {
					if ( strtolower( $country ) === $value ) {
						$billing_address[ $data_key ] = $code;
						break;
					}
				}
				continue;

			} else {

				$billing_address[ $data_key ] = $value;
			}
		}

		if ( empty( $billing_address['email'] ) || ! is_email( $billing_address['email'] ) ) {
			$billing_address['email'] = $this->afrfq_get_user_email( $quote_id );
		}

		if ( empty( $billing_address['first_name'] ) ) {
			$billing_address['first_name'] = $this->afrfq_get_user_name( $quote_id );
		}

		return $billing_address;
	}

	public function get_field_by_deafult_value( $default_value = '' ) {
		$fields = (array) $this->afrfq_get_fields_enabled();

		foreach ( $fields as $key => $field ) {
			$field_id = $field->ID;

			$field_value = get_post_meta( $field_id, 'afrfq_field_value', true );

			if ( $field_value === $default_value ) {
				return $field_id;
			}
		}
	}

	public function get_field_by_type( $type = '' ) {

		$fields = (array) $this->afrfq_get_fields_enabled();

		foreach ( $fields as $key => $field ) {
			$field_id = $field->ID;

			$field_type = get_post_meta( $field_id, 'afrfq_field_type', true );

			if ( $type === $field_type ) {
				return $field_id;
			}
		}
	}

	/**
	 * Validate Fields.
	 *
	 * @param object $quote_id returns $quote_id.
	 *
	 * @param object $billing retrns $billing.
	 */
	public function afrfq_get_user_email( $quote_id, $billing = false ) {

		$user_id = get_post_meta( $quote_id, '_customer_user', true );

		if ( empty( intval( $user_id ) ) ) {

			// First, check modern hardcoded email fields
			$modern_email_fields = array( 'afrfq_email_address', 'customer_email' );
			foreach ( $modern_email_fields as $field_name ) {
				$field_value = get_post_meta( $quote_id, $field_name, true );
				if ( ! empty( $field_value ) && is_email( $field_value ) ) {
					return $field_value;
				}
			}

			$billling_field = $this->get_field_by_deafult_value( 'billing_email' );

			if ( ! empty( $billling_field ) ) {

				$field_name  = get_post_meta( $billling_field, 'afrfq_field_name', true );
				$field_value = get_post_meta( $quote_id, $field_name, true );

				if ( ! empty( $field_value ) && is_email( $field_value ) ) {
					return $field_value;
				}
			}

			$email_field = $this->get_field_by_deafult_value( 'email' );

			if ( ! empty( $email_field ) ) {

				$field_name  = get_post_meta( $email_field, 'afrfq_field_name', true );
				$field_value = get_post_meta( $quote_id, $field_name, true );

				if ( ! empty( $field_value ) && is_email( $field_value ) ) {
					return $field_value;
				}
			}

			$email_field = $this->get_field_by_type( 'email' );

			if ( ! empty( $email_field ) ) {

				$field_name  = get_post_meta( $email_field, 'afrfq_field_name', true );
				$field_value = get_post_meta( $quote_id, $field_name, true );

				if ( ! empty( $field_value ) && is_email( $field_value ) ) {
					return $field_value;
				}
			}
		} else {

			$user = get_user_by( 'id', $user_id );

			if ( is_object( $user ) ) {

				if ( ! $billing ) {

					return $user->user_email;
				}

				$billling_field = $this->get_field_by_deafult_value( 'billing_email' );

				if ( ! empty( $billling_field ) ) {

					$field_name  = get_post_meta( $billling_field, 'afrfq_field_name', true );
					$field_value = get_post_meta( $quote_id, $field_name, true );

					if ( ! empty( $field_value ) && is_email( $field_value ) ) {
						return $field_value;
					}
				}

				if ( ! empty( get_user_meta( $user_id, 'billing_email', true ) ) ) {

					$billing_email = get_user_meta( $user_id, 'billing_email', true );

					if ( ! empty( $billing_email ) && is_email( $billing_email ) ) {
						return $billing_email;
					}
				}

				$email_field = $this->get_field_by_type( 'email' );

				if ( ! empty( $email_field ) ) {

					$field_name  = get_post_meta( $email_field, 'afrfq_field_name', true );
					$field_value = get_post_meta( $quote_id, $field_name, true );

					if ( ! empty( $field_value ) && is_email( $field_value ) ) {
						return $field_value;
					}
				}

				return $user->user_email;
			}
		}
	}

	/**
	 * Validate Fields.
	 *
	 * @param object $quote_id returns $quote_id.
	 */
	public function afrfq_get_user_name( $quote_id ) {

		$user_id = get_post_meta( $quote_id, '_customer_user', true );

		// First, check modern hardcoded name fields
		$modern_name_fields = array( 'afrfq_customer_name', 'customer_name' );
		foreach ( $modern_name_fields as $field_name ) {
			$field_value = get_post_meta( $quote_id, $field_name, true );
			if ( ! empty( $field_value ) ) {
				return sanitize_text_field( $field_value );
			}
		}

		$name_field = $this->get_field_by_deafult_value( 'display_name' );

		if ( ! empty( $name_field ) ) {

			$field_name  = get_post_meta( $name_field, 'afrfq_field_name', true );
			$field_value = get_post_meta( $quote_id, $field_name, true );

			if ( ! empty( $field_value ) ) {
				return sanitize_text_field( $field_value );
			}
		}

		$user = get_user_by( 'id', $user_id );

		if ( is_object( $user ) ) {
			return $user->display_name;
		}

		// Fallback to 'Guest' if no name found
		return __( 'Guest', 'addify_rfq' );
	}

	public function captcha_check( $res ) {

		$secret = get_option( 'afrfq_secret_key' );

		if ( empty( $secret ) ) {
			return false;
		}

		$verify_response = wp_remote_get( 'https://www.google.com/recaptcha/api/siteverify?secret=' . $secret . '&response=' . $res );

		if ( is_array( $verify_response ) && isset( $verify_response['body'] ) ) {
			$response_data = json_decode( $verify_response['body'] );
		} else {
			$response_data = json_decode( (string) $verify_response );
		}

		if ( isset( $response_data->success ) && isset( $response_data->success ) ) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Enhanced field validation with comprehensive validation rules.
	 *
	 * @param array $data Form data to validate.
	 * @param array $files File data to validate.
	 * @return array|true Array of error messages or true if valid.
	 */
	public function afrfq_validate_fields_data( $data, $files = array() ) {
		$messages = array();

		// Validate reCAPTCHA if enabled
		if ( 'yes' === get_option( 'afrfq_enable_captcha' ) ) {
			if ( isset( $data['g-recaptcha-response'] ) && ! empty( $data['g-recaptcha-response'] ) ) {
				$ccheck = $this->captcha_check( $data['g-recaptcha-response'] );
				if ( ! $ccheck ) {
					$messages[] = esc_html__( 'Invalid reCaptcha.', 'addify_rfq' );
				}
			} else {
				$messages[] = esc_html__( 'reCaptcha is required.', 'addify_rfq' );
			}
		}

		// Validate modern hardcoded fields first
		$modern_field_messages = $this->validate_modern_fields( $data );
		if ( ! empty( $modern_field_messages ) ) {
			$messages = array_merge( $messages, $modern_field_messages );
		}

		$fields = $this->afrfq_get_fields_enabled();

		// If no dynamic fields are configured, just return modern field validation results
		if ( empty( $fields ) ) {
			return empty( $messages ) ? true : $messages;
		}

		// Enhanced validation for dynamic fields
		$field_messages = $this->validate_dynamic_fields( $data, $files, $fields );
		if ( ! empty( $field_messages ) ) {
			$messages = array_merge( $messages, $field_messages );
		}

		return empty( $messages ) ? true : $messages;
	}

	/**
	 * Validate dynamic fields with comprehensive rules.
	 *
	 * @param array $data Form data.
	 * @param array $files File data.
	 * @param array $fields Field configurations.
	 * @return array Validation error messages.
	 */
	private function validate_dynamic_fields( $data, $files, $fields ) {
		$messages = array();
		$modern_field_names = array(
			'afrfq_customer_name',
			'afrfq_company_name',
			'afrfq_email_address',
			'afrfq_phone_number',
			'afrfq_state'
		);

		foreach ( $fields as $field_post ) {
			$field_id = $field_post->ID;
			$field_config = $this->get_field_config( $field_id );

			if ( ! $field_config ) {
				continue;
			}

			$field_name = $field_config['name'];

			// Skip validation for fields that are already handled by modern field validation
			if ( in_array( $field_name, $modern_field_names, true ) ) {
				continue;
			}

			$field_value = isset( $data[ $field_name ] ) ? $data[ $field_name ] : '';
			$file_value = isset( $files[ $field_name ] ) ? $files[ $field_name ] : array();

			// Validate field based on type and rules
			$field_messages = $this->validate_single_field( $field_value, $file_value, $field_config );

			if ( ! empty( $field_messages ) ) {
				$messages = array_merge( $messages, $field_messages );
			}
		}

		return empty( $messages ) ? true : $messages;
	}

	/**
	 * Validate modern hardcoded form fields.
	 *
	 * @param array $data Form data to validate.
	 * @return array Error messages.
	 */
	private function validate_modern_fields( $data ) {
		$messages = array();

		// Define modern field validation rules
		$modern_fields = array(
			'afrfq_customer_name' => array(
				'label' => __( 'Name', 'addify_rfq' ),
				'required' => true,
				'type' => 'text',
				'min_length' => 2,
			),
			'afrfq_company_name' => array(
				'label' => __( 'Company Name', 'addify_rfq' ),
				'required' => true,
				'type' => 'text',
				'min_length' => 2,
			),
			'afrfq_email_address' => array(
				'label' => __( 'Email Address', 'addify_rfq' ),
				'required' => true,
				'type' => 'email',
			),
			'afrfq_phone_number' => array(
				'label' => __( 'Phone Number', 'addify_rfq' ),
				'required' => true,
				'type' => 'phone',
			),
			'afrfq_state' => array(
				'label' => __( 'State', 'addify_rfq' ),
				'required' => true,
				'type' => 'text',
				'min_length' => 2,
			),
		);

		foreach ( $modern_fields as $field_name => $field_config ) {
			$field_value = isset( $data[ $field_name ] ) ? trim( $data[ $field_name ] ) : '';

			// Required field validation
			if ( $field_config['required'] && empty( $field_value ) ) {
				$messages[] = sprintf( __( '%s is required.', 'addify_rfq' ), $field_config['label'] );
				continue;
			}

			// Skip other validations if field is empty and not required
			if ( empty( $field_value ) && ! $field_config['required'] ) {
				continue;
			}

			// Type-specific validation
			switch ( $field_config['type'] ) {
				case 'email':
					if ( ! is_email( $field_value ) ) {
						$messages[] = sprintf( __( 'Please enter a valid %s.', 'addify_rfq' ), strtolower( $field_config['label'] ) );
					}
					break;

				case 'phone':
					$clean_phone = preg_replace( '/[^\d+]/', '', $field_value );
					if ( ! preg_match( '/^\+?[1-9]\d{9,14}$/', $clean_phone ) ) {
						$messages[] = sprintf( __( 'Please enter a valid %s.', 'addify_rfq' ), strtolower( $field_config['label'] ) );
					}
					break;

				case 'text':
					if ( isset( $field_config['min_length'] ) && strlen( $field_value ) < $field_config['min_length'] ) {
						$messages[] = sprintf( __( '%s must be at least %d characters long.', 'addify_rfq' ), $field_config['label'], $field_config['min_length'] );
					}
					break;
			}
		}

		return $messages;
	}

	/**
	 * Get field configuration.
	 *
	 * @param int $field_id Field ID.
	 * @return array|false Field configuration or false if not found.
	 */
	private function get_field_config( $field_id ) {
		$field_name = get_post_meta( $field_id, 'afrfq_field_name', true );
		$field_type = get_post_meta( $field_id, 'afrfq_field_type', true );
		$field_label = get_post_meta( $field_id, 'afrfq_field_label', true );
		$field_required = get_post_meta( $field_id, 'afrfq_field_required', true );

		if ( empty( $field_name ) || empty( $field_type ) ) {
			return false;
		}

		$config = array(
			'id' => $field_id,
			'name' => $field_name,
			'type' => $field_type,
			'label' => $field_label,
			'required' => 'yes' === $field_required,
			'placeholder' => get_post_meta( $field_id, 'afrfq_field_placeholder', true ),
			'default_value' => get_post_meta( $field_id, 'afrfq_field_value', true ),
			'options' => get_post_meta( $field_id, 'afrfq_field_options', true ),
			'file_types' => get_post_meta( $field_id, 'afrfq_file_types', true ),
			'max_size' => get_post_meta( $field_id, 'afrfq_file_size', true ),
			'width' => get_post_meta( $field_id, 'afrfq_field_width', true ),
			'terms_text' => get_post_meta( $field_id, 'afrfq_field_terms', true ),
		);

		// Add validation rules from meta
		$config['min_length'] = get_post_meta( $field_id, 'afrfq_field_min_length', true );
		$config['max_length'] = get_post_meta( $field_id, 'afrfq_field_max_length', true );
		$config['min_value'] = get_post_meta( $field_id, 'afrfq_field_min_value', true );
		$config['max_value'] = get_post_meta( $field_id, 'afrfq_field_max_value', true );
		$config['pattern'] = get_post_meta( $field_id, 'afrfq_field_pattern', true );

		return $config;
	}

	/**
	 * Validate a single field.
	 *
	 * @param mixed $value Field value.
	 * @param array $file File data for file fields.
	 * @param array $config Field configuration.
	 * @return array Error messages.
	 */
	private function validate_single_field( $value, $file, $config ) {
		$messages = array();
		$field_type_config = $this->get_field_type_config( $config['type'] );

		if ( ! $field_type_config ) {
			return $messages;
		}

		// Handle file fields differently
		if ( 'file' === $config['type'] ) {
			$value = $file;
		}

		// Required field validation
		if ( $config['required'] ) {
			if ( ! $this->validate_required( $value, $config ) ) {
				$messages[] = sprintf(
					$this->validation_rules['required']['message'],
					$config['label']
				);
				return $messages; // Don't continue if required field is empty
			}
		}

		// Skip other validations if field is empty and not required
		if ( empty( $value ) && ! $config['required'] ) {
			return $messages;
		}

		// Type-specific validations
		$validation_types = isset( $field_type_config['validation_types'] ) ? $field_type_config['validation_types'] : array();

		foreach ( $validation_types as $validation_type ) {
			if ( ! isset( $this->validation_rules[ $validation_type ] ) ) {
				continue;
			}

			$validation_rule = $this->validation_rules[ $validation_type ];
			$is_valid = call_user_func( $validation_rule['callback'], $value, $config );

			if ( ! $is_valid ) {
				$message = $validation_rule['message'];

				// Handle parameterized messages
				if ( strpos( $message, '%' ) !== false ) {
					switch ( $validation_type ) {
						case 'min_length':
							$message = sprintf( $message, $config['min_length'] );
							break;
						case 'max_length':
							$message = sprintf( $message, $config['max_length'] );
							break;
						case 'min_value':
							$message = sprintf( $message, $config['min_value'] );
							break;
						case 'max_value':
							$message = sprintf( $message, $config['max_value'] );
							break;
						default:
							$message = sprintf( $message, $config['label'] );
							break;
					}
				}

				$messages[] = $message;
			}
		}

		return $messages;
	}

	/**
	 * Migrate previous version fields to newer dynamic rule based fields.
	 */
	public function afrfq_migrate_fields_enabled_to_rules() {

		if ( ! empty( get_option( 'afrfq_fields' ) ) && 'migrated' !== get_option( 'afrfq_fields' ) ) {

			$af_fields = (array) unserialize( get_option( 'afrfq_fields' ) );

			try {
				foreach ( $af_fields as $key => $field ) {

					if ( ! is_array( $field ) ) {
						continue;
					}

					$enable_field = isset( $field['enable_field'] ) ? $field['enable_field'] : '';

					if ( 'yes' !== $enable_field ) {
						continue;
					}

					$field_required     = isset( $field['field_required'] ) ? $field['field_required'] : '';
					$field_label        = isset( $field['field_label'] ) ? $field['field_label'] : '';
					$field_sort_order   = isset( $field['field_sort_order'] ) ? $field['field_sort_order'] : '';
					$field_key          = isset( $field['field_key'] ) ? $field['field_key'] : '';
					$file_allowed_types = isset( $field['file_allowed_types'] ) ? $field['file_allowed_types'] : '';

					$field_id = wp_insert_post(
						array(
							'post_title'  => $field_label,
							'post_type'   => 'addify_rfq_fields',
							'menu_order'  => $field_sort_order,
							'post_status' => 'publish',
						)
					);

					$type    = 'text';
					$default = '';

					switch ( $field_key ) {
						case 'afrfq_message_field':
							$type = 'textarea';
							break;
						case 'afrfq_email_field':
							$default = 'email';
							$type    = 'email';
							break;
						case 'afrfq_name_field':
							$default = 'display_name';
							$type    = 'text';
							break;
						case 'afrfq_file_field':
							$type = 'file';
							break;
						default:
							$type = 'text';
							break;
					}

					update_post_meta( $field_id, 'afrfq_field_value', $default );
					update_post_meta( $field_id, 'afrfq_field_name', $field_key );
					update_post_meta( $field_id, 'afrfq_field_type', $type );
					update_post_meta( $field_id, 'afrfq_field_label', $field_label );
					update_post_meta( $field_id, 'afrfq_field_placeholder', $field_label );
					update_post_meta( $field_id, 'afrfq_field_options', array() );
					update_post_meta( $field_id, 'afrfq_field_enable', 'enable' );
					update_post_meta( $field_id, 'afrfq_field_required', $field_required );
					update_post_meta( $field_id, 'afrfq_file_size', '10000000' );
					update_post_meta( $field_id, 'afrfq_file_types', strtolower( trim( preg_replace( '/[\t\n\r\s]+/', ' ', $file_allowed_types ) ) ) );
				}

				update_option( 'afrfq_fields', 'migrated' );

			} catch ( Exception $ex ) {
				echo esc_html( $ex->getMessage() );
			}
		}

		$this->quote_fields = $this->afrfq_get_fields_enabled();
	}
}
