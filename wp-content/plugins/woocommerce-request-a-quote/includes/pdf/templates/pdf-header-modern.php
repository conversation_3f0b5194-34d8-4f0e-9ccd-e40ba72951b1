<?php
/**
 * Modern PDF header template.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/pdf/pdf-header-modern.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$company_info = $this->get_company_info();
$logo_info = $this->get_logo_info();
?>

<style>
.modern-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	padding: 30px;
	margin-bottom: 30px;
	border-radius: 15px;
	position: relative;
	overflow: hidden;
}

.modern-header::before {
	content: '';
	position: absolute;
	top: -50%;
	right: -50%;
	width: 200%;
	height: 200%;
	background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
	transform: rotate(45deg);
}

.header-content {
	position: relative;
	z-index: 2;
}

.logo-section {
	float: left;
	width: 40%;
}

.quote-info-section {
	float: right;
	width: 55%;
	text-align: right;
}

.company-name {
	font-size: 24px;
	font-weight: bold;
	margin: 10px 0 5px 0;
	color: #ffffff;
}

.company-tagline {
	font-size: 14px;
	opacity: 0.9;
	margin: 0 0 15px 0;
}

.quote-title {
	font-size: 36px;
	font-weight: bold;
	margin: 0;
	letter-spacing: 2px;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.quote-number {
	font-size: 18px;
	margin: 5px 0;
	opacity: 0.95;
}

.quote-date {
	font-size: 14px;
	opacity: 0.9;
}

.contact-info {
	clear: both;
	margin-top: 20px;
	padding-top: 15px;
	border-top: 1px solid rgba(255,255,255,0.3);
	font-size: 12px;
	opacity: 0.9;
}

.contact-info span {
	margin-right: 20px;
}

.clearfix {
	clear: both;
}
</style>

<div class="modern-header">
	<div class="header-content">
		<div class="logo-section">
			<?php if ( ! empty( $logo_info['url'] ) ) : ?>
				<img src="<?php echo esc_url( $logo_info['url'] ); ?>" 
					 style="max-width: 150px; max-height: 80px; margin-bottom: 10px;" 
					 alt="<?php echo esc_attr( $company_info['name'] ); ?>">
			<?php endif; ?>
			
			<div class="company-name"><?php echo esc_html( $company_info['name'] ); ?></div>
			
			<?php if ( ! empty( get_bloginfo( 'description' ) ) ) : ?>
				<div class="company-tagline"><?php echo esc_html( get_bloginfo( 'description' ) ); ?></div>
			<?php endif; ?>
		</div>
		
		<div class="quote-info-section">
			<h1 class="quote-title">QUOTE</h1>
			<div class="quote-number">#<?php echo esc_html( $quote_id ); ?></div>
			<div class="quote-date"><?php echo esc_html( get_the_date( 'F j, Y', $quote_id ) ); ?></div>
		</div>
		
		<div class="clearfix"></div>
		
		<div class="contact-info">
			<?php if ( ! empty( $company_info['email'] ) ) : ?>
				<span>📧 <?php echo esc_html( $company_info['email'] ); ?></span>
			<?php endif; ?>
			
			<?php if ( ! empty( $company_info['phone'] ) ) : ?>
				<span>📞 <?php echo esc_html( $company_info['phone'] ); ?></span>
			<?php endif; ?>
			
			<?php if ( ! empty( $company_info['website'] ) ) : ?>
				<span>🌐 <?php echo esc_html( $company_info['website'] ); ?></span>
			<?php endif; ?>
		</div>
	</div>
</div>
