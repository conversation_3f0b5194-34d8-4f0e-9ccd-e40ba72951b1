<?php
/**
 * Professional PDF header template.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/pdf/pdf-header-professional.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$company_info = $this->get_company_info();
$logo_info = $this->get_logo_info();
?>

<style>
.professional-header {
	border-bottom: 3px solid #2c3e50;
	padding-bottom: 20px;
	margin-bottom: 30px;
}

.header-top {
	background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
	color: #ffffff;
	padding: 25px;
	margin-bottom: 20px;
}

.company-section {
	float: left;
	width: 60%;
}

.quote-section {
	float: right;
	width: 35%;
	text-align: right;
}

.company-logo {
	margin-bottom: 15px;
}

.company-name {
	font-size: 28px;
	font-weight: bold;
	margin: 0 0 8px 0;
	color: #ffffff;
	font-family: Georgia, serif;
}

.company-address {
	font-size: 13px;
	line-height: 1.6;
	opacity: 0.95;
	margin-bottom: 10px;
}

.company-contact {
	font-size: 12px;
	opacity: 0.9;
}

.quote-title {
	font-size: 32px;
	font-weight: bold;
	margin: 0 0 10px 0;
	font-family: Georgia, serif;
	letter-spacing: 1px;
}

.quote-details {
	background: rgba(255,255,255,0.1);
	padding: 15px;
	border-radius: 8px;
	margin-top: 10px;
}

.quote-detail-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8px;
	font-size: 14px;
}

.quote-detail-row:last-child {
	margin-bottom: 0;
}

.quote-detail-label {
	font-weight: 600;
	opacity: 0.9;
}

.quote-detail-value {
	font-weight: bold;
}

.header-bottom {
	padding: 15px 0;
	border-top: 1px solid #ecf0f1;
	margin-top: 20px;
}

.business-info {
	text-align: center;
	font-size: 11px;
	color: #7f8c8d;
	line-height: 1.5;
}

.clearfix {
	clear: both;
}
</style>

<div class="professional-header">
	<div class="header-top">
		<div class="company-section">
			<?php if ( ! empty( $logo_info['url'] ) ) : ?>
				<div class="company-logo">
					<img src="<?php echo esc_url( $logo_info['url'] ); ?>" 
						 style="max-width: 180px; max-height: 90px;" 
						 alt="<?php echo esc_attr( $company_info['name'] ); ?>">
				</div>
			<?php endif; ?>
			
			<div class="company-name"><?php echo esc_html( $company_info['name'] ); ?></div>
			
			<?php if ( ! empty( $company_info['address'] ) ) : ?>
				<div class="company-address"><?php echo nl2br( esc_html( $company_info['address'] ) ); ?></div>
			<?php endif; ?>
			
			<div class="company-contact">
				<?php if ( ! empty( $company_info['phone'] ) ) : ?>
					Tel: <?php echo esc_html( $company_info['phone'] ); ?><br>
				<?php endif; ?>
				Email: <?php echo esc_html( $company_info['email'] ); ?><br>
				Web: <?php echo esc_html( $company_info['website'] ); ?>
				<?php if ( ! empty( $company_info['tax_number'] ) ) : ?>
					<br>Tax ID: <?php echo esc_html( $company_info['tax_number'] ); ?>
				<?php endif; ?>
			</div>
		</div>
		
		<div class="quote-section">
			<h1 class="quote-title">QUOTATION</h1>
			
			<div class="quote-details">
				<div class="quote-detail-row">
					<span class="quote-detail-label">Quote Number:</span>
					<span class="quote-detail-value">#<?php echo esc_html( $quote_id ); ?></span>
				</div>
				<div class="quote-detail-row">
					<span class="quote-detail-label">Date:</span>
					<span class="quote-detail-value"><?php echo esc_html( get_the_date( 'F j, Y', $quote_id ) ); ?></span>
				</div>
				<div class="quote-detail-row">
					<span class="quote-detail-label">Valid Until:</span>
					<span class="quote-detail-value">
						<?php 
						$valid_until = get_post_meta( $quote_id, 'quote_valid_until', true );
						if ( $valid_until ) {
							echo esc_html( date( 'F j, Y', strtotime( $valid_until ) ) );
						} else {
							echo esc_html( date( 'F j, Y', strtotime( '+30 days' ) ) );
						}
						?>
					</span>
				</div>
				<div class="quote-detail-row">
					<span class="quote-detail-label">Status:</span>
					<span class="quote-detail-value">
						<?php echo esc_html( ucwords( str_replace( '_', ' ', get_post_meta( $quote_id, 'quote_status', true ) ) ) ); ?>
					</span>
				</div>
			</div>
		</div>
		
		<div class="clearfix"></div>
	</div>
	
	<div class="header-bottom">
		<div class="business-info">
			<strong><?php echo esc_html( $company_info['name'] ); ?></strong> - 
			Professional Quote Services | 
			<?php echo esc_html( $company_info['email'] ); ?> | 
			<?php echo esc_html( $company_info['website'] ); ?>
		</div>
	</div>
</div>
