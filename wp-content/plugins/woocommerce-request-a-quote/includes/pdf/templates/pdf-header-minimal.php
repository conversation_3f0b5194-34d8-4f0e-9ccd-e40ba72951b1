<?php
/**
 * Minimal PDF header template.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/pdf/pdf-header-minimal.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$company_info = $this->get_company_info();
$logo_info = $this->get_logo_info();
?>

<style>
.minimal-header {
	border-bottom: 2px solid #e9ecef;
	padding-bottom: 20px;
	margin-bottom: 30px;
}

.header-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}

.company-info {
	flex: 1;
}

.quote-info {
	text-align: right;
	flex: 1;
}

.company-name {
	font-size: 24px;
	font-weight: bold;
	color: #2c3e50;
	margin: 0 0 5px 0;
}

.company-contact {
	font-size: 12px;
	color: #6c757d;
	line-height: 1.4;
}

.quote-title {
	font-size: 28px;
	font-weight: bold;
	color: #2c3e50;
	margin: 0;
}

.quote-number {
	font-size: 16px;
	color: #6c757d;
	margin: 5px 0;
}

.quote-date {
	font-size: 14px;
	color: #6c757d;
}

.logo-container {
	text-align: center;
	margin-bottom: 20px;
}

.divider {
	height: 1px;
	background: #dee2e6;
	margin: 20px 0;
}
</style>

<div class="minimal-header">
	<?php if ( ! empty( $logo_info['url'] ) ) : ?>
		<div class="logo-container">
			<img src="<?php echo esc_url( $logo_info['url'] ); ?>" 
				 style="max-width: 120px; max-height: 60px;" 
				 alt="<?php echo esc_attr( $company_info['name'] ); ?>">
		</div>
	<?php endif; ?>
	
	<div class="header-row">
		<div class="company-info">
			<div class="company-name"><?php echo esc_html( $company_info['name'] ); ?></div>
			<div class="company-contact">
				<?php echo esc_html( $company_info['email'] ); ?>
				<?php if ( ! empty( $company_info['phone'] ) ) : ?>
					 | <?php echo esc_html( $company_info['phone'] ); ?>
				<?php endif; ?>
			</div>
		</div>
		
		<div class="quote-info">
			<h1 class="quote-title">Quote</h1>
			<div class="quote-number">#<?php echo esc_html( $quote_id ); ?></div>
			<div class="quote-date"><?php echo esc_html( get_the_date( 'M j, Y', $quote_id ) ); ?></div>
		</div>
	</div>
	
	<div class="divider"></div>
</div>
