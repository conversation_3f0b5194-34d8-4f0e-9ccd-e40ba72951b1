<?php
/**
 * Addify Add to Quote
 *
 * The WooCommerce quote class stores quote data and maintain session of quotes.
 * The quote class also has a price calculation function which calls upon other classes to calculate totals.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;


// Include the main TCPDF library (search for installation path).
//require_once AFRFQ_PLUGIN_DIR . '/includes/class-af-r-f-q-pdf.php';

/**
 * AF_R_F_Q_PDF_Controller class.
 */
class AF_R_F_Q_PDF_Controller {
	/**
	 * Contains an array of quote items.
	 *
	 * @var array
	 */
	public $quote_contents = array();

	/**
	 * Contains an array of quote items.
	 *
	 * @var array
	 */
	public $af_rfq_pdf;

	/**
	 * Constructor for the AF_R_F_Q_PDF_Controller class. Loads quote contents.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function __construct( $quote_id = 0 ) {
		$this->af_rfq_pdf = new AF_R_F_Q_PDF( PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false );
		$this->init_pdf_settings();
	}

	/**
	 * Initialize PDF settings and customization options.
	 */
	private function init_pdf_settings() {
		// Set PDF metadata
		$this->af_rfq_pdf->SetCreator( 'WooCommerce Request a Quote' );
		$this->af_rfq_pdf->SetAuthor( get_bloginfo( 'name' ) );
		$this->af_rfq_pdf->SetTitle( 'Quote Document' );
		$this->af_rfq_pdf->SetSubject( 'Quote Request' );
		$this->af_rfq_pdf->SetKeywords( 'quote, request, woocommerce' );

		// Set custom colors from options
		$header_color = get_option( 'afrfq_pdf_header_color', '#2c3e50' );
		$text_color = get_option( 'afrfq_pdf_text_color', '#333333' );
		$accent_color = get_option( 'afrfq_pdf_accent_color', '#3498db' );

		// Store colors for later use
		$this->header_color = $this->hex_to_rgb( $header_color );
		$this->text_color = $this->hex_to_rgb( $text_color );
		$this->accent_color = $this->hex_to_rgb( $accent_color );
	}

	/**
	 * Convert hex color to RGB array.
	 *
	 * @param string $hex Hex color code.
	 * @return array RGB values.
	 */
	private function hex_to_rgb( $hex ) {
		$hex = ltrim( $hex, '#' );
		return array(
			hexdec( substr( $hex, 0, 2 ) ),
			hexdec( substr( $hex, 2, 2 ) ),
			hexdec( substr( $hex, 4, 2 ) ),
		);
	}

	/**
	 * Get_pdf_print.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function get_pdf_print( $quote_id ) {
		// Close and output PDF document.
		$site_name = str_replace( ' ', '_', get_bloginfo( 'name' ) );
		$file_name = $site_name . '_quote_' . substr( md5( $quote_id . time() ), 5 ) . '.pdf';
		$path      = AFRFQ_PLUGIN_DIR . '/includes/pdf/pdf-files/';

		$output_file = $path . $file_name;
		ob_start();
		$this->af_rfq_pdf->Output( $output_file, 'FI' );
		$file            = ob_get_clean();
		$output_file_url = AFRFQ_URL . 'includes/pdf/pdf-files/' . $file_name;
		return $output_file_url;
	}

	/**
	 * Get_header.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function get_header( $quote_id ) {
		$template_style = get_option( 'afrfq_pdf_template_style', 'modern' );

		// Get company information
		$company_info = $this->get_company_info();

		// Get logo
		$logo_info = $this->get_logo_info();

		$this->af_rfq_pdf->SetFont( 'helvetica', '', 10 );

		ob_start();

		// Check for custom template first
		$custom_template = get_stylesheet_directory() . '/woocommerce/addify/pdf/pdf-header-' . $template_style . '.php';
		if ( file_exists( $custom_template ) ) {
			include $custom_template;
		} elseif ( file_exists( AFRFQ_PLUGIN_DIR . 'includes/pdf/templates/pdf-header-' . $template_style . '.php' ) ) {
			include AFRFQ_PLUGIN_DIR . 'includes/pdf/templates/pdf-header-' . $template_style . '.php';
		} else {
			// Fallback to default
			include AFRFQ_PLUGIN_DIR . 'includes/pdf/templates/pdf-header.php';
		}

		$html = ob_get_clean();

		$this->af_rfq_pdf->writeHTML( $html, false, false, false, false, '' );
	}

	/**
	 * Get company information for PDF.
	 *
	 * @return array Company information.
	 */
	private function get_company_info() {
		return array(
			'name' => get_option( 'afrfq_company_name', get_bloginfo( 'name' ) ),
			'address' => get_option( 'afrfq_company_address', '' ),
			'phone' => get_option( 'afrfq_company_phone', '' ),
			'email' => get_option( 'afrfq_company_email', get_option( 'admin_email' ) ),
			'website' => get_option( 'afrfq_company_website', home_url() ),
			'tax_number' => get_option( 'afrfq_company_tax_number', '' ),
		);
	}

	/**
	 * Get logo information for PDF.
	 *
	 * @return array Logo information.
	 */
	private function get_logo_info() {
		$custom_logo_id = get_option( 'afrfq_pdf_logo', get_theme_mod( 'custom_logo' ) );
		$logo_url = '';
		$logo_width = 100;
		$logo_height = 50;

		if ( $custom_logo_id ) {
			$logo_url = wp_get_attachment_image_url( $custom_logo_id, 'full' );
			$logo_meta = wp_get_attachment_metadata( $custom_logo_id );

			if ( $logo_meta ) {
				$logo_width = min( $logo_meta['width'], 150 );
				$logo_height = min( $logo_meta['height'], 75 );
			}
		}

		return array(
			'url' => $logo_url,
			'width' => $logo_width,
			'height' => $logo_height,
		);
	}

	/**
	 * Set_properties.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function set_properties( $quote_id ) {

		// set default monospaced font.
		$this->af_rfq_pdf->SetDefaultMonospacedFont( PDF_FONT_MONOSPACED );

		// set margins.
		$this->af_rfq_pdf->SetMargins( PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT );

		// set auto page breaks.
		$this->af_rfq_pdf->SetAutoPageBreak( true, PDF_MARGIN_BOTTOM );

		// set image scale factor.
		$this->af_rfq_pdf->setImageScale( PDF_IMAGE_SCALE_RATIO );

		// set some language-dependent strings (optional).
		if ( file_exists( __DIR__ . '/lang/eng.php' ) ) {
			require_once __DIR__ . '/lang/eng.php';
			$this->af_rfq_pdf->setLanguageArray( $l );
		}

		// add a page.
		$this->af_rfq_pdf->AddPage();

		$this->af_rfq_pdf->SetFont( 'helvetica', '', 8 );
	}

	/**
	 * Set_properties.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function process_pdf_print( $quote_id ) {

		$this->set_properties( $quote_id );
		$this->get_header( $quote_id );
		$this->add_quote_contents_table( $quote_id );
		$this->add_customer_info_table( $quote_id );

		return $this->get_pdf_print( $quote_id );
	}

	/**
	 * Set_properties.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function add_customer_info_table( $quote_id ) {

		$customer_info = $this->get_quote_user_info( $quote_id );

		if ( empty( $customer_info ) ) {
			return;
		}

		ob_start();

		if ( file_exists( get_stylesheet_directory() . '/woocommerce/addify/pdf/customer-info.php' ) ) {

			include get_stylesheet_directory() . '/woocommerce/addify/pdf/customer-info.php';

		} else {

			include AFRFQ_PLUGIN_DIR . 'includes/pdf/templates/customer-info.php';
		}

		$table_html = ob_get_clean();

		$this->af_rfq_pdf->writeHTML( $table_html, true, false, false, false, '' );
	}

	/**
	 * Set_properties.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function add_quote_contents_table( $quote_id ) {

		$quote_contents = get_post_meta( $quote_id, 'quote_contents', true );

		$af_quote = new AF_R_F_Q_Quote();

		$totals = $af_quote->get_calculated_totals( $quote_contents, $quote_id );

		$quote_subtotal   = isset( $totals['_subtotal'] ) ? $totals['_subtotal'] : 0;
		$offered_subtotal = isset( $totals['_offered_total'] ) ? $totals['_offered_total'] : 0;
		$vat_total        = isset( $totals['_tax_total'] ) ? $totals['_tax_total'] : 0;
		$quote_total      = isset( $totals['_total'] ) ? $totals['_total'] : 0;

		if ( empty( $quote_contents ) || ! is_array( $quote_contents ) ) {
			return;
		}

		ob_start();

		if ( file_exists( get_stylesheet_directory() . '/woocommerce/addify/pdf/quote-contents.php' ) ) {

			include get_stylesheet_directory() . '/woocommerce/addify/pdf/quote-contents.php';

		} else {

			include AFRFQ_PLUGIN_DIR . 'includes/pdf/templates/quote-contents.php';
		}

		$table_html = ob_get_clean();

		$this->af_rfq_pdf->writeHTML( $table_html, true, false, false, false, '' );
	}

	/**
	 * Set_properties.
	 *
	 * @param object $quote_id returns qoute id.
	 */
	public function get_quote_user_info( $quote_id ) {

		$customer_info = array();
		$quote_date    = gmdate( 'M d, y', get_post_time( 'U', false, $quote_id, true ) );

		$customer_info['quote_id']   = array(
			'label' => __( 'Quote Number', 'addify_rfq' ),
			'value' => $quote_id,
		);
		$customer_info['quote_date'] = array(
			'label' => __( 'Quote Date', 'addify_rfq' ),
			'value' => $quote_date,
		);

		$quote_fiels_obj = new AF_R_F_Q_Quote_Fields();
		$quote_fields    = (array) $quote_fiels_obj->afrfq_get_fields_enabled();

		if ( empty( $quote_fields ) ) {
			return $customer_info;
		}

		foreach ( $quote_fields as $key => $field ) {

			if ( ! is_a( $field, 'WP_Post' ) ) {
				continue;
			}

			$post_id = $field->ID;

			$afrfq_field_name  = get_post_meta( $post_id, 'afrfq_field_name', true );
			$afrfq_field_type  = get_post_meta( $post_id, 'afrfq_field_type', true );
			$afrfq_field_label = get_post_meta( $post_id, 'afrfq_field_label', true );
			$field_data        = get_post_meta( $quote_id, $afrfq_field_name, true );

			if ( is_array( $field_data ) ) {
				$field_data = implode( ', ', $field_data );
			}

			if ( in_array( $afrfq_field_type, array( 'select', 'radio', 'mutliselect' ), true ) ) {
				$field_data = ucwords( $field_data );
			}

			$customer_info[ $afrfq_field_name ] = array(
				'label' => $afrfq_field_label,
				'value' => $field_data,
			);
		}

		return $customer_info;
	}

	/**
	 * Generate enhanced PDF with modern styling.
	 *
	 * @param int $quote_id Quote ID.
	 * @param array $options PDF generation options.
	 * @return string PDF file URL.
	 */
	public function generate_enhanced_pdf( $quote_id, $options = array() ) {
		$defaults = array(
			'template' => 'modern',
			'include_logo' => true,
			'include_watermark' => false,
			'watermark_text' => 'QUOTE',
			'page_orientation' => 'portrait',
			'font_family' => 'helvetica',
			'font_size' => 10,
		);

		$options = wp_parse_args( $options, $defaults );

		// Set page orientation
		if ( 'landscape' === $options['page_orientation'] ) {
			$this->af_rfq_pdf = new AF_R_F_Q_PDF( 'L', PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false );
			$this->init_pdf_settings();
		}

		$this->set_properties( $quote_id );

		// Add watermark if enabled
		if ( $options['include_watermark'] ) {
			$this->add_watermark( $options['watermark_text'] );
		}

		// Generate content based on template
		switch ( $options['template'] ) {
			case 'minimal':
				$this->generate_minimal_template( $quote_id, $options );
				break;
			case 'professional':
				$this->generate_professional_template( $quote_id, $options );
				break;
			case 'modern':
			default:
				$this->generate_modern_template( $quote_id, $options );
				break;
		}

		return $this->get_pdf_print( $quote_id );
	}

	/**
	 * Add watermark to PDF.
	 *
	 * @param string $text Watermark text.
	 */
	private function add_watermark( $text ) {
		$this->af_rfq_pdf->SetAlpha( 0.1 );
		$this->af_rfq_pdf->SetFont( 'helvetica', 'B', 50 );
		$this->af_rfq_pdf->SetTextColor( 200, 200, 200 );

		// Rotate and position watermark
		$this->af_rfq_pdf->StartTransform();
		$this->af_rfq_pdf->Rotate( 45, 105, 148 );
		$this->af_rfq_pdf->Text( 50, 150, $text );
		$this->af_rfq_pdf->StopTransform();

		$this->af_rfq_pdf->SetAlpha( 1 );
		$this->af_rfq_pdf->SetTextColor( 0, 0, 0 );
	}

	/**
	 * Generate modern template.
	 *
	 * @param int $quote_id Quote ID.
	 * @param array $options Template options.
	 */
	private function generate_modern_template( $quote_id, $options ) {
		// Modern header with gradient-like styling
		$this->add_modern_header( $quote_id );

		// Quote summary section
		$this->add_quote_summary( $quote_id );

		// Customer information
		$this->add_customer_info_table( $quote_id );

		// Quote contents with modern styling
		$this->add_quote_contents_table( $quote_id );

		// Terms and footer
		$this->add_modern_footer( $quote_id );
	}

	/**
	 * Add modern header to PDF.
	 *
	 * @param int $quote_id Quote ID.
	 */
	private function add_modern_header( $quote_id ) {
		$company_info = $this->get_company_info();
		$logo_info = $this->get_logo_info();

		$html = '<div style="background: #4f46e5; color: white; padding: 20px; margin-bottom: 20px;">';

		if ( ! empty( $logo_info['url'] ) ) {
			$html .= '<img src="' . esc_url( $logo_info['url'] ) . '" style="float: left; max-width: 120px; max-height: 60px; margin-right: 20px;">';
		}

		$html .= '<div style="float: right; text-align: right;">';
		$html .= '<h1 style="margin: 0; font-size: 28px; font-weight: bold;">QUOTE</h1>';
		$html .= '<p style="margin: 5px 0; font-size: 16px;">#' . esc_html( $quote_id ) . '</p>';
		$html .= '<p style="margin: 5px 0; font-size: 14px;">' . esc_html( get_the_date( 'F j, Y', $quote_id ) ) . '</p>';
		$html .= '</div>';

		$html .= '<div style="clear: both;"></div>';
		$html .= '</div>';

		$this->af_rfq_pdf->writeHTML( $html, true, false, false, false, '' );
	}

	/**
	 * Add quote summary section.
	 *
	 * @param int $quote_id Quote ID.
	 */
	private function add_quote_summary( $quote_id ) {
		$quote_status = get_post_meta( $quote_id, 'quote_status', true );
		$quote_total = $this->get_quote_total( $quote_id );

		$html = '<div style="background: #f8f9fa; padding: 15px; margin: 20px 0; border-left: 4px solid #007cba;">';
		$html .= '<h3 style="margin: 0 0 10px 0; color: #2c3e50;">Quote Summary</h3>';
		$html .= '<table style="width: 100%; border-collapse: collapse;">';
		$html .= '<tr><td style="padding: 5px 0; font-weight: bold;">Status:</td><td style="padding: 5px 0;">' . esc_html( ucwords( str_replace( '_', ' ', $quote_status ) ) ) . '</td></tr>';
		if ( $quote_total > 0 ) {
			$html .= '<tr><td style="padding: 5px 0; font-weight: bold;">Total:</td><td style="padding: 5px 0; font-size: 18px; font-weight: bold; color: #007cba;">' . wc_price( $quote_total ) . '</td></tr>';
		}
		$html .= '</table>';
		$html .= '</div>';

		$this->af_rfq_pdf->writeHTML( $html, true, false, false, false, '' );
	}

	/**
	 * Add modern footer to PDF.
	 *
	 * @param int $quote_id Quote ID.
	 */
	private function add_modern_footer( $quote_id ) {
		$company_info = $this->get_company_info();

		$html = '<div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-top: 3px solid #4f46e5;">';
		$html .= '<h4 style="margin: 0 0 10px 0; color: #2c3e50;">Contact Information</h4>';
		$html .= '<p style="margin: 5px 0; font-size: 12px;">';
		$html .= '<strong>' . esc_html( $company_info['name'] ) . '</strong><br>';
		if ( ! empty( $company_info['address'] ) ) {
			$html .= esc_html( $company_info['address'] ) . '<br>';
		}
		if ( ! empty( $company_info['phone'] ) ) {
			$html .= 'Phone: ' . esc_html( $company_info['phone'] ) . '<br>';
		}
		$html .= 'Email: ' . esc_html( $company_info['email'] ) . '<br>';
		$html .= 'Website: ' . esc_html( $company_info['website'] );
		$html .= '</p>';
		$html .= '</div>';

		$this->af_rfq_pdf->writeHTML( $html, true, false, false, false, '' );
	}

	/**
	 * Get quote total amount.
	 *
	 * @param int $quote_id Quote ID.
	 * @return float Quote total.
	 */
	private function get_quote_total( $quote_id ) {
		$quote_contents = get_post_meta( $quote_id, 'quote_contents', true );
		if ( empty( $quote_contents ) ) {
			return 0;
		}

		$af_quote = new AF_R_F_Q_Quote();
		$totals = $af_quote->get_calculated_totals( $quote_contents, $quote_id );

		return isset( $totals['_total'] ) ? $totals['_total'] : 0;
	}
}
