<?php
/**
 * Minimal email template for quote notifications.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/rfq/emails/quote-email-minimal.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php bloginfo( 'charset' ); ?>" />
	<meta content="width=device-width, initial-scale=1.0" name="viewport">
	<title><?php echo esc_html( get_bloginfo( 'name', 'display') ); ?></title>
	<style type="text/css">
		body {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
			line-height: 1.6;
			color: #333;
			margin: 0;
			padding: 0;
			background-color: #f8f9fa;
		}
		.email-container {
			max-width: 600px;
			margin: 20px auto;
			background: #ffffff;
			border-radius: 8px;
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
			overflow: hidden;
		}
		.email-header {
			background: #2c3e50;
			color: #ffffff;
			padding: 30px;
			text-align: center;
		}
		.email-header h1 {
			margin: 0;
			font-size: 24px;
			font-weight: 600;
		}
		.email-content {
			padding: 30px;
		}
		.email-message {
			font-size: 16px;
			margin-bottom: 30px;
			line-height: 1.6;
		}
		.quote-details {
			background: #f8f9fa;
			border-radius: 6px;
			padding: 20px;
			margin: 20px 0;
		}
		.quote-details h3 {
			margin: 0 0 15px 0;
			color: #2c3e50;
			font-size: 18px;
		}
		.detail-row {
			display: flex;
			justify-content: space-between;
			padding: 8px 0;
			border-bottom: 1px solid #e9ecef;
		}
		.detail-row:last-child {
			border-bottom: none;
		}
		.detail-label {
			font-weight: 600;
			color: #495057;
		}
		.detail-value {
			color: #6c757d;
		}
		.email-footer {
			background: #f8f9fa;
			padding: 20px 30px;
			text-align: center;
			font-size: 14px;
			color: #6c757d;
		}
		.button {
			display: inline-block;
			background: #007cba;
			color: #ffffff;
			padding: 12px 24px;
			text-decoration: none;
			border-radius: 6px;
			font-weight: 600;
			margin: 20px 0;
		}
		@media (max-width: 600px) {
			.email-container {
				margin: 10px;
				border-radius: 0;
			}
			.email-header, .email-content, .email-footer {
				padding: 20px;
			}
			.detail-row {
				flex-direction: column;
				gap: 4px;
			}
		}
	</style>
</head>
<body>
	<div class="email-container">
		<div class="email-header">
			<h1><?php echo esc_html( $email_heading ); ?></h1>
		</div>
		
		<div class="email-content">
			<div class="email-message">
				<?php
				echo wp_kses_post(
					str_replace(
						array( '{user_name}', '{quote_id}' ),
						array( $user_name, $quote_id ),
						wpautop( wptexturize( apply_filters( 'addify_rfq_email_text', $email_message ) ) )
					)
				);
				?>
			</div>

			<div class="quote-details">
				<h3><?php esc_html_e( 'Quote Details', 'addify_rfq' ); ?></h3>
				<div class="detail-row">
					<span class="detail-label"><?php esc_html_e( 'Quote ID:', 'addify_rfq' ); ?></span>
					<span class="detail-value">#<?php echo esc_html( $quote_id ); ?></span>
				</div>
				<div class="detail-row">
					<span class="detail-label"><?php esc_html_e( 'Date:', 'addify_rfq' ); ?></span>
					<span class="detail-value"><?php echo esc_html( get_the_date( 'F j, Y', $quote_id ) ); ?></span>
				</div>
				<div class="detail-row">
					<span class="detail-label"><?php esc_html_e( 'Status:', 'addify_rfq' ); ?></span>
					<span class="detail-value"><?php echo esc_html( ucwords( str_replace( '_', ' ', get_post_meta( $quote_id, 'quote_status', true ) ) ) ); ?></span>
				</div>
			</div>

			<?php
			/*
			 * @hooked AF_R_F_Q_Email_Controller::customer_details() Shows customer details
			 */
			do_action( 'addify_rfq_email_customer_details', $quote_id, $email );

			/*
			 * @hooked AF_R_F_Q_Email_Controller::quote_details() Shows the quote details table.
			 */
			do_action( 'addify_rfq_email_quote_details', $quote_id, $email );

			/*
			 * @hooked AF_R_F_Q_Email_Controller::quote_meta() Shows quote meta data.
			 */
			do_action( 'addify_rfq_email_quote_meta', $quote_id, $email );
			?>
		</div>

		<div class="email-footer">
			<p><?php printf( esc_html__( 'This email was sent from %s', 'addify_rfq' ), '<strong>' . esc_html( get_bloginfo( 'name' ) ) . '</strong>' ); ?></p>
			<p><?php printf( esc_html__( 'Visit our website: %s', 'addify_rfq' ), '<a href="' . esc_url( home_url() ) . '">' . esc_html( home_url() ) . '</a>' ); ?></p>
		</div>
	</div>
</body>
</html>
