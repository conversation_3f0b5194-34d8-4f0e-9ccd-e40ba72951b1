<?php
/**
 * Professional email template for quote notifications.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/rfq/emails/quote-email-professional.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php bloginfo( 'charset' ); ?>" />
	<meta content="width=device-width, initial-scale=1.0" name="viewport">
	<title><?php echo esc_html( get_bloginfo( 'name', 'display') ); ?></title>
	<style type="text/css">
		body {
			font-family: Georgia, 'Times New Roman', serif;
			line-height: 1.7;
			color: #2c3e50;
			margin: 0;
			padding: 0;
			background-color: #ecf0f1;
		}
		.email-wrapper {
			background-color: #ecf0f1;
			padding: 40px 20px;
		}
		.email-container {
			max-width: 700px;
			margin: 0 auto;
			background: #ffffff;
			border: 1px solid #bdc3c7;
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
		}
		.email-header {
			background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
			color: #ffffff;
			padding: 40px;
			text-align: center;
			position: relative;
		}
		.email-header::after {
			content: '';
			position: absolute;
			bottom: -10px;
			left: 50%;
			transform: translateX(-50%);
			width: 0;
			height: 0;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			border-top: 10px solid #2c3e50;
		}
		.company-logo {
			margin-bottom: 20px;
		}
		.email-header h1 {
			margin: 0;
			font-size: 28px;
			font-weight: 400;
			letter-spacing: 1px;
		}
		.email-content {
			padding: 50px 40px;
		}
		.greeting {
			font-size: 18px;
			margin-bottom: 30px;
			color: #34495e;
		}
		.email-message {
			font-size: 16px;
			margin-bottom: 40px;
			line-height: 1.8;
			text-align: justify;
		}
		.quote-summary {
			background: #f8f9fa;
			border-left: 4px solid #3498db;
			padding: 30px;
			margin: 30px 0;
			border-radius: 0 8px 8px 0;
		}
		.quote-summary h3 {
			margin: 0 0 20px 0;
			color: #2c3e50;
			font-size: 20px;
			font-weight: 600;
		}
		.summary-grid {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 20px;
		}
		.summary-item {
			padding: 15px 0;
			border-bottom: 1px solid #e9ecef;
		}
		.summary-item:last-child {
			border-bottom: none;
		}
		.summary-label {
			font-weight: 600;
			color: #34495e;
			font-size: 14px;
			text-transform: uppercase;
			letter-spacing: 0.5px;
			margin-bottom: 5px;
		}
		.summary-value {
			color: #2c3e50;
			font-size: 16px;
		}
		.action-section {
			text-align: center;
			margin: 40px 0;
			padding: 30px;
			background: #f8f9fa;
			border-radius: 8px;
		}
		.action-button {
			display: inline-block;
			background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
			color: #ffffff;
			padding: 15px 30px;
			text-decoration: none;
			border-radius: 6px;
			font-weight: 600;
			font-size: 16px;
			letter-spacing: 0.5px;
			text-transform: uppercase;
			box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
			transition: all 0.3s ease;
		}
		.contact-info {
			background: #34495e;
			color: #ffffff;
			padding: 30px 40px;
			text-align: center;
		}
		.contact-info h4 {
			margin: 0 0 15px 0;
			font-size: 18px;
			color: #ecf0f1;
		}
		.contact-details {
			font-size: 14px;
			line-height: 1.6;
		}
		.email-footer {
			background: #2c3e50;
			color: #bdc3c7;
			padding: 25px 40px;
			text-align: center;
			font-size: 13px;
		}
		.footer-links {
			margin-top: 15px;
		}
		.footer-links a {
			color: #3498db;
			text-decoration: none;
			margin: 0 10px;
		}
		@media (max-width: 600px) {
			.email-wrapper {
				padding: 20px 10px;
			}
			.email-header, .email-content, .contact-info, .email-footer {
				padding: 25px 20px;
			}
			.summary-grid {
				grid-template-columns: 1fr;
				gap: 10px;
			}
			.email-header h1 {
				font-size: 24px;
			}
		}
	</style>
</head>
<body>
	<div class="email-wrapper">
		<div class="email-container">
			<div class="email-header">
				<?php
				$img = get_option( 'woocommerce_email_header_image' );
				if ( $img ) {
					echo '<div class="company-logo"><img src="' . esc_url( $img ) . '" alt="' . esc_attr( get_bloginfo( 'name', 'display' ) ) . '" style="max-height: 60px;" /></div>';
				}
				?>
				<h1><?php echo esc_html( $email_heading ); ?></h1>
			</div>
			
			<div class="email-content">
				<div class="greeting">
					<?php printf( esc_html__( 'Dear %s,', 'addify_rfq' ), esc_html( $user_name ) ); ?>
				</div>

				<div class="email-message">
					<?php
					echo wp_kses_post(
						str_replace(
							array( '{user_name}', '{quote_id}' ),
							array( $user_name, $quote_id ),
							wpautop( wptexturize( apply_filters( 'addify_rfq_email_text', $email_message ) ) )
						)
					);
					?>
				</div>

				<div class="quote-summary">
					<h3><?php esc_html_e( 'Quote Summary', 'addify_rfq' ); ?></h3>
					<div class="summary-grid">
						<div class="summary-item">
							<div class="summary-label"><?php esc_html_e( 'Quote Number', 'addify_rfq' ); ?></div>
							<div class="summary-value">#<?php echo esc_html( $quote_id ); ?></div>
						</div>
						<div class="summary-item">
							<div class="summary-label"><?php esc_html_e( 'Date Submitted', 'addify_rfq' ); ?></div>
							<div class="summary-value"><?php echo esc_html( get_the_date( 'F j, Y \a\t g:i A', $quote_id ) ); ?></div>
						</div>
						<div class="summary-item">
							<div class="summary-label"><?php esc_html_e( 'Current Status', 'addify_rfq' ); ?></div>
							<div class="summary-value"><?php echo esc_html( ucwords( str_replace( '_', ' ', get_post_meta( $quote_id, 'quote_status', true ) ) ) ); ?></div>
						</div>
						<div class="summary-item">
							<div class="summary-label"><?php esc_html_e( 'Reference', 'addify_rfq' ); ?></div>
							<div class="summary-value"><?php echo esc_html( get_bloginfo( 'name' ) . '-' . $quote_id ); ?></div>
						</div>
					</div>
				</div>

				<?php
				/*
				 * @hooked AF_R_F_Q_Email_Controller::customer_details() Shows customer details
				 */
				do_action( 'addify_rfq_email_customer_details', $quote_id, $email );

				/*
				 * @hooked AF_R_F_Q_Email_Controller::quote_details() Shows the quote details table.
				 */
				do_action( 'addify_rfq_email_quote_details', $quote_id, $email );

				/*
				 * @hooked AF_R_F_Q_Email_Controller::quote_meta() Shows quote meta data.
				 */
				do_action( 'addify_rfq_email_quote_meta', $quote_id, $email );
				?>

				<div class="action-section">
					<p><?php esc_html_e( 'Need assistance or have questions about your quote?', 'addify_rfq' ); ?></p>
					<a href="mailto:<?php echo esc_attr( get_option( 'admin_email' ) ); ?>" class="action-button">
						<?php esc_html_e( 'Contact Us', 'addify_rfq' ); ?>
					</a>
				</div>
			</div>

			<div class="contact-info">
				<h4><?php esc_html_e( 'Contact Information', 'addify_rfq' ); ?></h4>
				<div class="contact-details">
					<p><?php echo esc_html( get_bloginfo( 'name' ) ); ?></p>
					<p><?php esc_html_e( 'Email:', 'addify_rfq' ); ?> <?php echo esc_html( get_option( 'admin_email' ) ); ?></p>
					<p><?php esc_html_e( 'Website:', 'addify_rfq' ); ?> <?php echo esc_html( home_url() ); ?></p>
				</div>
			</div>

			<div class="email-footer">
				<p><?php printf( esc_html__( '© %s %s. All rights reserved.', 'addify_rfq' ), esc_html( date( 'Y' ) ), esc_html( get_bloginfo( 'name' ) ) ); ?></p>
				<div class="footer-links">
					<a href="<?php echo esc_url( home_url() ); ?>"><?php esc_html_e( 'Visit Website', 'addify_rfq' ); ?></a>
					<a href="<?php echo esc_url( home_url( '/privacy-policy' ) ); ?>"><?php esc_html_e( 'Privacy Policy', 'addify_rfq' ); ?></a>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
