<?php
/**
 * Modern email template for quote notifications.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/rfq/emails/quote-email-modern.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;
?>

<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=<?php bloginfo( 'charset' ); ?>" />
	<meta content="width=device-width, initial-scale=1.0" name="viewport">
	<title><?php echo esc_html( get_bloginfo( 'name', 'display') ); ?></title>
	<style type="text/css">
		body {
			font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
			line-height: 1.6;
			color: #1a202c;
			margin: 0;
			padding: 0;
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		}
		.email-wrapper {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			padding: 40px 20px;
			min-height: 100vh;
		}
		.email-container {
			max-width: 650px;
			margin: 0 auto;
			background: #ffffff;
			border-radius: 20px;
			overflow: hidden;
			box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
		}
		.email-header {
			background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
			color: #ffffff;
			padding: 50px 40px;
			text-align: center;
			position: relative;
		}
		.email-header::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
			opacity: 0.3;
		}
		.header-content {
			position: relative;
			z-index: 1;
		}
		.email-header h1 {
			margin: 0;
			font-size: 32px;
			font-weight: 700;
			letter-spacing: -0.5px;
		}
		.header-subtitle {
			margin-top: 10px;
			font-size: 16px;
			opacity: 0.9;
			font-weight: 400;
		}
		.email-content {
			padding: 50px 40px;
		}
		.status-badge {
			display: inline-block;
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			color: #ffffff;
			padding: 8px 16px;
			border-radius: 20px;
			font-size: 12px;
			font-weight: 600;
			text-transform: uppercase;
			letter-spacing: 0.5px;
			margin-bottom: 30px;
		}
		.email-message {
			font-size: 18px;
			margin-bottom: 40px;
			line-height: 1.7;
			color: #374151;
		}
		.quote-card {
			background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
			border-radius: 16px;
			padding: 30px;
			margin: 30px 0;
			border: 1px solid #e2e8f0;
			position: relative;
			overflow: hidden;
		}
		.quote-card::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 4px;
			height: 100%;
			background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
		}
		.quote-card h3 {
			margin: 0 0 25px 0;
			color: #1e293b;
			font-size: 22px;
			font-weight: 700;
		}
		.info-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
			gap: 25px;
		}
		.info-item {
			background: #ffffff;
			padding: 20px;
			border-radius: 12px;
			border: 1px solid #e2e8f0;
			transition: all 0.3s ease;
		}
		.info-label {
			font-size: 12px;
			font-weight: 600;
			color: #6b7280;
			text-transform: uppercase;
			letter-spacing: 0.5px;
			margin-bottom: 8px;
		}
		.info-value {
			font-size: 16px;
			font-weight: 600;
			color: #1e293b;
		}
		.cta-section {
			text-align: center;
			margin: 50px 0;
			padding: 40px;
			background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
			border-radius: 16px;
			border: 1px solid #f59e0b;
		}
		.cta-title {
			font-size: 20px;
			font-weight: 700;
			color: #92400e;
			margin-bottom: 15px;
		}
		.cta-description {
			font-size: 16px;
			color: #b45309;
			margin-bottom: 25px;
		}
		.cta-button {
			display: inline-block;
			background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
			color: #ffffff;
			padding: 16px 32px;
			text-decoration: none;
			border-radius: 12px;
			font-weight: 700;
			font-size: 16px;
			letter-spacing: 0.5px;
			box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
			transition: all 0.3s ease;
		}
		.email-footer {
			background: #1f2937;
			color: #9ca3af;
			padding: 40px;
			text-align: center;
		}
		.footer-content {
			max-width: 400px;
			margin: 0 auto;
		}
		.footer-logo {
			font-size: 20px;
			font-weight: 700;
			color: #ffffff;
			margin-bottom: 15px;
		}
		.footer-text {
			font-size: 14px;
			line-height: 1.6;
			margin-bottom: 20px;
		}
		.social-links {
			margin-top: 20px;
		}
		.social-links a {
			display: inline-block;
			width: 40px;
			height: 40px;
			background: #374151;
			border-radius: 50%;
			margin: 0 5px;
			line-height: 40px;
			text-align: center;
			color: #9ca3af;
			text-decoration: none;
			transition: all 0.3s ease;
		}
		@media (max-width: 600px) {
			.email-wrapper {
				padding: 20px 10px;
			}
			.email-header, .email-content, .email-footer {
				padding: 30px 25px;
			}
			.info-grid {
				grid-template-columns: 1fr;
				gap: 15px;
			}
			.email-header h1 {
				font-size: 28px;
			}
			.cta-section {
				padding: 30px 20px;
			}
		}
	</style>
</head>
<body>
	<div class="email-wrapper">
		<div class="email-container">
			<div class="email-header">
				<div class="header-content">
					<h1><?php echo esc_html( $email_heading ); ?></h1>
					<div class="header-subtitle"><?php echo esc_html( get_bloginfo( 'name' ) ); ?></div>
				</div>
			</div>
			
			<div class="email-content">
				<div class="status-badge">
					<?php echo esc_html( ucwords( str_replace( '_', ' ', get_post_meta( $quote_id, 'quote_status', true ) ) ) ); ?>
				</div>

				<div class="email-message">
					<?php
					echo wp_kses_post(
						str_replace(
							array( '{user_name}', '{quote_id}' ),
							array( $user_name, $quote_id ),
							wpautop( wptexturize( apply_filters( 'addify_rfq_email_text', $email_message ) ) )
						)
					);
					?>
				</div>

				<div class="quote-card">
					<h3><?php esc_html_e( 'Quote Information', 'addify_rfq' ); ?></h3>
					<div class="info-grid">
						<div class="info-item">
							<div class="info-label"><?php esc_html_e( 'Quote ID', 'addify_rfq' ); ?></div>
							<div class="info-value">#<?php echo esc_html( $quote_id ); ?></div>
						</div>
						<div class="info-item">
							<div class="info-label"><?php esc_html_e( 'Submitted', 'addify_rfq' ); ?></div>
							<div class="info-value"><?php echo esc_html( get_the_date( 'M j, Y', $quote_id ) ); ?></div>
						</div>
						<div class="info-item">
							<div class="info-label"><?php esc_html_e( 'Customer', 'addify_rfq' ); ?></div>
							<div class="info-value"><?php echo esc_html( $user_name ); ?></div>
						</div>
						<div class="info-item">
							<div class="info-label"><?php esc_html_e( 'Reference', 'addify_rfq' ); ?></div>
							<div class="info-value"><?php echo esc_html( strtoupper( substr( get_bloginfo( 'name' ), 0, 3 ) ) . '-' . $quote_id ); ?></div>
						</div>
					</div>
				</div>

				<?php
				/*
				 * @hooked AF_R_F_Q_Email_Controller::customer_details() Shows customer details
				 */
				do_action( 'addify_rfq_email_customer_details', $quote_id, $email );

				/*
				 * @hooked AF_R_F_Q_Email_Controller::quote_details() Shows the quote details table.
				 */
				do_action( 'addify_rfq_email_quote_details', $quote_id, $email );

				/*
				 * @hooked AF_R_F_Q_Email_Controller::quote_meta() Shows quote meta data.
				 */
				do_action( 'addify_rfq_email_quote_meta', $quote_id, $email );
				?>

				<div class="cta-section">
					<div class="cta-title"><?php esc_html_e( 'Need Help?', 'addify_rfq' ); ?></div>
					<div class="cta-description"><?php esc_html_e( 'Our team is here to assist you with any questions about your quote.', 'addify_rfq' ); ?></div>
					<a href="mailto:<?php echo esc_attr( get_option( 'admin_email' ) ); ?>" class="cta-button">
						<?php esc_html_e( 'Contact Support', 'addify_rfq' ); ?>
					</a>
				</div>
			</div>

			<div class="email-footer">
				<div class="footer-content">
					<div class="footer-logo"><?php echo esc_html( get_bloginfo( 'name' ) ); ?></div>
					<div class="footer-text">
						<?php printf( esc_html__( 'Thank you for choosing %s. We appreciate your business and look forward to serving you.', 'addify_rfq' ), esc_html( get_bloginfo( 'name' ) ) ); ?>
					</div>
					<div class="footer-text">
						<?php echo esc_html( home_url() ); ?> | <?php echo esc_html( get_option( 'admin_email' ) ); ?>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>
