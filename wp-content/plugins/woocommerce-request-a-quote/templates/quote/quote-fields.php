<?php
/**
 * Addify Add to Quote Fields
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/addify/rfq/quote/quote-fields.php.
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

$user_id = get_current_user_id();

$quote_fields_obj = new AF_R_F_Q_Quote_Fields();
$quote_fields     = (array) $quote_fields_obj->quote_fields;
$cache_data       = wc()->session->get( 'quote_fields_data' );
?>

<div class="quote-fields">

	<?php
	foreach ( $quote_fields as $key => $field ) :

		$field_id = $field->ID;

		// Check conditional logic
		if ( ! $quote_fields_obj->should_field_be_visible( $field_id, $cache_data ) ) {
			continue;
		}

		$afrfq_field_name        = get_post_meta( $field_id, 'afrfq_field_name', true );
		$afrfq_field_type        = get_post_meta( $field_id, 'afrfq_field_type', true );
		$afrfq_field_label       = get_post_meta( $field_id, 'afrfq_field_label', true );
		$afrfq_field_value       = get_post_meta( $field_id, 'afrfq_field_value', true );
		$afrfq_field_title       = get_post_meta( $field_id, 'afrfq_field_title', true );
		$afrfq_field_placeholder = get_post_meta( $field_id, 'afrfq_field_placeholder', true );
		$afrfq_field_options     = (array) get_post_meta( $field_id, 'afrfq_field_options', true );
		$afrfq_file_types        = get_post_meta( $field_id, 'afrfq_file_types', true );
		$afrfq_file_size         = get_post_meta( $field_id, 'afrfq_file_size', true );
		$afrfq_field_enable      = get_post_meta( $field_id, 'afrfq_field_enable', true );
		$afrfq_field_terms       = get_post_meta( $field_id, 'afrfq_field_terms', true );
		$afrfq_field_width       = get_post_meta( $field_id, 'afrfq_field_width', true );

		// Enhanced field configuration
		$afrfq_field_description = get_post_meta( $field_id, 'afrfq_field_description', true );
		$afrfq_field_css_class   = get_post_meta( $field_id, 'afrfq_field_css_class', true );
		$afrfq_field_min_length  = get_post_meta( $field_id, 'afrfq_field_min_length', true );
		$afrfq_field_max_length  = get_post_meta( $field_id, 'afrfq_field_max_length', true );
		$afrfq_field_min_value   = get_post_meta( $field_id, 'afrfq_field_min_value', true );
		$afrfq_field_max_value   = get_post_meta( $field_id, 'afrfq_field_max_value', true );
		$afrfq_field_pattern     = get_post_meta( $field_id, 'afrfq_field_pattern', true );

		if ( isset( $cache_data[ $afrfq_field_name ] ) ) {
			$field_data = $cache_data[ $afrfq_field_name ];
		} else {
			$field_data = $quote_fields_obj->get_field_default_value( $field_id, $user_id );
		}

		if ( empty( $afrfq_field_name ) ) {
			continue;
		}

		$required = 'yes' === get_post_meta( $field_id, 'afrfq_field_required', true ) ? 'required=required' : '';

		// Build CSS classes
		$css_classes = array( 'addify-option-field' );

		// Add width class
		if ( ! empty( $afrfq_field_width ) ) {
			$css_classes[] = 'adf_' . esc_attr( $afrfq_field_width );
		} else {
			$css_classes[] = 'adf_full_width';
		}

		// Add custom CSS classes
		if ( ! empty( $afrfq_field_css_class ) ) {
			$custom_classes = explode( ' ', $afrfq_field_css_class );
			$css_classes = array_merge( $css_classes, array_map( 'sanitize_html_class', $custom_classes ) );
		}

		$css_class_string = implode( ' ', array_filter( $css_classes ) );

		// Get conditional logic for this field
		$conditional_logic = get_post_meta( $field_id, 'afrfq_conditional_logic', true );
		$conditional_data = '';
		if ( ! empty( $conditional_logic ) ) {
			$conditional_data = 'data-conditional-logic="' . esc_attr( wp_json_encode( $conditional_logic ) ) . '"';
		}

		// Build field attributes
		$field_attributes = array();

		if ( ! empty( $afrfq_field_placeholder ) ) {
			$field_attributes[] = 'placeholder="' . esc_attr( $afrfq_field_placeholder ) . '"';
		}

		if ( ! empty( $afrfq_field_min_length ) && in_array( $afrfq_field_type, array( 'text', 'textarea', 'email', 'url' ), true ) ) {
			$field_attributes[] = 'minlength="' . esc_attr( $afrfq_field_min_length ) . '"';
		}

		if ( ! empty( $afrfq_field_max_length ) && in_array( $afrfq_field_type, array( 'text', 'textarea', 'email', 'url' ), true ) ) {
			$field_attributes[] = 'maxlength="' . esc_attr( $afrfq_field_max_length ) . '"';
		}

		if ( ! empty( $afrfq_field_min_value ) && in_array( $afrfq_field_type, array( 'number', 'range' ), true ) ) {
			$field_attributes[] = 'min="' . esc_attr( $afrfq_field_min_value ) . '"';
		}

		if ( ! empty( $afrfq_field_max_value ) && in_array( $afrfq_field_type, array( 'number', 'range' ), true ) ) {
			$field_attributes[] = 'max="' . esc_attr( $afrfq_field_max_value ) . '"';
		}

		if ( ! empty( $afrfq_field_pattern ) && in_array( $afrfq_field_type, array( 'text', 'email', 'url' ), true ) ) {
			$field_attributes[] = 'pattern="' . esc_attr( $afrfq_field_pattern ) . '"';
		}

		$field_attributes[] = $required;
		$attributes_string = implode( ' ', array_filter( $field_attributes ) );

		if ( 'terms_cond' == $afrfq_field_type ) {
			?>
			<p class="<?php echo esc_attr( $css_class_string ); ?> adf-term-conditon" data-field-name="<?php echo esc_attr( $afrfq_field_name ); ?>" <?php echo $conditional_data; ?>>
				<input type="checkbox" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="yes" <?php echo esc_attr( $required ); ?> >
				<span><?php echo wp_kses_post( $afrfq_field_terms ); ?></span>
				<?php if ( ! empty( $afrfq_field_description ) ) : ?>
					<small class="field-description"><?php echo esc_html( $afrfq_field_description ); ?></small>
				<?php endif; ?>
			</p>
			<?php
			continue;
		}

		?>
		<p class="<?php echo esc_attr( $css_class_string ); ?>" data-field-name="<?php echo esc_attr( $afrfq_field_name ); ?>" <?php echo $conditional_data; ?>>
			<label for="<?php echo esc_attr( $afrfq_field_name ); ?>">
				<?php echo esc_html( $afrfq_field_label ); ?>
				<?php if ( 'yes' === get_post_meta( $field_id, 'afrfq_field_required', true ) ) : ?>
					<span class="required">*</span>
				<?php endif; ?>
			</label>
				<?php
				switch ( $afrfq_field_type ) {
					case 'text':
						?>
						<input type="text" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-text-field">
						<?php
						break;

					case 'phone':
						?>
						<input type="tel" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-phone-field">
						<?php
						break;

					case 'url':
						?>
						<input type="url" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-url-field">
						<?php
						break;

					case 'time':
						?>
						<input type="time" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-time-field">
						<?php
						break;

					case 'date':
						?>
						<input type="date" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-date-field">
						<?php
						break;

					case 'datetime':
						?>
						<input type="datetime-local" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-datetime-field">
						<?php
						break;

					case 'email':
						?>
						<input type="email" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-email-field">
						<?php
						break;

					case 'number':
						?>
						<input type="number" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-number-field">
						<?php
						break;

					case 'range':
						?>
						<input type="range" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-range-field">
						<output for="<?php echo esc_attr( $afrfq_field_name ); ?>" class="range-output"><?php echo esc_html( $field_data ); ?></output>
						<?php
						break;

					case 'color':
						?>
						<input type="color" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-color-field">
						<?php
						break;
					case 'file':
						$accept_types = '';
						$multiple_attr = '';
						if ( ! empty( $afrfq_file_types ) ) {
							$types = array_map( 'trim', explode( ',', $afrfq_file_types ) );
							$accept_types = 'accept=".' . implode( ',.', $types ) . '"';
						}

						// Check if multiple files are allowed
						$allow_multiple = get_post_meta( $field_id, 'afrfq_allow_multiple', true );
						if ( 'yes' === $allow_multiple ) {
							$multiple_attr = 'multiple';
						}
						?>
						<div class="afrfq-file-upload-wrapper" data-field-name="<?php echo esc_attr( $afrfq_field_name ); ?>">
							<input type="file" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?><?php echo 'yes' === $allow_multiple ? '[]' : ''; ?>" <?php echo $accept_types; ?> <?php echo $multiple_attr; ?> <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-file-field">
							<div class="file-upload-dropzone" data-target="<?php echo esc_attr( $afrfq_field_name ); ?>">
								<div class="file-upload-content">
									<div class="file-upload-icon">
										<svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
											<path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
											<polyline points="17,8 12,3 7,8"></polyline>
											<line x1="12" y1="3" x2="12" y2="15"></line>
										</svg>
									</div>
									<div class="file-upload-text">
										<span class="primary-text"><?php echo ! empty( $afrfq_field_placeholder ) ? esc_html( $afrfq_field_placeholder ) : esc_html__( 'Drag & drop files here', 'addify_rfq' ); ?></span>
										<span class="secondary-text"><?php esc_html_e( 'or click to browse', 'addify_rfq' ); ?></span>
									</div>
									<button type="button" class="file-upload-button"><?php esc_html_e( 'Choose Files', 'addify_rfq' ); ?></button>
								</div>
								<div class="file-upload-progress" style="display: none;">
									<div class="progress-bar">
										<div class="progress-fill"></div>
									</div>
									<span class="progress-text">0%</span>
								</div>
							</div>
							<div class="file-upload-list"></div>
							<?php if ( ! empty( $afrfq_file_types ) ) : ?>
								<small class="file-types-info"><?php printf( esc_html__( 'Allowed file types: %s', 'addify_rfq' ), esc_html( $afrfq_file_types ) ); ?></small>
							<?php endif; ?>
							<?php if ( ! empty( $afrfq_file_size ) ) : ?>
								<small class="file-size-info"><?php printf( esc_html__( 'Maximum file size: %s', 'addify_rfq' ), size_format( $afrfq_file_size ) ); ?></small>
							<?php endif; ?>
							<?php if ( 'yes' === $allow_multiple ) : ?>
								<small class="file-multiple-info"><?php esc_html_e( 'You can select multiple files.', 'addify_rfq' ); ?></small>
							<?php endif; ?>
						</div>
						<?php
						break;

					case 'textarea':
						$rows = ! empty( $afrfq_field_max_length ) && $afrfq_field_max_length < 500 ? 3 : 5;
						?>
						<textarea id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" rows="<?php echo esc_attr( $rows ); ?>" <?php echo $attributes_string; ?> class="afrfq-field afrfq-textarea-field"><?php echo esc_textarea( $field_data ); ?></textarea>
						<?php
						break;
					case 'select':
						?>
						<select id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-select-field">
							<?php if ( ! empty( $afrfq_field_placeholder ) ) : ?>
								<option value=""><?php echo esc_html( $afrfq_field_placeholder ); ?></option>
							<?php endif; ?>
							<?php foreach ( (array) $afrfq_field_options as $option ) :
								$value = strtolower( trim( $option ) );
								?>
								<option value="<?php echo esc_attr( $value ); ?>" <?php echo selected( $value, $field_data ); ?>><?php echo esc_html( $option ); ?></option>
							<?php endforeach; ?>
						</select>
						<?php
						break;

					case 'multiselect':
						?>
						<select id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>[]" multiple <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-multiselect-field">
							<?php foreach ( (array) $afrfq_field_options as $option ) :
								$value = strtolower( trim( $option ) );
								?>
								<option value="<?php echo esc_attr( $value ); ?>" <?php echo in_array( $value, (array) $field_data, true ) ? 'selected' : ''; ?>><?php echo esc_html( $option ); ?></option>
							<?php endforeach; ?>
						</select>
						<small class="multiselect-help"><?php esc_html_e( 'Hold Ctrl (Cmd on Mac) to select multiple options.', 'addify_rfq' ); ?></small>
						<?php
						break;

					case 'radio':
						?>
						<div class="afrfq-radio-group">
							<?php foreach ( (array) $afrfq_field_options as $index => $option ) :
								$value = strtolower( trim( $option ) );
								$radio_id = $afrfq_field_name . '_' . $index;
								?>
								<label class="afrfq-radio-option" for="<?php echo esc_attr( $radio_id ); ?>">
									<input type="radio" id="<?php echo esc_attr( $radio_id ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $value ); ?>" <?php echo checked( $value, $field_data ); ?> <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-radio-field">
									<span class="radio-label"><?php echo esc_html( $option ); ?></span>
								</label>
							<?php endforeach; ?>
						</div>
						<?php
						break;

					case 'checkbox':
						?>
						<div class="afrfq-checkbox-group">
							<?php foreach ( (array) $afrfq_field_options as $index => $option ) :
								$value = strtolower( trim( $option ) );
								$checkbox_id = $afrfq_field_name . '_' . $index;
								?>
								<label class="afrfq-checkbox-option" for="<?php echo esc_attr( $checkbox_id ); ?>">
									<input type="checkbox" id="<?php echo esc_attr( $checkbox_id ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>[]" value="<?php echo esc_attr( $value ); ?>" <?php echo esc_attr( $required ); ?> <?php echo checked( in_array( $value, (array) $field_data ), true ); ?> class="afrfq-field afrfq-checkbox-field">
									<span class="checkbox-label"><?php echo esc_html( $option ); ?></span>
								</label>
							<?php endforeach; ?>
						</div>
						<?php
						break;

					case 'rating':
						$max_rating = get_post_meta( $field_id, 'afrfq_max_rating', true ) ?: 5;
						?>
						<div class="afrfq-rating-wrapper">
							<div class="afrfq-rating-stars" data-rating="<?php echo esc_attr( $field_data ); ?>" data-max="<?php echo esc_attr( $max_rating ); ?>">
								<?php for ( $i = 1; $i <= $max_rating; $i++ ) : ?>
									<span class="rating-star" data-value="<?php echo esc_attr( $i ); ?>">★</span>
								<?php endfor; ?>
							</div>
							<input type="hidden" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-rating-field">
						</div>
						<?php
						break;

					case 'slider':
						$min_val = get_post_meta( $field_id, 'afrfq_field_min', true ) ?: 0;
						$max_val = get_post_meta( $field_id, 'afrfq_field_max', true ) ?: 100;
						$step_val = get_post_meta( $field_id, 'afrfq_field_step', true ) ?: 1;
						?>
						<div class="afrfq-slider-wrapper">
							<input type="range" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ?: $min_val ); ?>" min="<?php echo esc_attr( $min_val ); ?>" max="<?php echo esc_attr( $max_val ); ?>" step="<?php echo esc_attr( $step_val ); ?>" <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-slider-field">
							<div class="slider-output-wrapper">
								<span class="slider-min"><?php echo esc_html( $min_val ); ?></span>
								<output for="<?php echo esc_attr( $afrfq_field_name ); ?>" class="slider-output"><?php echo esc_html( $field_data ?: $min_val ); ?></output>
								<span class="slider-max"><?php echo esc_html( $max_val ); ?></span>
							</div>
						</div>
						<?php
						break;

					case 'toggle':
						?>
						<div class="afrfq-toggle-wrapper">
							<label class="toggle-switch" for="<?php echo esc_attr( $afrfq_field_name ); ?>">
								<input type="checkbox" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="1" <?php echo checked( $field_data, '1' ); ?> <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-toggle-field">
								<span class="toggle-slider"></span>
								<span class="toggle-label"><?php echo ! empty( $afrfq_field_placeholder ) ? esc_html( $afrfq_field_placeholder ) : esc_html__( 'Enable', 'addify_rfq' ); ?></span>
							</label>
						</div>
						<?php
						break;

					case 'signature':
						?>
						<div class="afrfq-signature-wrapper">
							<canvas id="<?php echo esc_attr( $afrfq_field_name ); ?>_canvas" class="signature-canvas" width="400" height="200"></canvas>
							<input type="hidden" id="<?php echo esc_attr( $afrfq_field_name ); ?>" name="<?php echo esc_html( $afrfq_field_name ); ?>" value="<?php echo esc_attr( $field_data ); ?>" <?php echo esc_attr( $required ); ?> class="afrfq-field afrfq-signature-field">
							<div class="signature-controls">
								<button type="button" class="signature-clear"><?php esc_html_e( 'Clear', 'addify_rfq' ); ?></button>
								<button type="button" class="signature-undo"><?php esc_html_e( 'Undo', 'addify_rfq' ); ?></button>
							</div>
						</div>
						<?php
						break;
				}
				?>

				<?php if ( ! empty( $afrfq_field_description ) ) : ?>
					<small class="field-description"><?php echo esc_html( $afrfq_field_description ); ?></small>
				<?php endif; ?>
			</p>

	<?php endforeach; ?>

</div>

<!-- Enhanced Field Styling -->
<style type="text/css">
/* Enhanced Quote Fields Styling - Minimal & Clean Design */
.quote-fields {
	max-width: 900px;
	margin: 0 auto;
	padding: 30px;
	background: #ffffff;
	border-radius: 12px;
	box-shadow: 0 2px 20px rgba(0, 0, 0, 0.04);
	border: 1px solid #f0f2f5;
}

.addify-option-field {
	margin-bottom: 24px;
	position: relative;
	padding: 0;
	background: transparent;
	transition: all 0.3s ease;
}

.addify-option-field:last-child {
	margin-bottom: 0;
}

.addify-option-field label {
	display: block;
	margin-bottom: 10px;
	font-weight: 500;
	color: #1a202c;
	font-size: 15px;
	line-height: 1.4;
	letter-spacing: -0.01em;
}

.addify-option-field .required {
	color: #e53e3e;
	margin-left: 4px;
	font-weight: 600;
}

/* Field width classes with improved spacing */
.adf_full_width {
	width: 100%;
	margin-bottom: 24px;
}

.adf_half_width {
	width: calc(50% - 12px);
	display: inline-block;
	margin-right: 24px;
	margin-bottom: 24px;
	vertical-align: top;
}

.adf_half_width:nth-child(2n) {
	margin-right: 0;
}

.adf_third_width {
	width: calc(33.333% - 16px);
	display: inline-block;
	margin-right: 24px;
	margin-bottom: 24px;
	vertical-align: top;
}

.adf_third_width:nth-child(3n) {
	margin-right: 0;
}

.adf_quarter_width {
	width: calc(25% - 18px);
	display: inline-block;
	margin-right: 24px;
	margin-bottom: 24px;
	vertical-align: top;
}

.adf_quarter_width:nth-child(4n) {
	margin-right: 0;
}

/* Enhanced field styling - Minimal & Clean */
.afrfq-field {
	width: 100%;
	padding: 16px 20px;
	border: 1px solid #e2e8f0;
	border-radius: 8px;
	font-size: 16px;
	line-height: 1.5;
	font-weight: 400;
	color: #2d3748;
	background-color: #ffffff;
	box-sizing: border-box;
	transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
}

.afrfq-field::placeholder {
	color: #a0aec0;
	font-weight: 400;
}

.afrfq-field:hover {
	border-color: #cbd5e0;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.afrfq-field:focus {
	outline: none;
	border-color: #4299e1;
	box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1), 0 2px 8px rgba(0, 0, 0, 0.06);
	background-color: #ffffff;
}

.afrfq-field:invalid {
	border-color: #fc8181;
}

.afrfq-field.error {
	border-color: #fc8181;
	background-color: #fef5f5;
	box-shadow: 0 0 0 3px rgba(252, 129, 129, 0.1);
}

/* Specific field type styling - Enhanced */
.afrfq-textarea-field {
	resize: vertical;
	min-height: 120px;
	font-family: inherit;
	line-height: 1.6;
}

.afrfq-range-field {
	padding: 12px 0;
	background: transparent;
	border: none;
	box-shadow: none;
}

.afrfq-range-field:focus {
	box-shadow: none;
	border: none;
}

.range-output {
	display: inline-block;
	margin-left: 16px;
	padding: 8px 12px;
	background: #f7fafc;
	border: 1px solid #e2e8f0;
	border-radius: 6px;
	font-size: 14px;
	font-weight: 500;
	color: #2d3748;
	min-width: 40px;
	text-align: center;
}

.afrfq-color-field {
	width: 80px;
	height: 48px;
	padding: 6px;
	border-radius: 8px;
	cursor: pointer;
	border: 1px solid #e2e8f0;
}

.afrfq-color-field:focus {
	box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

/* Enhanced File Upload with Drag & Drop */
.afrfq-file-upload-wrapper {
	position: relative;
	margin-bottom: 16px;
}

.afrfq-file-field {
	position: absolute;
	opacity: 0;
	width: 100%;
	height: 100%;
	cursor: pointer;
	z-index: 2;
}

.file-upload-dropzone {
	border: 2px dashed #cbd5e0;
	border-radius: 12px;
	background: #f7fafc;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.file-upload-dropzone:hover,
.file-upload-dropzone.dragover {
	border-color: #4299e1;
	background: #ebf8ff;
	transform: translateY(-2px);
	box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
}

.file-upload-dropzone.dragover {
	border-color: #3182ce;
	background: #bee3f8;
}

.file-upload-content {
	padding: 40px 24px;
	text-align: center;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16px;
}

.file-upload-icon {
	color: #4299e1;
	opacity: 0.8;
	transition: all 0.2s ease;
}

.file-upload-dropzone:hover .file-upload-icon {
	opacity: 1;
	transform: translateY(-2px);
}

.file-upload-text {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.file-upload-text .primary-text {
	color: #2d3748;
	font-size: 16px;
	font-weight: 500;
	line-height: 1.4;
}

.file-upload-text .secondary-text {
	color: #718096;
	font-size: 14px;
	font-weight: 400;
}

.file-upload-button {
	background: #4299e1;
	color: #ffffff;
	padding: 12px 24px;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 500;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease;
	box-shadow: 0 2px 4px rgba(66, 153, 225, 0.2);
}

.file-upload-button:hover {
	background: #3182ce;
	transform: translateY(-1px);
	box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

/* File Upload Progress */
.file-upload-progress {
	padding: 20px 24px;
	text-align: center;
}

.progress-bar {
	width: 100%;
	height: 8px;
	background: #e2e8f0;
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 8px;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #4299e1, #3182ce);
	border-radius: 4px;
	transition: width 0.3s ease;
	width: 0%;
}

.progress-text {
	font-size: 14px;
	color: #4a5568;
	font-weight: 500;
}

/* File List */
.file-upload-list {
	margin-top: 16px;
}

.file-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px 16px;
	background: #ffffff;
	border: 1px solid #e2e8f0;
	border-radius: 8px;
	margin-bottom: 8px;
	transition: all 0.2s ease;
}

.file-item:hover {
	border-color: #cbd5e0;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.file-info {
	display: flex;
	align-items: center;
	gap: 12px;
	flex: 1;
}

.file-icon {
	width: 32px;
	height: 32px;
	background: #f7fafc;
	border-radius: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #4a5568;
}

.file-details {
	flex: 1;
}

.file-name {
	font-size: 14px;
	font-weight: 500;
	color: #2d3748;
	margin-bottom: 2px;
}

.file-size {
	font-size: 12px;
	color: #718096;
}

.file-remove {
	background: #fed7d7;
	color: #c53030;
	border: none;
	border-radius: 6px;
	padding: 6px 8px;
	cursor: pointer;
	font-size: 12px;
	transition: all 0.2s ease;
}

.file-remove:hover {
	background: #feb2b2;
	transform: scale(1.05);
}

/* Advanced Field Types Styling */

/* Star Rating */
.afrfq-rating-wrapper {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.afrfq-rating-stars {
	display: flex;
	gap: 4px;
	font-size: 24px;
	cursor: pointer;
}

.rating-star {
	color: #e2e8f0;
	transition: all 0.2s ease;
	cursor: pointer;
	user-select: none;
}

.rating-star:hover,
.rating-star.active {
	color: #fbbf24;
	transform: scale(1.1);
}

.rating-star.hover {
	color: #f59e0b;
}

/* Slider */
.afrfq-slider-wrapper {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.afrfq-slider-field {
	width: 100%;
	height: 8px;
	border-radius: 4px;
	background: #e2e8f0;
	outline: none;
	padding: 0;
	border: none;
	box-shadow: none;
	cursor: pointer;
}

.afrfq-slider-field::-webkit-slider-thumb {
	appearance: none;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background: #4299e1;
	cursor: pointer;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
	transition: all 0.2s ease;
}

.afrfq-slider-field::-webkit-slider-thumb:hover {
	background: #3182ce;
	transform: scale(1.1);
}

.afrfq-slider-field::-moz-range-thumb {
	width: 20px;
	height: 20px;
	border-radius: 50%;
	background: #4299e1;
	cursor: pointer;
	border: none;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-output-wrapper {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 14px;
	color: #4a5568;
}

.slider-output {
	background: #f7fafc;
	border: 1px solid #e2e8f0;
	border-radius: 6px;
	padding: 6px 12px;
	font-weight: 500;
	color: #2d3748;
	min-width: 50px;
	text-align: center;
}

.slider-min,
.slider-max {
	color: #718096;
	font-size: 12px;
}

/* Toggle Switch */
.afrfq-toggle-wrapper {
	display: flex;
	align-items: center;
}

.toggle-switch {
	display: flex;
	align-items: center;
	gap: 12px;
	cursor: pointer;
	user-select: none;
}

.toggle-switch input {
	display: none;
}

.toggle-slider {
	position: relative;
	width: 50px;
	height: 24px;
	background: #cbd5e0;
	border-radius: 12px;
	transition: all 0.3s ease;
	cursor: pointer;
}

.toggle-slider::before {
	content: '';
	position: absolute;
	top: 2px;
	left: 2px;
	width: 20px;
	height: 20px;
	background: #ffffff;
	border-radius: 50%;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
	background: #4299e1;
}

.toggle-switch input:checked + .toggle-slider::before {
	transform: translateX(26px);
}

.toggle-label {
	font-size: 15px;
	color: #2d3748;
	font-weight: 500;
}

/* Digital Signature */
.afrfq-signature-wrapper {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.signature-canvas {
	border: 2px solid #e2e8f0;
	border-radius: 8px;
	cursor: crosshair;
	background: #ffffff;
	width: 100%;
	max-width: 400px;
	height: 200px;
}

.signature-controls {
	display: flex;
	gap: 8px;
}

.signature-clear,
.signature-undo {
	background: #f7fafc;
	border: 1px solid #e2e8f0;
	border-radius: 6px;
	padding: 8px 16px;
	font-size: 14px;
	color: #4a5568;
	cursor: pointer;
	transition: all 0.2s ease;
}

.signature-clear:hover,
.signature-undo:hover {
	background: #edf2f7;
	border-color: #cbd5e0;
}

.file-types-info,
.file-size-info {
	display: block;
	margin-top: 8px;
	font-size: 13px;
	color: #718096;
	line-height: 1.4;
}

/* Radio and checkbox groups - Enhanced Design */
.afrfq-radio-group,
.afrfq-checkbox-group {
	display: flex;
	flex-direction: column;
	gap: 12px;
	margin-top: 4px;
}

.afrfq-radio-option,
.afrfq-checkbox-option {
	display: flex;
	align-items: center;
	padding: 16px 20px;
	border: 1px solid #e2e8f0;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
	background: #ffffff;
	position: relative;
}

.afrfq-radio-option:hover,
.afrfq-checkbox-option:hover {
	border-color: #4299e1;
	background: #f7fafc;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.afrfq-radio-option input:checked + .radio-label,
.afrfq-checkbox-option input:checked + .checkbox-label {
	font-weight: 500;
}

.afrfq-radio-option input:checked,
.afrfq-checkbox-option input:checked {
	accent-color: #4299e1;
}

.afrfq-radio-option input,
.afrfq-checkbox-option input {
	width: 18px;
	height: 18px;
	margin-right: 14px;
	margin-bottom: 0;
	cursor: pointer;
}

.radio-label,
.checkbox-label {
	font-size: 15px;
	color: #2d3748;
	cursor: pointer;
	line-height: 1.5;
	flex: 1;
}

/* Field descriptions - Enhanced Typography */
.field-description {
	display: block;
	margin-top: 8px;
	font-size: 13px;
	color: #718096;
	font-style: normal;
	line-height: 1.5;
	font-weight: 400;
}

/* Multiselect help text */
.multiselect-help {
	display: block;
	margin-top: 8px;
	font-size: 12px;
	color: #a0aec0;
	font-style: normal;
	background: #f7fafc;
	padding: 8px 12px;
	border-radius: 6px;
	border-left: 3px solid #4299e1;
}

/* Terms and conditions styling - Modern Card Design */
.adf-term-conditon {
	background: #f7fafc;
	border: 1px solid #e2e8f0;
	border-radius: 8px;
	padding: 24px;
	margin-bottom: 24px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
	transition: all 0.2s ease;
}

.adf-term-conditon:hover {
	border-color: #cbd5e0;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.adf-term-conditon input[type="checkbox"] {
	width: 18px;
	height: 18px;
	margin-right: 14px;
	margin-bottom: 0;
	accent-color: #4299e1;
	cursor: pointer;
}

.adf-term-conditon span {
	font-size: 15px;
	line-height: 1.6;
	color: #2d3748;
	cursor: pointer;
}

/* Responsive design - Enhanced Mobile Experience */
@media (max-width: 1024px) {
	.quote-fields {
		padding: 24px;
		margin: 0 16px;
	}

	.adf_quarter_width {
		width: calc(50% - 12px);
	}

	.adf_quarter_width:nth-child(4n) {
		margin-right: 24px;
	}

	.adf_quarter_width:nth-child(2n) {
		margin-right: 0;
	}
}

@media (max-width: 768px) {
	.quote-fields {
		padding: 20px;
		margin: 0 12px;
		border-radius: 8px;
	}

	.adf_half_width,
	.adf_third_width,
	.adf_quarter_width {
		width: 100%;
		margin-right: 0;
		margin-bottom: 20px;
	}

	.addify-option-field {
		margin-bottom: 20px;
	}

	.addify-option-field label {
		font-size: 14px;
		margin-bottom: 8px;
	}

	.afrfq-field {
		padding: 14px 16px;
		font-size: 16px; /* Prevents zoom on iOS */
	}

	.afrfq-radio-group,
	.afrfq-checkbox-group {
		gap: 10px;
	}

	.afrfq-radio-option,
	.afrfq-checkbox-option {
		padding: 14px 16px;
	}

	.file-upload-label {
		flex-direction: column;
		gap: 12px;
		text-align: center;
		padding: 16px 20px;
	}

	.file-upload-button {
		align-self: center;
		min-width: 120px;
	}

	.adf-term-conditon {
		padding: 20px;
	}

	.range-output {
		margin-left: 12px;
		margin-top: 8px;
	}
}

@media (max-width: 480px) {
	.quote-fields {
		padding: 16px;
		margin: 0 8px;
	}

	.addify-option-field {
		margin-bottom: 18px;
	}

	.afrfq-field {
		padding: 12px 14px;
	}

	.afrfq-radio-option,
	.afrfq-checkbox-option {
		padding: 12px 14px;
	}

	.file-upload-label {
		padding: 14px 16px;
	}
}

/* Enhanced focus and validation states */
.afrfq-field:focus + .field-description {
	color: #4299e1;
	font-weight: 500;
}

.afrfq-field.error + .field-description {
	color: #fc8181;
	font-weight: 500;
}

.afrfq-field.success {
	border-color: #48bb78;
	background-color: #f0fff4;
}

.afrfq-field.success + .field-description {
	color: #48bb78;
}

/* Loading and disabled states */
.afrfq-field:disabled {
	background-color: #f7fafc;
	color: #a0aec0;
	cursor: not-allowed;
	border-color: #e2e8f0;
}

.afrfq-field.loading {
	background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" fill="none" stroke="%234299e1" stroke-width="2" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416"><animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/></circle></svg>');
	background-repeat: no-repeat;
	background-position: right 16px center;
	background-size: 18px 18px;
}

/* Character counter styling */
.char-counter {
	display: block;
	margin-top: 6px;
	font-size: 12px;
	color: #a0aec0;
	text-align: right;
	font-weight: 500;
}

/* Form section styling */
.quote-fields-section {
	margin-bottom: 32px;
}

.quote-fields-section:last-child {
	margin-bottom: 0;
}

/* Enhanced select styling */
.afrfq-select-field,
.afrfq-multiselect-field {
	background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="%23a0aec0"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/></svg>');
	background-repeat: no-repeat;
	background-position: right 16px center;
	background-size: 16px 16px;
	padding-right: 48px;
	appearance: none;
}

.afrfq-multiselect-field {
	background-image: none;
	padding-right: 20px;
}

/* Smooth animations */
* {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.addify-option-field,
.afrfq-field,
.file-upload-label,
.afrfq-radio-option,
.afrfq-checkbox-option {
	will-change: transform, box-shadow, border-color;
}
</style>

<!-- Enhanced Field JavaScript -->
<script type="text/javascript">
jQuery(document).ready(function($) {
	// Initialize enhanced form functionality
	initializeEnhancedForm();

	function initializeEnhancedForm() {
		// Range field output update with smooth animation
		$('.afrfq-range-field').on('input', function() {
			var output = $(this).next('.range-output');
			output.text($(this).val());

			// Add visual feedback
			output.addClass('updating');
			setTimeout(function() {
				output.removeClass('updating');
			}, 200);
		});

		// Enhanced File Upload with Drag & Drop
		initializeFileUpload();

		// Initialize advanced field types
		initializeAdvancedFields();

		// Initialize conditional logic
		initializeConditionalLogic();

		// Enhanced validation system
		initializeAdvancedValidation();

		// Enhanced character counter with color coding
		$('.afrfq-field[maxlength]').each(function() {
			var field = $(this);
			var maxLength = parseInt(field.attr('maxlength'));
			var counter = $('<small class="char-counter"></small>');
			field.after(counter);

			function updateCounter() {
				var currentLength = field.val().length;
				counter.text(currentLength + '/' + maxLength);

				// Color coding based on usage
				if (currentLength > maxLength * 0.9) {
					counter.css('color', '#fc8181');
				} else if (currentLength > maxLength * 0.7) {
					counter.css('color', '#f6ad55');
				} else {
					counter.css('color', '#a0aec0');
				}

				// Add warning class for styling
				if (currentLength > maxLength * 0.9) {
					counter.addClass('warning');
				} else {
					counter.removeClass('warning');
				}
			}

			field.on('input', updateCounter);
			updateCounter();
		});

		// Smooth focus transitions
		$('.afrfq-field').on('focus', function() {
			$(this).closest('.addify-option-field').addClass('field-focused');
		}).on('blur', function() {
			$(this).closest('.addify-option-field').removeClass('field-focused');
		});

		// Enhanced radio/checkbox interactions
		$('.afrfq-radio-option, .afrfq-checkbox-option').on('click', function() {
			var input = $(this).find('input');
			if (input.attr('type') === 'radio') {
				// Remove selected class from other radio options in the same group
				$('input[name="' + input.attr('name') + '"]').closest('.afrfq-radio-option').removeClass('selected');
			}

			if (input.is(':checked')) {
				$(this).addClass('selected');
			} else {
				$(this).removeClass('selected');
			}
		});

		// Initialize selected states for pre-checked inputs
		$('.afrfq-radio-option input:checked, .afrfq-checkbox-option input:checked').each(function() {
			$(this).closest('.afrfq-radio-option, .afrfq-checkbox-option').addClass('selected');
		});

		// Terms and conditions enhancement
		$('.adf-term-conditon input[type="checkbox"]').on('change', function() {
			var container = $(this).closest('.adf-term-conditon');
			if ($(this).is(':checked')) {
				container.addClass('accepted');
			} else {
				container.removeClass('accepted');
			}
		});
	}

	function initializeFileUpload() {
		$('.afrfq-file-upload-wrapper').each(function() {
			var $wrapper = $(this);
			var $input = $wrapper.find('.afrfq-file-field');
			var $dropzone = $wrapper.find('.file-upload-dropzone');
			var $button = $wrapper.find('.file-upload-button');
			var $list = $wrapper.find('.file-upload-list');
			var fieldName = $wrapper.data('field-name');
			var isMultiple = $input.prop('multiple');
			var selectedFiles = [];

			// Click handler for button and dropzone
			$button.on('click', function(e) {
				e.preventDefault();
				$input.click();
			});

			$dropzone.on('click', function(e) {
				if (e.target === this || $(e.target).closest('.file-upload-content').length) {
					$input.click();
				}
			});

			// Drag and drop handlers
			$dropzone.on('dragover dragenter', function(e) {
				e.preventDefault();
				e.stopPropagation();
				$(this).addClass('dragover');
			});

			$dropzone.on('dragleave dragend', function(e) {
				e.preventDefault();
				e.stopPropagation();
				$(this).removeClass('dragover');
			});

			$dropzone.on('drop', function(e) {
				e.preventDefault();
				e.stopPropagation();
				$(this).removeClass('dragover');

				var files = e.originalEvent.dataTransfer.files;
				handleFiles(files);
			});

			// File input change handler
			$input.on('change', function() {
				var files = this.files;
				handleFiles(files);
			});

			function handleFiles(files) {
				if (!isMultiple) {
					selectedFiles = [];
					$list.empty();
				}

				for (var i = 0; i < files.length; i++) {
					var file = files[i];
					if (validateFile(file)) {
						selectedFiles.push(file);
						addFileToList(file);
					}
				}

				updateFileInput();
			}

			function validateFile(file) {
				// Get allowed file types from accept attribute
				var accept = $input.attr('accept');
				if (accept) {
					var allowedTypes = accept.split(',').map(function(type) {
						return type.trim().toLowerCase();
					});

					var fileExt = '.' + file.name.split('.').pop().toLowerCase();
					var mimeType = file.type.toLowerCase();

					var isValid = allowedTypes.some(function(type) {
						return type === fileExt || type === mimeType ||
							   (type.endsWith('/*') && mimeType.startsWith(type.slice(0, -1)));
					});

					if (!isValid) {
						alert('File type not allowed: ' + file.name);
						return false;
					}
				}

				return true;
			}

			function addFileToList(file) {
				var fileItem = $('<div class="file-item">' +
					'<div class="file-info">' +
						'<div class="file-icon">📄</div>' +
						'<div class="file-details">' +
							'<div class="file-name">' + escapeHtml(file.name) + '</div>' +
							'<div class="file-size">' + formatFileSize(file.size) + '</div>' +
						'</div>' +
					'</div>' +
					'<button type="button" class="file-remove">✕</button>' +
				'</div>');

				fileItem.find('.file-remove').on('click', function() {
					var index = selectedFiles.indexOf(file);
					if (index > -1) {
						selectedFiles.splice(index, 1);
						fileItem.remove();
						updateFileInput();
					}
				});

				$list.append(fileItem);
			}

			function updateFileInput() {
				// Update UI
				if (selectedFiles.length > 0) {
					$dropzone.addClass('has-files');
					$wrapper.find('.primary-text').text(
						selectedFiles.length === 1 ?
						selectedFiles[0].name :
						selectedFiles.length + ' files selected'
					);
				} else {
					$dropzone.removeClass('has-files');
					$wrapper.find('.primary-text').text($wrapper.find('.primary-text').data('original-text') || 'Drag & drop files here');
				}
			}

			function formatFileSize(bytes) {
				if (bytes === 0) return '0 Bytes';
				var k = 1024;
				var sizes = ['Bytes', 'KB', 'MB', 'GB'];
				var i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}

			function escapeHtml(text) {
				var map = {
					'&': '&amp;',
					'<': '&lt;',
					'>': '&gt;',
					'"': '&quot;',
					"'": '&#039;'
				};
				return text.replace(/[&<>"']/g, function(m) { return map[m]; });
			}

			// Store original text for reset
			$wrapper.find('.primary-text').data('original-text', $wrapper.find('.primary-text').text());
		});
	}

	function initializeAdvancedFields() {
		// Star Rating
		$('.afrfq-rating-stars').each(function() {
			var $stars = $(this);
			var $input = $stars.siblings('.afrfq-rating-field');
			var currentRating = parseInt($stars.data('rating')) || 0;
			var maxRating = parseInt($stars.data('max')) || 5;

			// Set initial state
			updateStars(currentRating);

			$stars.find('.rating-star').on('click', function() {
				var rating = parseInt($(this).data('value'));
				$input.val(rating);
				updateStars(rating);
			});

			$stars.find('.rating-star').on('mouseenter', function() {
				var rating = parseInt($(this).data('value'));
				highlightStars(rating);
			});

			$stars.on('mouseleave', function() {
				updateStars(parseInt($input.val()) || 0);
			});

			function updateStars(rating) {
				$stars.find('.rating-star').each(function(index) {
					$(this).toggleClass('active', index < rating);
				});
			}

			function highlightStars(rating) {
				$stars.find('.rating-star').each(function(index) {
					$(this).toggleClass('hover', index < rating);
					$(this).toggleClass('active', false);
				});
			}
		});

		// Enhanced Slider
		$('.afrfq-slider-field').on('input', function() {
			var output = $(this).siblings('.slider-output-wrapper').find('.slider-output');
			output.text($(this).val());

			// Add visual feedback
			output.addClass('updating');
			setTimeout(function() {
				output.removeClass('updating');
			}, 200);
		});

		// Digital Signature
		$('.signature-canvas').each(function() {
			var canvas = this;
			var ctx = canvas.getContext('2d');
			var $canvas = $(canvas);
			var $input = $canvas.siblings('.afrfq-signature-field');
			var $wrapper = $canvas.closest('.afrfq-signature-wrapper');
			var isDrawing = false;
			var strokes = [];
			var currentStroke = [];

			// Set canvas size
			var rect = canvas.getBoundingClientRect();
			canvas.width = rect.width;
			canvas.height = rect.height;

			// Drawing functions
			function startDrawing(e) {
				isDrawing = true;
				currentStroke = [];
				var pos = getMousePos(e);
				currentStroke.push(pos);
				ctx.beginPath();
				ctx.moveTo(pos.x, pos.y);
			}

			function draw(e) {
				if (!isDrawing) return;
				var pos = getMousePos(e);
				currentStroke.push(pos);
				ctx.lineTo(pos.x, pos.y);
				ctx.stroke();
			}

			function stopDrawing() {
				if (!isDrawing) return;
				isDrawing = false;
				strokes.push([...currentStroke]);
				saveSignature();
			}

			function getMousePos(e) {
				var rect = canvas.getBoundingClientRect();
				var clientX = e.clientX || (e.touches && e.touches[0].clientX);
				var clientY = e.clientY || (e.touches && e.touches[0].clientY);
				return {
					x: clientX - rect.left,
					y: clientY - rect.top
				};
			}

			function saveSignature() {
				var dataURL = canvas.toDataURL('image/png');
				$input.val(dataURL);
			}

			function clearSignature() {
				ctx.clearRect(0, 0, canvas.width, canvas.height);
				strokes = [];
				$input.val('');
			}

			function undoStroke() {
				if (strokes.length > 0) {
					strokes.pop();
					redrawSignature();
				}
			}

			function redrawSignature() {
				ctx.clearRect(0, 0, canvas.width, canvas.height);
				ctx.beginPath();
				strokes.forEach(function(stroke) {
					if (stroke.length > 0) {
						ctx.moveTo(stroke[0].x, stroke[0].y);
						stroke.forEach(function(point) {
							ctx.lineTo(point.x, point.y);
						});
					}
				});
				ctx.stroke();
				saveSignature();
			}

			// Mouse events
			$canvas.on('mousedown', startDrawing);
			$canvas.on('mousemove', draw);
			$canvas.on('mouseup', stopDrawing);
			$canvas.on('mouseleave', stopDrawing);

			// Touch events
			$canvas.on('touchstart', function(e) {
				e.preventDefault();
				startDrawing(e.originalEvent);
			});
			$canvas.on('touchmove', function(e) {
				e.preventDefault();
				draw(e.originalEvent);
			});
			$canvas.on('touchend', function(e) {
				e.preventDefault();
				stopDrawing();
			});

			// Control buttons
			$wrapper.find('.signature-clear').on('click', clearSignature);
			$wrapper.find('.signature-undo').on('click', undoStroke);

			// Set drawing style
			ctx.strokeStyle = '#2d3748';
			ctx.lineWidth = 2;
			ctx.lineCap = 'round';
			ctx.lineJoin = 'round';

			// Load existing signature if any
			if ($input.val()) {
				var img = new Image();
				img.onload = function() {
					ctx.drawImage(img, 0, 0);
				};
				img.src = $input.val();
			}
		});
	}

	function initializeConditionalLogic() {
		var conditionalFields = [];

		// Collect all fields with conditional logic
		$('[data-conditional-logic]').each(function() {
			var $field = $(this);
			var fieldName = $field.data('field-name');
			var logic = $field.data('conditional-logic');

			if (logic && fieldName) {
				conditionalFields.push({
					element: $field,
					fieldName: fieldName,
					logic: logic
				});
			}
		});

		// Function to evaluate conditional logic
		function evaluateConditions() {
			conditionalFields.forEach(function(field) {
				var shouldShow = evaluateFieldConditions(field.logic);
				toggleFieldVisibility(field.element, shouldShow);
			});
		}

		function evaluateFieldConditions(logic) {
			if (!logic.rules || logic.rules.length === 0) {
				return true;
			}

			var results = logic.rules.map(function(rule) {
				return evaluateRule(rule);
			});

			// Apply logic type (AND/OR)
			if (logic.logic_type === 'or') {
				return results.some(function(result) { return result; });
			} else {
				return results.every(function(result) { return result; });
			}
		}

		function evaluateRule(rule) {
			var triggerValue = getTriggerValue(rule);
			return applyOperator(rule.operator, triggerValue, rule.value);
		}

		function getTriggerValue(rule) {
			switch (rule.trigger) {
				case 'field_value':
					var $targetField = $('[name="' + rule.field + '"]');
					if ($targetField.length === 0) return '';

					if ($targetField.is(':checkbox') || $targetField.is(':radio')) {
						return $targetField.filter(':checked').val() || '';
					} else if ($targetField.is('select[multiple]')) {
						return $targetField.val() || [];
					} else {
						return $targetField.val() || '';
					}

				case 'user_role':
					// This would need to be passed from PHP
					return window.afrfqUserRole || 'guest';

				case 'product_type':
					// This would need to be passed from PHP
					return window.afrfqProductType || '';

				case 'product_category':
					// This would need to be passed from PHP
					return window.afrfqProductCategory || '';

				case 'cart_total':
					// This would need to be passed from PHP
					return parseFloat(window.afrfqCartTotal) || 0;

				default:
					return '';
			}
		}

		function applyOperator(operator, value1, value2) {
			switch (operator) {
				case 'equals':
					return String(value1) === String(value2);

				case 'not_equals':
					return String(value1) !== String(value2);

				case 'contains':
					return String(value1).indexOf(String(value2)) !== -1;

				case 'not_contains':
					return String(value1).indexOf(String(value2)) === -1;

				case 'greater_than':
					return parseFloat(value1) > parseFloat(value2);

				case 'less_than':
					return parseFloat(value1) < parseFloat(value2);

				case 'is_empty':
					return !value1 || (Array.isArray(value1) && value1.length === 0);

				case 'is_not_empty':
					return !!value1 && (!Array.isArray(value1) || value1.length > 0);

				default:
					return true;
			}
		}

		function toggleFieldVisibility($field, show) {
			if (show) {
				$field.show().removeClass('conditionally-hidden');
			} else {
				$field.hide().addClass('conditionally-hidden');
				// Clear field values when hidden
				$field.find('.afrfq-field').val('').prop('checked', false);
			}
		}

		// Listen for field changes
		$(document).on('change input', '.afrfq-field', function() {
			setTimeout(evaluateConditions, 10); // Small delay to ensure value is updated
		});

		// Initial evaluation
		evaluateConditions();
	}

	function initializeAdvancedValidation() {
		var validationRules = {
			email: {
				pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
				message: 'Please enter a valid email address'
			},
			phone: {
				pattern: /^[\+]?[1-9][\d]{0,15}$/,
				message: 'Please enter a valid phone number'
			},
			url: {
				pattern: /^https?:\/\/.+/,
				message: 'Please enter a valid URL starting with http:// or https://'
			},
			number: {
				pattern: /^-?\d*\.?\d+$/,
				message: 'Please enter a valid number'
			},
			date: {
				pattern: /^\d{4}-\d{2}-\d{2}$/,
				message: 'Please enter a valid date (YYYY-MM-DD)'
			},
			time: {
				pattern: /^\d{2}:\d{2}$/,
				message: 'Please enter a valid time (HH:MM)'
			},
			color: {
				pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
				message: 'Please enter a valid color code (e.g., #FF0000)'
			}
		};

		// Real-time validation on field blur
		$(document).on('blur', '.afrfq-field', function() {
			validateField($(this));
		});

		// Clear errors on input
		$(document).on('input', '.afrfq-field', function() {
			var $field = $(this);
			if ($field.hasClass('validation-error')) {
				clearFieldError($field);
				// Re-validate if field has content
				if ($field.val().trim()) {
					setTimeout(function() {
						validateField($field);
					}, 300);
				}
			}
		});

		// Form submission validation
		$(document).on('submit', '.afrfq-quote-form', function(e) {
			if (!validateForm($(this))) {
				e.preventDefault();
				return false;
			}
		});

		function validateField($field) {
			var fieldType = $field.attr('type') || $field.prop('tagName').toLowerCase();
			var fieldName = $field.attr('name') || '';
			var value = $field.val().trim();
			var isRequired = $field.prop('required') || $field.hasClass('required');
			var isValid = true;
			var errorMessage = '';

			// Clear previous validation state
			clearFieldError($field);

			// Required field validation
			if (isRequired && !value) {
				isValid = false;
				errorMessage = 'This field is required';
			}
			// Type-specific validation
			else if (value && validationRules[fieldType]) {
				if (!validationRules[fieldType].pattern.test(value)) {
					isValid = false;
					errorMessage = validationRules[fieldType].message;
				}
			}
			// Custom pattern validation
			else if (value && $field.data('pattern')) {
				var customPattern = new RegExp($field.data('pattern'));
				if (!customPattern.test(value)) {
					isValid = false;
					errorMessage = $field.data('pattern-message') || 'Please enter a value in the correct format';
				}
			}
			// Length validation
			else if (value) {
				var minLength = parseInt($field.attr('minlength') || $field.data('min-length')) || 0;
				var maxLength = parseInt($field.attr('maxlength') || $field.data('max-length')) || 0;

				if (minLength > 0 && value.length < minLength) {
					isValid = false;
					errorMessage = 'Minimum length is ' + minLength + ' characters';
				} else if (maxLength > 0 && value.length > maxLength) {
					isValid = false;
					errorMessage = 'Maximum length is ' + maxLength + ' characters';
				}
			}
			// Numeric range validation
			if (value && (fieldType === 'number' || fieldType === 'range')) {
				var numValue = parseFloat(value);
				var min = parseFloat($field.attr('min')) || null;
				var max = parseFloat($field.attr('max')) || null;

				if (min !== null && numValue < min) {
					isValid = false;
					errorMessage = 'Minimum value is ' + min;
				} else if (max !== null && numValue > max) {
					isValid = false;
					errorMessage = 'Maximum value is ' + max;
				}
			}

			// File validation
			if (fieldType === 'file' && $field[0].files && $field[0].files.length > 0) {
				var file = $field[0].files[0];
				var allowedTypes = $field.attr('accept');
				var maxSize = parseInt($field.data('max-size')) || 10485760; // 10MB default

				if (allowedTypes) {
					var fileExt = '.' + file.name.split('.').pop().toLowerCase();
					var allowedExts = allowedTypes.split(',').map(function(type) {
						return type.trim().toLowerCase();
					});

					if (allowedExts.indexOf(fileExt) === -1 && allowedExts.indexOf(file.type.toLowerCase()) === -1) {
						isValid = false;
						errorMessage = 'File type not allowed. Allowed types: ' + allowedTypes;
					}
				}

				if (file.size > maxSize) {
					isValid = false;
					errorMessage = 'File size too large. Maximum size: ' + formatFileSize(maxSize);
				}
			}

			// Apply validation result
			if (isValid) {
				showFieldSuccess($field);
			} else {
				showFieldError($field, errorMessage);
			}

			return isValid;
		}

		function validateForm($form) {
			var isValid = true;
			var firstErrorField = null;

			$form.find('.afrfq-field').each(function() {
				if (!validateField($(this))) {
					isValid = false;
					if (!firstErrorField) {
						firstErrorField = $(this);
					}
				}
			});

			// Scroll to first error
			if (!isValid && firstErrorField) {
				$('html, body').animate({
					scrollTop: firstErrorField.offset().top - 100
				}, 500);
				firstErrorField.focus();
			}

			return isValid;
		}

		function showFieldError($field, message) {
			$field.addClass('validation-error');
			clearFieldError($field);

			var $errorDiv = $('<div class="field-validation-error">' + message + '</div>');
			$field.after($errorDiv);

			// Add shake animation
			$field.addClass('shake');
			setTimeout(function() {
				$field.removeClass('shake');
			}, 600);
		}

		function showFieldSuccess($field) {
			$field.removeClass('validation-error').addClass('validation-success');
			clearFieldError($field);
		}

		function clearFieldError($field) {
			$field.removeClass('validation-error validation-success');
			$field.siblings('.field-validation-error').remove();
		}

		function formatFileSize(bytes) {
			if (bytes === 0) return '0 Bytes';
			var k = 1024;
			var sizes = ['Bytes', 'KB', 'MB', 'GB'];
			var i = Math.floor(Math.log(bytes) / Math.log(k));
			return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
		}

		// Initialize character counters
		initializeCharacterCounters();
	}

	function initializeCharacterCounters() {
		// Add character counters to fields with maxlength
		$('.afrfq-field[maxlength], .afrfq-field[data-max-length]').each(function() {
			var $field = $(this);
			var maxLength = parseInt($field.attr('maxlength') || $field.data('max-length')) || 0;

			if (maxLength > 0) {
				var fieldId = $field.attr('id') || $field.attr('name');
				var currentLength = $field.val().length;

				var $counter = $('<div class="char-counter" data-target="' + fieldId + '">' +
					'<span class="current-count">' + currentLength + '</span>/' +
					'<span class="max-count">' + maxLength + '</span>' +
				'</div>');

				$field.after($counter);
				updateCharacterCounter($field, $counter, maxLength);
			}
		});

		// Update character counters on input
		$(document).on('input', '.afrfq-field[maxlength], .afrfq-field[data-max-length]', function() {
			var $field = $(this);
			var maxLength = parseInt($field.attr('maxlength') || $field.data('max-length')) || 0;
			var $counter = $field.siblings('.char-counter');

			if ($counter.length && maxLength > 0) {
				updateCharacterCounter($field, $counter, maxLength);
			}
		});

		function updateCharacterCounter($field, $counter, maxLength) {
			var currentLength = $field.val().length;
			var percentage = (currentLength / maxLength) * 100;

			$counter.find('.current-count').text(currentLength);

			// Update counter styling based on usage
			$counter.removeClass('warning error');
			if (percentage >= 90) {
				$counter.addClass('error');
			} else if (percentage >= 75) {
				$counter.addClass('warning');
			}
		}
	}

	// Add CSS for dynamic states
	$('<style>')
		.prop('type', 'text/css')
		.html(`
			.field-focused { transform: translateY(-1px); }
			.range-output.updating, .slider-output.updating {
				transform: scale(1.1);
				transition: transform 0.2s ease;
			}
			.upload-success { border-color: #48bb78 !important; background: #f0fff4 !important; }
			.char-counter.warning { font-weight: 600; }
			.afrfq-radio-option.selected, .afrfq-checkbox-option.selected {
				border-color: #4299e1;
				background: #ebf8ff;
				box-shadow: 0 2px 8px rgba(66, 153, 225, 0.15);
			}
			.adf-term-conditon.accepted {
				border-color: #48bb78;
				background: #f0fff4;
			}
			.file-upload-dropzone.has-files {
				border-color: #48bb78;
				background: #f0fff4;
				border-style: solid;
			}
			.signature-canvas:hover {
				border-color: #4299e1;
			}
			.conditionally-hidden {
				display: none !important;
			}
			.addify-option-field {
				transition: opacity 0.3s ease, transform 0.3s ease;
			}
			.addify-option-field.conditionally-hidden {
				opacity: 0;
				transform: translateY(-10px);
			}

			/* Enhanced Validation Styles */
			.afrfq-field.validation-error {
				border-color: #e74c3c !important;
				box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.2) !important;
				background-color: #fdf2f2 !important;
			}

			.afrfq-field.validation-success {
				border-color: #27ae60 !important;
				box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.2) !important;
				background-color: #f8fff8 !important;
			}

			.field-validation-error {
				color: #e74c3c;
				font-size: 12px;
				margin-top: 4px;
				padding: 4px 8px;
				background: #fdf2f2;
				border: 1px solid #fadbd8;
				border-radius: 4px;
				display: flex;
				align-items: center;
				animation: slideDown 0.3s ease;
			}

			.field-validation-error:before {
				content: "⚠";
				margin-right: 6px;
				font-weight: bold;
			}

			@keyframes slideDown {
				from {
					opacity: 0;
					transform: translateY(-10px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			@keyframes shake {
				0%, 100% { transform: translateX(0); }
				10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
				20%, 40%, 60%, 80% { transform: translateX(5px); }
			}

			.shake {
				animation: shake 0.6s ease-in-out;
			}

			/* Real-time validation feedback */
			.afrfq-field:focus {
				outline: none;
				border-color: #4299e1;
				box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.2);
			}

			.afrfq-field.validation-error:focus {
				border-color: #e74c3c;
				box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.3);
			}

			.afrfq-field.validation-success:focus {
				border-color: #27ae60;
				box-shadow: 0 0 0 2px rgba(39, 174, 96, 0.3);
			}

			/* Character counter */
			.char-counter {
				font-size: 11px;
				color: #718096;
				text-align: right;
				margin-top: 2px;
				transition: color 0.2s ease;
			}

			.char-counter.warning {
				color: #f39c12;
				font-weight: 600;
			}

			.char-counter.error {
				color: #e74c3c;
				font-weight: 600;
			}

			/* Progress indicators for complex fields */
			.field-strength-indicator {
				height: 4px;
				background: #e2e8f0;
				border-radius: 2px;
				margin-top: 4px;
				overflow: hidden;
			}

			.field-strength-bar {
				height: 100%;
				transition: width 0.3s ease, background-color 0.3s ease;
				border-radius: 2px;
			}

			.field-strength-weak { background: #e74c3c; width: 25%; }
			.field-strength-fair { background: #f39c12; width: 50%; }
			.field-strength-good { background: #f1c40f; width: 75%; }
			.field-strength-strong { background: #27ae60; width: 100%; }
		`)
		.appendTo('head');
});
</script>
