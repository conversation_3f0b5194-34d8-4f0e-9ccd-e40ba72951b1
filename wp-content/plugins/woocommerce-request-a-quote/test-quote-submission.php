<?php
/**
 * Test Quote Submission Functionality
 * 
 * This file helps test the quote submission workflow.
 * Access via: /wp-content/plugins/woocommerce-request-a-quote/test-quote-submission.php
 * 
 * @package addify-request-a-quote
 * @version 1.6.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
	// Load WordPress if not already loaded
	require_once( '../../../wp-load.php' );
}

// Check if user has admin capabilities
if ( ! current_user_can( 'manage_options' ) ) {
	wp_die( 'You do not have sufficient permissions to access this page.' );
}

?>
<!DOCTYPE html>
<html>
<head>
	<title>Quote Submission Test</title>
	<style>
		body { font-family: Arial, sans-serif; margin: 20px; }
		.test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
		.success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
		.error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
		.info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
		.test-form { max-width: 500px; }
		.test-form input, .test-form select { width: 100%; padding: 8px; margin: 5px 0; }
		.test-form button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
	</style>
</head>
<body>
	<h1>WooCommerce Request a Quote - Test Suite</h1>
	
	<?php
	// Test 1: Check if classes exist
	echo '<div class="test-section">';
	echo '<h2>Test 1: Class Availability</h2>';
	
	$classes_to_check = array(
		'AF_R_F_Q_Quote',
		'AF_R_F_Q_Quote_Fields',
		'AF_R_F_Q_Email_Controller',
		'AF_R_F_Q_Front'
	);
	
	foreach ( $classes_to_check as $class_name ) {
		if ( class_exists( $class_name ) ) {
			echo "<p class='success'>✓ Class {$class_name} exists</p>";
		} else {
			echo "<p class='error'>✗ Class {$class_name} missing</p>";
		}
	}
	echo '</div>';
	
	// Test 2: Check email settings
	echo '<div class="test-section">';
	echo '<h2>Test 2: Email Configuration</h2>';
	
	$email_settings = get_option( 'afrfq_emails' );
	if ( ! empty( $email_settings ) ) {
		echo "<p class='success'>✓ Email settings configured</p>";
		
		// Check specific email types
		$required_emails = array( 'af_pending', 'af_admin' );
		foreach ( $required_emails as $email_type ) {
			if ( isset( $email_settings[ $email_type ] ) ) {
				$enabled = isset( $email_settings[ $email_type ]['enable'] ) ? $email_settings[ $email_type ]['enable'] : 'no';
				if ( 'yes' === $enabled ) {
					echo "<p class='success'>✓ {$email_type} email enabled</p>";
				} else {
					echo "<p class='error'>✗ {$email_type} email disabled</p>";
				}
			} else {
				echo "<p class='error'>✗ {$email_type} email not configured</p>";
			}
		}
	} else {
		echo "<p class='error'>✗ No email settings found</p>";
	}
	
	// Check WooCommerce email settings
	$wc_from_email = get_option( 'woocommerce_email_from_address' );
	$wc_from_name = get_option( 'woocommerce_email_from_name' );
	
	if ( ! empty( $wc_from_email ) ) {
		echo "<p class='success'>✓ WooCommerce from email: {$wc_from_email}</p>";
	} else {
		echo "<p class='error'>✗ WooCommerce from email not set</p>";
	}
	
	if ( ! empty( $wc_from_name ) ) {
		echo "<p class='success'>✓ WooCommerce from name: {$wc_from_name}</p>";
	} else {
		echo "<p class='error'>✗ WooCommerce from name not set</p>";
	}
	
	echo '</div>';
	
	// Test 3: Database tables
	echo '<div class="test-section">';
	echo '<h2>Test 3: Database Structure</h2>';
	
	global $wpdb;
	
	// Check if quote post type exists
	$quote_posts = $wpdb->get_var( "SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'addify_quote'" );
	echo "<p class='info'>Found {$quote_posts} quote records in database</p>";
	
	// Check recent quotes
	$recent_quotes = $wpdb->get_results( 
		"SELECT ID, post_title, post_date FROM {$wpdb->posts} 
		 WHERE post_type = 'addify_quote' 
		 ORDER BY post_date DESC 
		 LIMIT 5"
	);
	
	if ( ! empty( $recent_quotes ) ) {
		echo "<p class='success'>✓ Recent quotes found:</p>";
		echo "<ul>";
		foreach ( $recent_quotes as $quote ) {
			echo "<li>Quote #{$quote->ID}: {$quote->post_title} ({$quote->post_date})</li>";
		}
		echo "</ul>";
	} else {
		echo "<p class='info'>No quotes found in database</p>";
	}
	
	echo '</div>';
	
	// Test 4: Form submission test
	if ( isset( $_POST['test_submit'] ) ) {
		echo '<div class="test-section">';
		echo '<h2>Test 4: Form Submission Result</h2>';
		
		// Simulate form submission
		$test_data = array(
			'afrfq_customer_name' => sanitize_text_field( $_POST['test_name'] ),
			'afrfq_company_name' => sanitize_text_field( $_POST['test_company'] ),
			'afrfq_email_address' => sanitize_email( $_POST['test_email'] ),
			'afrfq_phone_number' => sanitize_text_field( $_POST['test_phone'] ),
			'afrfq_state' => sanitize_text_field( $_POST['test_state'] ),
		);
		
		try {
			// Initialize WooCommerce session if not already done
			if ( ! WC()->session ) {
				WC()->session = new WC_Session_Handler();
				WC()->session->init();
			}
			
			// Create quote instance
			$quote_obj = new AF_R_F_Q_Quote();
			
			// Test the insertion
			ob_start();
			$quote_obj->insert_new_quote( $test_data );
			$output = ob_get_clean();
			
			if ( strpos( $output, 'hiddenField' ) !== false ) {
				// Extract quote ID from output
				preg_match( '/value="(\d+)"/', $output, $matches );
				$quote_id = isset( $matches[1] ) ? $matches[1] : 'unknown';
				echo "<p class='success'>✓ Quote created successfully! Quote ID: {$quote_id}</p>";
				
				// Check if email was sent (this is basic - real email testing would need more setup)
				echo "<p class='info'>Email notifications should have been triggered</p>";
				
			} else {
				echo "<p class='error'>✗ Quote creation failed</p>";
				if ( ! empty( $output ) ) {
					echo "<p>Output: " . esc_html( $output ) . "</p>";
				}
			}
			
		} catch ( Exception $e ) {
			echo "<p class='error'>✗ Exception: " . esc_html( $e->getMessage() ) . "</p>";
		}
		
		echo '</div>';
	}
	
	// Test form
	echo '<div class="test-section">';
	echo '<h2>Test 4: Submit Test Quote</h2>';
	echo '<form method="post" class="test-form">';
	echo '<p><input type="text" name="test_name" placeholder="Test Name" value="John Doe" required></p>';
	echo '<p><input type="text" name="test_company" placeholder="Test Company" value="Test Company Inc" required></p>';
	echo '<p><input type="email" name="test_email" placeholder="Test Email" value="<EMAIL>" required></p>';
	echo '<p><input type="tel" name="test_phone" placeholder="Test Phone" value="1234567890" required></p>';
	echo '<p><input type="text" name="test_state" placeholder="Test State" value="CA" required></p>';
	echo '<p><button type="submit" name="test_submit">Submit Test Quote</button></p>';
	echo '</form>';
	echo '</div>';
	
	// Test 5: Action hooks
	echo '<div class="test-section">';
	echo '<h2>Test 5: Action Hooks</h2>';
	
	$hooks_to_check = array(
		'addify_rfq_send_quote_email_to_customer',
		'addify_rfq_send_quote_email_to_admin',
		'addify_quote_created'
	);
	
	foreach ( $hooks_to_check as $hook ) {
		$callbacks = $GLOBALS['wp_filter'][ $hook ] ?? null;
		if ( $callbacks ) {
			echo "<p class='success'>✓ Hook '{$hook}' has " . count( $callbacks->callbacks ) . " callback(s)</p>";
		} else {
			echo "<p class='error'>✗ Hook '{$hook}' has no callbacks</p>";
		}
	}
	
	echo '</div>';
	?>
	
	<div class="test-section info">
		<h2>Testing Instructions</h2>
		<ol>
			<li>Ensure all classes are available (Test 1)</li>
			<li>Configure email settings if needed (Test 2)</li>
			<li>Submit the test form above (Test 4)</li>
			<li>Check your email for notifications</li>
			<li>Verify the quote appears in WP Admin > Quotes</li>
		</ol>
		<p><strong>Note:</strong> This is a basic test. For production testing, use the actual quote form on your website.</p>
	</div>
	
</body>
</html>
