/**
 * Modern Quote Form JavaScript
 * Enhanced form validation, user experience, and security
 */

(function($) {
    'use strict';

    // Form validation and enhancement
    class ModernQuoteForm {
        constructor() {
            this.form = $('.afrfq-modern-contact-form');
            this.submitBtn = $('.afrfq-submit-btn');
            this.fields = this.form.find('input, select, textarea');
            
            this.init();
        }

        init() {
            if (this.form.length === 0) return;

            this.bindEvents();
            this.setupValidation();
            this.enhanceUX();
        }

        bindEvents() {
            // Real-time validation
            this.fields.on('blur', (e) => this.validateField($(e.target)));
            this.fields.on('input', (e) => this.clearFieldError($(e.target)));
            
            // Form submission
            this.form.on('submit', (e) => this.handleSubmit(e));
            
            // Phone number formatting
            $('input[name="afrfq_phone_number"]').on('input', (e) => this.formatPhoneNumber($(e.target)));
        }

        setupValidation() {
            // Add required field indicators
            this.form.find('input[required], select[required]').each(function() {
                const label = $(this).siblings('label');
                if (label.length && !label.find('.required-indicator').length) {
                    label.append(' <span class="required-indicator" style="color: #e74c3c;">*</span>');
                }
            });
        }

        enhanceUX() {
            // Add loading states
            this.addLoadingStates();
            
            // Add field focus effects
            this.addFocusEffects();
            
            // Add form progress indicator
            this.addProgressIndicator();
        }

        validateField($field) {
            const fieldName = $field.attr('name');
            const value = $field.val().trim();
            let isValid = true;
            let errorMessage = '';

            // Clear previous errors
            this.clearFieldError($field);

            // Required field validation
            if ($field.prop('required') && !value) {
                isValid = false;
                errorMessage = 'This field is required.';
            }

            // Specific field validations
            switch (fieldName) {
                case 'afrfq_email_address':
                    if (value && !this.isValidEmail(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address.';
                    }
                    break;

                case 'afrfq_phone_number':
                    if (value && !this.isValidPhone(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid phone number.';
                    }
                    break;

                case 'afrfq_customer_name':
                    if (value && !this.isValidName(value)) {
                        isValid = false;
                        errorMessage = 'Name should only contain letters, spaces, and common punctuation.';
                    }
                    break;
            }

            if (!isValid) {
                this.showFieldError($field, errorMessage);
            } else {
                this.showFieldSuccess($field);
            }

            return isValid;
        }

        validateForm() {
            let isValid = true;
            
            this.fields.each((index, field) => {
                if (!this.validateField($(field))) {
                    isValid = false;
                }
            });

            return isValid;
        }

        handleSubmit(e) {
            e.preventDefault();

            // Validate form
            if (!this.validateForm()) {
                this.showFormError('Please correct the errors above and try again.');
                return false;
            }

            // Show loading state
            this.setLoadingState(true);

            // Submit form
            this.submitForm();
        }

        submitForm() {
            const formData = new FormData(this.form[0]);
            
            $.ajax({
                url: this.form.attr('action') || window.location.href,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => {
                    this.handleSubmitSuccess(response);
                },
                error: (xhr, status, error) => {
                    this.handleSubmitError(error);
                },
                complete: () => {
                    this.setLoadingState(false);
                }
            });
        }

        handleSubmitSuccess(response) {
            // Show success message
            const successMessage = (typeof afrfq_modern_form !== 'undefined' && afrfq_modern_form.messages)
                ? afrfq_modern_form.messages.submit_success
                : 'Your quote request has been submitted successfully!';
            this.showSuccessMessage(successMessage);

            // Reset form
            this.form[0].reset();
            this.clearAllErrors();

            // Scroll to top
            $('html, body').animate({ scrollTop: 0 }, 500);
        }

        handleSubmitError(error) {
            const errorMessage = (typeof afrfq_modern_form !== 'undefined' && afrfq_modern_form.messages)
                ? afrfq_modern_form.messages.submit_error
                : 'An error occurred while submitting your request. Please try again.';
            this.showFormError(errorMessage);
            console.error('Form submission error:', error);
        }

        // Validation helpers
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        isValidPhone(phone) {
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            const cleanPhone = phone.replace(/[^\d\+]/g, '');
            return cleanPhone.length >= 10 && phoneRegex.test(cleanPhone);
        }

        isValidName(name) {
            const nameRegex = /^[a-zA-Z\s\-\'\.]+$/;
            return nameRegex.test(name);
        }

        // UI helpers
        showFieldError($field, message) {
            $field.addClass('error').css('border-color', '#e74c3c');
            
            // Remove existing error message
            $field.siblings('.field-error').remove();
            
            // Add error message
            $field.after(`<div class="field-error" style="color: #e74c3c; font-size: 12px; margin-top: 5px;">${message}</div>`);
        }

        showFieldSuccess($field) {
            $field.removeClass('error').css('border-color', '#27ae60');
            $field.siblings('.field-error').remove();
        }

        clearFieldError($field) {
            $field.removeClass('error').css('border-color', '');
            $field.siblings('.field-error').remove();
        }

        clearAllErrors() {
            this.fields.removeClass('error').css('border-color', '');
            this.form.find('.field-error, .form-error').remove();
        }

        showFormError(message) {
            this.form.find('.form-error').remove();
            this.form.prepend(`<div class="form-error" style="background: #fee; border: 1px solid #e74c3c; color: #e74c3c; padding: 15px; border-radius: 6px; margin-bottom: 20px;">${message}</div>`);
        }

        showSuccessMessage(message) {
            this.form.find('.form-error, .form-success').remove();
            this.form.prepend(`<div class="form-success" style="background: #efe; border: 1px solid #27ae60; color: #27ae60; padding: 15px; border-radius: 6px; margin-bottom: 20px;">${message}</div>`);
        }

        setLoadingState(loading) {
            if (loading) {
                const submittingText = (typeof afrfq_modern_form !== 'undefined' && afrfq_modern_form.messages)
                    ? afrfq_modern_form.messages.submitting
                    : 'Submitting...';
                this.submitBtn.prop('disabled', true).text(submittingText);
                this.form.addClass('loading');
            } else {
                this.submitBtn.prop('disabled', false).text(this.submitBtn.data('original-text') || 'Submit');
                this.form.removeClass('loading');
            }
        }

        formatPhoneNumber($field) {
            let value = $field.val().replace(/\D/g, '');
            
            if (value.length >= 6) {
                value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
            } else if (value.length >= 3) {
                value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
            }
            
            $field.val(value);
        }

        addLoadingStates() {
            // Store original button text
            this.submitBtn.data('original-text', this.submitBtn.text());
        }

        addFocusEffects() {
            this.fields.on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
            });
        }

        addProgressIndicator() {
            // Simple progress indicator based on filled fields
            const updateProgress = () => {
                const totalFields = this.fields.filter('[required]').length;
                const filledFields = this.fields.filter('[required]').filter(function() {
                    return $(this).val().trim() !== '';
                }).length;
                
                const progress = Math.round((filledFields / totalFields) * 100);
                
                // Update progress indicator if it exists
                const progressBar = $('.form-progress-bar');
                if (progressBar.length) {
                    progressBar.css('width', progress + '%');
                }
            };

            this.fields.on('input change', updateProgress);
            updateProgress(); // Initial update
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        new ModernQuoteForm();
    });

})(jQuery);
