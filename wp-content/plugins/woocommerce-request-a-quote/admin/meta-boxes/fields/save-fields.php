<?php
/**
 * Field Attributes.
 *
 * Deal field attributes in metabox .
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

global $post;

if ( ! is_object( $post ) ) {
	return;
}

$field_id = $post->ID;

if ( $validation ) {

	if ( isset( $form_data['afrfq_field_name'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_name', $form_data['afrfq_field_name'] );
	}

	if ( isset( $form_data['afrfq_field_type'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_type', $form_data['afrfq_field_type'] );
	}

	if ( isset( $form_data['afrfq_field_label'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_label', $form_data['afrfq_field_label'] );
	}

	if ( isset( $form_data['afrfq_field_value'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_value', $form_data['afrfq_field_value'] );
	}


	if ( isset( $form_data['afrfq_field_placeholder'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_placeholder', $form_data['afrfq_field_placeholder'] );
	}

	if ( isset( $form_data['afrfq_field_width'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_width', $form_data['afrfq_field_width'] );
	}

	if ( isset( $form_data['afrfq_file_types'] ) ) {
		update_post_meta( $field_id, 'afrfq_file_types', strtolower( trim( preg_replace( '/[\t\n\r\s]+/', ' ', $form_data['afrfq_file_types'] ) ) ) );
	}

	if ( isset( $form_data['afrfq_file_size'] ) ) {
		update_post_meta( $field_id, 'afrfq_file_size', $form_data['afrfq_file_size'] );
	}

	if ( isset( $form_data['afrfq_field_options'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_options', array_unique( array_filter( $form_data['afrfq_field_options'] ) ) );
	}

	if ( isset( $form_data['afrfq_field_enable'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_enable', $form_data['afrfq_field_enable'] );
	}
	if ( isset( $form_data['afrfq_field_terms'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_terms', $form_data['afrfq_field_terms'] );
	}

	if ( isset( $form_data['afrfq_field_required'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_required', $form_data['afrfq_field_required'] );
	}

	// Enhanced field configuration options
	if ( isset( $form_data['afrfq_field_description'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_description', sanitize_textarea_field( $form_data['afrfq_field_description'] ) );
	}

	if ( isset( $form_data['afrfq_field_css_class'] ) ) {
		update_post_meta( $field_id, 'afrfq_field_css_class', sanitize_text_field( $form_data['afrfq_field_css_class'] ) );
	}

	// Validation configuration
	if ( isset( $form_data['afrfq_field_min_length'] ) ) {
		$min_length = absint( $form_data['afrfq_field_min_length'] );
		update_post_meta( $field_id, 'afrfq_field_min_length', $min_length );
	}

	if ( isset( $form_data['afrfq_field_max_length'] ) ) {
		$max_length = absint( $form_data['afrfq_field_max_length'] );
		update_post_meta( $field_id, 'afrfq_field_max_length', $max_length );
	}

	if ( isset( $form_data['afrfq_field_min_value'] ) ) {
		$min_value = is_numeric( $form_data['afrfq_field_min_value'] ) ? floatval( $form_data['afrfq_field_min_value'] ) : '';
		update_post_meta( $field_id, 'afrfq_field_min_value', $min_value );
	}

	if ( isset( $form_data['afrfq_field_max_value'] ) ) {
		$max_value = is_numeric( $form_data['afrfq_field_max_value'] ) ? floatval( $form_data['afrfq_field_max_value'] ) : '';
		update_post_meta( $field_id, 'afrfq_field_max_value', $max_value );
	}

	if ( isset( $form_data['afrfq_field_pattern'] ) ) {
		$pattern = sanitize_text_field( $form_data['afrfq_field_pattern'] );
		// Validate the regex pattern
		if ( ! empty( $pattern ) ) {
			$test_result = @preg_match( '/' . $pattern . '/', 'test' );
			if ( $test_result === false ) {
				// Invalid regex pattern, don't save it
				$pattern = '';
			}
		}
		update_post_meta( $field_id, 'afrfq_field_pattern', $pattern );
	} else {
		update_post_meta( $field_id, 'afrfq_field_required', 'no' );
	}

	// Conditional Logic Configuration
	if ( isset( $form_data['afrfq_enable_conditional_logic'] ) ) {
		update_post_meta( $field_id, 'afrfq_enable_conditional_logic', sanitize_text_field( $form_data['afrfq_enable_conditional_logic'] ) );

		if ( 'yes' === $form_data['afrfq_enable_conditional_logic'] ) {
			// Save logic type
			if ( isset( $form_data['afrfq_conditional_logic_type'] ) ) {
				update_post_meta( $field_id, 'afrfq_conditional_logic_type', sanitize_text_field( $form_data['afrfq_conditional_logic_type'] ) );
			}

			// Save conditional rules
			if ( isset( $form_data['afrfq_conditional_rules'] ) && is_array( $form_data['afrfq_conditional_rules'] ) ) {
				$sanitized_rules = array();
				foreach ( $form_data['afrfq_conditional_rules'] as $rule ) {
					if ( is_array( $rule ) ) {
						$sanitized_rules[] = array(
							'trigger'  => isset( $rule['trigger'] ) ? sanitize_text_field( $rule['trigger'] ) : '',
							'field'    => isset( $rule['field'] ) ? sanitize_text_field( $rule['field'] ) : '',
							'operator' => isset( $rule['operator'] ) ? sanitize_text_field( $rule['operator'] ) : '',
							'value'    => isset( $rule['value'] ) ? sanitize_text_field( $rule['value'] ) : '',
						);
					}
				}
				update_post_meta( $field_id, 'afrfq_conditional_rules', $sanitized_rules );

				// Save combined conditional logic for easier access
				$conditional_logic = array(
					'logic_type' => isset( $form_data['afrfq_conditional_logic_type'] ) ? sanitize_text_field( $form_data['afrfq_conditional_logic_type'] ) : 'and',
					'rules'      => $sanitized_rules,
				);
				update_post_meta( $field_id, 'afrfq_conditional_logic', $conditional_logic );
			}
		} else {
			// Clear conditional logic if disabled
			delete_post_meta( $field_id, 'afrfq_conditional_logic_type' );
			delete_post_meta( $field_id, 'afrfq_conditional_rules' );
			delete_post_meta( $field_id, 'afrfq_conditional_logic' );
		}
	} else {
		// Clear all conditional logic if not set
		delete_post_meta( $field_id, 'afrfq_enable_conditional_logic' );
		delete_post_meta( $field_id, 'afrfq_conditional_logic_type' );
		delete_post_meta( $field_id, 'afrfq_conditional_rules' );
		delete_post_meta( $field_id, 'afrfq_conditional_logic' );
	}
}
