<?php
/**
 * Field Attributes.
 *
 * Deal field attributes in metabox .
 *
 * @package addify-request-a-quote
 * @version 1.6.0
 */

defined( 'ABSPATH' ) || exit;

global $post;
$field_id = $post->ID;

// Get field types from the enhanced field class
$quote_fields_obj = new AF_R_F_Q_Quote_Fields();
$field_types_config = $quote_fields_obj->get_field_types();

$field_types = array();
foreach ( $field_types_config as $type => $config ) {
	$field_types[ $type ] = $config['label'];
}

$default_values = array(
	''                    => 'Select a default value',
	'user_login'          => __( 'Username', 'addify_rfq' ),
	'first_name'          => __( 'First Name', 'addify_rfq' ),
	'last_name'           => __( 'Last Name', 'addify_rfq' ),
	'nickname'            => __( 'Nickname', 'addify_rfq' ),
	'display_name'        => __( 'Display Name', 'addify_rfq' ),
	'email'               => __( 'Email', 'addify_rfq' ),
	'billing_first_name'  => __( 'Billing First Name', 'addify_rfq' ),
	'billing_last_name'   => __( 'Billing Last Name', 'addify_rfq' ),
	'billing_company'     => __( 'Billing Company', 'addify_rfq' ),
	'billing_address_1'   => __( 'Billing Address 1', 'addify_rfq' ),
	'billing_address_2'   => __( 'Billing Address 2', 'addify_rfq' ),
	'billing_city'        => __( 'Billing City', 'addify_rfq' ),
	'billing_postcode'    => __( 'Billing Postcode', 'addify_rfq' ),
	'billing_phone'       => __( 'Billing Phone', 'addify_rfq' ),
	'billing_email'       => __( 'Billing Email', 'addify_rfq' ),
	'shipping_first_name' => __( 'Shipping First Name', 'addify_rfq' ),
	'shipping_last_name'  => __( 'Shipping Last Name', 'addify_rfq' ),
	'shipping_company'    => __( 'Shipping Company', 'addify_rfq' ),
	'shipping_address_1'  => __( 'Shipping Address 1', 'addify_rfq' ),
	'shipping_address_2'  => __( 'Shipping Address 2', 'addify_rfq' ),
	'shipping_city'       => __( 'Shipping City', 'addify_rfq' ),
	'shipping_postcode'   => __( 'Shipping Postcode', 'addify_rfq' ),
	'shipping_phone'      => __( 'Shipping Phone', 'addify_rfq' ),
	'shipping_email'      => __( 'Shipping Email', 'addify_rfq' ),
);

$afrfq_field_name        = get_post_meta( $field_id, 'afrfq_field_name', true );
$afrfq_field_type        = get_post_meta( $field_id, 'afrfq_field_type', true );
$afrfq_field_label       = get_post_meta( $field_id, 'afrfq_field_label', true );
$afrfq_field_value       = get_post_meta( $field_id, 'afrfq_field_value', true );
$afrfq_field_title       = get_post_meta( $field_id, 'afrfq_field_title', true );
$afrfq_field_placeholder = get_post_meta( $field_id, 'afrfq_field_placeholder', true );
$afrfq_field_options     = (array) get_post_meta( $field_id, 'afrfq_field_options', true );
$afrfq_file_types        = get_post_meta( $field_id, 'afrfq_file_types', true );
$afrfq_file_size         = get_post_meta( $field_id, 'afrfq_file_size', true );
$afrfq_field_enable      = get_post_meta( $field_id, 'afrfq_field_enable', true );
$afrfq_field_terms       = get_post_meta( $field_id, 'afrfq_field_terms', true );
$afrfq_field_width       = get_post_meta( $field_id, 'afrfq_field_width', true );

// Enhanced field configuration options
$afrfq_field_required    = get_post_meta( $field_id, 'afrfq_field_required', true );
$afrfq_field_min_length  = get_post_meta( $field_id, 'afrfq_field_min_length', true );
$afrfq_field_max_length  = get_post_meta( $field_id, 'afrfq_field_max_length', true );
$afrfq_field_min_value   = get_post_meta( $field_id, 'afrfq_field_min_value', true );
$afrfq_field_max_value   = get_post_meta( $field_id, 'afrfq_field_max_value', true );
$afrfq_field_pattern     = get_post_meta( $field_id, 'afrfq_field_pattern', true );
$afrfq_field_description = get_post_meta( $field_id, 'afrfq_field_description', true );
$afrfq_field_css_class   = get_post_meta( $field_id, 'afrfq_field_css_class', true );

// Conditional logic variables
$afrfq_enable_conditional_logic = get_post_meta( $field_id, 'afrfq_enable_conditional_logic', true );
$afrfq_conditional_logic_type   = get_post_meta( $field_id, 'afrfq_conditional_logic_type', true ) ?: 'and';
$afrfq_conditional_rules        = get_post_meta( $field_id, 'afrfq_conditional_rules', true ) ?: array();

if ( empty( $afrfq_field_name ) ) {
	$afrfq_field_name = 'afrfq_field_' . $field_id;
	$readonly         = '';
} else {
	$readonly = 'readonly';
}

?>
<div class="afrfq-metabox-fields">
	<?php wp_nonce_field( 'afrfq_fields_nonce_action', 'afrfq_field_nonce' ); ?>
	<table class="addify-table-optoin">
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Name', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_name" value="<?php echo esc_html( $afrfq_field_name ); ?>" required <?php echo esc_html( $readonly ); ?> >
				<p class="description"><?php echo esc_html__( 'Add a unique name for each quote field. It is also used as meta_key to store values in database. Once publish, you will not be able to modify it.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Type', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select id="afrfq_field_type" name="afrfq_field_type" required>
					<?php foreach ( $field_types as $value => $label ) : ?>
						<option value="<?php echo esc_html( $value ); ?>" <?php echo selected( $value, $afrfq_field_type ); ?> > <?php echo esc_html( $label ); ?> </option>
					<?php endforeach; ?>
				</select>
				<p class="description"></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field label', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_label" value="<?php echo esc_html( $afrfq_field_label ); ?>">
				<p class="description"></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Terms & Conditions', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<textarea rows="5" id="afrfq_field_terms" name="afrfq_field_terms"><?php echo esc_html( $afrfq_field_terms ); ?></textarea>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Default Value', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select name="afrfq_field_value" >
					<?php foreach ( $default_values as $value => $label ) : ?>
						<option value="<?php echo esc_html( $value ); ?>" <?php echo selected( $value, $afrfq_field_value ); ?> > <?php echo esc_html( $label ); ?> </option>
					<?php endforeach; ?>
				</select>
				<p class="description"></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Placeholder', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_placeholder" value="<?php echo esc_html( $afrfq_field_placeholder ); ?>" >
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Width', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select name="afrfq_field_width">
					<option value="full_width" <?php selected( $afrfq_field_width, 'full_width' ); ?>><?php echo esc_html__( 'Full Width', 'addify_rfq' ); ?></option>
					<option value="half_width" <?php selected( $afrfq_field_width, 'half_width' ); ?>><?php echo esc_html__( 'Half Width', 'addify_rfq' ); ?></option>
					<option value="third_width" <?php selected( $afrfq_field_width, 'third_width' ); ?>><?php echo esc_html__( 'One Third Width', 'addify_rfq' ); ?></option>
					<option value="quarter_width" <?php selected( $afrfq_field_width, 'quarter_width' ); ?>><?php echo esc_html__( 'Quarter Width', 'addify_rfq' ); ?></option>
				</select>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Required', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<select name="afrfq_field_required">
					<option value="no" <?php selected( $afrfq_field_required, 'no' ); ?>><?php echo esc_html__( 'No', 'addify_rfq' ); ?></option>
					<option value="yes" <?php selected( $afrfq_field_required, 'yes' ); ?>><?php echo esc_html__( 'Yes', 'addify_rfq' ); ?></option>
				</select>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Description', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<textarea name="afrfq_field_description" rows="3" placeholder="<?php esc_attr_e( 'Optional help text for this field', 'addify_rfq' ); ?>"><?php echo esc_textarea( $afrfq_field_description ); ?></textarea>
				<p class="description"><?php echo esc_html__( 'Help text that will be displayed below the field.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'CSS Class', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_css_class" value="<?php echo esc_attr( $afrfq_field_css_class ); ?>" placeholder="<?php esc_attr_e( 'custom-class another-class', 'addify_rfq' ); ?>">
				<p class="description"><?php echo esc_html__( 'Additional CSS classes for styling (space-separated).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Allowed File Types', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_file_types" value="<?php echo esc_html( $afrfq_file_types ); ?>" >
				<p class="description"><?php echo esc_html__( 'Add Comma separated file extensions. Ex. pdf,txt,jpg.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Allowed File Size', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_file_size" value="<?php echo esc_html( $afrfq_file_size ); ?>" >
				<p class="description"><?php echo esc_html__( 'File size in bytes 1KB = 1000 bytes and 1MB = 1000000 bytes', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<!-- Validation Configuration Section -->
		<tr class="addify-option-field validation-field">
			<th colspan="2">
				<div class="option-head">
					<h3 style="border-bottom: 2px solid #f1c40f; padding-bottom: 10px; margin-bottom: 20px;">
						<?php echo esc_html__( 'Validation Settings', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
		</tr>
		<tr class="addify-option-field validation-field text-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Minimum Length', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_min_length" value="<?php echo esc_attr( $afrfq_field_min_length ); ?>" min="0" placeholder="0">
				<p class="description"><?php echo esc_html__( 'Minimum number of characters required.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field text-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Maximum Length', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_max_length" value="<?php echo esc_attr( $afrfq_field_max_length ); ?>" min="0" placeholder="255">
				<p class="description"><?php echo esc_html__( 'Maximum number of characters allowed.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field number-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Minimum Value', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_min_value" value="<?php echo esc_attr( $afrfq_field_min_value ); ?>" step="any" placeholder="0">
				<p class="description"><?php echo esc_html__( 'Minimum numeric value allowed.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field number-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Maximum Value', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="number" name="afrfq_field_max_value" value="<?php echo esc_attr( $afrfq_field_max_value ); ?>" step="any" placeholder="100">
				<p class="description"><?php echo esc_html__( 'Maximum numeric value allowed.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field validation-field pattern-validation">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Validation Pattern', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<input type="text" name="afrfq_field_pattern" value="<?php echo esc_attr( $afrfq_field_pattern ); ?>" placeholder="^[A-Za-z0-9]+$">
				<p class="description"><?php echo esc_html__( 'Regular expression pattern for custom validation (advanced users only).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field options-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Field Options', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<?php
				if ( 0 === count( $afrfq_field_options ) ) {
					$afrfq_field_options = array( '' );
				}
				foreach ( $afrfq_field_options as $value ) :
					?>
					<div class="option_row">
						<input type="text" name="afrfq_field_options[]" value="<?php echo esc_html( $value ); ?>" >
						<span type="button" title="Add Option" class="dashicons dashicons-plus-alt2 add_option_button" value="add_more"></span>
						<span type="button" title="Remove Option" class="dashicons dashicons-no-alt remove_option_button" value="add_more"></span>
					</div>
				<?php endforeach; ?>
				<p class="description"><?php echo esc_html__( 'Add Option(s) for fields types ( Select, Multi-Select, Radio, and Checkbox ).', 'addify_rfq' ); ?></p>
			</td>
		</tr>
		<tr class="addify-option-field conditional-logic-field">
			<th>
				<div class="option-head">
					<h3>
						<?php echo esc_html__( 'Conditional Logic', 'addify_rfq' ); ?>
					</h3>
				</div>
			</th>
			<td>
				<div class="conditional-logic-wrapper">
					<label>
						<input type="checkbox" name="afrfq_enable_conditional_logic" value="yes" <?php checked( $afrfq_enable_conditional_logic, 'yes' ); ?>>
						<?php echo esc_html__( 'Enable conditional logic for this field', 'addify_rfq' ); ?>
					</label>

					<div class="conditional-logic-rules" style="<?php echo 'yes' !== $afrfq_enable_conditional_logic ? 'display: none;' : ''; ?>">
						<div class="logic-type-wrapper">
							<label><?php echo esc_html__( 'Show this field if:', 'addify_rfq' ); ?></label>
							<select name="afrfq_conditional_logic_type">
								<option value="and" <?php selected( $afrfq_conditional_logic_type, 'and' ); ?>><?php echo esc_html__( 'All conditions are met', 'addify_rfq' ); ?></option>
								<option value="or" <?php selected( $afrfq_conditional_logic_type, 'or' ); ?>><?php echo esc_html__( 'Any condition is met', 'addify_rfq' ); ?></option>
							</select>
						</div>

						<div class="conditional-rules-list">
							<?php
							if ( empty( $afrfq_conditional_rules ) ) {
								$afrfq_conditional_rules = array(
									array(
										'trigger' => 'field_value',
										'field' => '',
										'operator' => 'equals',
										'value' => '',
									),
								);
							}
							foreach ( $afrfq_conditional_rules as $index => $rule ) :
								?>
								<div class="conditional-rule" data-index="<?php echo esc_attr( $index ); ?>">
									<select name="afrfq_conditional_rules[<?php echo esc_attr( $index ); ?>][trigger]" class="rule-trigger">
										<option value="field_value" <?php selected( $rule['trigger'], 'field_value' ); ?>><?php echo esc_html__( 'Field Value', 'addify_rfq' ); ?></option>
										<option value="user_role" <?php selected( $rule['trigger'], 'user_role' ); ?>><?php echo esc_html__( 'User Role', 'addify_rfq' ); ?></option>
										<option value="product_type" <?php selected( $rule['trigger'], 'product_type' ); ?>><?php echo esc_html__( 'Product Type', 'addify_rfq' ); ?></option>
										<option value="product_category" <?php selected( $rule['trigger'], 'product_category' ); ?>><?php echo esc_html__( 'Product Category', 'addify_rfq' ); ?></option>
										<option value="cart_total" <?php selected( $rule['trigger'], 'cart_total' ); ?>><?php echo esc_html__( 'Cart Total', 'addify_rfq' ); ?></option>
									</select>

									<select name="afrfq_conditional_rules[<?php echo esc_attr( $index ); ?>][field]" class="rule-field" style="<?php echo 'field_value' !== $rule['trigger'] ? 'display: none;' : ''; ?>">
										<option value=""><?php echo esc_html__( 'Select Field', 'addify_rfq' ); ?></option>
										<?php
										$available_fields = get_posts( array(
											'post_type' => 'addify_rfq_fields',
											'post_status' => 'publish',
											'posts_per_page' => -1,
											'meta_query' => array(
												array(
													'key' => 'afrfq_field_enable',
													'value' => 'enable',
													'compare' => '=',
												),
											),
										) );
										foreach ( $available_fields as $available_field ) :
											$field_name = get_post_meta( $available_field->ID, 'afrfq_field_name', true );
											if ( $field_name ) :
												?>
												<option value="<?php echo esc_attr( $field_name ); ?>" <?php selected( $rule['field'], $field_name ); ?>><?php echo esc_html( $available_field->post_title ); ?></option>
												<?php
											endif;
										endforeach;
										?>
									</select>

									<select name="afrfq_conditional_rules[<?php echo esc_attr( $index ); ?>][operator]" class="rule-operator">
										<option value="equals" <?php selected( $rule['operator'], 'equals' ); ?>><?php echo esc_html__( 'Equals', 'addify_rfq' ); ?></option>
										<option value="not_equals" <?php selected( $rule['operator'], 'not_equals' ); ?>><?php echo esc_html__( 'Not Equals', 'addify_rfq' ); ?></option>
										<option value="contains" <?php selected( $rule['operator'], 'contains' ); ?>><?php echo esc_html__( 'Contains', 'addify_rfq' ); ?></option>
										<option value="not_contains" <?php selected( $rule['operator'], 'not_contains' ); ?>><?php echo esc_html__( 'Does Not Contain', 'addify_rfq' ); ?></option>
										<option value="greater_than" <?php selected( $rule['operator'], 'greater_than' ); ?>><?php echo esc_html__( 'Greater Than', 'addify_rfq' ); ?></option>
										<option value="less_than" <?php selected( $rule['operator'], 'less_than' ); ?>><?php echo esc_html__( 'Less Than', 'addify_rfq' ); ?></option>
										<option value="is_empty" <?php selected( $rule['operator'], 'is_empty' ); ?>><?php echo esc_html__( 'Is Empty', 'addify_rfq' ); ?></option>
										<option value="is_not_empty" <?php selected( $rule['operator'], 'is_not_empty' ); ?>><?php echo esc_html__( 'Is Not Empty', 'addify_rfq' ); ?></option>
									</select>

									<input type="text" name="afrfq_conditional_rules[<?php echo esc_attr( $index ); ?>][value]" value="<?php echo esc_attr( $rule['value'] ); ?>" placeholder="<?php echo esc_attr__( 'Value', 'addify_rfq' ); ?>" class="rule-value">

									<button type="button" class="button remove-rule" <?php echo count( $afrfq_conditional_rules ) <= 1 ? 'style="display: none;"' : ''; ?>><?php echo esc_html__( 'Remove', 'addify_rfq' ); ?></button>
								</div>
								<?php
							endforeach;
							?>
						</div>

						<button type="button" class="button add-rule"><?php echo esc_html__( 'Add Rule', 'addify_rfq' ); ?></button>
					</div>
				</div>
				<p class="description"><?php echo esc_html__( 'Set conditions for when this field should be visible to users.', 'addify_rfq' ); ?></p>
			</td>
		</tr>
	</table>
</div>

<!-- Enhanced Field Management JavaScript -->
<script type="text/javascript">
jQuery(document).ready(function($) {
	// Field type configuration
	var fieldTypeConfig = {
		'text': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: ['text-validation', 'pattern-validation']
		},
		'textarea': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: ['text-validation']
		},
		'email': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: []
		},
		'phone': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: []
		},
		'number': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: ['number-validation']
		},
		'url': {
			supports: ['placeholder', 'default_value', 'validation', 'width'],
			validations: []
		},
		'date': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'time': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'datetime': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'select': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'multiselect': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'radio': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'checkbox': {
			supports: ['options', 'default_value', 'validation', 'width'],
			validations: [],
			showOptions: true
		},
		'file': {
			supports: ['validation', 'width', 'file_types', 'max_size'],
			validations: [],
			showFileOptions: true
		},
		'range': {
			supports: ['default_value', 'validation', 'width'],
			validations: ['number-validation']
		},
		'color': {
			supports: ['default_value', 'validation', 'width'],
			validations: []
		},
		'terms_cond': {
			supports: ['validation', 'width', 'terms_text'],
			validations: [],
			showTerms: true
		}
	};

	// Function to toggle field visibility based on field type
	function toggleFieldVisibility() {
		var selectedType = $('#afrfq_field_type').val();
		var config = fieldTypeConfig[selectedType] || {};

		// Hide all validation fields first
		$('.validation-field').hide();
		$('.options-field').hide();

		// Show validation section header if any validations are supported
		if (config.validations && config.validations.length > 0) {
			$('.validation-field').first().show(); // Show header

			// Show specific validation fields
			config.validations.forEach(function(validation) {
				$('.' + validation).show();
			});
		}

		// Show options field for select, radio, checkbox, multiselect
		if (config.showOptions) {
			$('.options-field').show();
		}

		// Show file options for file fields
		if (config.showFileOptions) {
			$('tr:has([name="afrfq_file_types"]), tr:has([name="afrfq_file_size"])').show();
		} else {
			$('tr:has([name="afrfq_file_types"]), tr:has([name="afrfq_file_size"])').hide();
		}

		// Show terms field for terms_cond
		if (config.showTerms) {
			$('tr:has([name="afrfq_field_terms"])').show();
		} else {
			$('tr:has([name="afrfq_field_terms"])').hide();
		}

		// Show/hide placeholder field
		if (config.supports && config.supports.includes('placeholder')) {
			$('tr:has([name="afrfq_field_placeholder"])').show();
		} else {
			$('tr:has([name="afrfq_field_placeholder"])').hide();
		}
	}

	// Initialize on page load
	toggleFieldVisibility();

	// Update when field type changes
	$('#afrfq_field_type').on('change', toggleFieldVisibility);

	// Enhanced option management
	$(document).on('click', '.add_option_button', function() {
		var optionRow = $(this).closest('.option_row');
		var newRow = optionRow.clone();
		newRow.find('input').val('');
		optionRow.after(newRow);
	});

	$(document).on('click', '.remove_option_button', function() {
		if ($('.option_row').length > 1) {
			$(this).closest('.option_row').remove();
		}
	});

	// Add field type descriptions
	$('#afrfq_field_type').on('change', function() {
		var selectedType = $(this).val();
		var description = '';

		<?php
		foreach ( $field_types_config as $type => $config ) {
			echo "if (selectedType === '" . esc_js( $type ) . "') { description = '" . esc_js( $config['description'] ) . "'; }\n";
		}
		?>

		$(this).siblings('.description').text(description);
	}).trigger('change');

	// Conditional Logic Management
	$('input[name="afrfq_enable_conditional_logic"]').on('change', function() {
		if ($(this).is(':checked')) {
			$('.conditional-logic-rules').show();
		} else {
			$('.conditional-logic-rules').hide();
		}
	});

	// Add new conditional rule
	$(document).on('click', '.add-rule', function() {
		var $rulesList = $('.conditional-rules-list');
		var ruleIndex = $rulesList.find('.conditional-rule').length;
		var newRule = `
			<div class="conditional-rule" data-index="${ruleIndex}">
				<select name="afrfq_conditional_rules[${ruleIndex}][trigger]" class="rule-trigger">
					<option value="field_value">Field Value</option>
					<option value="user_role">User Role</option>
					<option value="product_type">Product Type</option>
					<option value="product_category">Product Category</option>
					<option value="cart_total">Cart Total</option>
				</select>

				<select name="afrfq_conditional_rules[${ruleIndex}][field]" class="rule-field" style="display: none;">
					<option value="">Select Field</option>
					<?php
					foreach ( $available_fields as $available_field ) :
						$field_name = get_post_meta( $available_field->ID, 'afrfq_field_name', true );
						if ( $field_name ) :
							echo '<option value="' . esc_attr( $field_name ) . '">' . esc_html( $available_field->post_title ) . '</option>';
						endif;
					endforeach;
					?>
				</select>

				<select name="afrfq_conditional_rules[${ruleIndex}][operator]" class="rule-operator">
					<option value="equals">Equals</option>
					<option value="not_equals">Not Equals</option>
					<option value="contains">Contains</option>
					<option value="not_contains">Does Not Contain</option>
					<option value="greater_than">Greater Than</option>
					<option value="less_than">Less Than</option>
					<option value="is_empty">Is Empty</option>
					<option value="is_not_empty">Is Not Empty</option>
				</select>

				<input type="text" name="afrfq_conditional_rules[${ruleIndex}][value]" value="" placeholder="Value" class="rule-value">

				<button type="button" class="button remove-rule">Remove</button>
			</div>
		`;
		$rulesList.append(newRule);
		updateRemoveButtons();
	});

	// Remove conditional rule
	$(document).on('click', '.remove-rule', function() {
		$(this).closest('.conditional-rule').remove();
		updateRemoveButtons();
		reindexRules();
	});

	// Handle trigger change
	$(document).on('change', '.rule-trigger', function() {
		var $rule = $(this).closest('.conditional-rule');
		var $fieldSelect = $rule.find('.rule-field');

		if ($(this).val() === 'field_value') {
			$fieldSelect.show();
		} else {
			$fieldSelect.hide();
		}
	});

	function updateRemoveButtons() {
		var $rules = $('.conditional-rule');
		if ($rules.length <= 1) {
			$rules.find('.remove-rule').hide();
		} else {
			$rules.find('.remove-rule').show();
		}
	}

	function reindexRules() {
		$('.conditional-rule').each(function(index) {
			$(this).attr('data-index', index);
			$(this).find('select, input').each(function() {
				var name = $(this).attr('name');
				if (name) {
					var newName = name.replace(/\[\d+\]/, '[' + index + ']');
					$(this).attr('name', newName);
				}
			});
		});
	}

	// Initialize trigger visibility
	$('.rule-trigger').trigger('change');
});
</script>

<!-- Enhanced Field Management Styles -->
<style type="text/css">
.afrfq-metabox-fields .addify-table-optoin {
	width: 100%;
	border-collapse: collapse;
}

.afrfq-metabox-fields .addify-option-field th {
	width: 25%;
	padding: 15px;
	background: #f9f9f9;
	border-bottom: 1px solid #e5e5e5;
	vertical-align: top;
}

.afrfq-metabox-fields .addify-option-field td {
	padding: 15px;
	border-bottom: 1px solid #e5e5e5;
}

.afrfq-metabox-fields .option-head h3 {
	margin: 0;
	font-size: 14px;
	font-weight: 600;
	color: #2c3e50;
}

.afrfq-metabox-fields input[type="text"],
.afrfq-metabox-fields input[type="number"],
.afrfq-metabox-fields select,
.afrfq-metabox-fields textarea {
	width: 100%;
	max-width: 400px;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
}

.afrfq-metabox-fields .description {
	margin-top: 5px;
	font-style: italic;
	color: #666;
	font-size: 12px;
}

.option_row {
	display: flex;
	align-items: center;
	margin-bottom: 10px;
	gap: 10px;
}

.option_row input {
	flex: 1;
	max-width: 300px;
}

.add_option_button,
.remove_option_button {
	cursor: pointer;
	color: #0073aa;
	font-size: 16px;
	padding: 5px;
	border-radius: 3px;
	transition: all 0.3s ease;
}

.add_option_button:hover {
	color: #005a87;
	background: #f0f8ff;
}

.remove_option_button:hover {
	color: #d63638;
	background: #fff0f0;
}

.validation-field {
	background: #f8f9fa;
}

.validation-field th,
.validation-field td {
	background: #f8f9fa;
}

/* Conditional Logic Styles */
.conditional-logic-wrapper {
	max-width: 600px;
}

.conditional-logic-rules {
	margin-top: 15px;
	padding: 15px;
	background: #f9f9f9;
	border-radius: 5px;
	border: 1px solid #e5e5e5;
}

.logic-type-wrapper {
	margin-bottom: 15px;
}

.logic-type-wrapper label {
	font-weight: 600;
	margin-right: 10px;
}

.conditional-rule {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-bottom: 10px;
	padding: 10px;
	background: #ffffff;
	border-radius: 4px;
	border: 1px solid #ddd;
}

.conditional-rule select,
.conditional-rule input[type="text"] {
	flex: 1;
	min-width: 120px;
	max-width: none;
}

.conditional-rule .remove-rule {
	flex-shrink: 0;
	background: #dc3545;
	color: white;
	border: none;
	padding: 5px 10px;
	border-radius: 3px;
	cursor: pointer;
	font-size: 12px;
}

.conditional-rule .remove-rule:hover {
	background: #c82333;
}

.add-rule {
	background: #007cba;
	color: white;
	border: none;
	padding: 8px 15px;
	border-radius: 4px;
	cursor: pointer;
	margin-top: 10px;
}

.add-rule:hover {
	background: #005a87;
}

.conditional-logic-field {
	background: #f0f8ff;
}

.conditional-logic-field th,
.conditional-logic-field td {
	background: #f0f8ff;
}
</style>
