<?php
/**
 * Pricing & Tax Settings Tab
 *
 * @package addify-request-for-quote
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$afrfq_pricing_tax_settings = array(

	array(
		'title' => __( 'Pricing & Tax Settings', 'addify_rfq' ),
		'type'  => 'title',
		'desc'  => __( 'Configure pricing display options, tax calculations, and price adjustments for quotes.', 'addify_rfq' ),
		'id'    => 'afrfq_pricing_tax_settings',
	),

	// Price Display Options
	array(
		'title'   => __( 'Show Prices in Quotes', 'addify_rfq' ),
		'desc'    => __( 'Display product prices in quote forms and quote pages.', 'addify_rfq' ),
		'id'      => 'afrfq_show_prices',
		'default' => 'yes',
		'type'    => 'select',
		'options' => array(
			'yes' => __( 'Show prices', 'addify_rfq' ),
			'no'  => __( 'Hide prices (Price on request)', 'addify_rfq' ),
		),
	),

	array(
		'title'   => __( 'Tax Display in Quotes', 'addify_rfq' ),
		'desc'    => __( 'Choose how to display prices with tax in quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_show_tax_inclusive',
		'default' => 'auto',
		'type'    => 'select',
		'options' => array(
			'auto' => __( 'Use WooCommerce settings', 'addify_rfq' ),
			'incl' => __( 'Including tax', 'addify_rfq' ),
			'excl' => __( 'Excluding tax', 'addify_rfq' ),
		),
	),

	array(
		'title'   => __( 'Show Price Suffix', 'addify_rfq' ),
		'desc'    => __( 'Display tax suffix (inc. tax / ex. tax) with prices.', 'addify_rfq' ),
		'id'      => 'afrfq_show_price_suffix',
		'default' => 'yes',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Price Format', 'addify_rfq' ),
		'desc'    => __( 'Choose how prices are formatted in quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_price_format',
		'default' => 'default',
		'type'    => 'select',
		'options' => array(
			'default'  => __( 'Default WooCommerce format', 'addify_rfq' ),
			'compact'  => __( 'Compact format (no decimals for whole numbers)', 'addify_rfq' ),
			'detailed' => __( 'Detailed format (always show decimals)', 'addify_rfq' ),
		),
	),

	// Price Adjustments
	array(
		'title' => __( 'Price Adjustments', 'addify_rfq' ),
		'type'  => 'title',
		'desc'  => __( 'Configure automatic price adjustments for quotes.', 'addify_rfq' ),
		'id'    => 'afrfq_price_adjustments',
	),

	array(
		'title'   => __( 'Enable Price Adjustments', 'addify_rfq' ),
		'desc'    => __( 'Allow automatic price increases or decreases for quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_enable_price_adjustments',
		'default' => 'no',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Adjustment Type', 'addify_rfq' ),
		'desc'    => __( 'Choose how price adjustments are calculated.', 'addify_rfq' ),
		'id'      => 'afrfq_adjustment_type',
		'default' => 'percentage',
		'type'    => 'select',
		'options' => array(
			'percentage' => __( 'Percentage', 'addify_rfq' ),
			'fixed'      => __( 'Fixed amount', 'addify_rfq' ),
		),
	),

	array(
		'title'   => __( 'Adjustment Value', 'addify_rfq' ),
		'desc'    => __( 'Enter the adjustment value (percentage or fixed amount).', 'addify_rfq' ),
		'id'      => 'afrfq_adjustment_value',
		'default' => '0',
		'type'    => 'number',
		'custom_attributes' => array(
			'min'  => '0',
			'step' => '0.01',
		),
	),

	array(
		'title'   => __( 'Adjustment Direction', 'addify_rfq' ),
		'desc'    => __( 'Choose whether to increase or decrease prices.', 'addify_rfq' ),
		'id'      => 'afrfq_adjustment_direction',
		'default' => 'increase',
		'type'    => 'select',
		'options' => array(
			'increase' => __( 'Increase prices', 'addify_rfq' ),
			'decrease' => __( 'Decrease prices', 'addify_rfq' ),
		),
	),

	// Offered Price System
	array(
		'title' => __( 'Offered Price System', 'addify_rfq' ),
		'type'  => 'title',
		'desc'  => __( 'Configure the offered price system for admin quotes.', 'addify_rfq' ),
		'id'    => 'afrfq_offered_price_system',
	),

	array(
		'title'   => __( 'Enable Offered Prices', 'addify_rfq' ),
		'desc'    => __( 'Allow admins to offer custom pricing for quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_enable_offered_prices',
		'default' => 'yes',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Show Original Prices', 'addify_rfq' ),
		'desc'    => __( 'Display original prices alongside offered prices.', 'addify_rfq' ),
		'id'      => 'afrfq_show_original_prices',
		'default' => 'yes',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Offered Price Label', 'addify_rfq' ),
		'desc'    => __( 'Label to display for offered prices.', 'addify_rfq' ),
		'id'      => 'afrfq_offered_price_label',
		'default' => __( 'Our Price', 'addify_rfq' ),
		'type'    => 'text',
	),

	array(
		'title'   => __( 'Original Price Label', 'addify_rfq' ),
		'desc'    => __( 'Label to display for original prices when showing both.', 'addify_rfq' ),
		'id'      => 'afrfq_original_price_label',
		'default' => __( 'List Price', 'addify_rfq' ),
		'type'    => 'text',
	),

	// Tax Settings
	array(
		'title' => __( 'Tax Settings', 'addify_rfq' ),
		'type'  => 'title',
		'desc'  => __( 'Configure tax-specific settings for quotes.', 'addify_rfq' ),
		'id'    => 'afrfq_tax_settings',
	),

	array(
		'title'   => __( 'Tax Calculation Method', 'addify_rfq' ),
		'desc'    => __( 'Choose how taxes are calculated for quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_tax_calculation_method',
		'default' => 'woocommerce',
		'type'    => 'select',
		'options' => array(
			'woocommerce' => __( 'Use WooCommerce tax settings', 'addify_rfq' ),
			'custom'      => __( 'Custom tax calculation', 'addify_rfq' ),
			'none'        => __( 'No tax calculation', 'addify_rfq' ),
		),
	),

	array(
		'title'   => __( 'Show Tax Breakdown', 'addify_rfq' ),
		'desc'    => __( 'Display detailed tax breakdown in quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_show_tax_breakdown',
		'default' => 'no',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Tax Exemption for Quotes', 'addify_rfq' ),
		'desc'    => __( 'Allow tax exemption for specific quote requests.', 'addify_rfq' ),
		'id'      => 'afrfq_allow_tax_exemption',
		'default' => 'no',
		'type'    => 'checkbox',
	),

	// Currency Settings
	array(
		'title' => __( 'Currency Settings', 'addify_rfq' ),
		'type'  => 'title',
		'desc'  => __( 'Configure multi-currency support for quotes.', 'addify_rfq' ),
		'id'    => 'afrfq_currency_settings',
	),

	array(
		'title'   => __( 'Multi-Currency Support', 'addify_rfq' ),
		'desc'    => __( 'Enable multi-currency support for quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_enable_multi_currency',
		'default' => 'no',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Default Quote Currency', 'addify_rfq' ),
		'desc'    => __( 'Default currency for new quotes.', 'addify_rfq' ),
		'id'      => 'afrfq_default_quote_currency',
		'default' => get_woocommerce_currency(),
		'type'    => 'select',
		'options' => get_woocommerce_currencies(),
	),

	array(
		'title'   => __( 'Currency Conversion', 'addify_rfq' ),
		'desc'    => __( 'Automatically convert prices based on exchange rates.', 'addify_rfq' ),
		'id'      => 'afrfq_auto_currency_conversion',
		'default' => 'no',
		'type'    => 'checkbox',
	),

	// Advanced Settings
	array(
		'title' => __( 'Advanced Pricing', 'addify_rfq' ),
		'type'  => 'title',
		'desc'  => __( 'Advanced pricing configuration options.', 'addify_rfq' ),
		'id'    => 'afrfq_advanced_pricing',
	),

	array(
		'title'   => __( 'Round Prices', 'addify_rfq' ),
		'desc'    => __( 'Round prices to nearest whole number.', 'addify_rfq' ),
		'id'      => 'afrfq_round_prices',
		'default' => 'no',
		'type'    => 'checkbox',
	),

	array(
		'title'   => __( 'Minimum Quote Amount', 'addify_rfq' ),
		'desc'    => __( 'Minimum total amount required for quote requests.', 'addify_rfq' ),
		'id'      => 'afrfq_minimum_quote_amount',
		'default' => '0',
		'type'    => 'number',
		'custom_attributes' => array(
			'min'  => '0',
			'step' => '0.01',
		),
	),

	array(
		'title'   => __( 'Maximum Quote Amount', 'addify_rfq' ),
		'desc'    => __( 'Maximum total amount allowed for quote requests (0 for unlimited).', 'addify_rfq' ),
		'id'      => 'afrfq_maximum_quote_amount',
		'default' => '0',
		'type'    => 'number',
		'custom_attributes' => array(
			'min'  => '0',
			'step' => '0.01',
		),
	),

	array(
		'type' => 'sectionend',
		'id'   => 'afrfq_pricing_tax_settings_end',
	),
);

return apply_filters( 'afrfq_pricing_tax_settings', $afrfq_pricing_tax_settings );
