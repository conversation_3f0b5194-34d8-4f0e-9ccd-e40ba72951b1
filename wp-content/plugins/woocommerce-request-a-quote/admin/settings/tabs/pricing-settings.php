<?php
/**
 * Pricing settings tab fields registration
 *
 * @package  woocommerce-request-a-quote
 * @version  1.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

// Register pricing settings section
add_settings_section(
	'afrfq-pricing-sec',
	esc_html__( 'Pricing & Tax Settings', 'addify_rfq' ),
	'',
	'afrfq_pricing_setting_section'
);

// Show Prices in Quotes
add_settings_field(
	'afrfq_show_prices',
	esc_html__( 'Show Prices in Quotes', 'addify_rfq' ),
	'afrfq_show_prices_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Display product prices in quote forms and quote pages.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_show_prices'
);

// Tax Display in Quotes
add_settings_field(
	'afrfq_show_tax_inclusive',
	esc_html__( 'Tax Display in Quotes', 'addify_rfq' ),
	'afrfq_show_tax_inclusive_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Choose how to display prices with tax in quotes.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_show_tax_inclusive'
);

// Show Price Suffix
add_settings_field(
	'afrfq_show_price_suffix',
	esc_html__( 'Show Price Suffix', 'addify_rfq' ),
	'afrfq_show_price_suffix_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Display tax suffix (inc. tax / ex. tax) with prices.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_show_price_suffix'
);

// Enable Price Adjustments
add_settings_field(
	'afrfq_enable_price_adjustments',
	esc_html__( 'Enable Price Adjustments', 'addify_rfq' ),
	'afrfq_enable_price_adjustments_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Allow automatic price increases or decreases for quotes.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_enable_price_adjustments'
);

// Adjustment Type
add_settings_field(
	'afrfq_adjustment_type',
	esc_html__( 'Adjustment Type', 'addify_rfq' ),
	'afrfq_adjustment_type_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Choose how price adjustments are calculated.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_adjustment_type'
);

// Adjustment Value
add_settings_field(
	'afrfq_adjustment_value',
	esc_html__( 'Adjustment Value', 'addify_rfq' ),
	'afrfq_adjustment_value_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Enter the adjustment value (percentage or fixed amount).', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_adjustment_value'
);

// Adjustment Direction
add_settings_field(
	'afrfq_adjustment_direction',
	esc_html__( 'Adjustment Direction', 'addify_rfq' ),
	'afrfq_adjustment_direction_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Choose whether to increase or decrease prices.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_adjustment_direction'
);

// Enable Offered Prices
add_settings_field(
	'afrfq_enable_offered_prices',
	esc_html__( 'Enable Offered Prices', 'addify_rfq' ),
	'afrfq_enable_offered_prices_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Allow admins to offer custom pricing for quotes.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_enable_offered_prices'
);

// Show Original Prices
add_settings_field(
	'afrfq_show_original_prices',
	esc_html__( 'Show Original Prices', 'addify_rfq' ),
	'afrfq_show_original_prices_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Display original prices alongside offered prices.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_show_original_prices'
);

// Offered Price Label
add_settings_field(
	'afrfq_offered_price_label',
	esc_html__( 'Offered Price Label', 'addify_rfq' ),
	'afrfq_offered_price_label_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Label to display for offered prices.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_offered_price_label'
);

// Tax Calculation Method
add_settings_field(
	'afrfq_tax_calculation_method',
	esc_html__( 'Tax Calculation Method', 'addify_rfq' ),
	'afrfq_tax_calculation_method_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Choose how taxes are calculated for quotes.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_tax_calculation_method'
);

// Show Tax Breakdown
add_settings_field(
	'afrfq_show_tax_breakdown',
	esc_html__( 'Show Tax Breakdown', 'addify_rfq' ),
	'afrfq_show_tax_breakdown_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Display detailed tax breakdown in quotes.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_show_tax_breakdown'
);

// Multi-Currency Support
add_settings_field(
	'afrfq_enable_multi_currency',
	esc_html__( 'Multi-Currency Support', 'addify_rfq' ),
	'afrfq_enable_multi_currency_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Enable multi-currency support for quotes.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_enable_multi_currency'
);

// Minimum Quote Amount
add_settings_field(
	'afrfq_minimum_quote_amount',
	esc_html__( 'Minimum Quote Amount', 'addify_rfq' ),
	'afrfq_minimum_quote_amount_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Minimum total amount required for quote requests.', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_minimum_quote_amount'
);

// Maximum Quote Amount
add_settings_field(
	'afrfq_maximum_quote_amount',
	esc_html__( 'Maximum Quote Amount', 'addify_rfq' ),
	'afrfq_maximum_quote_amount_callback',
	'afrfq_pricing_setting_section',
	'afrfq-pricing-sec',
	array( esc_html__( 'Maximum total amount allowed for quote requests (0 for unlimited).', 'addify_rfq' ) )
);

register_setting(
	'afrfq_pricing_setting_fields',
	'afrfq_maximum_quote_amount'
);

/**
 * Callback functions for pricing settings
 */

function afrfq_show_prices_callback( $args ) {
	$value = get_option( 'afrfq_show_prices', 'yes' );
	echo '<select name="afrfq_show_prices" id="afrfq_show_prices">';
	echo '<option value="yes"' . selected( $value, 'yes', false ) . '>' . esc_html__( 'Show prices', 'addify_rfq' ) . '</option>';
	echo '<option value="no"' . selected( $value, 'no', false ) . '>' . esc_html__( 'Hide prices (Price on request)', 'addify_rfq' ) . '</option>';
	echo '</select>';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_show_tax_inclusive_callback( $args ) {
	$value = get_option( 'afrfq_show_tax_inclusive', 'auto' );
	echo '<select name="afrfq_show_tax_inclusive" id="afrfq_show_tax_inclusive">';
	echo '<option value="auto"' . selected( $value, 'auto', false ) . '>' . esc_html__( 'Use WooCommerce settings', 'addify_rfq' ) . '</option>';
	echo '<option value="incl"' . selected( $value, 'incl', false ) . '>' . esc_html__( 'Including tax', 'addify_rfq' ) . '</option>';
	echo '<option value="excl"' . selected( $value, 'excl', false ) . '>' . esc_html__( 'Excluding tax', 'addify_rfq' ) . '</option>';
	echo '</select>';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_show_price_suffix_callback( $args ) {
	$value = get_option( 'afrfq_show_price_suffix', 'yes' );
	echo '<input type="checkbox" name="afrfq_show_price_suffix" id="afrfq_show_price_suffix" value="yes"' . checked( $value, 'yes', false ) . ' />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_enable_price_adjustments_callback( $args ) {
	$value = get_option( 'afrfq_enable_price_adjustments', 'no' );
	echo '<input type="checkbox" name="afrfq_enable_price_adjustments" id="afrfq_enable_price_adjustments" value="yes"' . checked( $value, 'yes', false ) . ' />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_adjustment_type_callback( $args ) {
	$value = get_option( 'afrfq_adjustment_type', 'percentage' );
	echo '<select name="afrfq_adjustment_type" id="afrfq_adjustment_type">';
	echo '<option value="percentage"' . selected( $value, 'percentage', false ) . '>' . esc_html__( 'Percentage', 'addify_rfq' ) . '</option>';
	echo '<option value="fixed"' . selected( $value, 'fixed', false ) . '>' . esc_html__( 'Fixed amount', 'addify_rfq' ) . '</option>';
	echo '</select>';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_adjustment_value_callback( $args ) {
	$value = get_option( 'afrfq_adjustment_value', '0' );
	echo '<input type="number" name="afrfq_adjustment_value" id="afrfq_adjustment_value" value="' . esc_attr( $value ) . '" min="0" step="0.01" />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_adjustment_direction_callback( $args ) {
	$value = get_option( 'afrfq_adjustment_direction', 'increase' );
	echo '<select name="afrfq_adjustment_direction" id="afrfq_adjustment_direction">';
	echo '<option value="increase"' . selected( $value, 'increase', false ) . '>' . esc_html__( 'Increase prices', 'addify_rfq' ) . '</option>';
	echo '<option value="decrease"' . selected( $value, 'decrease', false ) . '>' . esc_html__( 'Decrease prices', 'addify_rfq' ) . '</option>';
	echo '</select>';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_enable_offered_prices_callback( $args ) {
	$value = get_option( 'afrfq_enable_offered_prices', 'yes' );
	echo '<input type="checkbox" name="afrfq_enable_offered_prices" id="afrfq_enable_offered_prices" value="yes"' . checked( $value, 'yes', false ) . ' />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_show_original_prices_callback( $args ) {
	$value = get_option( 'afrfq_show_original_prices', 'yes' );
	echo '<input type="checkbox" name="afrfq_show_original_prices" id="afrfq_show_original_prices" value="yes"' . checked( $value, 'yes', false ) . ' />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_offered_price_label_callback( $args ) {
	$value = get_option( 'afrfq_offered_price_label', __( 'Our Price', 'addify_rfq' ) );
	echo '<input type="text" name="afrfq_offered_price_label" id="afrfq_offered_price_label" value="' . esc_attr( $value ) . '" class="regular-text" />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_tax_calculation_method_callback( $args ) {
	$value = get_option( 'afrfq_tax_calculation_method', 'woocommerce' );
	echo '<select name="afrfq_tax_calculation_method" id="afrfq_tax_calculation_method">';
	echo '<option value="woocommerce"' . selected( $value, 'woocommerce', false ) . '>' . esc_html__( 'Use WooCommerce tax settings', 'addify_rfq' ) . '</option>';
	echo '<option value="custom"' . selected( $value, 'custom', false ) . '>' . esc_html__( 'Custom tax calculation', 'addify_rfq' ) . '</option>';
	echo '<option value="none"' . selected( $value, 'none', false ) . '>' . esc_html__( 'No tax calculation', 'addify_rfq' ) . '</option>';
	echo '</select>';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_show_tax_breakdown_callback( $args ) {
	$value = get_option( 'afrfq_show_tax_breakdown', 'no' );
	echo '<input type="checkbox" name="afrfq_show_tax_breakdown" id="afrfq_show_tax_breakdown" value="yes"' . checked( $value, 'yes', false ) . ' />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_enable_multi_currency_callback( $args ) {
	$value = get_option( 'afrfq_enable_multi_currency', 'no' );
	echo '<input type="checkbox" name="afrfq_enable_multi_currency" id="afrfq_enable_multi_currency" value="yes"' . checked( $value, 'yes', false ) . ' />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_minimum_quote_amount_callback( $args ) {
	$value = get_option( 'afrfq_minimum_quote_amount', '0' );
	echo '<input type="number" name="afrfq_minimum_quote_amount" id="afrfq_minimum_quote_amount" value="' . esc_attr( $value ) . '" min="0" step="0.01" />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}

function afrfq_maximum_quote_amount_callback( $args ) {
	$value = get_option( 'afrfq_maximum_quote_amount', '0' );
	echo '<input type="number" name="afrfq_maximum_quote_amount" id="afrfq_maximum_quote_amount" value="' . esc_attr( $value ) . '" min="0" step="0.01" />';
	echo '<p class="description">' . esc_html( $args[0] ) . '</p>';
}
