<?php
// Simple script to check for quotes in the database
require_once('wp-config.php');
require_once('wp-load.php');

// Get all quotes
$quotes = get_posts(array(
    'post_type' => 'addify_quote',
    'post_status' => 'any',
    'numberposts' => -1,
    'orderby' => 'date',
    'order' => 'DESC'
));

echo "Found " . count($quotes) . " quotes:\n\n";

foreach ($quotes as $quote) {
    echo "Quote ID: " . $quote->ID . "\n";
    echo "Title: " . $quote->post_title . "\n";
    echo "Status: " . $quote->post_status . "\n";
    echo "Date: " . $quote->post_date . "\n";
    
    // Get quote meta
    $quote_status = get_post_meta($quote->ID, 'quote_status', true);
    $customer_user = get_post_meta($quote->ID, '_customer_user', true);
    $quote_contents = get_post_meta($quote->ID, 'quote_contents', true);
    
    echo "Quote Status: " . $quote_status . "\n";
    echo "Customer User: " . $customer_user . "\n";
    echo "Has Quote Contents: " . (empty($quote_contents) ? 'No' : 'Yes') . "\n";
    echo "---\n\n";
}
?>
